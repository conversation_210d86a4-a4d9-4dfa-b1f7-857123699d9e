import React, { Fragment } from "react";
import { LogBox, StatusBar, Text,Platform } from "react-native";
import { Provider } from "react-redux";
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { NavigationContainer } from '@react-navigation/native';
import configStore from "./src/configs/StoreConfig";
import InitialRoute from "./src/navigation/InitialRoute";
import AddCrane from "./src/views/Crane/AddCrane";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "./src/components/NoInternet/noInternet";
import { SafeAreaView } from "react-native-safe-area-context";

class App extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isConnected: true
    };
    console.reportErrorsAsExceptions = false;
    LogBox.ignoreAllLogs(true);
    Text.defaultProps = Text.defaultProps || {};
    Text.defaultProps.allowFontScaling = false;
    Text.defaultProps = Text.defaultProps || {};
    Text.defaultProps.allowFontScaling = false;
  }

  componentDidMount = () => {
    if (Platform.OS === 'ios') {
      this.networkCheck()
    }
  }
  networkCheck = () => {
    NetInfo.fetch().then(state => {
      if (!state.isConnected && !state.isInternetReachable) {
        this.setState({ isConnected: false })
      } else {
        this.setState({ isConnected: true })
      }
    });
  }

  render() {
    return (
      <SafeAreaView style={{ flex: 1, }}>
        <GestureHandlerRootView style={{ flex: 1 }}>
          {!this.state.isConnected ?
            <NoInternet
              Refresh={() => this.networkCheck()}
            /> :
            <Provider store={configStore}>
              <NavigationContainer>
                <InitialRoute />
              </NavigationContainer>
            </Provider>
          }
        </GestureHandlerRootView>
      </SafeAreaView>
      //<AddCrane/>
    );
  }
}


export default App;
