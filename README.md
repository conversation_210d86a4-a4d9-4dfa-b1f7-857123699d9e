# FolloIT React Native App

React Native application upgraded to version 0.68.0 with comprehensive package compatibility fixes.

## 📋 Version Requirements

### Development Environment
- **Node.js**: v23.10.0
- **npm**: 10.9.2
- **Java**: OpenJDK 17.0.13 (Corretto-17.0.13.11.1)
- **Xcode**: 16.2 (Build version 16C5032a)
- **Ruby**: https://stackoverflow.com/a/79221894

### React Native & Dependencies
- **React Native**: 0.68.0
- **React**: 17.0.2
- **Android Gradle Plugin**: 7.0.1
- **Gradle**: 7.3

### Target Platforms
- **Android**: 
  - Compile SDK: 31
  - Target SDK: 34
  - Min SDK: 23
- **iOS**: 
  - Deployment Target: 11.0
  - Xcode 16.2 compatible

## 🚀 Quick Start

### Installation

1. **Install dependencies:**
   ```bash
   npm install --legacy-peer-deps
   ```

2. **Install iOS dependencies:**
   ```bash
   cd ios && pod install && cd ..
   ```

### Running the App

#### Android
```bash
npm run android
```

#### iOS
```bash
npm run ios
```

## 🔧 Package Compatibility Fixes

This project includes a unified patch system that automatically fixes package compatibility issues after `npm install`. The patches are applied via the `postinstall` script.

### What Gets Fixed

#### Android Namespace Issues
- All React Native packages get proper Android namespaces for AGP 8+ compatibility
- BuildConfig generation enabled for all packages
- Fixes for 20+ popular React Native packages including:
  - `@react-native-community/blur`
  - `@react-native-firebase/*` packages
  - `react-native-gesture-handler`
  - `react-native-reanimated`
  - `react-native-maps`
  - And many more...

#### iOS Compatibility
- `boost.podspec` URL fixes for proper download
- Yoga boolean operands compilation fixes
- React Native 0.68.0 iOS compatibility patches

### Manual Patch Application

If you need to manually apply patches:

```bash
# Apply all patches
npm run postinstall

# Or run directly
./apply-patches.sh
```

## 📦 Key Package Versions

### Core Dependencies
```json
{
  "react-native": "0.68.0",
  "react": "17.0.2",
  "@react-native-community/cli": "^7.0.3",
  "@react-native-community/cli-platform-android": "^7.0.1",
  "@react-native-community/cli-platform-ios": "^7.0.1"
}
```

### Major Packages
- **Firebase**: `@react-native-firebase/*@11.5.0`
- **Navigation**: `react-navigation@4.4.4`
- **Gesture Handler**: `react-native-gesture-handler@2.16.0`
- **Reanimated**: `react-native-reanimated@2.17.0`
- **Vector Icons**: `react-native-vector-icons@7.1.0`
- **PDF Viewer**: `react-native-pdf@6.2.2` (downgraded for Java 11 compatibility)

## 🛠 Build Configuration

### Android
- **Compile SDK**: 31 (React Native 0.68.0 standard)
- **Target SDK**: 34 (latest Android)
- **Java Version**: 11 (React Native 0.68.0 requirement)
- **Gradle**: 7.3
- **Android Gradle Plugin**: 7.0.1

### iOS
- **Deployment Target**: 11.0
- **Xcode**: 16.2
- **CocoaPods**: Latest

## 🚨 Important Notes

### Package Downgrades
- **react-native-pdf**: Downgraded from 6.6.2 to 6.2.2 for Java 11 compatibility
- **react-native-image-picker**: Maintained at 4.10.3 for React Native 0.68.0 compatibility
- **react-native-orientation-locker**: Maintained at 1.1.8 for SDK 31 compatibility

### Autolinking Configuration
The project includes a custom `react-native.config.js` with proper package import paths for:
- `react-native-vector-icons`
- `@react-native-community/blur`
- `@react-native-community/netinfo`
- `@react-native-firebase/analytics`
- And other packages with autolinking issues

## 🧪 Testing

### Tested Configurations
- ✅ **Android**: Pixel 8a API 31 (Android 12)
- ✅ **Build**: 681 tasks completed successfully
- ✅ **APK Generation**: Working
- ✅ **App Installation**: Working

### Build Status
- **Last Successful Build**: Android APK built and installed successfully
- **Build Time**: ~26 seconds
- **Package Compilation**: All 30+ packages compile without errors

## 🔄 Upgrade Process

This project was upgraded from React Native 0.66.0 to 0.68.0 following these steps:

1. **Core React Native Upgrade**: Updated to 0.68.0 specifications
2. **Gradle Configuration**: Updated Android Gradle Plugin and build tools
3. **Package Compatibility**: Fixed autolinking and namespace issues
4. **Java Compatibility**: Maintained Java 11 for React Native 0.68.0
5. **iOS Compatibility**: Applied necessary iOS patches

## 📁 Project Structure

```
├── android/                 # Android project files
├── ios/                     # iOS project files
├── patches/                 # Package compatibility patches
│   ├── unified-package-fixes.patch  # Main patch file
│   └── ...                  # Individual package patches
├── apply-patches.sh         # Patch application script
├── react-native.config.js   # Autolinking configuration
└── package.json             # Dependencies and scripts
```

## 🆘 Troubleshooting

### Common Issues

1. **Build Failures**: Run `npm run clean` followed by `npm run install-all`
2. **iOS Pod Issues**: Delete `ios/Pods` and `ios/Podfile.lock`, then run `cd ios && pod install`
3. **Android Namespace Errors**: Ensure patches are applied with `./apply-patches.sh`
4. **Metro Cache Issues**: Run `npm start -- --reset-cache`

### Clean Install
```bash
npm run clean
npm run install-all
```

## 📜 Scripts

- `npm run android` - Run Android app
- `npm run ios` - Run iOS app
- `npm run clean` - Clean all caches and dependencies
- `npm run install-all` - Install all dependencies (npm + pods)
- `npm run postinstall` - Apply compatibility patches

## 🤝 Contributing

When adding new React Native packages:

1. Test the build after installation
2. If namespace errors occur, add the package to `patches/unified-package-fixes.patch`
3. Update the autolinking configuration in `react-native.config.js` if needed
4. Test on both Android and iOS platforms

## 📄 License

This project is private and proprietary to Follo Inc.

---

**Last Updated**: December 2024  
**React Native Version**: 0.68.0  
**Tested Platforms**: Android (API 31), iOS (16.2)