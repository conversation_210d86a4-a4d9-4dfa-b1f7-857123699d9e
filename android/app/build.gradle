apply plugin: "com.android.application"
apply plugin: "com.facebook.react"
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'

import com.android.build.OutputFile

react {
    /* Folders */
    root = file("../../")
    reactNativeDir = file("../../node_modules/react-native")
    codegenDir = file("../../node_modules/@react-native/codegen")
    cliFile = file("../../node_modules/react-native/cli.js")
}

// Project extension for compatibility with libraries that expect project.ext.react
project.ext.react = [
    enableHermes: false,
    hermesEnabled: false
]
// Temporarily disabled for React Native 0.72 compatibility
// apply from: "../../node_modules/react-native-code-push/android/codepush.gradle"

def enableSeparateBuildPerCPUArchitecture = false
def enableProguardInReleaseBuilds = false
def jscFlavor = 'org.webkit:android-jsc:+'
def hermesEnabled = false;

android {
    namespace "com.follo.scm"
    compileSdkVersion rootProject.ext.compileSdkVersion
    // buildToolsVersion rootProject.ext.buildToolsVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    
    buildFeatures {
        buildConfig true
    }

    defaultConfig {
        applicationId "com.follo.scm"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion

        versionCode 84
        versionName "2.0.0"
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
    }

    packagingOptions {
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        exclude '/META-INF/{AL2.0,LGPL2.1}'
    }

    splits {
        abi {
            reset()
            universalApk false
            include "armeabi-v7a", "x86", "arm64-v8a", "x86_64"
        }
    }

    signingConfigs {
        debug {
            storeFile file("Keystore/folloIT.jks")
            storePassword "folloIT"
            keyAlias "folloIT"
            keyPassword "folloIT"
        }
        release{
            storeFile file("Keystore/follo_production_keystore")
            storePassword "T?K)2?vLP+"
            keyAlias "follo"
            keyPassword "ev-N9:Q6V2"
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }

    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            def versionCodes = ["armeabi-v7a": 1, "x86": 2, "arm64-v8a": 3, "x86_64": 4]
            def abi = output.getFilter(OutputFile.ABI)
            if (abi != null) {
                output.versionCodeOverride =
                        versionCodes.get(abi) * 1048576 + defaultConfig.versionCode
            }
        }
    }
}

dependencies {
    implementation 'com.facebook.fresco:fresco:2.0.0'
    implementation 'com.facebook.fresco:animated-gif:2.0.0'
    implementation 'com.github.Dimezis:BlurView:version-2.0.3'
    // implementation('com.eightbitlab:blurview:1.6.6') {
    //     force = true
    // }
    implementation fileTree(dir: "libs", include: ["*.jar"])
    
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    implementation "com.google.firebase:firebase-messaging:23.1.2"
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
    implementation project(':react-native-notifications')
    implementation("com.squareup.okhttp3:okhttp-urlconnection:4.10.0")

    def multidex_version = "2.0.1"
    implementation "androidx.multidex:multidex:$multidex_version"
    
    // Explicitly include the correct annotation library for React Native 0.72.0 with compileSdk 35
    implementation 'androidx.annotation:annotation:1.6.0'
    
    // Remove problematic AndroidX Core dependency
    // implementation 'androidx.core:core:1.8.0'
    // implementation 'androidx.core:core-ktx:1.8.0'

    // debugImplementation("com.facebook.flipper:flipper:${FLIPPER_VERSION}") {
    //     exclude group:'com.facebook.fbjni'
    // }

    // debugImplementation("com.facebook.flipper:flipper-network-plugin:${FLIPPER_VERSION}") {
    //     exclude group:'com.facebook.flipper'
    //     exclude group:'com.squareup.okhttp3', module:'okhttp'
    // }

    // debugImplementation("com.facebook.flipper:flipper-fresco-plugin:${FLIPPER_VERSION}") {
    //     exclude group:'com.facebook.flipper'
    // }

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}


task copyDownloadableDepsToLibs(type: Copy) {
    from configurations.implementation
    into 'libs'
}

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
