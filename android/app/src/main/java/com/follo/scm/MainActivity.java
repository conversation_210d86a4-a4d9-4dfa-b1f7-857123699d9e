package com.follo.scm;

import com.facebook.react.ReactActivity;
import io.branch.rnbranch.*; // <-- add this
import android.content.Intent; // <-- and this
import android.content.res.Configuration;

public class MainActivity extends ReactActivity {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  @Override
  protected String getMainComponentName() {
    return "FolloIT";
  }

//  adb reverse tcp:8081 tcp:8081
// Override onStart, onNewIntent:

  @Override
  protected void onStart() {
    super.onStart();
    RNBranchModule.initSession(getIntent().getData(), this);
  }

  @Override
  public void onNewIntent(Intent intent) {
    super.onNewIntent(intent);
    setIntent(intent);
        RNBranchModule.onNewIntent(intent);
  }

  @Override
   public void onConfigurationChanged(Configuration newConfig) {
       super.onConfigurationChanged(newConfig);
       Intent intent = new Intent("onConfigurationChanged");
       intent.putExtra("newConfig", newConfig);
       this.sendBroadcast(intent);
   }
   


//  @Override
//  public void onNewIntent(Intent intent) {
//    super.onNewIntent(intent);
//    intent.putExtra("branch_force_new_session", true);
//    RNBranchModule.onNewIntent(intent);
//  }
// ...

}
