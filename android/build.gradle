buildscript {
    ext {
//        buildToolsVersion = "31.0.0"
        minSdkVersion = 24
        compileSdkVersion = 34
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
    
        kotlinVersion = "1.8.10"
        googlePlayServicesVersion = "17.0.0"
        playServicesVersion = "17.0.0"
        androidMapsUtilsVersion = "2.2.2"
        okhttp = "4.10.0"
    }
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }

    dependencies {
        classpath("com.android.tools.build:gradle:7.4.2")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("com.google.gms:google-services:4.3.15")
        classpath("com.google.firebase:firebase-crashlytics-gradle:2.9.2")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}

allprojects {
    // Force all Kotlin and Java compilations to use Java 17 for compatibility
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions {
            jvmTarget = "17"
        }
    }
    
    tasks.withType(JavaCompile).configureEach {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
   configurations.all {
    resolutionStrategy {
        // force 'androidx.core:core-ktx:1.8.0' 
        // force 'androidx.core:core:1.8.0'    
        force 'com.facebook.soloader:soloader:0.10.4' 
        force 'com.google.guava:guava:31.1-android'
        force 'com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava'

        // Force React Native 0.72.0 compatible AndroidX versions (compileSdk 35)
        force 'androidx.annotation:annotation:1.3.0'
        force 'androidx.annotation:annotation-experimental:1.3.0'
        force 'androidx.core:core:1.10.1'
        force 'androidx.core:core-ktx:1.10.1'

         eachDependency { DependencyResolveDetails details ->
                if (details.requested.group == 'com.eightbitlab' && details.requested.name == 'blurview') {
                    details.useTarget group: 'com.github.Dimezis', name: 'BlurView', version: 'version-2.0.3'
                }
                // Fix Guava conflicts
                if (details.requested.group == 'com.google.guava' && details.requested.name == 'guava') {
                    details.useVersion '31.1-android'
                }
                // Exclude conflicting annotation-jvm library
                if (details.requested.group == 'androidx.annotation' && details.requested.name == 'annotation-jvm') {
                    details.useTarget group: 'androidx.annotation', name: 'annotation', version: '1.3.0'
                }
         }
    }
    
    // Exclude the conflicting annotation-jvm library globally
    exclude group: 'androidx.annotation', module: 'annotation-jvm'
}

    // Set Java 17 compatibility for all projects (React Native 0.72.0 requirement)
    tasks.withType(JavaCompile) {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile) {
        kotlinOptions {
            jvmTarget = "17"
        }
    }

    repositories {
        google()
        mavenCentral()
        maven { url 'https://www.jitpack.io' }
        maven { url 'https://maven.google.com' }
        
//        exclusiveContent {
//            filter {
//                includeGroup "com.facebook.react"
//            }
//            forRepository {
//                maven {
//                    url "$rootDir/../node_modules/react-native/android"
//                }
//            }
//        }
        
        maven {
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        mavenLocal()
    }
}

subprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            project.android {
                if (namespace == null) {
                    namespace project.group
                }

                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_17
                    targetCompatibility JavaVersion.VERSION_17
                }
                tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                    kotlinOptions {
                        jvmTarget = "17"
                    }
                }
                java {
                    toolchain {
                        languageVersion = JavaLanguageVersion.of(17)
                    }
                }
            }
        }
        
        // Add hermesEnabled property for all subprojects
        if (project.name == "app") {
            project.ext.hermesEnabled = false
        }
//        if (project.hasProperty("android")) {
//            android {
//                compileOptions {
//                    sourceCompatibility JavaVersion.VERSION_11
//                    targetCompatibility JavaVersion.VERSION_11
//                }
//            }
//        }
//
//        tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile) {
//            kotlinOptions {
//                jvmTarget = "11"
//            }
//        }
    }
}