#!/bin/bash

echo "🔧 Applying all package patches and fixes..."

# Apply patch-package patches first (including react-native+0.68.0.patch)
echo "📦 Applying patch-package patches..."
if command -v patch-package &> /dev/null; then
    patch-package
    if [ $? -eq 0 ]; then
        echo "✅ patch-package patches applied successfully"
    else
        echo "❌ patch-package patches failed"
        exit 1
    fi
else
    echo "⚠️  patch-package not found, installing..."
    npm install patch-package --save-dev
    patch-package
fi

# Execute the unified patch file for additional fixes
echo "📦 Running unified package fixes..."
if [ -f "patches/unified-package-fixes.patch" ]; then
    bash patches/unified-package-fixes.patch
    if [ $? -eq 0 ]; then
        echo "✅ Unified package fixes applied successfully"
    else
        echo "❌ Unified package fixes failed"
        exit 1
    fi
else
    echo "⚠️  Unified patch file not found!"
    exit 1
fi

echo ""
echo "🎉 All patches applied successfully!"
echo ""
echo "📋 Summary of patches applied:"
echo "  ✅ react-native+0.68.0.patch (via patch-package)"
echo "  ✅ Unified package compatibility fixes"
echo "  ✅ Android namespace fixes"
echo "  ✅ iOS boost.podspec fixes"
echo "  ✅ Yoga boolean operands fixes"
echo "  ✅ Yoga hadOverflow bitwise operator fix"
echo ""
echo "Next steps:"
echo "1. Run 'cd ios && pod install' to update iOS dependencies"
echo "2. Build and test your app" 