<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CodePushDeploymentKey</key>
	<string>nHjMRDTPPnw-XDAm1upoDRJgCvQWtkA2SOKt-</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Follo</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleURLName</key>
			<string>BRANCH_LINK</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>FolloIT</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to access  the camera for profile picture capture</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to access the location for camera meta data in profile picture </string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to access photo library for choose existing image to change profile picture</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to access photo library for choose existing image to change profile picture</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to access the location</string>
	<key>UIAppFonts</key>
	<array>
		<string>Montserrat-Thin.ttf</string>
		<string>Montserrat-ThinItalic.ttf</string>
		<string>Montserrat-ExtraLight.ttf</string>
		<string>Montserrat-ExtraLightItalic.ttf</string>
		<string>Montserrat-Light.ttf</string>
		<string>Montserrat-LightItalic.ttf</string>
		<string>Montserrat-Regular.ttf</string>
		<string>Montserrat-Italic.ttf</string>
		<string>Montserrat-Medium.ttf</string>
		<string>Montserrat-MediumItalic.ttf</string>
		<string>Montserrat-SemiBold.ttf</string>
		<string>Montserrat-SemiBoldItalic.ttf</string>
		<string>Montserrat-Bold.ttf</string>
		<string>Montserrat-BoldItalic.ttf</string>
		<string>Montserrat-ExtraBold.ttf</string>
		<string>Montserrat-ExtraBoldItalic.ttf</string>
		<string>Montserrat-Black.ttf</string>
		<string>Montserrat-BlackItalic.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>branch_app_domain</key>
	<string>j912x.test-app.link</string>
	<key>branch_key</key>
	<string>key_test_if8lwW33x1F0ejtcWQSuvbojEBet6BI4</string>
</dict>
</plist>
