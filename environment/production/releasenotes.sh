#!/bin/bash
# Generates changelog day by day
# Based on sample from https://stackoverflow.com/questions/2976665/git-changelog-day-by-day
CREATE_DIR="mkdir -p ../../release-notes && touch ../../release-notes/release-notes-prod.txt"
eval $CREATE_DIR
BRANCH=$(git symbolic-ref HEAD | sed -e 's,.*/\(.*\),\1,')
FILE=../../release-notes/release-notes-prod.txt
TMP=$(mktemp)
TODAY=$(date +%Y-%m-%d)
YESTERDAY=$(date -v-1d +%Y-%m-%d)
THIS_WEEK=$(date -v-15d +%Y-%m-%d)

for i in "$@"
do
case $i in
    -f=*|--file=*)
    FILE="${i#*=}"
    ;;
    -b=*|--branch=*)
    BRANCH="${i#*=}"
    ;;
    *)
            # unknown option
    ;;
esac
done

{
#READ ENVIRONMENT
READ_ENVIRONMENT="ENVIRONMENT: PRODUCTION"
# Read the version Code and Build Number
READ_BUILD_AND_VERSION_NUMBER="cat ../../version.properties | sed -n -e 2,4p | sed 2d | egrep -w 'versionCode|versionName' | sed 's/versionName/Version Number/' | sed 's/versionCode/Build Number/'"
echo $READ_ENVIRONMENT
eval $READ_BUILD_AND_VERSION_NUMBER
echo **TODAY '('$TODAY')'**
git log --no-merges --since="$TODAY 00:00:00" --format="%cd" --date=short | sort -u -r | while read DATE ; do
    echo
    GIT_PAGER=cat git log --no-merges --format=" * %s" --since="$DATE 00:00:00" --until="$DATE 24:00:00"
    echo
done
} > ${TMP}

{
echo
echo **LAST WEEK '(thru '$THIS_WEEK')'**
git log --no-merges --before="$TODAY 00:00:00" --since="$THIS_WEEK  00:00:00" --format="%cd" --date=short | sort -u -r | while read DATE ; do
    echo
    echo *$DATE*
    echo
    GIT_PAGER=cat git log --no-merges --format=" * %s" --since="$DATE 00:00:00" --until="$DATE 24:00:00"
    echo
done
} >> ${TMP}

head -c 5000 ${TMP} > ${FILE}
rm ${TMP}