<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.follo.scm">
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="false" />

    <application
        android:name=".MainApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:exported='true'
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
            </intent-filter>

            <!-- Branch URI scheme -->
            <intent-filter>
                <data
                    android:host="open"
                    android:scheme="com.follo.scm://"
                    tools:ignore="AppLinkUrlError" />
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <!-- Branch App Links -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="j912x.test-app.link"
                    android:scheme="https" />
                <data
                    android:host="j912x-alternate.test-app.link"
                    android:scheme="https" />

                <data
                    android:host="j912x-alternate.app.link"
                    android:scheme="https" />
                <data
                    android:host="j912x.app.link"
                    android:scheme="https" />
            </intent-filter>


        </activity>


        <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" android:exported='false' />

        <meta-data
            android:name="io.branch.sdk.BranchKey"
            android:value="key_test_if8lwW33x1F0ejtcWQSuvbojEBet6BI4" />
    
        <meta-data
            android:name="io.branch.sdk.TestMode"
            android:value="true" />

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyB2EHXZ7yQZ3KItOpshW2F4DJ5ewmagOWU"/>
 
        <!-- You will also only need to add this uses-library tag -->
        <uses-library android:name="org.apache.http.legacy" android:required="false"/>

    </application>

</manifest>
