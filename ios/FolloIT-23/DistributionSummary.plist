<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>FolloIT.ipa</key>
	<array>
		<dict>
			<key>architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>buildNumber</key>
			<string>5</string>
			<key>certificate</key>
			<dict>
				<key>SHA1</key>
				<string>B76D60A115B9F7468A3CC9DD36F8254E2CAD6BC3</string>
				<key>dateExpires</key>
				<string>17/07/25</string>
				<key>type</key>
				<string>iOS Development</string>
			</dict>
			<key>embeddedBinaries</key>
			<array>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>12.4.3</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>B76D60A115B9F7468A3CC9DD36F8254E2CAD6BC3</string>
						<key>dateExpires</key>
						<string>17/07/25</string>
						<key>type</key>
						<string>iOS Development</string>
					</dict>
					<key>name</key>
					<string>Intercom.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>N9899XA46N</string>
						<key>name</key>
						<string>Follo Technology Inc.</string>
					</dict>
					<key>versionNumber</key>
					<string>12.4.3</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>buildNumber</key>
					<string>1</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>B76D60A115B9F7468A3CC9DD36F8254E2CAD6BC3</string>
						<key>dateExpires</key>
						<string>17/07/25</string>
						<key>type</key>
						<string>iOS Development</string>
					</dict>
					<key>name</key>
					<string>OpenSSL.framework</string>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>N9899XA46N</string>
						<key>name</key>
						<string>Follo Technology Inc.</string>
					</dict>
					<key>versionNumber</key>
					<string>1.1.180</string>
				</dict>
			</array>
			<key>entitlements</key>
			<dict>
				<key>application-identifier</key>
				<string>N9899XA46N.com.follo.scm</string>
				<key>aps-environment</key>
				<string>development</string>
				<key>com.apple.developer.associated-domains</key>
				<array>
					<string>applinks:j912x-alternate.test-app.link</string>
					<string>applinks:j912x.test-app.link</string>
					<string>applinks:j912x-alternate.app.link</string>
					<string>applinks:j912x.app.link</string>
				</array>
				<key>com.apple.developer.icloud-container-environment</key>
				<string>Production</string>
				<key>com.apple.developer.icloud-container-identifiers</key>
				<array>
					<string>iCloud.com.follo.scm</string>
				</array>
				<key>com.apple.developer.icloud-services</key>
				<array>
					<string>CloudDocuments</string>
				</array>
				<key>com.apple.developer.team-identifier</key>
				<string>N9899XA46N</string>
				<key>com.apple.developer.ubiquity-container-identifiers</key>
				<array>
					<string>iCloud.com.follo.scm</string>
				</array>
				<key>com.apple.developer.ubiquity-kvstore-identifier</key>
				<string>N9899XA46N.com.follo.scm</string>
				<key>get-task-allow</key>
				<true/>
			</dict>
			<key>name</key>
			<string>FolloIT.app</string>
			<key>profile</key>
			<dict>
				<key>UUID</key>
				<string>b557316d-dc71-4776-b931-0d4572933c09</string>
				<key>dateExpires</key>
				<string>17/07/25</string>
				<key>name</key>
				<string>Follo_dev_profile</string>
			</dict>
			<key>team</key>
			<dict>
				<key>id</key>
				<string>N9899XA46N</string>
				<key>name</key>
				<string>Follo Technology Inc.</string>
			</dict>
			<key>versionNumber</key>
			<string>1.8.12</string>
		</dict>
	</array>
</dict>
</plist>
