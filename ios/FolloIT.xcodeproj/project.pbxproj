// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		0443A3AB7A0A4D52B439E7F0 /* Montserrat-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E6975FC7550A4AB29B39A334 /* Montserrat-ExtraLightItalic.ttf */; };
		0E1E1CF7256E1B9100AA37A0 /* CloudKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0E1E1CF6256E1B9100AA37A0 /* CloudKit.framework */; };
		0EFB732825878669001A0D9C /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 0EFB732725878669001A0D9C /* GoogleService-Info.plist */; };
		1040A2DF833D445584294FD5 /* Montserrat-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 29DDC907E25E4B7EB83040B8 /* Montserrat-ThinItalic.ttf */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		15641A1E028F465A8DF77CC1 /* Montserrat-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0A7A1C82AEEE4C878D28B11B /* Montserrat-BoldItalic.ttf */; };
		38A0946C08EC43F19AEC30FF /* Montserrat-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9BA3924673354E59A86AD778 /* Montserrat-MediumItalic.ttf */; };
		42EC952793F541D49602BAD4 /* Montserrat-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9C3C1FF19FC54FEE94BB6753 /* Montserrat-BlackItalic.ttf */; };
		46B676DE13024F728D164682 /* Montserrat-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 798A292F02034E54807D7DD0 /* Montserrat-SemiBoldItalic.ttf */; };
		4A94DECC2E258DD300620FDC /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 4A94DECB2E258DD300620FDC /* PrivacyInfo.xcprivacy */; };
		584D3DCB25DD2548004FFB3A /* FolloIT.entitlements in Resources */ = {isa = PBXBuildFile; fileRef = 0E1E1CF5256E175700AA37A0 /* FolloIT.entitlements */; };
		58D93D9577794B4386D51EA8 /* Montserrat-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F5BDDC8AAD3E45938A2D793D /* Montserrat-Bold.ttf */; };
		5D56E6F784494A9497CD7718 /* Montserrat-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F954C580763844E182BF5E9F /* Montserrat-Light.ttf */; };
		5F8DE24591EA469B922792EA /* Montserrat-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4ACC16C9E8714B93B70EF57D /* Montserrat-ExtraBoldItalic.ttf */; };
		6C1603EDF44C4520B124B592 /* Montserrat-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = DA7E8B8281F941DE86021CE1 /* Montserrat-ExtraLight.ttf */; };
		6DD451B022B14F67AF3165B0 /* Montserrat-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 93CD75A0E02F441B95CF696E /* Montserrat-SemiBold.ttf */; };
		70EC633E6DDF49B982F2C41B /* Montserrat-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C29866B33EB14469BE2BFB3C /* Montserrat-ExtraBold.ttf */; };
		727E034104784CA6871685AB /* Montserrat-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C9756C6F084E4340AE7CF963 /* Montserrat-Italic.ttf */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		8F916D58B16B43E294270575 /* Montserrat-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F3F22179A51B4FA6BE345BD8 /* Montserrat-LightItalic.ttf */; };
		965C5B0670CA945A27C88A05 /* libPods-FolloIT.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 96D12EC111F4D69A988D3E60 /* libPods-FolloIT.a */; };
		98A640C2151A4FB886C69915 /* Montserrat-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 64678620DFD648BD9A12509D /* Montserrat-Medium.ttf */; };
		A71C95832DFABFB0008FBF25 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A71C95822DFABFB0008FBF25 /* CoreGraphics.framework */; };
		A71C95852DFABFBF008FBF25 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A71C95842DFABFBF008FBF25 /* Foundation.framework */; };
		A76E403B2DFC0BB5000A49B6 /* libswiftCoreGraphics.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = A76E403A2DFC0BB5000A49B6 /* libswiftCoreGraphics.tbd */; };
		AB7B04AC2C4FC5BC00D15EC9 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = AB7B04AB2C4FC5BC00D15EC9 /* assets */; };
		ABF1E16E2DFC5A2C007682BB /* main.jsbundle in Resources */ = {isa = PBXBuildFile; fileRef = ABF1E16D2DFC5A22007682BB /* main.jsbundle */; };
		D54DDA88177D48B7A139EBAB /* Montserrat-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8F7480EB215D4B7EA868B11C /* Montserrat-Thin.ttf */; };
		D8C97567FA8545DD8DA668F6 /* Montserrat-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E272EC4208664137A8511FB8 /* Montserrat-Black.ttf */; };
		F46FF1E7170D4FE989F2DBA4 /* Montserrat-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 1B75BE900432417A9BFAC9D2 /* Montserrat-Regular.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* FolloITTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FolloITTests.m; sourceTree = "<group>"; };
		0A7A1C82AEEE4C878D28B11B /* Montserrat-BoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-BoldItalic.ttf"; path = "../src/assets/fonts/Montserrat-BoldItalic.ttf"; sourceTree = "<group>"; };
		0E1E1CF5256E175700AA37A0 /* FolloIT.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = FolloIT.entitlements; path = FolloIT/FolloIT.entitlements; sourceTree = "<group>"; };
		0E1E1CF6256E1B9100AA37A0 /* CloudKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CloudKit.framework; path = System/Library/Frameworks/CloudKit.framework; sourceTree = SDKROOT; };
		0E1EEE732518FB370018D2BE /* Montserrat-ExtraLight.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-ExtraLight.ttf"; sourceTree = "<group>"; };
		0E1EEE742518FB370018D2BE /* Montserrat-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-Italic.ttf"; sourceTree = "<group>"; };
		0E1EEE752518FB370018D2BE /* Montserrat-Black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-Black.ttf"; sourceTree = "<group>"; };
		0E1EEE762518FB370018D2BE /* Montserrat-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-Light.ttf"; sourceTree = "<group>"; };
		0E1EEE772518FB370018D2BE /* Montserrat-SemiBoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		0E1EEE782518FB370018D2BE /* Montserrat-LightItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-LightItalic.ttf"; sourceTree = "<group>"; };
		0E1EEE792518FB380018D2BE /* Montserrat-SemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-SemiBold.ttf"; sourceTree = "<group>"; };
		0E1EEE7A2518FB380018D2BE /* Montserrat-Thin.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-Thin.ttf"; sourceTree = "<group>"; };
		0E1EEE7B2518FB380018D2BE /* Montserrat-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-Medium.ttf"; sourceTree = "<group>"; };
		0E1EEE7C2518FB380018D2BE /* Montserrat-BoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-BoldItalic.ttf"; sourceTree = "<group>"; };
		0E1EEE7D2518FB380018D2BE /* Montserrat-ExtraBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-ExtraBold.ttf"; sourceTree = "<group>"; };
		0E1EEE7E2518FB380018D2BE /* Montserrat-ExtraBoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-ExtraBoldItalic.ttf"; sourceTree = "<group>"; };
		0E1EEE7F2518FB380018D2BE /* Montserrat-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-Bold.ttf"; sourceTree = "<group>"; };
		0E1EEE802518FB380018D2BE /* Montserrat-MediumItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-MediumItalic.ttf"; sourceTree = "<group>"; };
		0E1EEE812518FB380018D2BE /* Montserrat-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-Regular.ttf"; sourceTree = "<group>"; };
		0E1EEE822518FB390018D2BE /* Montserrat-ThinItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-ThinItalic.ttf"; sourceTree = "<group>"; };
		0E1EEE832518FB390018D2BE /* Montserrat-ExtraLightItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-ExtraLightItalic.ttf"; sourceTree = "<group>"; };
		0E1EEE842518FB390018D2BE /* Montserrat-BlackItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Montserrat-BlackItalic.ttf"; sourceTree = "<group>"; };
		0EFB732725878669001A0D9C /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "FolloIT/GoogleService-Info.plist"; sourceTree = "<group>"; };
		1319511953EE89026FB999CA /* Pods-FolloIT.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FolloIT.release.xcconfig"; path = "Target Support Files/Pods-FolloIT/Pods-FolloIT.release.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* FolloIT.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FolloIT.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = FolloIT/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = FolloIT/AppDelegate.m; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = FolloIT/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = FolloIT/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = FolloIT/main.m; sourceTree = "<group>"; };
		15DEF1316348433BA2D0D9A7 /* Feather.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Feather.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Feather.ttf"; sourceTree = "<group>"; };
		1A58EE433CA149DF864EA840 /* AntDesign.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = AntDesign.ttf; path = "../node_modules/react-native-vector-icons/Fonts/AntDesign.ttf"; sourceTree = "<group>"; };
		1B75BE900432417A9BFAC9D2 /* Montserrat-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Regular.ttf"; path = "../src/assets/fonts/Montserrat-Regular.ttf"; sourceTree = "<group>"; };
		29DDC907E25E4B7EB83040B8 /* Montserrat-ThinItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-ThinItalic.ttf"; path = "../src/assets/fonts/Montserrat-ThinItalic.ttf"; sourceTree = "<group>"; };
		3BF006DCE4304D3E91CE726B /* MaterialIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf"; sourceTree = "<group>"; };
		41C2B3B25A024812B5D62FD2 /* Ionicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Ionicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf"; sourceTree = "<group>"; };
		4824A324D69BBE7E0CB9A775 /* Pods-FolloIT.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-FolloIT.debug.xcconfig"; path = "Target Support Files/Pods-FolloIT/Pods-FolloIT.debug.xcconfig"; sourceTree = "<group>"; };
		4A94DECB2E258DD300620FDC /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = FolloIT/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		4ACC16C9E8714B93B70EF57D /* Montserrat-ExtraBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-ExtraBoldItalic.ttf"; path = "../src/assets/fonts/Montserrat-ExtraBoldItalic.ttf"; sourceTree = "<group>"; };
		4D0BB8A4CA4D4EC5B7830436 /* Fontisto.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Fontisto.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Fontisto.ttf"; sourceTree = "<group>"; };
		51CB889D958C4CD2A9FDDC8B /* Entypo.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Entypo.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Entypo.ttf"; sourceTree = "<group>"; };
		5818E98225ED0C85003443DB /* libYogaKit.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libYogaKit.a; sourceTree = BUILT_PRODUCTS_DIR; };
		589E86ED25ED01FB00C241AB /* libYogaKit.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libYogaKit.a; sourceTree = BUILT_PRODUCTS_DIR; };
		63932EF835F14F2FA040405F /* FontAwesome.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf"; sourceTree = "<group>"; };
		64678620DFD648BD9A12509D /* Montserrat-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Medium.ttf"; path = "../src/assets/fonts/Montserrat-Medium.ttf"; sourceTree = "<group>"; };
		798A292F02034E54807D7DD0 /* Montserrat-SemiBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-SemiBoldItalic.ttf"; path = "../src/assets/fonts/Montserrat-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = FolloIT/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8447D27B50644A889659F600 /* Octicons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Octicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Octicons.ttf"; sourceTree = "<group>"; };
		8B24EC5C2604F6D1003D2609 /* libYoga.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libYoga.a; sourceTree = BUILT_PRODUCTS_DIR; };
		8B24EC5F2604F706003D2609 /* libYoga.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libYoga.a; sourceTree = BUILT_PRODUCTS_DIR; };
		8F7480EB215D4B7EA868B11C /* Montserrat-Thin.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Thin.ttf"; path = "../src/assets/fonts/Montserrat-Thin.ttf"; sourceTree = "<group>"; };
		93CD75A0E02F441B95CF696E /* Montserrat-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-SemiBold.ttf"; path = "../src/assets/fonts/Montserrat-SemiBold.ttf"; sourceTree = "<group>"; };
		96D12EC111F4D69A988D3E60 /* libPods-FolloIT.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-FolloIT.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		9BA3924673354E59A86AD778 /* Montserrat-MediumItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-MediumItalic.ttf"; path = "../src/assets/fonts/Montserrat-MediumItalic.ttf"; sourceTree = "<group>"; };
		9C3C1FF19FC54FEE94BB6753 /* Montserrat-BlackItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-BlackItalic.ttf"; path = "../src/assets/fonts/Montserrat-BlackItalic.ttf"; sourceTree = "<group>"; };
		9C70C608D2904C4AAFEE7194 /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = MaterialCommunityIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf"; sourceTree = "<group>"; };
		A447FF282C2E4B469348446F /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Solid.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf"; sourceTree = "<group>"; };
		A71C95822DFABFB0008FBF25 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		A71C95842DFABFBF008FBF25 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		A76E403A2DFC0BB5000A49B6 /* libswiftCoreGraphics.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libswiftCoreGraphics.tbd; path = usr/lib/swift/libswiftCoreGraphics.tbd; sourceTree = SDKROOT; };
		AB4EBF6E2E00411600735A88 /* yoga.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = yoga.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AB7B04AB2C4FC5BC00D15EC9 /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; path = assets; sourceTree = "<group>"; };
		ABF1E16D2DFC5A22007682BB /* main.jsbundle */ = {isa = PBXFileReference; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		B4A6C4485740407586E7E3F0 /* Zocial.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Zocial.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Zocial.ttf"; sourceTree = "<group>"; };
		BA5783375300458A92CE4928 /* Foundation.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = Foundation.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Foundation.ttf"; sourceTree = "<group>"; };
		C11EF459DC7349C8A3F25682 /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Regular.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf"; sourceTree = "<group>"; };
		C1DDA1B5008547F7813CBE3C /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = SimpleLineIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/SimpleLineIcons.ttf"; sourceTree = "<group>"; };
		C29866B33EB14469BE2BFB3C /* Montserrat-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-ExtraBold.ttf"; path = "../src/assets/fonts/Montserrat-ExtraBold.ttf"; sourceTree = "<group>"; };
		C9756C6F084E4340AE7CF963 /* Montserrat-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Italic.ttf"; path = "../src/assets/fonts/Montserrat-Italic.ttf"; sourceTree = "<group>"; };
		D63B9205A4B7458591AB7B28 /* EvilIcons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = EvilIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/EvilIcons.ttf"; sourceTree = "<group>"; };
		DA7E8B8281F941DE86021CE1 /* Montserrat-ExtraLight.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-ExtraLight.ttf"; path = "../src/assets/fonts/Montserrat-ExtraLight.ttf"; sourceTree = "<group>"; };
		E047143BC88F4AB2BE706D7D /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = FontAwesome5_Brands.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf"; sourceTree = "<group>"; };
		E272EC4208664137A8511FB8 /* Montserrat-Black.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Black.ttf"; path = "../src/assets/fonts/Montserrat-Black.ttf"; sourceTree = "<group>"; };
		E6975FC7550A4AB29B39A334 /* Montserrat-ExtraLightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-ExtraLightItalic.ttf"; path = "../src/assets/fonts/Montserrat-ExtraLightItalic.ttf"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		ED2971642150620600B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS12.0.sdk/System/Library/Frameworks/JavaScriptCore.framework; sourceTree = DEVELOPER_DIR; };
		F3F22179A51B4FA6BE345BD8 /* Montserrat-LightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-LightItalic.ttf"; path = "../src/assets/fonts/Montserrat-LightItalic.ttf"; sourceTree = "<group>"; };
		F5BDDC8AAD3E45938A2D793D /* Montserrat-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Bold.ttf"; path = "../src/assets/fonts/Montserrat-Bold.ttf"; sourceTree = "<group>"; };
		F954C580763844E182BF5E9F /* Montserrat-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Montserrat-Light.ttf"; path = "../src/assets/fonts/Montserrat-Light.ttf"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				A76E403B2DFC0BB5000A49B6 /* libswiftCoreGraphics.tbd in Frameworks */,
				A71C95852DFABFBF008FBF25 /* Foundation.framework in Frameworks */,
				A71C95832DFABFB0008FBF25 /* CoreGraphics.framework in Frameworks */,
				0E1E1CF7256E1B9100AA37A0 /* CloudKit.framework in Frameworks */,
				965C5B0670CA945A27C88A05 /* libPods-FolloIT.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* FolloITTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* FolloITTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = FolloITTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		0E1EEE722518FB210018D2BE /* Resources */ = {
			isa = PBXGroup;
			children = (
				0E1EEE752518FB370018D2BE /* Montserrat-Black.ttf */,
				0E1EEE842518FB390018D2BE /* Montserrat-BlackItalic.ttf */,
				0E1EEE7F2518FB380018D2BE /* Montserrat-Bold.ttf */,
				0E1EEE7C2518FB380018D2BE /* Montserrat-BoldItalic.ttf */,
				0E1EEE7D2518FB380018D2BE /* Montserrat-ExtraBold.ttf */,
				0E1EEE7E2518FB380018D2BE /* Montserrat-ExtraBoldItalic.ttf */,
				0E1EEE732518FB370018D2BE /* Montserrat-ExtraLight.ttf */,
				0E1EEE832518FB390018D2BE /* Montserrat-ExtraLightItalic.ttf */,
				0E1EEE742518FB370018D2BE /* Montserrat-Italic.ttf */,
				0E1EEE762518FB370018D2BE /* Montserrat-Light.ttf */,
				0E1EEE782518FB370018D2BE /* Montserrat-LightItalic.ttf */,
				0E1EEE7B2518FB380018D2BE /* Montserrat-Medium.ttf */,
				0E1EEE802518FB380018D2BE /* Montserrat-MediumItalic.ttf */,
				0E1EEE812518FB380018D2BE /* Montserrat-Regular.ttf */,
				0E1EEE792518FB380018D2BE /* Montserrat-SemiBold.ttf */,
				0E1EEE772518FB370018D2BE /* Montserrat-SemiBoldItalic.ttf */,
				0E1EEE7A2518FB380018D2BE /* Montserrat-Thin.ttf */,
				0E1EEE822518FB390018D2BE /* Montserrat-ThinItalic.ttf */,
				E272EC4208664137A8511FB8 /* Montserrat-Black.ttf */,
				9C3C1FF19FC54FEE94BB6753 /* Montserrat-BlackItalic.ttf */,
				F5BDDC8AAD3E45938A2D793D /* Montserrat-Bold.ttf */,
				0A7A1C82AEEE4C878D28B11B /* Montserrat-BoldItalic.ttf */,
				C29866B33EB14469BE2BFB3C /* Montserrat-ExtraBold.ttf */,
				4ACC16C9E8714B93B70EF57D /* Montserrat-ExtraBoldItalic.ttf */,
				DA7E8B8281F941DE86021CE1 /* Montserrat-ExtraLight.ttf */,
				E6975FC7550A4AB29B39A334 /* Montserrat-ExtraLightItalic.ttf */,
				C9756C6F084E4340AE7CF963 /* Montserrat-Italic.ttf */,
				F954C580763844E182BF5E9F /* Montserrat-Light.ttf */,
				F3F22179A51B4FA6BE345BD8 /* Montserrat-LightItalic.ttf */,
				64678620DFD648BD9A12509D /* Montserrat-Medium.ttf */,
				9BA3924673354E59A86AD778 /* Montserrat-MediumItalic.ttf */,
				1B75BE900432417A9BFAC9D2 /* Montserrat-Regular.ttf */,
				93CD75A0E02F441B95CF696E /* Montserrat-SemiBold.ttf */,
				798A292F02034E54807D7DD0 /* Montserrat-SemiBoldItalic.ttf */,
				8F7480EB215D4B7EA868B11C /* Montserrat-Thin.ttf */,
				29DDC907E25E4B7EB83040B8 /* Montserrat-ThinItalic.ttf */,
				1A58EE433CA149DF864EA840 /* AntDesign.ttf */,
				51CB889D958C4CD2A9FDDC8B /* Entypo.ttf */,
				D63B9205A4B7458591AB7B28 /* EvilIcons.ttf */,
				15DEF1316348433BA2D0D9A7 /* Feather.ttf */,
				63932EF835F14F2FA040405F /* FontAwesome.ttf */,
				E047143BC88F4AB2BE706D7D /* FontAwesome5_Brands.ttf */,
				C11EF459DC7349C8A3F25682 /* FontAwesome5_Regular.ttf */,
				A447FF282C2E4B469348446F /* FontAwesome5_Solid.ttf */,
				4D0BB8A4CA4D4EC5B7830436 /* Fontisto.ttf */,
				BA5783375300458A92CE4928 /* Foundation.ttf */,
				41C2B3B25A024812B5D62FD2 /* Ionicons.ttf */,
				9C70C608D2904C4AAFEE7194 /* MaterialCommunityIcons.ttf */,
				3BF006DCE4304D3E91CE726B /* MaterialIcons.ttf */,
				8447D27B50644A889659F600 /* Octicons.ttf */,
				C1DDA1B5008547F7813CBE3C /* SimpleLineIcons.ttf */,
				B4A6C4485740407586E7E3F0 /* Zocial.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* FolloIT */ = {
			isa = PBXGroup;
			children = (
				ABF1E16D2DFC5A22007682BB /* main.jsbundle */,
				0E1E1CF5256E175700AA37A0 /* FolloIT.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				0EFB732725878669001A0D9C /* GoogleService-Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
			);
			name = FolloIT;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				AB4EBF6E2E00411600735A88 /* yoga.framework */,
				A76E403A2DFC0BB5000A49B6 /* libswiftCoreGraphics.tbd */,
				A71C95842DFABFBF008FBF25 /* Foundation.framework */,
				A71C95822DFABFB0008FBF25 /* CoreGraphics.framework */,
				8B24EC5F2604F706003D2609 /* libYoga.a */,
				8B24EC5C2604F6D1003D2609 /* libYoga.a */,
				5818E98225ED0C85003443DB /* libYogaKit.a */,
				589E86ED25ED01FB00C241AB /* libYogaKit.a */,
				0E1E1CF6256E1B9100AA37A0 /* CloudKit.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				ED2971642150620600B7C4FE /* JavaScriptCore.framework */,
				96D12EC111F4D69A988D3E60 /* libPods-FolloIT.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		7BE618E4C255C25ECF9A85C5 /* Pods */ = {
			isa = PBXGroup;
			children = (
				4824A324D69BBE7E0CB9A775 /* Pods-FolloIT.debug.xcconfig */,
				1319511953EE89026FB999CA /* Pods-FolloIT.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				AB7B04AB2C4FC5BC00D15EC9 /* assets */,
				0E1EEE722518FB210018D2BE /* Resources */,
				13B07FAE1A68108700A75B9A /* FolloIT */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* FolloITTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				7BE618E4C255C25ECF9A85C5 /* Pods */,
				4A94DECB2E258DD300620FDC /* PrivacyInfo.xcprivacy */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
			wrapsLines = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* FolloIT.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* FolloIT */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "FolloIT" */;
			buildPhases = (
				6F6452E59E0BE9F95F085AD4 /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				8BBA1AAE263A95950004CC77 /* ShellScript */,
				D5ACAB86E2F543B747520F7C /* [CP] Embed Pods Frameworks */,
				721B7AFAD25207FE25290400 /* [CP] Copy Pods Resources */,
				02B9E50E0EA5C0930CE5B0C1 /* [CP-User] [RNFB] Core Configuration */,
				470E6A855AE0CF701D0EABEE /* [CP-User] [RNFB] Crashlytics Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = FolloIT;
			productName = FolloIT;
			productReference = 13B07F961A680F5B00A75B9A /* FolloIT.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1130;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "FolloIT" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* FolloIT */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				AB7B04AC2C4FC5BC00D15EC9 /* assets in Resources */,
				584D3DCB25DD2548004FFB3A /* FolloIT.entitlements in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				0EFB732825878669001A0D9C /* GoogleService-Info.plist in Resources */,
				D8C97567FA8545DD8DA668F6 /* Montserrat-Black.ttf in Resources */,
				42EC952793F541D49602BAD4 /* Montserrat-BlackItalic.ttf in Resources */,
				58D93D9577794B4386D51EA8 /* Montserrat-Bold.ttf in Resources */,
				15641A1E028F465A8DF77CC1 /* Montserrat-BoldItalic.ttf in Resources */,
				70EC633E6DDF49B982F2C41B /* Montserrat-ExtraBold.ttf in Resources */,
				5F8DE24591EA469B922792EA /* Montserrat-ExtraBoldItalic.ttf in Resources */,
				6C1603EDF44C4520B124B592 /* Montserrat-ExtraLight.ttf in Resources */,
				0443A3AB7A0A4D52B439E7F0 /* Montserrat-ExtraLightItalic.ttf in Resources */,
				727E034104784CA6871685AB /* Montserrat-Italic.ttf in Resources */,
				5D56E6F784494A9497CD7718 /* Montserrat-Light.ttf in Resources */,
				ABF1E16E2DFC5A2C007682BB /* main.jsbundle in Resources */,
				8F916D58B16B43E294270575 /* Montserrat-LightItalic.ttf in Resources */,
				98A640C2151A4FB886C69915 /* Montserrat-Medium.ttf in Resources */,
				4A94DECC2E258DD300620FDC /* PrivacyInfo.xcprivacy in Resources */,
				38A0946C08EC43F19AEC30FF /* Montserrat-MediumItalic.ttf in Resources */,
				F46FF1E7170D4FE989F2DBA4 /* Montserrat-Regular.ttf in Resources */,
				6DD451B022B14F67AF3165B0 /* Montserrat-SemiBold.ttf in Resources */,
				46B676DE13024F728D164682 /* Montserrat-SemiBoldItalic.ttf in Resources */,
				D54DDA88177D48B7A139EBAB /* Montserrat-Thin.ttf in Resources */,
				1040A2DF833D445584294FD5 /* Montserrat-ThinItalic.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 8;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 1;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n";
		};
		02B9E50E0EA5C0930CE5B0C1 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		470E6A855AE0CF701D0EABEE /* [CP-User] [RNFB] Crashlytics Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Crashlytics Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\nif [[ ${PODS_ROOT} ]]; then\n  echo \"info: Exec FirebaseCrashlytics Run from Pods\"\n  \"${PODS_ROOT}/FirebaseCrashlytics/run\"\nelse\n  echo \"info: Exec FirebaseCrashlytics Run from framework\"\n  \"${PROJECT_DIR}/FirebaseCrashlytics.framework/run\"\nfi\n";
		};
		6F6452E59E0BE9F95F085AD4 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-FolloIT-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		721B7AFAD25207FE25290400 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FolloIT/Pods-FolloIT-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport_Privacy.bundle",
				"${PODS_ROOT}/GoogleMaps/Maps/Frameworks/GoogleMaps.framework/Resources/GoogleMaps.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift/Promises_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage_resources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/QBImagePicker.bundle",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/AntDesign.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Entypo.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/EvilIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Feather.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome6_Brands.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome6_Regular.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/FontAwesome6_Solid.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Fontisto.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Foundation.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Octicons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/SimpleLineIcons.ttf",
				"${PODS_ROOT}/../../node_modules/react-native-vector-icons/Fonts/Zocial.ttf",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/AccessibilityResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Sentry/Sentry.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/TOCropViewController/TOCropViewControllerBundle.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/intercom-react-native/IntercomFramework.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseCoreInternal_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FirebaseInstallations_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleDataTransport_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMaps.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleUtilities_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FBLPromises_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Promises_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNCAsyncStorage_resources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/QBImagePicker.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AntDesign.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Entypo.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EvilIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Feather.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Brands.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Regular.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome5_Solid.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome6_Brands.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome6_Regular.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FontAwesome6_Solid.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Fontisto.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Foundation.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Ionicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/MaterialCommunityIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/MaterialIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Octicons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/SimpleLineIcons.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Zocial.ttf",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AccessibilityResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Sentry.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TOCropViewControllerBundle.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/IntercomFramework.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-FolloIT/Pods-FolloIT-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8BBA1AAE263A95950004CC77 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"${SRCROOT}/${BUILD_PRODUCTS_DIR}/${INFOLIST_PATH}",
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\n\"${PODS_ROOT}/FirebaseCrashlytics/run\"\n";
		};
		D5ACAB86E2F543B747520F7C /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-FolloIT/Pods-FolloIT-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAEMKit/FBAEMKit.framework/FBAEMKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit/FBSDKCoreKit.framework/FBSDKCoreKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKLoginKit/FBSDKLoginKit.framework/FBSDKLoginKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKShareKit/FBSDKShareKit.framework/FBSDKShareKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/Intercom/Intercom.framework/Intercom",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBAEMKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit_Basics.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKLoginKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKShareKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Intercom.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-FolloIT/Pods-FolloIT-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4824A324D69BBE7E0CB9A775 /* Pods-FolloIT.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = FolloIT/FolloIT.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = 23;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = N9899XA46N;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Base64\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BranchSDK\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CodePush\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FBReactNativeSpec\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreDiagnostics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstanceID\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/JWT\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/LiveChat\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Mixpanel-swift\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MixpanelReactNative\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPushNotificationIOS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBCrashlytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFileViewer\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNLocalize\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNPermissions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSentry\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNShare\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNVectorIcons\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-logger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactNativeExceptionHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Sentry\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TOCropViewController\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fmt\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/intercom-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/nanopb\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-blob-util\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-branch\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-geolocation-service\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-notifications\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-orientation-locker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-pdf\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-version-check\"",
					"\"${PODS_ROOT}/FBAEMKit/XCFrameworks\"",
					"\"${PODS_ROOT}/FBSDKCoreKit/XCFrameworks\"",
					"\"${PODS_ROOT}/FBSDKCoreKit_Basics/XCFrameworks\"",
					"\"${PODS_ROOT}/FBSDKLoginKit/XCFrameworks\"",
					"\"${PODS_ROOT}/FBSDKShareKit/XCFrameworks\"",
					"\"${PODS_ROOT}/FirebaseAnalytics/Frameworks\"",
					"\"${PODS_ROOT}/GoogleAppMeasurement/Frameworks\"",
					"\"${PODS_ROOT}/GoogleMaps/Base/Frameworks\"",
					"\"${PODS_ROOT}/GoogleMaps/Maps/Frameworks\"",
					"\"${PODS_ROOT}/Intercom\"",
					"\"${PODS_ROOT}/OneSignal/iOS_SDK/OneSignalSDK/Framework\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAEMKit\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit_Basics\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKLoginKit\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKShareKit\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FirebaseAnalytics/Base\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/AdIdSupport\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/Intercom\"",
					"\"$(PROJECT_DIR)/../../ios/Pods\"",
					"\"$(SRCROOT)/../node_modules/react-native/React\"",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Base64/Base64.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BranchSDK/BranchSDK.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CodePush/CodePush.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion/DoubleConversion.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FBReactNativeSpec/FBReactNativeSpec.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstanceID/FirebaseInstanceID.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging/FirebaseMessaging.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/JWT/JWT.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/LiveChat/LiveChat.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Mixpanel-swift/Mixpanel.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MixpanelReactNative/MixpanelReactNative.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly/folly.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety/RCTTypeSafety.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard/RNCClipboard.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView/RNCMaskedView.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPushNotificationIOS/RNCPushNotificationIOS.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker/RNDateTimePicker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics/RNFBAnalytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp/RNFBApp.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBCrashlytics/RNFBCrashlytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging/RNFBMessaging.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFS/RNFS.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFileViewer/RNFileViewer.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler/RNGestureHandler.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/RNImageCropPicker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNLocalize/RNLocalize.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNPermissions/RNPermissions.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated/RNReanimated.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG/RNSVG.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens/RNScreens.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSentry/RNSentry.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNShare/RNShare.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNVectorIcons/RNVectorIcons.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/React.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules/CoreModules.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation/RCTAnimation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob/RCTBlob.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage/RCTImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking/RCTLinking.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork/RCTNetwork.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings/RCTSettings.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText/RCTText.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration/RCTVibration.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact/cxxreact.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi/jsi.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor/jsireact.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-logger/logger.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger/reactperflogger.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactNativeExceptionHandler/ReactNativeExceptionHandler.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive/SSZipArchive.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Sentry/Sentry.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TOCropViewController/TOCropViewController.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga/yoga.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fmt/fmt.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog/glog.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/intercom-react-native/intercom_react_native.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-blob-util/react_native_blob_util.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-branch/RNBranch.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker/react_native_document_picker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-geolocation-service/react_native_geolocation_service.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker/react_native_image_picker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo/react_native_netinfo.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-notifications/RNNotifications.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-orientation-locker/react_native_orientation_locker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-pdf/react_native_pdf.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context/react_native_safe_area_context.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-version-check/react_native_version_check.framework/Headers\"",
					"\"$(PODS_ROOT)/Headers/Public/React-Core\"/**",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/Google-Maps-iOS-Utils\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/boost\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-google-maps\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-maps\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"${PODS_TARGET_SRCROOT}/Sources/FBLPromises/include\"",
					"\"$(PODS_ROOT)/boost\"",
					"$(SRCROOT)/../node_modules/**",
					"$(SRCROOT)/../../../React/**",
					"\"$(SRCROOT)/../../React/** \"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"$(SRCROOT)/../node_modules/react-native/React/**/**",
					"'$(SRCROOT)/Pods/Development Pods/React-Core/Default/Base'",
					"'$(SRCROOT)/../node_modules/react-native/React/Base'",
				);
				INFOPLIST_FILE = FolloIT/Info.plist;
				"INFOPLIST_KEY_CFBundleDisplayName[sdk=*]" = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
				);
				MARKETING_VERSION = 2.0.0;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = com.follo.scm;
				PRODUCT_NAME = FolloIT;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = follo_dev;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1319511953EE89026FB999CA /* Pods-FolloIT.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = FolloIT/FolloIT.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 23;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = N9899XA46N;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Base64\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BranchSDK\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CodePush\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FBReactNativeSpec\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreDiagnostics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstanceID\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/JWT\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/LiveChat\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Mixpanel-swift\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MixpanelReactNative\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPushNotificationIOS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBCrashlytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFileViewer\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNLocalize\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNPermissions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSentry\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNShare\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNVectorIcons\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-logger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactNativeExceptionHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Sentry\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TOCropViewController\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fmt\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/intercom-react-native\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/nanopb\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-blob-util\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-branch\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-geolocation-service\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-notifications\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-orientation-locker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-pdf\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-version-check\"",
					"\"${PODS_ROOT}/FBAEMKit/XCFrameworks\"",
					"\"${PODS_ROOT}/FBSDKCoreKit/XCFrameworks\"",
					"\"${PODS_ROOT}/FBSDKCoreKit_Basics/XCFrameworks\"",
					"\"${PODS_ROOT}/FBSDKLoginKit/XCFrameworks\"",
					"\"${PODS_ROOT}/FBSDKShareKit/XCFrameworks\"",
					"\"${PODS_ROOT}/FirebaseAnalytics/Frameworks\"",
					"\"${PODS_ROOT}/GoogleAppMeasurement/Frameworks\"",
					"\"${PODS_ROOT}/GoogleMaps/Base/Frameworks\"",
					"\"${PODS_ROOT}/GoogleMaps/Maps/Frameworks\"",
					"\"${PODS_ROOT}/Intercom\"",
					"\"${PODS_ROOT}/OneSignal/iOS_SDK/OneSignalSDK/Framework\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAEMKit\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit_Basics\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKLoginKit\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKShareKit\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FirebaseAnalytics/Base\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/AdIdSupport\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/Intercom\"",
					"\"$(PROJECT_DIR)/../../ios/Pods\"",
					"\"$(SRCROOT)/../node_modules/react-native/React\"",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Base64/Base64.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BranchSDK/BranchSDK.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CodePush/CodePush.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion/DoubleConversion.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FBReactNativeSpec/FBReactNativeSpec.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreDiagnostics/FirebaseCoreDiagnostics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstanceID/FirebaseInstanceID.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging/FirebaseMessaging.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/JWT/JWT.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/LiveChat/LiveChat.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Mixpanel-swift/Mixpanel.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MixpanelReactNative/MixpanelReactNative.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly/folly.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety/RCTTypeSafety.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard/RNCClipboard.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView/RNCMaskedView.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPushNotificationIOS/RNCPushNotificationIOS.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker/RNDateTimePicker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics/RNFBAnalytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp/RNFBApp.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBCrashlytics/RNFBCrashlytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging/RNFBMessaging.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFS/RNFS.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFileViewer/RNFileViewer.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler/RNGestureHandler.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNImageCropPicker/RNImageCropPicker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNLocalize/RNLocalize.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNPermissions/RNPermissions.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated/RNReanimated.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG/RNSVG.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens/RNScreens.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSentry/RNSentry.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNShare/RNShare.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNVectorIcons/RNVectorIcons.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/React.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules/CoreModules.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation/RCTAnimation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob/RCTBlob.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage/RCTImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking/RCTLinking.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork/RCTNetwork.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings/RCTSettings.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText/RCTText.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration/RCTVibration.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact/cxxreact.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi/jsi.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor/jsireact.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-logger/logger.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger/reactperflogger.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactNativeExceptionHandler/ReactNativeExceptionHandler.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive/SSZipArchive.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Sentry/Sentry.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/TOCropViewController/TOCropViewController.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga/yoga.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fmt/fmt.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog/glog.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/intercom-react-native/intercom_react_native.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-blob-util/react_native_blob_util.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-branch/RNBranch.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker/react_native_document_picker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-geolocation-service/react_native_geolocation_service.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker/react_native_image_picker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo/react_native_netinfo.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-notifications/RNNotifications.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-orientation-locker/react_native_orientation_locker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-pdf/react_native_pdf.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context/react_native_safe_area_context.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-version-check/react_native_version_check.framework/Headers\"",
					"\"$(PODS_ROOT)/Headers/Public/React-Core\"/**",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/Google-Maps-iOS-Utils\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/boost\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-google-maps\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-maps\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"${PODS_TARGET_SRCROOT}/Sources/FBLPromises/include\"",
					"\"$(PODS_ROOT)/boost\"",
					"$(SRCROOT)/../node_modules/**",
					"$(SRCROOT)/../../../React/**",
					"\"$(SRCROOT)/../../React/** \"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"$(SRCROOT)/../node_modules/react-native/React/**/**",
					"'$(SRCROOT)/Pods/Development Pods/React-Core/Default/Base'",
					"'$(SRCROOT)/../node_modules/react-native/React/Base'",
				);
				INFOPLIST_FILE = FolloIT/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
				);
				MARKETING_VERSION = 2.0.0;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "";
				PRODUCT_BUNDLE_IDENTIFIER = com.follo.scm;
				PRODUCT_NAME = FolloIT;
				PROVISIONING_PROFILE_SPECIFIER = Follo_Distribution_Profile;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = Follo_Distribution_Profile;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer: Arun Kumar (V3GNX9UGH5)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer: Arun Kumar (V3GNX9UGH5)";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				EXCLUDED_ARCHS = arm64;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = NO;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Follo Technology Inc. (N9899XA46N)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Follo Technology Inc. (N9899XA46N)";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				EXCLUDED_ARCHS = arm64;
				"EXCLUDED_ARCHS[sdk=*]" = arm64;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = NO;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "FolloIT" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "FolloIT" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
