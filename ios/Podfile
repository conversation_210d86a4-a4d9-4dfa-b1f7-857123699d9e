require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

platform :ios, '13.4'

use_modular_headers!

target 'FolloIT' do
  config = use_native_modules!

  # Pods for FolloIT
  use_react_native!(
    :path => config[:reactNativePath],
    # Her<PERSON> is now enabled by default. Disable by setting this flag to false.
    # Upcoming versions of React Native will require <PERSON><PERSON> so it is recommended to keep this on.
    :hermes_enabled => false, #enabling this facing reanimated crash error on release build.
    :fabric_enabled => false
  )

  # Permissions
  permissions_path = '../node_modules/react-native-permissions/ios'
  pod 'Permission-Camera', :path => File.join(permissions_path, 'Camera')
  pod 'Permission-LocationWhenInUse', :path => File.join(permissions_path, 'LocationWhenInUse')
  pod 'Permission-PhotoLibrary', :path => File.join(permissions_path, 'PhotoLibrary')

  # Facebook SDK
  pod 'FBSDKCoreKit'
  pod 'FBSDKLoginKit'
  pod 'FBSDKShareKit'
  
  rn_maps_path = '../node_modules/react-native-maps'
  pod 'react-native-google-maps', :path => rn_maps_path
  # Other dependencies from your original Podfile that are still needed
  # Note: react-native-maps, firebase, etc., are now autolinked by use_native_modules!
  # so they don't need to be explicitly listed unless you need specific versions.

post_install do |installer|
  react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )
  
  # M1 Mac support
  __apply_Xcode_12_5_M1_post_install_workaround(installer)
  
  # General settings
  installer.pods_project.build_configurations.each do |config|
    config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.4'
    config.build_settings['ONLY_ACTIVE_ARCH'] = 'YES'
    config.build_settings['CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER'] = 'NO'
    config.build_settings['OTHER_LDFLAGS'] = config.build_settings['OTHER_LDFLAGS'].to_a.uniq
  end

  # Target-specific settings
  installer.pods_project.targets.each do |target|
    # Set minimum deployment target for all pods to avoid warnings
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.4'
    end
    
    # FBReactNativeSpec fix
    if target.name == 'FBReactNativeSpec'
      target.build_configurations.each do |config|
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)', '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION']
      end
      
      generate_specs_phase = target.shell_script_build_phases.find { |phase| phase.name == '[CP-User] Generate Specs' }
      if generate_specs_phase
        target.build_phases.delete(generate_specs_phase)
        target.build_phases.unshift(generate_specs_phase)
      end
    end

    # Yoga settings
    if target.name == 'Yoga'
      target.build_configurations.each do |config|
        config.build_settings['ENABLE_BITCODE'] = 'NO'
        config.build_settings['HEADER_SEARCH_PATHS'] = [
          '$(inherited)',
          '"$(SRCROOT)/../node_modules/react-native/ReactCommon/yoga"'
        ]
      end
    end
    # Google Maps settings
    if target.name == 'react-native-google-maps'
      target.build_configurations.each do |config|
        config.build_settings['CLANG_ENABLE_MODULES'] = 'No'
      end
    end

    # Bundle targets
    if target.respond_to?(:product_type) && target.product_type == "com.apple.product-type.bundle"
      target.build_configurations.each do |config|
        config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
        config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'c++17'
        config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'
      end
    end

    # Workaround for libc++ removal of allocator<const T> in Xcode 16.3+
    if target.name == 'Sentry'
      target.build_configurations.each do |config|
        flags = Array(config.build_settings['OTHER_CPLUSPLUSFLAGS'])
        flags << '-D_LIBCPP_ENABLE_REMOVED_ALLOCATOR_CONST'
        config.build_settings['OTHER_CPLUSPLUSFLAGS'] = flags.uniq
        cflags = Array(config.build_settings['OTHER_CFLAGS'])
        cflags << '-D_LIBCPP_ENABLE_REMOVED_ALLOCATOR_CONST'
        config.build_settings['OTHER_CFLAGS'] = cflags.uniq
      end
    end
  end
  
  installer.pods_project.build_configurations.each do |config|
    config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
  end
    # Apply library search path fixes
    fix_library_search_paths(installer)

  # Patch Sentry Cocoa header for Xcode 16.3+ (remove const element type in std::vector)
  begin
    require 'fileutils'
    sentry_header_path = File.join(__dir__, 'Pods', 'Sentry', 'Sources', 'Sentry', 'include', 'SentryThreadMetadataCache.hpp')
    if File.exist?(sentry_header_path)
      contents = File.read(sentry_header_path)
      fixed = contents.gsub('std::vector<const ThreadHandleMetadataPair>', 'std::vector<ThreadHandleMetadataPair>')
      if contents != fixed
        FileUtils.chmod('u+w', sentry_header_path) rescue nil
        File.open(sentry_header_path, 'w') { |f| f.write(fixed) }
        puts '✅ Patched SentryThreadMetadataCache.hpp to remove const in std::vector element type'
      else
        puts 'ℹ️ SentryThreadMetadataCache.hpp already patched or not affected'
      end
    else
      puts "ℹ️ SentryThreadMetadataCache.hpp not found at #{sentry_header_path} (will skip patch)"
    end
  rescue => e
    puts "⚠️ Failed to patch SentryThreadMetadataCache.hpp: #{e}"
  end
  end

  bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
  
  def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
    framework_path = File.join(Dir.pwd, framework_relative_path)
    command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
    puts "Stripping bitcode: #{command}"
    system(command)
  end

  # Strip bitcode from Intercom framework
  framework_paths = [
    "Pods/Intercom/Intercom.xcframework/ios-arm64/Intercom.framework/Intercom"
  ]
  
  framework_paths.each do |framework_relative_path|
    strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
  end

end

def fix_library_search_paths(installer)
  installer.pods_project.build_configurations.each do |config|
    config.build_settings['LIBRARY_SEARCH_PATHS'] = ['$(inherited)', '$(SDKROOT)/usr/lib/swift']
  end
end
