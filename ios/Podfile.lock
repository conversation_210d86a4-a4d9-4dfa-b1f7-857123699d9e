PODS:
  - Base64 (1.1.2)
  - boost (1.76.0)
  - BranchSDK (2.2.1)
  - CodePush (7.1.0):
    - Base64 (~> 1.1)
    - JWT (~> 3.0.0-beta.12)
    - React-Core
    - SSZipArchive (~> 2.2.2)
  - DoubleConversion (1.1.6)
  - FBAEMKit (18.0.1):
    - FBSDKCoreKit_Basics (= 18.0.1)
  - FBLazyVector (0.72.17)
  - FBReactNativeSpec (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.17)
    - RCTTypeSafety (= 0.72.17)
    - React-Core (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - FBSDKCoreKit (18.0.1):
    - FBAEMKit (= 18.0.1)
    - FBSDKCoreKit_Basics (= 18.0.1)
  - FBSDKCoreKit_Basics (18.0.1)
  - FBSDKLoginKit (18.0.1):
    - FBSDKCoreKit (= 18.0.1)
  - FBSDKShareKit (18.0.1):
    - FBSDKCoreKit (= 18.0.1)
  - Firebase/Analytics (10.20.0):
    - Firebase/Core
  - Firebase/Core (10.20.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.20.0)
  - Firebase/CoreOnly (10.20.0):
    - FirebaseCore (= 10.20.0)
  - Firebase/Crashlytics (10.20.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.20.0)
  - Firebase/Messaging (10.20.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.20.0)
  - FirebaseAnalytics (10.20.0):
    - FirebaseAnalytics/AdIdSupport (= 10.20.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCore (10.20.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.20.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.20.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (3.10.3):
    - Google-Maps-iOS-Utils/Clustering (= 3.10.3)
    - Google-Maps-iOS-Utils/Geometry (= 3.10.3)
    - Google-Maps-iOS-Utils/GeometryUtils (= 3.10.3)
    - Google-Maps-iOS-Utils/Heatmap (= 3.10.3)
    - Google-Maps-iOS-Utils/QuadTree (= 3.10.3)
    - GoogleMaps
  - Google-Maps-iOS-Utils/Clustering (3.10.3):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/Geometry (3.10.3):
    - GoogleMaps
  - Google-Maps-iOS-Utils/GeometryUtils (3.10.3):
    - GoogleMaps
  - Google-Maps-iOS-Utils/Heatmap (3.10.3):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/QuadTree (3.10.3):
    - GoogleMaps
  - GoogleAppMeasurement (10.20.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.20.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.20.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (5.1.0):
    - GoogleMaps/Maps (= 5.1.0)
  - GoogleMaps/Base (5.1.0)
  - GoogleMaps/Maps (5.1.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - Intercom (12.4.3)
  - intercom-react-native (3.0.5):
    - Intercom (~> 12.4.3)
    - React-Core
  - JWT (3.0.0-beta.14):
    - Base64 (~> 1.1.2)
  - Mixpanel-swift (3.3.0):
    - Mixpanel-swift/Complete (= 3.3.0)
  - Mixpanel-swift/Complete (3.3.0)
  - MixpanelReactNative (1.5.0):
    - Mixpanel-swift (= 3.3.0)
    - React
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - Permission-Camera (3.10.1):
    - RNPermissions
  - Permission-LocationWhenInUse (3.10.1):
    - RNPermissions
  - Permission-PhotoLibrary (3.10.1):
    - RNPermissions
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.72.17)
  - RCTTypeSafety (0.72.17):
    - FBLazyVector (= 0.72.17)
    - RCTRequired (= 0.72.17)
    - React-Core (= 0.72.17)
  - React (0.72.17):
    - React-Core (= 0.72.17)
    - React-Core/DevSupport (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-RCTActionSheet (= 0.72.17)
    - React-RCTAnimation (= 0.72.17)
    - React-RCTBlob (= 0.72.17)
    - React-RCTImage (= 0.72.17)
    - React-RCTLinking (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - React-RCTSettings (= 0.72.17)
    - React-RCTText (= 0.72.17)
    - React-RCTVibration (= 0.72.17)
  - React-callinvoker (0.72.17)
  - React-Codegen (0.72.17):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.17)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.17)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/CoreModulesHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTBlob
    - React-RCTImage (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.17):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-debug (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-jsinspector (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
    - React-runtimeexecutor (= 0.72.17)
  - React-debug (0.72.17)
  - React-jsc (0.72.17):
    - React-jsc/Fabric (= 0.72.17)
    - React-jsi (= 0.72.17)
  - React-jsc/Fabric (0.72.17):
    - React-jsi (= 0.72.17)
  - React-jsi (0.72.17):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.17):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - React-jsinspector (0.72.17)
  - React-logger (0.72.17):
    - glog
  - react-native-blob-util (0.16.4):
    - React-Core
  - react-native-blur (0.8.0):
    - React
  - react-native-branch (5.9.2):
    - BranchSDK (= 2.2.1)
    - React-Core
  - react-native-document-picker (7.1.3):
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-google-maps (0.30.2):
    - Google-Maps-iOS-Utils (= 3.10.3)
    - GoogleMaps (= 5.1.0)
    - React-Core
  - react-native-image-picker (4.10.3):
    - React-Core
  - react-native-maps (0.30.2):
    - React-Core
  - react-native-netinfo (9.5.0):
    - React-Core
  - react-native-notifications (4.2.4):
    - React-Core
  - react-native-orientation-locker (1.7.0):
    - React-Core
  - react-native-pager-view (6.4.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-pdf (6.2.2):
    - React-Core
  - react-native-safe-area-context (4.14.1):
    - React-Core
  - react-native-version-check (3.5.0):
    - React-Core
  - React-NativeModulesApple (0.72.17):
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.17)
  - React-RCTActionSheet (0.72.17):
    - React-Core/RCTActionSheetHeaders (= 0.72.17)
  - React-RCTAnimation (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTAnimationHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTAppDelegate (0.72.17):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-jsc
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTBlobHeaders (= 0.72.17)
    - React-Core/RCTWebSocket (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTImage (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTImageHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-RCTNetwork (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTLinking (0.72.17):
    - React-Codegen (= 0.72.17)
    - React-Core/RCTLinkingHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTNetwork (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTNetworkHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTSettings (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.17)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTSettingsHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-RCTText (0.72.17):
    - React-Core/RCTTextHeaders (= 0.72.17)
  - React-RCTVibration (0.72.17):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.17)
    - React-Core/RCTVibrationHeaders (= 0.72.17)
    - React-jsi (= 0.72.17)
    - ReactCommon/turbomodule/core (= 0.72.17)
  - React-rncore (0.72.17)
  - React-runtimeexecutor (0.72.17):
    - React-jsi (= 0.72.17)
  - React-runtimescheduler (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.17):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.17):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - ReactCommon/turbomodule/core (0.72.17):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.17)
    - React-cxxreact (= 0.72.17)
    - React-jsi (= 0.72.17)
    - React-logger (= 0.72.17)
    - React-perflogger (= 0.72.17)
  - ReactNativeExceptionHandler (2.10.10):
    - React-Core
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNCClipboard (1.16.3):
    - React-Core
  - RNCMaskedView (0.1.11):
    - React
  - RNCPushNotificationIOS (1.11.0):
    - React-Core
  - RNDateTimePicker (7.7.0):
    - React-Core
  - RNFBAnalytics (18.9.0):
    - Firebase/Analytics (= 10.20.0)
    - React-Core
    - RNFBApp
  - RNFBApp (18.9.0):
    - Firebase/CoreOnly (= 10.20.0)
    - React-Core
  - RNFBCrashlytics (18.9.0):
    - Firebase/Crashlytics (= 10.20.0)
    - FirebaseCoreExtension (= 10.20.0)
    - React-Core
    - RNFBApp
  - RNFBMessaging (18.9.0):
    - Firebase/Messaging (= 10.20.0)
    - FirebaseCoreExtension (= 10.20.0)
    - React-Core
    - RNFBApp
  - RNFileViewer (2.1.5):
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.12.1):
    - React-Core
  - RNImageCropPicker (0.40.3):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.40.3)
    - TOCropViewController
  - RNImageCropPicker/QBImagePickerController (0.40.3):
    - React-Core
    - React-RCTImage
    - TOCropViewController
  - RNLocalize (2.2.6):
    - React-Core
  - RNPermissions (3.10.1):
    - React-Core
  - RNReanimated (3.6.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (4.0.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTImage
  - RNSentry (5.36.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - Sentry/HybridSDK (= 8.41.0)
  - RNShare (5.3.0):
    - React-Core
  - RNSVG (13.14.1):
    - React-Core
  - RNVectorIcons (10.2.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - Sentry/HybridSDK (8.41.0)
  - SocketRocket (0.6.1)
  - SSZipArchive (2.2.3)
  - TOCropViewController (3.1.0)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - CodePush (from `../node_modules/react-native-code-push`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - FBSDKCoreKit
  - FBSDKLoginKit
  - FBSDKShareKit
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - "intercom-react-native (from `../node_modules/@intercom/intercom-react-native`)"
  - MixpanelReactNative (from `../node_modules/mixpanel-react-native`)
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera`)
  - Permission-LocationWhenInUse (from `../node_modules/react-native-permissions/ios/LocationWhenInUse`)
  - Permission-PhotoLibrary (from `../node_modules/react-native-permissions/ios/PhotoLibrary`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-jsc (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-blob-util (from `../node_modules/react-native-blob-util`)
  - "react-native-blur (from `../node_modules/@react-native-community/blur`)"
  - react-native-branch (from `../node_modules/react-native-branch`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-google-maps (from `../node_modules/react-native-maps`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-notifications (from `../node_modules/react-native-notifications`)
  - react-native-orientation-locker (from `../node_modules/react-native-orientation-locker`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-pdf (from `../node_modules/react-native-pdf`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-version-check (from `../node_modules/react-native-version-check`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeExceptionHandler (from `../node_modules/react-native-exception-handler`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-community/masked-view`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBCrashlytics (from `../node_modules/@react-native-firebase/crashlytics`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNFileViewer (from `../node_modules/react-native-file-viewer`)
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native`)"
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Base64
    - BranchSDK
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - FBSDKShareKit
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseSessions
    - fmt
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - Intercom
    - JWT
    - Mixpanel-swift
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - Sentry
    - SocketRocket
    - SSZipArchive
    - TOCropViewController

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  CodePush:
    :path: "../node_modules/react-native-code-push"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  intercom-react-native:
    :path: "../node_modules/@intercom/intercom-react-native"
  MixpanelReactNative:
    :path: "../node_modules/mixpanel-react-native"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera"
  Permission-LocationWhenInUse:
    :path: "../node_modules/react-native-permissions/ios/LocationWhenInUse"
  Permission-PhotoLibrary:
    :path: "../node_modules/react-native-permissions/ios/PhotoLibrary"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-jsc:
    :path: "../node_modules/react-native/ReactCommon/jsc"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-blob-util:
    :path: "../node_modules/react-native-blob-util"
  react-native-blur:
    :path: "../node_modules/@react-native-community/blur"
  react-native-branch:
    :path: "../node_modules/react-native-branch"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-google-maps:
    :path: "../node_modules/react-native-maps"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-notifications:
    :path: "../node_modules/react-native-notifications"
  react-native-orientation-locker:
    :path: "../node_modules/react-native-orientation-locker"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-pdf:
    :path: "../node_modules/react-native-pdf"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-version-check:
    :path: "../node_modules/react-native-version-check"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeExceptionHandler:
    :path: "../node_modules/react-native-exception-handler"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-community/masked-view"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBCrashlytics:
    :path: "../node_modules/@react-native-firebase/crashlytics"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFileViewer:
    :path: "../node_modules/react-native-file-viewer"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  Base64: cecfb41a004124895a7bcee567a89bae5a89d49b
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  BranchSDK: cb046c2714b03e573484ce9e349e2ddbad7016e8
  CodePush: dce1b253fde81078249ea9cd4b948e4ac7b761a9
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBAEMKit: b2ed182002dbcb65d5a60059c9693d322186cd00
  FBLazyVector: 66398fc2381d8fa1eee4c0f80d931587a7b927e8
  FBReactNativeSpec: 0f8cecf999d709dba7626bbf565b1b5f8f46a5c1
  FBSDKCoreKit: 7f96852f2539bdb88ba8d47e5ab4ae70a6f8d691
  FBSDKCoreKit_Basics: 22d4c1a509ded4e45116f3bb167a14907550f62e
  FBSDKLoginKit: f48c06446995cd209332831f692cea28b26e51da
  FBSDKShareKit: d3fef7f6690e173369134beb5f43983d7338e933
  Firebase: 10c8cb12fb7ad2ae0c09ffc86cd9c1ab392a0031
  FirebaseAnalytics: a2731bf3670747ce8f65368b118d18aa8e368246
  FirebaseCore: 28045c1560a2600d284b9c45a904fe322dc890b6
  FirebaseCoreExtension: 0659f035b88c5a7a15a9763c48c2e6ca8c0a2977
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: 81530595edb6d99f1918f723a6c33766a24a4c86
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 06c414a21b122396a26847c523d5c370f8325df5
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  Google-Maps-iOS-Utils: 95d5ca4d2004314eff88c1819b969db6cac37ab6
  GoogleAppMeasurement: bb3c564c3efb933136af0e94899e0a46167466a8
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 086ae1dab659eaa7339bdb30fc88e2f44e7c597c
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  Intercom: 43bfacc55b1c93cce9ec569833696d3f15678430
  intercom-react-native: b2a48297f0ed988970c819133cabe8c965080003
  JWT: ef71dfb03e1f842081e64dc42eef0e164f35d251
  Mixpanel-swift: 6ffe368bcfc3b538f540f31372bc798afd90c1ad
  MixpanelReactNative: 53b80c245f89ea602fbdda0568900e2b69fa632a
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  Permission-Camera: 9b70902f34a83c10e198d2d01f0e453e58842776
  Permission-LocationWhenInUse: 31f52ebddef50c306a585b5a82ca16c8ff582dec
  Permission-PhotoLibrary: 03c52ed95dadfb0f2ba4c7663786cce0c4e0c978
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: 01c639ec840ee03928b2d65f5cd5297d737b3834
  RCTTypeSafety: 9623592521a1576363baf3d6ab8d164cfe9062bf
  React: 3c0beeda318c3c515a6bb2c1f197b55bd731aa43
  React-callinvoker: 0cd6ff2cdd80255c82cd4628fc925df1e7133a1a
  React-Codegen: eba4fe2e6cb1a411e1bd9c4bdb229c486f344a2e
  React-Core: 2cf63bca3dbce5e6e3ecac8b923f07460ca500e4
  React-CoreModules: cebd223e814ac07bc1f597bbd2480167a2c7a130
  React-cxxreact: 3b98c9f715f38cba8b72528717b03a7a1f2330dc
  React-debug: 3a5091cbda7ffe5f11ad0443109810fcd1a3e885
  React-jsc: 7c8f243222c43ce677fe107056a99ddaf061d017
  React-jsi: c3abf8d7ec734bc37b790f461c65f8e5101854cd
  React-jsiexecutor: 5a2bcc79d8ebe27cf63601b9e7d13296e9c4dc5c
  React-jsinspector: 853b8631b908636bb09ef77cb217376c38a0c8ff
  React-logger: 9ca44bb5703bf2355f3c2d2e5e67bfe98ca2dc34
  react-native-blob-util: 60453b777610c87a22b3524032c0214e8db555db
  react-native-blur: cad4d93b364f91e7b7931b3fa935455487e5c33c
  react-native-branch: 021b9c261f732d0950e9a304284779d48bf81109
  react-native-document-picker: ec07866a30707f23660c0f3ae591d669d3e89096
  react-native-geolocation-service: 608e1da71a1ac31b4de64d9ef2815f697978c55b
  react-native-google-maps: 4fa56492c7f4323edfa4574a2e5ca3eaa71dc62f
  react-native-image-picker: 60f4246eb5bb7187fc15638a8c1f13abd3820695
  react-native-maps: df7b9fca1b1c8d356fadbf5b8a63a5f8cf32fc73
  react-native-netinfo: 48c5f79a84fbc3ba1d28a8b0d04adeda72885fa8
  react-native-notifications: 3de8ef9cd800e5db0225d9aa46b228d2b94ce51e
  react-native-orientation-locker: 5819fd23ca89cbac0d736fb4314745f62716d517
  react-native-pager-view: 4be639d9742fb683ea538b69a8855f9239ea72c6
  react-native-pdf: 4b5a9e4465a6a3b399e91dc4838eb44ddf716d1f
  react-native-safe-area-context: 141eca0fd4e4191288dfc8b96a7c7e1c2983447a
  react-native-version-check: 54a471d70dadf0f72fc142e70f16e4bf473a91a5
  React-NativeModulesApple: 649702e2b756b907c0c1c35f450a5bae61c33d6c
  React-perflogger: 785b0063af5178298a61b54bb46aae9a19c7bbb5
  React-RCTActionSheet: 84f37b34bd77249263ace75471d6664393c29972
  React-RCTAnimation: 5713910b6223154df4bba80a0bda4e2e671b00f8
  React-RCTAppDelegate: f93dffef18d35a752ef322be1d3fd6c2a1a6c098
  React-RCTBlob: 071e9e4c5f8369016eee855ad1f87ddd4cc4d75f
  React-RCTImage: 2e63a483be5d4e46a80dea3b17c9abee38006feb
  React-RCTLinking: e3ff685ee62187f8f61e938357307c1f890125b5
  React-RCTNetwork: a35842997a403edfdc1ec25b61a0e10a0526368d
  React-RCTSettings: aef81e0ac54268d2928ad31c4f91056cc75e5ce9
  React-RCTText: 7becec5f53f03b20da11f4b7e40e6bcfd476d134
  React-RCTVibration: defaae8016de9b3351a2a67ee8ef3fbdd643b0e1
  React-rncore: dfd20469cfad38e48b1c3cc9c4367db63f5231d7
  React-runtimeexecutor: 448409b5ae5a01b7793239f630051960c7dd39f9
  React-runtimescheduler: 54af986f214ce82ff2b746c4552c3ae1d90b815a
  React-utils: 7959d4553163b61e01bbe83dbd80e58ca420aecb
  ReactCommon: cd970cbd9e212b78dca50ca47fea4dafa485f674
  ReactNativeExceptionHandler: b11ff67c78802b2f62eed0e10e75cb1ef7947c60
  RNCAsyncStorage: ec53e44dc3e75b44aa2a9f37618a49c3bc080a7a
  RNCClipboard: dfeb43751adff21e588657b5b6c888c72f3aa68e
  RNCMaskedView: 0e1bc4bfa8365eba5fbbb71e07fbdc0555249489
  RNCPushNotificationIOS: 64218f3c776c03d7408284a819b2abfda1834bc8
  RNDateTimePicker: 4f3c4dbd4f908be32ec8c93f086e8924bd4a2e07
  RNFBAnalytics: c79cf8da7d0bbbad0e0cc233402d2cadc4a694b9
  RNFBApp: a3e139715386fe79a09c387f2dbeb6890eb05b39
  RNFBCrashlytics: e6d595ed2619e5e8ee3cdfd12c6a62e470280a03
  RNFBMessaging: a65862d8eba03cb6c838241bd328166504996894
  RNFileViewer: ce7ca3ac370e18554d35d6355cffd7c30437c592
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: c0d04458598fcb26052494ae23dda8f8f5162b13
  RNImageCropPicker: e7ab6fb43d2fc3e84651e786ef4a080d63b0ed3d
  RNLocalize: d4b8af4e442d4bcca54e68fc687a2129b4d71a81
  RNPermissions: 4e3714e18afe7141d000beae3755e5b5fb2f5e05
  RNReanimated: 60b4fb9d2e86b9e686b309e135e40c7d7eb01d13
  RNScreens: f04da1d235cd8ea93ac52af126080bcad2b0778e
  RNSentry: 99d06c4d78096b475add2538486d45523bb823e2
  RNShare: 4df87d1791f50a2c7b1d89432fb9bbb7c02a9c9a
  RNSVG: af3907ac5d4fa26a862b75a16d8f15bc74f2ceda
  RNVectorIcons: 4785c0f1161b4fdc691c802c989248326d0b005d
  Sentry: 54d0fe6c0df448497c8ed4cce66ccf7027e1823e
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  SSZipArchive: 62d4947b08730e4cda640473b0066d209ff033c9
  TOCropViewController: 0938b5745261eae080abbe5aa5797a9e659a3a7e
  Yoga: ef534101bb891fb09bae657417f34d399c1efe38

PODFILE CHECKSUM: ffe763e21dd7dd44ec2c4cdbfed0d00bd83bc718

COCOAPODS: 1.15.0
