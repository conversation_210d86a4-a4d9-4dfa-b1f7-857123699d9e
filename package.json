{"name": "FolloIT", "version": "0.0.3", "private": true, "scripts": {"android": "react-native run-android --port 8081 ", "ios": "react-native run-ios --port 8081 --simulator \"iPhone 15\"", "ios-release": "react-native run-ios --port 8081 --simulator \"iPhone 15\" --configuration Release", "start": "react-native start", "bundle-android": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/", "bundle-ios": "react-native bundle --entry-file index.js --platform ios --dev false --bundle-output ios/main.jsbundle --assets-dest ios", "test": "jest", "lint": "eslint .", "clear-cache": "watchman watch-del-all && rm -rf $TMPDIR/react-* && rm -rf node_modules/ && npm cache verify && npm install --legacy-peer-deps", "sonar_scan": "sonar-scanner", "setup_environment": "./setup_env.sh", "pod_install": "cd ios/ && pod install --repo-update", "appcenter_release_testing": "appcenter codepush release-react -a Follo-Inc./Follo-Android-1 -d Testing", "appcenter_release_staging": "appcenter codepush release-react -a Follo-Inc./Follo-Android-1 -d Staging", "appcenter_release_production": "appcenter codepush release-react -a Follo-Inc./Follo-Android-1 -d Production", "clean": "watchman watch-del-all &&  rm -rf package-lock.json node_modules android/app/build ios/Pods ios/Podfile.lock ~/Library/Developer/Xcode/DerivedData", "install-all": "npm install --legacy-peer-deps && cd ios && pod install && cd ..", "postinstall": "patch-package && ./apply-patches.sh", "crw": "cd android && gradlew clean && cd.. && npm run android ", "crwi": "cd android && ./gradlew clean && cd .. && npm run android", "release": "cd android && gradlew clean && gradlew app:assembleRelease && cd..", "releasei": "cd android && ./gradlew clean && ./gradlew app:assembleRelease && cd ..", "ios-device": "react-native run-ios --port 8081 --simulator \"iPhone 15\"", "android-device": "react-native run-android --port 8081 --deviceId \"000000000000000\""}, "dependencies": {"@intercom/intercom-react-native": "^3.0.0", "@react-native-async-storage/async-storage": "^1.19.0", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-community/blur": "^3.6.0", "@react-native-community/datetimepicker": "^7.6.2", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "^9.4.0", "@react-native-community/push-notification-ios": "^1.8.0", "@react-native-firebase/analytics": "^18.3.0", "@react-native-firebase/app": "^18.3.0", "@react-native-firebase/crashlytics": "^18.3.0", "@react-native-firebase/messaging": "^18.3.0", "@react-native/normalize-color": "^2.1.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "@sentry/react-native": "^5.10.0", "@valdio/react-native-scrollable-tabview": "^0.8.12", "axios": "^0.21.1", "deprecated-react-native-listview": "0.0.6", "deprecated-react-native-prop-types": "^5.0.0", "email-validator": "^2.0.4", "lodash": "^4.17.21", "mixpanel-react-native": "^1.4.2", "moment": "^2.28.0", "moment-timezone": "^0.5.34", "patch-package": "^8.0.0", "prop-types": "^15.8.1", "react": "18.2.0", "react-native": "0.72.17", "react-native-animatable": "^1.3.3", "react-native-blob-util": "^0.16.4", "react-native-branch": "^5.0.0", "react-native-calendars": "^1.1274.0", "react-native-code-push": "^7.0.1", "react-native-deep-linking": "^2.2.0", "react-native-document-picker": "^7.1.3", "react-native-exception-handler": "^2.10.10", "react-native-file-viewer": "^2.1.4", "react-native-fs": "^2.16.6", "react-native-geocoding": "^0.5.0", "react-native-geolocation-service": "^5.3.0-beta.3", "react-native-gesture-handler": "~2.12.0", "react-native-google-places-autocomplete": "^2.2.0", "react-native-image-crop-picker": "^0.40.0", "react-native-image-picker": "^4.10.3", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-aware-scrollview": "^2.1.0", "react-native-localize": "^2.2.2", "react-native-maps": "^0.30.2", "react-native-material-selectize": "git+https://github.com/raynor85/react-native-material-selectize.git#email-validator-absolute-list", "react-native-modal": "^11.5.6", "react-native-modal-dropdown": "git+https://github.com/siemiatj/react-native-modal-dropdown.git", "react-native-multiple-select": "^0.5.6", "react-native-notifications": "4.2.4", "react-native-orientation-locker": "^1.1.8", "react-native-pager-view": "6.4.1", "react-native-paper": "^4.12.1", "react-native-pdf": "6.2.2", "react-native-permissions": "^3.9.1", "react-native-picker-scrollview": "^1.0.1", "react-native-progress": "^4.1.2", "react-native-push-notification": "^6.1.3", "react-native-reanimated": "~3.6.1", "react-native-responsive-screen": "^1.4.1", "react-native-safe-area-context": "^4.14.1", "react-native-screens": "4.0.0", "react-native-scrollable-tab-view": "^1.0.0", "react-native-share": "^5.1.0", "react-native-svg": "^13.10.0", "react-native-swiper": "^1.6.0-rc.3", "react-native-tab-view": "^4.1.2", "react-native-vector-icons": "^10.0.0", "react-native-version-check": "^3.5.0", "react-redux": "^7.2.1", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "sonar-scanner": "^3.1.0", "uniqid": "^5.2.0", "xlsx": "^0.16.8"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/cli": "^11.0.0", "@react-native-community/cli-platform-android": "^11.0.0", "@react-native-community/cli-platform-ios": "^11.0.0", "@react-native/eslint-config": "^0.72.0", "@react-native/metro-config": "^0.80.1", "babel-jest": "^27.5.1", "eslint": "^8.10.0", "jest": "^27.5.1", "metro-react-native-babel-preset": "^0.76.0"}, "jest": {"preset": "react-native"}}