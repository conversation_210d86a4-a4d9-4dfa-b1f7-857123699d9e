{"name": "FolloIT", "version": "0.0.3", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "bundle-android": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/", "bundle-ios": "react-native bundle --entry-file index.js --platform ios --dev false --bundle-output ios/main.jsbundle --assets-dest ios", "test": "jest", "lint": "eslint .", "clear-cache": "watchman watch-del-all && rm -rf $TMPDIR/react-* && rm -rf node_modules/ && npm cache verify && npm install && npm start -- --reset-cache", "sonar_scan": "sonar-scanner", "setup_environment": "./setup_env.sh", "pod_install": "cd ios/ && pod install --repo-update", "appcenter_release_testing": "appcenter codepush release-react -a Follo-Inc./Follo-Android-1 -d Testing", "appcenter_release_staging": "appcenter codepush release-react -a Follo-Inc./Follo-Android-1 -d Staging", "appcenter_release_production": "appcenter codepush release-react -a Follo-Inc./Follo-Android-1 -d Production", "clean": "watchman watch-del-all &&  rm -rf package-lock.json node_modules android/app/build ios/Pods ios/Podfile.lock ~/Library/Developer/Xcode/DerivedData", "install-all": "npm install --legacy-peer-deps && cd ios && pod install && cd ..", "postinstall": "patch-package"}, "dependencies": {"@intercom/intercom-react-native": "^3.0.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-community/blur": "^3.6.0", "@react-native-community/datetimepicker": "^6.3.4", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "^6.0.0", "@react-native-community/push-notification-ios": "^1.8.0", "@react-native-community/viewpager": "^5.0.10", "@react-native-firebase/analytics": "^11.5.0", "@react-native-firebase/app": "^11.5.0", "@react-native-firebase/crashlytics": "^11.5.0", "@react-native-firebase/messaging": "^11.5.0", "@react-native/normalize-color": "^2.1.0", "@sentry/react-native": "^5.10.0", "@valdio/react-native-scrollable-tabview": "^0.8.12", "axios": "^0.21.1", "deprecated-react-native-listview": "0.0.6", "email-validator": "^2.0.4", "lodash": "^4.17.21", "mixpanel-react-native": "^1.4.2", "moment": "^2.28.0", "moment-timezone": "^0.5.34", "patch-package": "^8.0.0", "react": "17.0.2", "react-native": "0.67.0", "react-native-animatable": "^1.3.3", "react-native-blob-util": "^0.16.4", "react-native-branch": "^5.0.0", "react-native-calendars": "^1.1274.0", "react-native-code-push": "^7.0.1", "react-native-deep-linking": "^2.2.0", "react-native-document-picker": "^7.1.3", "react-native-dropdown-picker": "^3.7.0", "react-native-exception-handler": "^2.10.10", "react-native-file-viewer": "^2.1.4", "react-native-fs": "^2.16.6", "react-native-geocoding": "^0.5.0", "react-native-geolocation-service": "^5.3.0-beta.3", "react-native-gesture-handler": "^1.10.3", "react-native-google-places-autocomplete": "^2.2.0", "react-native-image-crop-picker": "^0.40.0", "react-native-image-picker": "^5.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-aware-scrollview": "^2.1.0", "react-native-localize": "^2.2.2", "react-native-maps": "^0.29.4", "react-native-material-selectize": "git+https://github.com/raynor85/react-native-material-selectize.git#email-validator-absolute-list", "react-native-modal": "^11.5.6", "react-native-modal-dropdown": "git+https://github.com/siemiatj/react-native-modal-dropdown.git", "react-native-multiple-select": "^0.5.6", "react-native-notifications": "4.2.4", "react-native-orientation-locker": "^1.5.0", "react-native-paper": "^4.12.1", "react-native-pdf": "^6.6.2", "react-native-permissions": "^3.9.1", "react-native-picker-scrollview": "^1.0.1", "react-native-progress": "^4.1.2", "react-native-push-notification": "^6.1.3", "react-native-reanimated": "^1.13.4", "react-native-responsive-screen": "^1.4.1", "react-native-safe-area-context": "^3.2.0", "react-native-screens": "^2.18.1", "react-native-scrollable-tab-view": "^1.0.0", "react-native-share": "^5.1.0", "react-native-svg": "^12.1.0", "react-native-swiper": "^1.6.0-rc.3", "react-native-tab-view": "^2.15.1", "react-native-vector-icons": "^7.1.0", "react-native-version-check": "^3.4.4", "react-navigation": "^4.4.4", "react-navigation-stack": "^2.10.4", "react-navigation-tabs": "^2.11.1", "react-redux": "^7.2.1", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "sonar-scanner": "^3.1.0", "uniqid": "^5.2.0", "xlsx": "^0.16.8"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/runtime": "^7.11.2", "@react-native-community/cli": "^6.0.0", "@react-native-community/eslint-config": "^2.0.0", "babel-jest": "^26.3.0", "eslint": "^7.9.0", "jest": "^26.4.2", "metro-react-native-babel-preset": "^0.63.0"}, "jest": {"preset": "react-native"}}