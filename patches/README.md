# React Native Build Patches

This directory contains patches required for the React Native 0.68 → 0.72 upgrade to work properly on both iOS and Android.

## Patches Included

### 1. boost.podspec.patch
**Purpose**: Fixes the boost dependency download issue during pod installation.

**Issue**: The original boost 1.76.0 source URL was failing with checksum errors.

**Solution**: Updated to boost 1.83.0 from SourceForge with correct SHA256 checksum.

**Files Modified**:
- `node_modules/react-native/third-party-podspecs/boost.podspec`

### 2. yoga-boolean-operands.patch
**Purpose**: Fixes boolean operands warning in Yoga layout engine.

**Issue**: Compiler warning about boolean operands in logical OR operation.

**Solution**: Changed logical OR (`||`) to bitwise OR (`|`) for boolean values.

**Files Modified**:
- `node_modules/react-native/ReactCommon/yoga/yoga/Yoga.cpp` (line 3008)

### 3. react-native-calendars+1.1313.0.patch
**Purpose**: Fixes compatibility issues with react-native-calendars.

**Files Modified**:
- `node_modules/react-native-calendars/src/calendar/day/basic/index.js`

## Android Namespace Patches (React Native 0.72 Compatibility)

The following patches fix Android namespace issues required for Android Gradle Plugin 8.0+ compatibility:

### 4. react-native-exception-handler+2.10.10.patch
**Purpose**: Adds namespace declaration for Android AGP 8.0+ compatibility.
**Issue**: Missing namespace declaration causes build failures with "package attribute no longer supported".
**Solution**: Added `namespace "com.masteratul.exceptionhandler"` to build.gradle and removed package attribute from AndroidManifest.xml.

### 5. react-native-fs+2.20.0.patch
**Purpose**: Adds namespace declaration for react-native-fs.
**Solution**: Added `namespace "com.rnfs"` to build.gradle and removed package attribute from AndroidManifest.xml.

### 6. react-native-geolocation-service+5.3.1.patch
**Purpose**: Adds namespace declaration for react-native-geolocation-service.
**Solution**: Added `namespace "com.agontuk.RNFusedLocation"` to build.gradle.

### 7. react-native-image-crop-picker+0.40.3.patch
**Purpose**: Adds namespace declaration for react-native-image-crop-picker.
**Solution**: Added `namespace "com.reactnative.ivpusic.imagepicker"` to build.gradle and removed package attribute from AndroidManifest.xml.

### 8. react-native-localize+2.2.6.patch
**Purpose**: Adds namespace declaration for react-native-localize.
**Solution**: Added `namespace "com.zoontek.rnlocalize"` to build.gradle and removed package attribute from AndroidManifest.xml.

### 9. react-native-notifications+4.2.4.patch
**Purpose**: Adds namespace declaration for react-native-notifications.
**Solution**: Added `namespace "com.wix.reactnativenotifications"` to build.gradle and removed package attribute from AndroidManifest.xml.

### 10. react-native-orientation-locker+1.7.0.patch
**Purpose**: Adds namespace declaration for react-native-orientation-locker.
**Solution**: Added `namespace "org.wonday.orientation"` to build.gradle and removed package attribute from AndroidManifest.xml.

### 11. react-native-push-notification+6.1.3.patch
**Purpose**: Adds namespace declaration for react-native-push-notification.
**Solution**: Added `namespace "com.dieam.reactnativepushnotification"` to build.gradle and removed package attribute from AndroidManifest.xml.

### 12. react-native-share+5.3.0.patch
**Purpose**: Adds namespace declaration for react-native-share.
**Solution**: Added `namespace "cl.json"` to build.gradle and removed package attribute from AndroidManifest.xml.

### 13. react-native-version-check+3.5.0.patch
**Purpose**: Adds namespace declaration for react-native-version-check.
**Solution**: Added `namespace "io.xogus.reactnative.versioncheck"` to build.gradle and removed package attribute from AndroidManifest.xml.

### 14. @react-native-community+blur+3.6.0.patch
**Purpose**: Removes deprecated package attribute from AndroidManifest.xml.
**Solution**: Removed `package="com.cmcewen.blurview"` from AndroidManifest.xml (namespace already declared in build.gradle).

### 15. @react-native-community+datetimepicker+7.7.0.patch
**Purpose**: Removes deprecated package attribute from AndroidManifest.xml.
**Solution**: Removed `package="com.reactcommunity.rndatetimepicker"` from AndroidManifest.xml (namespace already declared in build.gradle).

### 16. @react-native-firebase+app+18.9.0.patch
**Purpose**: Fixes BuildConfig generation issues with Android Gradle Plugin 7.4.2.
**Issue**: BuildConfig class not generated causing compilation failures.
**Solution**: 
- Disabled namespace declaration for AGP 7.4.2 compatibility
- Updated AndroidManifest.xml package to match expected BuildConfig package
- Added fallback logic in ReactNativeFirebaseJSON.java to handle missing BuildConfig

## How to Use

### Automatic Application
The patches are automatically applied after `npm install` via the `postinstall` script in `package.json`:

```json
{
  "scripts": {
    "postinstall": "patch-package && ./apply-patches.sh"
  }
}
```

### Manual Application
If you need to apply patches manually:

```bash
# Apply all patches
./apply-patches.sh

# Or apply individual patches
patch -p1 < patches/boost.podspec.patch
patch -p1 < patches/yoga-boolean-operands.patch
```

### Verification
To verify patches are applied:

```bash
# Check boost version
grep -A 2 "spec.source" node_modules/react-native/third-party-podspecs/boost.podspec

# Check yoga boolean fix
grep -A 2 -B 2 "hadOverflow()" node_modules/react-native/ReactCommon/yoga/yoga/Yoga.cpp
```

## Creating New Patches

If you need to create additional patches:

1. Make your changes in `node_modules/`
2. Create a patch file:
   ```bash
   # For npm packages
   npx patch-package package-name
   
   # For manual patches
   diff -u original-file modified-file > patches/your-patch.patch
   ```
3. Add the patch to `apply-patches.sh`

## Troubleshooting

### Patch Application Fails
- Clean node_modules and reinstall: `npm run clean && npm install`
- Check if patches are already applied
- Verify file paths in patch files

### iOS Build Issues
- Run `cd ios && pod install` after applying patches
- Clean iOS build: `cd ios && rm -rf build && rm -rf ~/Library/Developer/Xcode/DerivedData`

## Notes

- These patches are specific to React Native 0.72.17 upgrade from 0.68.0
- iOS patches are for React Native 0.68.0 compatibility
- Android patches are for React Native 0.72.17 with Android Gradle Plugin 7.4.2 compatibility
- Always backup your changes before applying patches
- Test thoroughly after applying patches
- Consider contributing fixes upstream when possible

## React Native 0.72 Upgrade Notes

### Android Gradle Plugin Compatibility
- These patches are designed for AGP 7.4.2 with Gradle 7.6.3
- AGP 8.0+ requires namespace declarations for all libraries
- Package attributes in AndroidManifest.xml are deprecated in AGP 8.0+

### Build Requirements
- Java 17 required for React Native 0.72
- Android compileSdk 34 (downgraded from 35 for AAPT2 compatibility)
- NDK 26.1.10909125

### Known Issues Fixed
- Task dependency validation errors with AGP 8.0+
- Firebase BuildConfig generation issues
- Third-party library namespace compatibility
- Gradle configuration cache problems
- React Navigation v4 Linking.removeEventListener compatibility with RN 0.72
- Deprecated componentWillReceiveProps lifecycle method

## React Navigation v4 Compatibility Fix

### Issue
React Navigation v4 uses the deprecated `Linking.removeEventListener` API which was removed in React Native 0.72, causing runtime errors:
```
TypeError: _reactNative.Linking.removeEventListener is not a function
```

### Solution
Created a polyfill (`src/polyfills/LinkingPolyfill.js`) that:
- Adds back the `Linking.removeEventListener` method
- Maps old API to new subscription-based event listeners
- Maintains compatibility with React Navigation v4
- Provides proper cleanup for event listeners

### Files Modified
- **App.js** - Import polyfill at app startup
- **src/navigation/InitialRoute.js** - Import polyfill before React Navigation
- **src/navigation/TabBar.js** - Import polyfill and fix deprecated lifecycle method
- **src/polyfills/LinkingPolyfill.js** - New polyfill implementation 