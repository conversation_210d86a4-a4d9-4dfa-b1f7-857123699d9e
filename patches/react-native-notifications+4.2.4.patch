diff --git a/node_modules/react-native-notifications/lib/android/app/build.gradle b/node_modules/react-native-notifications/lib/android/app/build.gradle
index d049e84..0a70760 100644
--- a/node_modules/react-native-notifications/lib/android/app/build.gradle
+++ b/node_modules/react-native-notifications/lib/android/app/build.gradle
@@ -47,6 +47,7 @@ String resolveFlavor() {
 
 
 android {
+    namespace "com.wix.reactnativenotifications"
     compileSdkVersion androidSdkVersion
     defaultConfig {
         minSdkVersion androidMinSdkVersion
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/results.bin b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/results.bin
new file mode 100644
index 0000000..35907e5
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/results.bin
@@ -0,0 +1 @@
+o/reactNative60Debug
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/desugar_graph.bin b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/desugar_graph.bin
new file mode 100644
index 0000000..2f875e8
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/desugar_graph.bin differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/BuildConfig.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/BuildConfig.dex
new file mode 100644
index 0000000..f4e5fca
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/BuildConfig.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/Defs.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/Defs.dex
new file mode 100644
index 0000000..05dd52a
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/Defs.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/NotificationManagerCompatFacade.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/NotificationManagerCompatFacade.dex
new file mode 100644
index 0000000..2c94d60
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/NotificationManagerCompatFacade.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsModule.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsModule.dex
new file mode 100644
index 0000000..627cda7
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsModule.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsPackage.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsPackage.dex
new file mode 100644
index 0000000..80a8d6a
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsPackage.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLaunchHelper.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLaunchHelper.dex
new file mode 100644
index 0000000..eddb271
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLaunchHelper.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade$AppVisibilityListener.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade$AppVisibilityListener.dex
new file mode 100644
index 0000000..114d9df
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade$AppVisibilityListener.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade.dex
new file mode 100644
index 0000000..3df0957
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacadeHolder.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacadeHolder.dex
new file mode 100644
index 0000000..5965a0a
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacadeHolder.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/InitialNotificationHolder.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/InitialNotificationHolder.dex
new file mode 100644
index 0000000..ab51db1
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/InitialNotificationHolder.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/JsIOHelper.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/JsIOHelper.dex
new file mode 100644
index 0000000..a5c4e51
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/JsIOHelper.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/NotificationIntentAdapter.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/NotificationIntentAdapter.dex
new file mode 100644
index 0000000..d6e30a2
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/NotificationIntentAdapter.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/ProxyService.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/ProxyService.dex
new file mode 100644
index 0000000..5e08f3f
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/ProxyService.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade$1.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade$1.dex
new file mode 100644
index 0000000..0681181
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade$1.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade.dex
new file mode 100644
index 0000000..22dbe3d
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationChannel.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationChannel.dex
new file mode 100644
index 0000000..1672ea5
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationChannel.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationsApplication.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationsApplication.dex
new file mode 100644
index 0000000..a568e96
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationsApplication.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification$InvalidNotificationException.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification$InvalidNotificationException.dex
new file mode 100644
index 0000000..7da36e8
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification$InvalidNotificationException.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification.dex
new file mode 100644
index 0000000..aef49b1
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannel.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannel.dex
new file mode 100644
index 0000000..65f16da
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannel.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannelProps.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannelProps.dex
new file mode 100644
index 0000000..170ff9e
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannelProps.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification$1.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification$1.dex
new file mode 100644
index 0000000..f56862a
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification$1.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification.dex
new file mode 100644
index 0000000..88aa08e
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotificationProps.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotificationProps.dex
new file mode 100644
index 0000000..d020caa
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotificationProps.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/INotificationsDrawerApplication.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/INotificationsDrawerApplication.dex
new file mode 100644
index 0000000..0842a7c
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/INotificationsDrawerApplication.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/IPushNotificationsDrawer.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/IPushNotificationsDrawer.dex
new file mode 100644
index 0000000..0566f44
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/IPushNotificationsDrawer.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/PushNotificationsDrawer.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/PushNotificationsDrawer.dex
new file mode 100644
index 0000000..e1954f1
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/PushNotificationsDrawer.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdListenerService.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdListenerService.dex
new file mode 100644
index 0000000..384a1a3
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdListenerService.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdRefreshHandlerService.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdRefreshHandlerService.dex
new file mode 100644
index 0000000..f520bdb
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdRefreshHandlerService.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmToken.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmToken.dex
new file mode 100644
index 0000000..a54125d
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmToken.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmToken.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmToken.dex
new file mode 100644
index 0000000..ad77ca4
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmToken.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmTokenListenerApplication.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmTokenListenerApplication.dex
new file mode 100644
index 0000000..71efd51
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmTokenListenerApplication.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/INotificationsFcmApplication.dex b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/INotificationsFcmApplication.dex
new file mode 100644
index 0000000..005f010
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/.transforms/20a800cdc5512cbc57f674e3f95b535b/transformed/reactNative60Debug/com/wix/reactnativenotifications/fcm/INotificationsFcmApplication.dex differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/.transforms/9e96eec09f27b911207ab116f2e03f07/results.bin b/node_modules/react-native-notifications/lib/android/app/build/.transforms/9e96eec09f27b911207ab116f2e03f07/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/.transforms/9e96eec09f27b911207ab116f2e03f07/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/react-native-notifications/lib/android/app/build/generated/source/buildConfig/reactNative60/debug/com/wix/reactnativenotifications/BuildConfig.java b/node_modules/react-native-notifications/lib/android/app/build/generated/source/buildConfig/reactNative60/debug/com/wix/reactnativenotifications/BuildConfig.java
new file mode 100644
index 0000000..994ee1e
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/generated/source/buildConfig/reactNative60/debug/com/wix/reactnativenotifications/BuildConfig.java
@@ -0,0 +1,11 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.wix.reactnativenotifications;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.wix.reactnativenotifications";
+  public static final String BUILD_TYPE = "debug";
+  public static final String FLAVOR = "reactNative60";
+}
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/aapt_friendly_merged_manifests/reactNative60Debug/aapt/AndroidManifest.xml b/node_modules/react-native-notifications/lib/android/app/build/intermediates/aapt_friendly_merged_manifests/reactNative60Debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..e541a6d
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/aapt_friendly_merged_manifests/reactNative60Debug/aapt/AndroidManifest.xml
@@ -0,0 +1,25 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.wix.reactnativenotifications" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <application>
+
+<!--          A proxy-service that gives the library an opportunity to do some work before launching/resuming the actual application task. -->
+        <service android:name="com.wix.reactnativenotifications.core.ProxyService" />
+        <service
+            android:name="com.wix.reactnativenotifications.fcm.FcmInstanceIdListenerService"
+            android:exported="true" >
+            <intent-filter>
+                <action android:name="com.google.firebase.MESSAGING_EVENT" />
+                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
+            </intent-filter>
+        </service>
+        <service
+            android:name="com.wix.reactnativenotifications.fcm.FcmInstanceIdRefreshHandlerService"
+            android:exported="false"
+            android:permission="android.permission.BIND_JOB_SERVICE" />
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/aapt_friendly_merged_manifests/reactNative60Debug/aapt/output-metadata.json b/node_modules/react-native-notifications/lib/android/app/build/intermediates/aapt_friendly_merged_manifests/reactNative60Debug/aapt/output-metadata.json
new file mode 100644
index 0000000..30ff200
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/aapt_friendly_merged_manifests/reactNative60Debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.wix.reactnativenotifications",
+  "variantName": "reactNative60Debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/aar_metadata/reactNative60Debug/aar-metadata.properties b/node_modules/react-native-notifications/lib/android/app/build/intermediates/aar_metadata/reactNative60Debug/aar-metadata.properties
new file mode 100644
index 0000000..776557e
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/aar_metadata/reactNative60Debug/aar-metadata.properties
@@ -0,0 +1,5 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/annotation_processor_list/reactNative60Debug/annotationProcessors.json b/node_modules/react-native-notifications/lib/android/app/build/intermediates/annotation_processor_list/reactNative60Debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/annotation_processor_list/reactNative60Debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/compile_library_classes_jar/reactNative60Debug/classes.jar b/node_modules/react-native-notifications/lib/android/app/build/intermediates/compile_library_classes_jar/reactNative60Debug/classes.jar
new file mode 100644
index 0000000..118c0fd
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/compile_library_classes_jar/reactNative60Debug/classes.jar differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/compile_r_class_jar/reactNative60Debug/R.jar b/node_modules/react-native-notifications/lib/android/app/build/intermediates/compile_r_class_jar/reactNative60Debug/R.jar
new file mode 100644
index 0000000..7b0d25e
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/compile_r_class_jar/reactNative60Debug/R.jar differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/compile_symbol_list/reactNative60Debug/R.txt b/node_modules/react-native-notifications/lib/android/app/build/intermediates/compile_symbol_list/reactNative60Debug/R.txt
new file mode 100644
index 0000000..275dd97
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/compile_symbol_list/reactNative60Debug/R.txt
@@ -0,0 +1,1999 @@
+int anim abc_fade_in 0x0
+int anim abc_fade_out 0x0
+int anim abc_grow_fade_in_from_bottom 0x0
+int anim abc_popup_enter 0x0
+int anim abc_popup_exit 0x0
+int anim abc_shrink_fade_out_from_bottom 0x0
+int anim abc_slide_in_bottom 0x0
+int anim abc_slide_in_top 0x0
+int anim abc_slide_out_bottom 0x0
+int anim abc_slide_out_top 0x0
+int anim abc_tooltip_enter 0x0
+int anim abc_tooltip_exit 0x0
+int anim btn_checkbox_to_checked_box_inner_merged_animation 0x0
+int anim btn_checkbox_to_checked_box_outer_merged_animation 0x0
+int anim btn_checkbox_to_checked_icon_null_animation 0x0
+int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x0
+int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x0
+int anim btn_checkbox_to_unchecked_icon_null_animation 0x0
+int anim btn_radio_to_off_mtrl_dot_group_animation 0x0
+int anim btn_radio_to_off_mtrl_ring_outer_animation 0x0
+int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x0
+int anim btn_radio_to_on_mtrl_dot_group_animation 0x0
+int anim btn_radio_to_on_mtrl_ring_outer_animation 0x0
+int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x0
+int anim catalyst_fade_in 0x0
+int anim catalyst_fade_out 0x0
+int anim catalyst_push_up_in 0x0
+int anim catalyst_push_up_out 0x0
+int anim catalyst_slide_down 0x0
+int anim catalyst_slide_up 0x0
+int anim fragment_fast_out_extra_slow_in 0x0
+int animator fragment_close_enter 0x0
+int animator fragment_close_exit 0x0
+int animator fragment_fade_enter 0x0
+int animator fragment_fade_exit 0x0
+int animator fragment_open_enter 0x0
+int animator fragment_open_exit 0x0
+int attr actionBarDivider 0x0
+int attr actionBarItemBackground 0x0
+int attr actionBarPopupTheme 0x0
+int attr actionBarSize 0x0
+int attr actionBarSplitStyle 0x0
+int attr actionBarStyle 0x0
+int attr actionBarTabBarStyle 0x0
+int attr actionBarTabStyle 0x0
+int attr actionBarTabTextStyle 0x0
+int attr actionBarTheme 0x0
+int attr actionBarWidgetTheme 0x0
+int attr actionButtonStyle 0x0
+int attr actionDropDownStyle 0x0
+int attr actionLayout 0x0
+int attr actionMenuTextAppearance 0x0
+int attr actionMenuTextColor 0x0
+int attr actionModeBackground 0x0
+int attr actionModeCloseButtonStyle 0x0
+int attr actionModeCloseContentDescription 0x0
+int attr actionModeCloseDrawable 0x0
+int attr actionModeCopyDrawable 0x0
+int attr actionModeCutDrawable 0x0
+int attr actionModeFindDrawable 0x0
+int attr actionModePasteDrawable 0x0
+int attr actionModePopupWindowStyle 0x0
+int attr actionModeSelectAllDrawable 0x0
+int attr actionModeShareDrawable 0x0
+int attr actionModeSplitBackground 0x0
+int attr actionModeStyle 0x0
+int attr actionModeTheme 0x0
+int attr actionModeWebSearchDrawable 0x0
+int attr actionOverflowButtonStyle 0x0
+int attr actionOverflowMenuStyle 0x0
+int attr actionProviderClass 0x0
+int attr actionViewClass 0x0
+int attr activityChooserViewStyle 0x0
+int attr actualImageResource 0x0
+int attr actualImageScaleType 0x0
+int attr actualImageUri 0x0
+int attr alertDialogButtonGroupStyle 0x0
+int attr alertDialogCenterButtons 0x0
+int attr alertDialogStyle 0x0
+int attr alertDialogTheme 0x0
+int attr allowStacking 0x0
+int attr alpha 0x0
+int attr alphabeticModifiers 0x0
+int attr arrowHeadLength 0x0
+int attr arrowShaftLength 0x0
+int attr autoCompleteTextViewStyle 0x0
+int attr autoSizeMaxTextSize 0x0
+int attr autoSizeMinTextSize 0x0
+int attr autoSizePresetSizes 0x0
+int attr autoSizeStepGranularity 0x0
+int attr autoSizeTextType 0x0
+int attr autofillInlineSuggestionChip 0x0
+int attr autofillInlineSuggestionEndIconStyle 0x0
+int attr autofillInlineSuggestionStartIconStyle 0x0
+int attr autofillInlineSuggestionSubtitle 0x0
+int attr autofillInlineSuggestionTitle 0x0
+int attr background 0x0
+int attr backgroundImage 0x0
+int attr backgroundSplit 0x0
+int attr backgroundStacked 0x0
+int attr backgroundTint 0x0
+int attr backgroundTintMode 0x0
+int attr barLength 0x0
+int attr borderlessButtonStyle 0x0
+int attr buttonBarButtonStyle 0x0
+int attr buttonBarNegativeButtonStyle 0x0
+int attr buttonBarNeutralButtonStyle 0x0
+int attr buttonBarPositiveButtonStyle 0x0
+int attr buttonBarStyle 0x0
+int attr buttonCompat 0x0
+int attr buttonGravity 0x0
+int attr buttonIconDimen 0x0
+int attr buttonPanelSideLayout 0x0
+int attr buttonStyle 0x0
+int attr buttonStyleSmall 0x0
+int attr buttonTint 0x0
+int attr buttonTintMode 0x0
+int attr checkMarkCompat 0x0
+int attr checkMarkTint 0x0
+int attr checkMarkTintMode 0x0
+int attr checkboxStyle 0x0
+int attr checkedTextViewStyle 0x0
+int attr closeIcon 0x0
+int attr closeItemLayout 0x0
+int attr collapseContentDescription 0x0
+int attr collapseIcon 0x0
+int attr color 0x0
+int attr colorAccent 0x0
+int attr colorBackgroundFloating 0x0
+int attr colorButtonNormal 0x0
+int attr colorControlActivated 0x0
+int attr colorControlHighlight 0x0
+int attr colorControlNormal 0x0
+int attr colorError 0x0
+int attr colorPrimary 0x0
+int attr colorPrimaryDark 0x0
+int attr colorSwitchThumbNormal 0x0
+int attr commitIcon 0x0
+int attr contentDescription 0x0
+int attr contentInsetEnd 0x0
+int attr contentInsetEndWithActions 0x0
+int attr contentInsetLeft 0x0
+int attr contentInsetRight 0x0
+int attr contentInsetStart 0x0
+int attr contentInsetStartWithNavigation 0x0
+int attr controlBackground 0x0
+int attr coordinatorLayoutStyle 0x0
+int attr customNavigationLayout 0x0
+int attr defaultQueryHint 0x0
+int attr dialogCornerRadius 0x0
+int attr dialogPreferredPadding 0x0
+int attr dialogTheme 0x0
+int attr displayOptions 0x0
+int attr divider 0x0
+int attr dividerHorizontal 0x0
+int attr dividerPadding 0x0
+int attr dividerVertical 0x0
+int attr drawableBottomCompat 0x0
+int attr drawableEndCompat 0x0
+int attr drawableLeftCompat 0x0
+int attr drawableRightCompat 0x0
+int attr drawableSize 0x0
+int attr drawableStartCompat 0x0
+int attr drawableTint 0x0
+int attr drawableTintMode 0x0
+int attr drawableTopCompat 0x0
+int attr drawerArrowStyle 0x0
+int attr dropDownListViewStyle 0x0
+int attr dropdownListPreferredItemHeight 0x0
+int attr editTextBackground 0x0
+int attr editTextColor 0x0
+int attr editTextStyle 0x0
+int attr elevation 0x0
+int attr emojiCompatEnabled 0x0
+int attr expandActivityOverflowButtonDrawable 0x0
+int attr fadeDuration 0x0
+int attr failureImage 0x0
+int attr failureImageScaleType 0x0
+int attr firstBaselineToTopHeight 0x0
+int attr font 0x0
+int attr fontFamily 0x0
+int attr fontProviderAuthority 0x0
+int attr fontProviderCerts 0x0
+int attr fontProviderFetchStrategy 0x0
+int attr fontProviderFetchTimeout 0x0
+int attr fontProviderPackage 0x0
+int attr fontProviderQuery 0x0
+int attr fontProviderSystemFontFamily 0x0
+int attr fontStyle 0x0
+int attr fontVariationSettings 0x0
+int attr fontWeight 0x0
+int attr gapBetweenBars 0x0
+int attr goIcon 0x0
+int attr height 0x0
+int attr hideOnContentScroll 0x0
+int attr homeAsUpIndicator 0x0
+int attr homeLayout 0x0
+int attr icon 0x0
+int attr iconTint 0x0
+int attr iconTintMode 0x0
+int attr iconifiedByDefault 0x0
+int attr imageButtonStyle 0x0
+int attr indeterminateProgressStyle 0x0
+int attr initialActivityCount 0x0
+int attr isAutofillInlineSuggestionTheme 0x0
+int attr isLightTheme 0x0
+int attr itemPadding 0x0
+int attr keylines 0x0
+int attr lStar 0x0
+int attr lastBaselineToBottomHeight 0x0
+int attr layout 0x0
+int attr layout_anchor 0x0
+int attr layout_anchorGravity 0x0
+int attr layout_behavior 0x0
+int attr layout_dodgeInsetEdges 0x0
+int attr layout_insetEdge 0x0
+int attr layout_keyline 0x0
+int attr lineHeight 0x0
+int attr listChoiceBackgroundIndicator 0x0
+int attr listChoiceIndicatorMultipleAnimated 0x0
+int attr listChoiceIndicatorSingleAnimated 0x0
+int attr listDividerAlertDialog 0x0
+int attr listItemLayout 0x0
+int attr listLayout 0x0
+int attr listMenuViewStyle 0x0
+int attr listPopupWindowStyle 0x0
+int attr listPreferredItemHeight 0x0
+int attr listPreferredItemHeightLarge 0x0
+int attr listPreferredItemHeightSmall 0x0
+int attr listPreferredItemPaddingEnd 0x0
+int attr listPreferredItemPaddingLeft 0x0
+int attr listPreferredItemPaddingRight 0x0
+int attr listPreferredItemPaddingStart 0x0
+int attr logo 0x0
+int attr logoDescription 0x0
+int attr maxButtonHeight 0x0
+int attr measureWithLargestChild 0x0
+int attr menu 0x0
+int attr multiChoiceItemLayout 0x0
+int attr navigationContentDescription 0x0
+int attr navigationIcon 0x0
+int attr navigationMode 0x0
+int attr nestedScrollViewStyle 0x0
+int attr numericModifiers 0x0
+int attr overlapAnchor 0x0
+int attr overlayImage 0x0
+int attr paddingBottomNoButtons 0x0
+int attr paddingEnd 0x0
+int attr paddingStart 0x0
+int attr paddingTopNoTitle 0x0
+int attr panelBackground 0x0
+int attr panelMenuListTheme 0x0
+int attr panelMenuListWidth 0x0
+int attr placeholderImage 0x0
+int attr placeholderImageScaleType 0x0
+int attr popupMenuStyle 0x0
+int attr popupTheme 0x0
+int attr popupWindowStyle 0x0
+int attr preserveIconSpacing 0x0
+int attr pressedStateOverlayImage 0x0
+int attr progressBarAutoRotateInterval 0x0
+int attr progressBarImage 0x0
+int attr progressBarImageScaleType 0x0
+int attr progressBarPadding 0x0
+int attr progressBarStyle 0x0
+int attr queryBackground 0x0
+int attr queryHint 0x0
+int attr queryPatterns 0x0
+int attr radioButtonStyle 0x0
+int attr ratingBarStyle 0x0
+int attr ratingBarStyleIndicator 0x0
+int attr ratingBarStyleSmall 0x0
+int attr retryImage 0x0
+int attr retryImageScaleType 0x0
+int attr roundAsCircle 0x0
+int attr roundBottomEnd 0x0
+int attr roundBottomLeft 0x0
+int attr roundBottomRight 0x0
+int attr roundBottomStart 0x0
+int attr roundTopEnd 0x0
+int attr roundTopLeft 0x0
+int attr roundTopRight 0x0
+int attr roundTopStart 0x0
+int attr roundWithOverlayColor 0x0
+int attr roundedCornerRadius 0x0
+int attr roundingBorderColor 0x0
+int attr roundingBorderPadding 0x0
+int attr roundingBorderWidth 0x0
+int attr searchHintIcon 0x0
+int attr searchIcon 0x0
+int attr searchViewStyle 0x0
+int attr seekBarStyle 0x0
+int attr selectableItemBackground 0x0
+int attr selectableItemBackgroundBorderless 0x0
+int attr shortcutMatchRequired 0x0
+int attr showAsAction 0x0
+int attr showDividers 0x0
+int attr showText 0x0
+int attr showTitle 0x0
+int attr singleChoiceItemLayout 0x0
+int attr spinBars 0x0
+int attr spinnerDropDownItemStyle 0x0
+int attr spinnerStyle 0x0
+int attr splitTrack 0x0
+int attr srcCompat 0x0
+int attr state_above_anchor 0x0
+int attr statusBarBackground 0x0
+int attr subMenuArrow 0x0
+int attr submitBackground 0x0
+int attr subtitle 0x0
+int attr subtitleTextAppearance 0x0
+int attr subtitleTextColor 0x0
+int attr subtitleTextStyle 0x0
+int attr suggestionRowLayout 0x0
+int attr switchMinWidth 0x0
+int attr switchPadding 0x0
+int attr switchStyle 0x0
+int attr switchTextAppearance 0x0
+int attr textAllCaps 0x0
+int attr textAppearanceLargePopupMenu 0x0
+int attr textAppearanceListItem 0x0
+int attr textAppearanceListItemSecondary 0x0
+int attr textAppearanceListItemSmall 0x0
+int attr textAppearancePopupMenuHeader 0x0
+int attr textAppearanceSearchResultSubtitle 0x0
+int attr textAppearanceSearchResultTitle 0x0
+int attr textAppearanceSmallPopupMenu 0x0
+int attr textColorAlertDialogListItem 0x0
+int attr textColorSearchUrl 0x0
+int attr textLocale 0x0
+int attr theme 0x0
+int attr thickness 0x0
+int attr thumbTextPadding 0x0
+int attr thumbTint 0x0
+int attr thumbTintMode 0x0
+int attr tickMark 0x0
+int attr tickMarkTint 0x0
+int attr tickMarkTintMode 0x0
+int attr tint 0x0
+int attr tintMode 0x0
+int attr title 0x0
+int attr titleMargin 0x0
+int attr titleMarginBottom 0x0
+int attr titleMarginEnd 0x0
+int attr titleMarginStart 0x0
+int attr titleMarginTop 0x0
+int attr titleMargins 0x0
+int attr titleTextAppearance 0x0
+int attr titleTextColor 0x0
+int attr titleTextStyle 0x0
+int attr toolbarNavigationButtonStyle 0x0
+int attr toolbarStyle 0x0
+int attr tooltipForegroundColor 0x0
+int attr tooltipFrameBackground 0x0
+int attr tooltipText 0x0
+int attr track 0x0
+int attr trackTint 0x0
+int attr trackTintMode 0x0
+int attr ttcIndex 0x0
+int attr viewAspectRatio 0x0
+int attr viewInflaterClass 0x0
+int attr voiceIcon 0x0
+int attr windowActionBar 0x0
+int attr windowActionBarOverlay 0x0
+int attr windowActionModeOverlay 0x0
+int attr windowFixedHeightMajor 0x0
+int attr windowFixedHeightMinor 0x0
+int attr windowFixedWidthMajor 0x0
+int attr windowFixedWidthMinor 0x0
+int attr windowMinWidthMajor 0x0
+int attr windowMinWidthMinor 0x0
+int attr windowNoTitle 0x0
+int bool abc_action_bar_embed_tabs 0x0
+int bool abc_config_actionMenuItemAllCaps 0x0
+int color abc_background_cache_hint_selector_material_dark 0x0
+int color abc_background_cache_hint_selector_material_light 0x0
+int color abc_btn_colored_borderless_text_material 0x0
+int color abc_btn_colored_text_material 0x0
+int color abc_color_highlight_material 0x0
+int color abc_decor_view_status_guard 0x0
+int color abc_decor_view_status_guard_light 0x0
+int color abc_hint_foreground_material_dark 0x0
+int color abc_hint_foreground_material_light 0x0
+int color abc_primary_text_disable_only_material_dark 0x0
+int color abc_primary_text_disable_only_material_light 0x0
+int color abc_primary_text_material_dark 0x0
+int color abc_primary_text_material_light 0x0
+int color abc_search_url_text 0x0
+int color abc_search_url_text_normal 0x0
+int color abc_search_url_text_pressed 0x0
+int color abc_search_url_text_selected 0x0
+int color abc_secondary_text_material_dark 0x0
+int color abc_secondary_text_material_light 0x0
+int color abc_tint_btn_checkable 0x0
+int color abc_tint_default 0x0
+int color abc_tint_edittext 0x0
+int color abc_tint_seek_thumb 0x0
+int color abc_tint_spinner 0x0
+int color abc_tint_switch_track 0x0
+int color accent_material_dark 0x0
+int color accent_material_light 0x0
+int color androidx_core_ripple_material_light 0x0
+int color androidx_core_secondary_text_default_material_light 0x0
+int color background_floating_material_dark 0x0
+int color background_floating_material_light 0x0
+int color background_material_dark 0x0
+int color background_material_light 0x0
+int color bright_foreground_disabled_material_dark 0x0
+int color bright_foreground_disabled_material_light 0x0
+int color bright_foreground_inverse_material_dark 0x0
+int color bright_foreground_inverse_material_light 0x0
+int color bright_foreground_material_dark 0x0
+int color bright_foreground_material_light 0x0
+int color button_material_dark 0x0
+int color button_material_light 0x0
+int color call_notification_answer_color 0x0
+int color call_notification_decline_color 0x0
+int color catalyst_logbox_background 0x0
+int color catalyst_redbox_background 0x0
+int color dim_foreground_disabled_material_dark 0x0
+int color dim_foreground_disabled_material_light 0x0
+int color dim_foreground_material_dark 0x0
+int color dim_foreground_material_light 0x0
+int color error_color_material_dark 0x0
+int color error_color_material_light 0x0
+int color foreground_material_dark 0x0
+int color foreground_material_light 0x0
+int color highlighted_text_material_dark 0x0
+int color highlighted_text_material_light 0x0
+int color material_blue_grey_800 0x0
+int color material_blue_grey_900 0x0
+int color material_blue_grey_950 0x0
+int color material_deep_teal_200 0x0
+int color material_deep_teal_500 0x0
+int color material_grey_100 0x0
+int color material_grey_300 0x0
+int color material_grey_50 0x0
+int color material_grey_600 0x0
+int color material_grey_800 0x0
+int color material_grey_850 0x0
+int color material_grey_900 0x0
+int color notification_action_color_filter 0x0
+int color notification_icon_bg_color 0x0
+int color primary_dark_material_dark 0x0
+int color primary_dark_material_light 0x0
+int color primary_material_dark 0x0
+int color primary_material_light 0x0
+int color primary_text_default_material_dark 0x0
+int color primary_text_default_material_light 0x0
+int color primary_text_disabled_material_dark 0x0
+int color primary_text_disabled_material_light 0x0
+int color ripple_material_dark 0x0
+int color ripple_material_light 0x0
+int color secondary_text_default_material_dark 0x0
+int color secondary_text_default_material_light 0x0
+int color secondary_text_disabled_material_dark 0x0
+int color secondary_text_disabled_material_light 0x0
+int color switch_thumb_disabled_material_dark 0x0
+int color switch_thumb_disabled_material_light 0x0
+int color switch_thumb_material_dark 0x0
+int color switch_thumb_material_light 0x0
+int color switch_thumb_normal_material_dark 0x0
+int color switch_thumb_normal_material_light 0x0
+int color tooltip_background_dark 0x0
+int color tooltip_background_light 0x0
+int dimen abc_action_bar_content_inset_material 0x0
+int dimen abc_action_bar_content_inset_with_nav 0x0
+int dimen abc_action_bar_default_height_material 0x0
+int dimen abc_action_bar_default_padding_end_material 0x0
+int dimen abc_action_bar_default_padding_start_material 0x0
+int dimen abc_action_bar_elevation_material 0x0
+int dimen abc_action_bar_icon_vertical_padding_material 0x0
+int dimen abc_action_bar_overflow_padding_end_material 0x0
+int dimen abc_action_bar_overflow_padding_start_material 0x0
+int dimen abc_action_bar_stacked_max_height 0x0
+int dimen abc_action_bar_stacked_tab_max_width 0x0
+int dimen abc_action_bar_subtitle_bottom_margin_material 0x0
+int dimen abc_action_bar_subtitle_top_margin_material 0x0
+int dimen abc_action_button_min_height_material 0x0
+int dimen abc_action_button_min_width_material 0x0
+int dimen abc_action_button_min_width_overflow_material 0x0
+int dimen abc_alert_dialog_button_bar_height 0x0
+int dimen abc_alert_dialog_button_dimen 0x0
+int dimen abc_button_inset_horizontal_material 0x0
+int dimen abc_button_inset_vertical_material 0x0
+int dimen abc_button_padding_horizontal_material 0x0
+int dimen abc_button_padding_vertical_material 0x0
+int dimen abc_cascading_menus_min_smallest_width 0x0
+int dimen abc_config_prefDialogWidth 0x0
+int dimen abc_control_corner_material 0x0
+int dimen abc_control_inset_material 0x0
+int dimen abc_control_padding_material 0x0
+int dimen abc_dialog_corner_radius_material 0x0
+int dimen abc_dialog_fixed_height_major 0x0
+int dimen abc_dialog_fixed_height_minor 0x0
+int dimen abc_dialog_fixed_width_major 0x0
+int dimen abc_dialog_fixed_width_minor 0x0
+int dimen abc_dialog_list_padding_bottom_no_buttons 0x0
+int dimen abc_dialog_list_padding_top_no_title 0x0
+int dimen abc_dialog_min_width_major 0x0
+int dimen abc_dialog_min_width_minor 0x0
+int dimen abc_dialog_padding_material 0x0
+int dimen abc_dialog_padding_top_material 0x0
+int dimen abc_dialog_title_divider_material 0x0
+int dimen abc_disabled_alpha_material_dark 0x0
+int dimen abc_disabled_alpha_material_light 0x0
+int dimen abc_dropdownitem_icon_width 0x0
+int dimen abc_dropdownitem_text_padding_left 0x0
+int dimen abc_dropdownitem_text_padding_right 0x0
+int dimen abc_edit_text_inset_bottom_material 0x0
+int dimen abc_edit_text_inset_horizontal_material 0x0
+int dimen abc_edit_text_inset_top_material 0x0
+int dimen abc_floating_window_z 0x0
+int dimen abc_list_item_height_large_material 0x0
+int dimen abc_list_item_height_material 0x0
+int dimen abc_list_item_height_small_material 0x0
+int dimen abc_list_item_padding_horizontal_material 0x0
+int dimen abc_panel_menu_list_width 0x0
+int dimen abc_progress_bar_height_material 0x0
+int dimen abc_search_view_preferred_height 0x0
+int dimen abc_search_view_preferred_width 0x0
+int dimen abc_seekbar_track_background_height_material 0x0
+int dimen abc_seekbar_track_progress_height_material 0x0
+int dimen abc_select_dialog_padding_start_material 0x0
+int dimen abc_star_big 0x0
+int dimen abc_star_medium 0x0
+int dimen abc_star_small 0x0
+int dimen abc_switch_padding 0x0
+int dimen abc_text_size_body_1_material 0x0
+int dimen abc_text_size_body_2_material 0x0
+int dimen abc_text_size_button_material 0x0
+int dimen abc_text_size_caption_material 0x0
+int dimen abc_text_size_display_1_material 0x0
+int dimen abc_text_size_display_2_material 0x0
+int dimen abc_text_size_display_3_material 0x0
+int dimen abc_text_size_display_4_material 0x0
+int dimen abc_text_size_headline_material 0x0
+int dimen abc_text_size_large_material 0x0
+int dimen abc_text_size_medium_material 0x0
+int dimen abc_text_size_menu_header_material 0x0
+int dimen abc_text_size_menu_material 0x0
+int dimen abc_text_size_small_material 0x0
+int dimen abc_text_size_subhead_material 0x0
+int dimen abc_text_size_subtitle_material_toolbar 0x0
+int dimen abc_text_size_title_material 0x0
+int dimen abc_text_size_title_material_toolbar 0x0
+int dimen autofill_inline_suggestion_icon_size 0x0
+int dimen compat_button_inset_horizontal_material 0x0
+int dimen compat_button_inset_vertical_material 0x0
+int dimen compat_button_padding_horizontal_material 0x0
+int dimen compat_button_padding_vertical_material 0x0
+int dimen compat_control_corner_material 0x0
+int dimen compat_notification_large_icon_max_height 0x0
+int dimen compat_notification_large_icon_max_width 0x0
+int dimen disabled_alpha_material_dark 0x0
+int dimen disabled_alpha_material_light 0x0
+int dimen highlight_alpha_material_colored 0x0
+int dimen highlight_alpha_material_dark 0x0
+int dimen highlight_alpha_material_light 0x0
+int dimen hint_alpha_material_dark 0x0
+int dimen hint_alpha_material_light 0x0
+int dimen hint_pressed_alpha_material_dark 0x0
+int dimen hint_pressed_alpha_material_light 0x0
+int dimen notification_action_icon_size 0x0
+int dimen notification_action_text_size 0x0
+int dimen notification_big_circle_margin 0x0
+int dimen notification_content_margin_start 0x0
+int dimen notification_large_icon_height 0x0
+int dimen notification_large_icon_width 0x0
+int dimen notification_main_column_padding_top 0x0
+int dimen notification_media_narrow_margin 0x0
+int dimen notification_right_icon_size 0x0
+int dimen notification_right_side_padding_top 0x0
+int dimen notification_small_icon_background_padding 0x0
+int dimen notification_small_icon_size_as_large 0x0
+int dimen notification_subtext_size 0x0
+int dimen notification_top_pad 0x0
+int dimen notification_top_pad_large_text 0x0
+int dimen tooltip_corner_radius 0x0
+int dimen tooltip_horizontal_padding 0x0
+int dimen tooltip_margin 0x0
+int dimen tooltip_precise_anchor_extra_offset 0x0
+int dimen tooltip_precise_anchor_threshold 0x0
+int dimen tooltip_vertical_padding 0x0
+int dimen tooltip_y_offset_non_touch 0x0
+int dimen tooltip_y_offset_touch 0x0
+int drawable abc_ab_share_pack_mtrl_alpha 0x0
+int drawable abc_action_bar_item_background_material 0x0
+int drawable abc_btn_borderless_material 0x0
+int drawable abc_btn_check_material 0x0
+int drawable abc_btn_check_material_anim 0x0
+int drawable abc_btn_check_to_on_mtrl_000 0x0
+int drawable abc_btn_check_to_on_mtrl_015 0x0
+int drawable abc_btn_colored_material 0x0
+int drawable abc_btn_default_mtrl_shape 0x0
+int drawable abc_btn_radio_material 0x0
+int drawable abc_btn_radio_material_anim 0x0
+int drawable abc_btn_radio_to_on_mtrl_000 0x0
+int drawable abc_btn_radio_to_on_mtrl_015 0x0
+int drawable abc_btn_switch_to_on_mtrl_00001 0x0
+int drawable abc_btn_switch_to_on_mtrl_00012 0x0
+int drawable abc_cab_background_internal_bg 0x0
+int drawable abc_cab_background_top_material 0x0
+int drawable abc_cab_background_top_mtrl_alpha 0x0
+int drawable abc_control_background_material 0x0
+int drawable abc_dialog_material_background 0x0
+int drawable abc_edit_text_material 0x0
+int drawable abc_ic_ab_back_material 0x0
+int drawable abc_ic_arrow_drop_right_black_24dp 0x0
+int drawable abc_ic_clear_material 0x0
+int drawable abc_ic_commit_search_api_mtrl_alpha 0x0
+int drawable abc_ic_go_search_api_material 0x0
+int drawable abc_ic_menu_copy_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_cut_mtrl_alpha 0x0
+int drawable abc_ic_menu_overflow_material 0x0
+int drawable abc_ic_menu_paste_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_selectall_mtrl_alpha 0x0
+int drawable abc_ic_menu_share_mtrl_alpha 0x0
+int drawable abc_ic_search_api_material 0x0
+int drawable abc_ic_voice_search_api_material 0x0
+int drawable abc_item_background_holo_dark 0x0
+int drawable abc_item_background_holo_light 0x0
+int drawable abc_list_divider_material 0x0
+int drawable abc_list_divider_mtrl_alpha 0x0
+int drawable abc_list_focused_holo 0x0
+int drawable abc_list_longpressed_holo 0x0
+int drawable abc_list_pressed_holo_dark 0x0
+int drawable abc_list_pressed_holo_light 0x0
+int drawable abc_list_selector_background_transition_holo_dark 0x0
+int drawable abc_list_selector_background_transition_holo_light 0x0
+int drawable abc_list_selector_disabled_holo_dark 0x0
+int drawable abc_list_selector_disabled_holo_light 0x0
+int drawable abc_list_selector_holo_dark 0x0
+int drawable abc_list_selector_holo_light 0x0
+int drawable abc_menu_hardkey_panel_mtrl_mult 0x0
+int drawable abc_popup_background_mtrl_mult 0x0
+int drawable abc_ratingbar_indicator_material 0x0
+int drawable abc_ratingbar_material 0x0
+int drawable abc_ratingbar_small_material 0x0
+int drawable abc_scrubber_control_off_mtrl_alpha 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_000 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_005 0x0
+int drawable abc_scrubber_primary_mtrl_alpha 0x0
+int drawable abc_scrubber_track_mtrl_alpha 0x0
+int drawable abc_seekbar_thumb_material 0x0
+int drawable abc_seekbar_tick_mark_material 0x0
+int drawable abc_seekbar_track_material 0x0
+int drawable abc_spinner_mtrl_am_alpha 0x0
+int drawable abc_spinner_textfield_background_material 0x0
+int drawable abc_star_black_48dp 0x0
+int drawable abc_star_half_black_48dp 0x0
+int drawable abc_switch_thumb_material 0x0
+int drawable abc_switch_track_mtrl_alpha 0x0
+int drawable abc_tab_indicator_material 0x0
+int drawable abc_tab_indicator_mtrl_alpha 0x0
+int drawable abc_text_cursor_material 0x0
+int drawable abc_text_select_handle_left_mtrl 0x0
+int drawable abc_text_select_handle_middle_mtrl 0x0
+int drawable abc_text_select_handle_right_mtrl 0x0
+int drawable abc_textfield_activated_mtrl_alpha 0x0
+int drawable abc_textfield_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_activated_mtrl_alpha 0x0
+int drawable abc_textfield_search_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_material 0x0
+int drawable abc_vector_test 0x0
+int drawable autofill_inline_suggestion_chip_background 0x0
+int drawable btn_checkbox_checked_mtrl 0x0
+int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x0
+int drawable btn_checkbox_unchecked_mtrl 0x0
+int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x0
+int drawable btn_radio_off_mtrl 0x0
+int drawable btn_radio_off_to_on_mtrl_animation 0x0
+int drawable btn_radio_on_mtrl 0x0
+int drawable btn_radio_on_to_off_mtrl_animation 0x0
+int drawable ic_call_answer 0x0
+int drawable ic_call_answer_low 0x0
+int drawable ic_call_answer_video 0x0
+int drawable ic_call_answer_video_low 0x0
+int drawable ic_call_decline 0x0
+int drawable ic_call_decline_low 0x0
+int drawable notification_action_background 0x0
+int drawable notification_bg 0x0
+int drawable notification_bg_low 0x0
+int drawable notification_bg_low_normal 0x0
+int drawable notification_bg_low_pressed 0x0
+int drawable notification_bg_normal 0x0
+int drawable notification_bg_normal_pressed 0x0
+int drawable notification_icon_background 0x0
+int drawable notification_template_icon_bg 0x0
+int drawable notification_template_icon_low_bg 0x0
+int drawable notification_tile_bg 0x0
+int drawable notify_panel_notification_icon_bg 0x0
+int drawable redbox_top_border_background 0x0
+int drawable test_level_drawable 0x0
+int drawable tooltip_frame_dark 0x0
+int drawable tooltip_frame_light 0x0
+int id accessibility_action_clickable_span 0x0
+int id accessibility_actions 0x0
+int id accessibility_collection 0x0
+int id accessibility_collection_item 0x0
+int id accessibility_custom_action_0 0x0
+int id accessibility_custom_action_1 0x0
+int id accessibility_custom_action_10 0x0
+int id accessibility_custom_action_11 0x0
+int id accessibility_custom_action_12 0x0
+int id accessibility_custom_action_13 0x0
+int id accessibility_custom_action_14 0x0
+int id accessibility_custom_action_15 0x0
+int id accessibility_custom_action_16 0x0
+int id accessibility_custom_action_17 0x0
+int id accessibility_custom_action_18 0x0
+int id accessibility_custom_action_19 0x0
+int id accessibility_custom_action_2 0x0
+int id accessibility_custom_action_20 0x0
+int id accessibility_custom_action_21 0x0
+int id accessibility_custom_action_22 0x0
+int id accessibility_custom_action_23 0x0
+int id accessibility_custom_action_24 0x0
+int id accessibility_custom_action_25 0x0
+int id accessibility_custom_action_26 0x0
+int id accessibility_custom_action_27 0x0
+int id accessibility_custom_action_28 0x0
+int id accessibility_custom_action_29 0x0
+int id accessibility_custom_action_3 0x0
+int id accessibility_custom_action_30 0x0
+int id accessibility_custom_action_31 0x0
+int id accessibility_custom_action_4 0x0
+int id accessibility_custom_action_5 0x0
+int id accessibility_custom_action_6 0x0
+int id accessibility_custom_action_7 0x0
+int id accessibility_custom_action_8 0x0
+int id accessibility_custom_action_9 0x0
+int id accessibility_hint 0x0
+int id accessibility_label 0x0
+int id accessibility_links 0x0
+int id accessibility_role 0x0
+int id accessibility_state 0x0
+int id accessibility_state_expanded 0x0
+int id accessibility_value 0x0
+int id action_bar 0x0
+int id action_bar_activity_content 0x0
+int id action_bar_container 0x0
+int id action_bar_root 0x0
+int id action_bar_spinner 0x0
+int id action_bar_subtitle 0x0
+int id action_bar_title 0x0
+int id action_container 0x0
+int id action_context_bar 0x0
+int id action_divider 0x0
+int id action_image 0x0
+int id action_menu_divider 0x0
+int id action_menu_presenter 0x0
+int id action_mode_bar 0x0
+int id action_mode_bar_stub 0x0
+int id action_mode_close_button 0x0
+int id action_text 0x0
+int id actions 0x0
+int id activity_chooser_view_content 0x0
+int id add 0x0
+int id alertTitle 0x0
+int id async 0x0
+int id autofill_inline_suggestion_end_icon 0x0
+int id autofill_inline_suggestion_start_icon 0x0
+int id autofill_inline_suggestion_subtitle 0x0
+int id autofill_inline_suggestion_title 0x0
+int id blocking 0x0
+int id bottom 0x0
+int id buttonPanel 0x0
+int id catalyst_redbox_title 0x0
+int id center 0x0
+int id centerCrop 0x0
+int id centerInside 0x0
+int id checkbox 0x0
+int id checked 0x0
+int id chronometer 0x0
+int id content 0x0
+int id contentPanel 0x0
+int id custom 0x0
+int id customPanel 0x0
+int id decor_content_parent 0x0
+int id default_activity_button 0x0
+int id dialog_button 0x0
+int id edit_query 0x0
+int id end 0x0
+int id expand_activities_button 0x0
+int id expanded_menu 0x0
+int id fitBottomStart 0x0
+int id fitCenter 0x0
+int id fitEnd 0x0
+int id fitStart 0x0
+int id fitXY 0x0
+int id focusCrop 0x0
+int id forever 0x0
+int id fps_text 0x0
+int id fragment_container_view_tag 0x0
+int id group_divider 0x0
+int id home 0x0
+int id icon 0x0
+int id icon_group 0x0
+int id image 0x0
+int id info 0x0
+int id italic 0x0
+int id item1 0x0
+int id item2 0x0
+int id item3 0x0
+int id item4 0x0
+int id labelled_by 0x0
+int id left 0x0
+int id line1 0x0
+int id line3 0x0
+int id listMode 0x0
+int id list_item 0x0
+int id message 0x0
+int id multiply 0x0
+int id none 0x0
+int id normal 0x0
+int id notification_background 0x0
+int id notification_main_column 0x0
+int id notification_main_column_container 0x0
+int id off 0x0
+int id on 0x0
+int id parentPanel 0x0
+int id pointer_events 0x0
+int id progress_circular 0x0
+int id progress_horizontal 0x0
+int id radio 0x0
+int id react_test_id 0x0
+int id right 0x0
+int id right_icon 0x0
+int id right_side 0x0
+int id rn_frame_file 0x0
+int id rn_frame_method 0x0
+int id rn_redbox_dismiss_button 0x0
+int id rn_redbox_line_separator 0x0
+int id rn_redbox_loading_indicator 0x0
+int id rn_redbox_reload_button 0x0
+int id rn_redbox_report_button 0x0
+int id rn_redbox_report_label 0x0
+int id rn_redbox_stack 0x0
+int id screen 0x0
+int id scrollIndicatorDown 0x0
+int id scrollIndicatorUp 0x0
+int id scrollView 0x0
+int id search_badge 0x0
+int id search_bar 0x0
+int id search_button 0x0
+int id search_close_btn 0x0
+int id search_edit_frame 0x0
+int id search_go_btn 0x0
+int id search_mag_icon 0x0
+int id search_plate 0x0
+int id search_src_text 0x0
+int id search_voice_btn 0x0
+int id select_dialog_listview 0x0
+int id shortcut 0x0
+int id spacer 0x0
+int id special_effects_controller_view_tag 0x0
+int id split_action_bar 0x0
+int id src_atop 0x0
+int id src_in 0x0
+int id src_over 0x0
+int id start 0x0
+int id submenuarrow 0x0
+int id submit_area 0x0
+int id tabMode 0x0
+int id tag_accessibility_actions 0x0
+int id tag_accessibility_clickable_spans 0x0
+int id tag_accessibility_heading 0x0
+int id tag_accessibility_pane_title 0x0
+int id tag_on_apply_window_listener 0x0
+int id tag_on_receive_content_listener 0x0
+int id tag_on_receive_content_mime_types 0x0
+int id tag_screen_reader_focusable 0x0
+int id tag_state_description 0x0
+int id tag_transition_group 0x0
+int id tag_unhandled_key_event_manager 0x0
+int id tag_unhandled_key_listeners 0x0
+int id tag_window_insets_animation_callback 0x0
+int id text 0x0
+int id text2 0x0
+int id textSpacerNoButtons 0x0
+int id textSpacerNoTitle 0x0
+int id time 0x0
+int id title 0x0
+int id titleDividerNoCustom 0x0
+int id title_template 0x0
+int id top 0x0
+int id topPanel 0x0
+int id unchecked 0x0
+int id uniform 0x0
+int id up 0x0
+int id view_tag_instance_handle 0x0
+int id view_tag_native_id 0x0
+int id view_tree_lifecycle_owner 0x0
+int id view_tree_saved_state_registry_owner 0x0
+int id view_tree_view_model_store_owner 0x0
+int id visible_removing_fragment_view_tag 0x0
+int id wrap_content 0x0
+int integer abc_config_activityDefaultDur 0x0
+int integer abc_config_activityShortDur 0x0
+int integer cancel_button_image_alpha 0x0
+int integer config_tooltipAnimTime 0x0
+int integer google_play_services_version 0x0
+int integer react_native_dev_server_port 0x0
+int integer react_native_inspector_proxy_port 0x0
+int integer status_bar_notification_info_maxnum 0x0
+int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x0
+int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x0
+int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x0
+int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x0
+int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x0
+int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x0
+int interpolator fast_out_slow_in 0x0
+int layout abc_action_bar_title_item 0x0
+int layout abc_action_bar_up_container 0x0
+int layout abc_action_menu_item_layout 0x0
+int layout abc_action_menu_layout 0x0
+int layout abc_action_mode_bar 0x0
+int layout abc_action_mode_close_item_material 0x0
+int layout abc_activity_chooser_view 0x0
+int layout abc_activity_chooser_view_list_item 0x0
+int layout abc_alert_dialog_button_bar_material 0x0
+int layout abc_alert_dialog_material 0x0
+int layout abc_alert_dialog_title_material 0x0
+int layout abc_cascading_menu_item_layout 0x0
+int layout abc_dialog_title_material 0x0
+int layout abc_expanded_menu_layout 0x0
+int layout abc_list_menu_item_checkbox 0x0
+int layout abc_list_menu_item_icon 0x0
+int layout abc_list_menu_item_layout 0x0
+int layout abc_list_menu_item_radio 0x0
+int layout abc_popup_menu_header_item_layout 0x0
+int layout abc_popup_menu_item_layout 0x0
+int layout abc_screen_content_include 0x0
+int layout abc_screen_simple 0x0
+int layout abc_screen_simple_overlay_action_mode 0x0
+int layout abc_screen_toolbar 0x0
+int layout abc_search_dropdown_item_icons_2line 0x0
+int layout abc_search_view 0x0
+int layout abc_select_dialog_material 0x0
+int layout abc_tooltip 0x0
+int layout autofill_inline_suggestion 0x0
+int layout custom_dialog 0x0
+int layout dev_loading_view 0x0
+int layout fps_view 0x0
+int layout notification_action 0x0
+int layout notification_action_tombstone 0x0
+int layout notification_template_custom_big 0x0
+int layout notification_template_icon_group 0x0
+int layout notification_template_part_chronometer 0x0
+int layout notification_template_part_time 0x0
+int layout redbox_item_frame 0x0
+int layout redbox_item_title 0x0
+int layout redbox_view 0x0
+int layout select_dialog_item_material 0x0
+int layout select_dialog_multichoice_material 0x0
+int layout select_dialog_singlechoice_material 0x0
+int layout support_simple_spinner_dropdown_item 0x0
+int menu example_menu 0x0
+int menu example_menu2 0x0
+int string abc_action_bar_home_description 0x0
+int string abc_action_bar_up_description 0x0
+int string abc_action_menu_overflow_description 0x0
+int string abc_action_mode_done 0x0
+int string abc_activity_chooser_view_see_all 0x0
+int string abc_activitychooserview_choose_application 0x0
+int string abc_capital_off 0x0
+int string abc_capital_on 0x0
+int string abc_menu_alt_shortcut_label 0x0
+int string abc_menu_ctrl_shortcut_label 0x0
+int string abc_menu_delete_shortcut_label 0x0
+int string abc_menu_enter_shortcut_label 0x0
+int string abc_menu_function_shortcut_label 0x0
+int string abc_menu_meta_shortcut_label 0x0
+int string abc_menu_shift_shortcut_label 0x0
+int string abc_menu_space_shortcut_label 0x0
+int string abc_menu_sym_shortcut_label 0x0
+int string abc_prepend_shortcut_label 0x0
+int string abc_search_hint 0x0
+int string abc_searchview_description_clear 0x0
+int string abc_searchview_description_query 0x0
+int string abc_searchview_description_search 0x0
+int string abc_searchview_description_submit 0x0
+int string abc_searchview_description_voice 0x0
+int string abc_shareactionprovider_share_with 0x0
+int string abc_shareactionprovider_share_with_application 0x0
+int string abc_toolbar_collapse_description 0x0
+int string alert_description 0x0
+int string call_notification_answer_action 0x0
+int string call_notification_answer_video_action 0x0
+int string call_notification_decline_action 0x0
+int string call_notification_hang_up_action 0x0
+int string call_notification_incoming_text 0x0
+int string call_notification_ongoing_text 0x0
+int string call_notification_screening_text 0x0
+int string catalyst_change_bundle_location 0x0
+int string catalyst_copy_button 0x0
+int string catalyst_debug 0x0
+int string catalyst_debug_chrome 0x0
+int string catalyst_debug_chrome_stop 0x0
+int string catalyst_debug_connecting 0x0
+int string catalyst_debug_error 0x0
+int string catalyst_debug_open 0x0
+int string catalyst_debug_stop 0x0
+int string catalyst_devtools_open 0x0
+int string catalyst_dismiss_button 0x0
+int string catalyst_heap_capture 0x0
+int string catalyst_hot_reloading 0x0
+int string catalyst_hot_reloading_auto_disable 0x0
+int string catalyst_hot_reloading_auto_enable 0x0
+int string catalyst_hot_reloading_stop 0x0
+int string catalyst_inspector 0x0
+int string catalyst_inspector_stop 0x0
+int string catalyst_loading_from_url 0x0
+int string catalyst_open_flipper_error 0x0
+int string catalyst_perf_monitor 0x0
+int string catalyst_perf_monitor_stop 0x0
+int string catalyst_reload 0x0
+int string catalyst_reload_button 0x0
+int string catalyst_reload_error 0x0
+int string catalyst_report_button 0x0
+int string catalyst_sample_profiler_disable 0x0
+int string catalyst_sample_profiler_enable 0x0
+int string catalyst_settings 0x0
+int string catalyst_settings_title 0x0
+int string combobox_description 0x0
+int string common_google_play_services_unknown_issue 0x0
+int string fcm_fallback_notification_channel_label 0x0
+int string header_description 0x0
+int string image_description 0x0
+int string imagebutton_description 0x0
+int string link_description 0x0
+int string menu_description 0x0
+int string menubar_description 0x0
+int string menuitem_description 0x0
+int string progressbar_description 0x0
+int string radiogroup_description 0x0
+int string rn_tab_description 0x0
+int string scrollbar_description 0x0
+int string search_menu_title 0x0
+int string spinbutton_description 0x0
+int string state_busy_description 0x0
+int string state_collapsed_description 0x0
+int string state_expanded_description 0x0
+int string state_mixed_description 0x0
+int string state_off_description 0x0
+int string state_on_description 0x0
+int string state_unselected_description 0x0
+int string status_bar_notification_info_overflow 0x0
+int string summary_description 0x0
+int string tablist_description 0x0
+int string timer_description 0x0
+int string toolbar_description 0x0
+int style AlertDialog_AppCompat 0x0
+int style AlertDialog_AppCompat_Light 0x0
+int style Animation_AppCompat_Dialog 0x0
+int style Animation_AppCompat_DropDownUp 0x0
+int style Animation_AppCompat_Tooltip 0x0
+int style Animation_Catalyst_LogBox 0x0
+int style Animation_Catalyst_RedBox 0x0
+int style Base_AlertDialog_AppCompat 0x0
+int style Base_AlertDialog_AppCompat_Light 0x0
+int style Base_Animation_AppCompat_Dialog 0x0
+int style Base_Animation_AppCompat_DropDownUp 0x0
+int style Base_Animation_AppCompat_Tooltip 0x0
+int style Base_DialogWindowTitleBackground_AppCompat 0x0
+int style Base_DialogWindowTitle_AppCompat 0x0
+int style Base_TextAppearance_AppCompat 0x0
+int style Base_TextAppearance_AppCompat_Body1 0x0
+int style Base_TextAppearance_AppCompat_Body2 0x0
+int style Base_TextAppearance_AppCompat_Button 0x0
+int style Base_TextAppearance_AppCompat_Caption 0x0
+int style Base_TextAppearance_AppCompat_Display1 0x0
+int style Base_TextAppearance_AppCompat_Display2 0x0
+int style Base_TextAppearance_AppCompat_Display3 0x0
+int style Base_TextAppearance_AppCompat_Display4 0x0
+int style Base_TextAppearance_AppCompat_Headline 0x0
+int style Base_TextAppearance_AppCompat_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Large 0x0
+int style Base_TextAppearance_AppCompat_Large_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Medium 0x0
+int style Base_TextAppearance_AppCompat_Medium_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Menu 0x0
+int style Base_TextAppearance_AppCompat_SearchResult 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Title 0x0
+int style Base_TextAppearance_AppCompat_Small 0x0
+int style Base_TextAppearance_AppCompat_Small_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Subhead 0x0
+int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Title 0x0
+int style Base_TextAppearance_AppCompat_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Tooltip 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Widget_Switch 0x0
+int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Base_ThemeOverlay_AppCompat 0x0
+int style Base_ThemeOverlay_AppCompat_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dark 0x0
+int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style Base_ThemeOverlay_AppCompat_Light 0x0
+int style Base_Theme_AppCompat 0x0
+int style Base_Theme_AppCompat_CompactMenu 0x0
+int style Base_Theme_AppCompat_Dialog 0x0
+int style Base_Theme_AppCompat_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Dialog_MinWidth 0x0
+int style Base_Theme_AppCompat_Light 0x0
+int style Base_Theme_AppCompat_Light_DarkActionBar 0x0
+int style Base_Theme_AppCompat_Light_Dialog 0x0
+int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat 0x0
+int style Base_V21_Theme_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat_Light 0x0
+int style Base_V21_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V22_Theme_AppCompat 0x0
+int style Base_V22_Theme_AppCompat_Light 0x0
+int style Base_V23_Theme_AppCompat 0x0
+int style Base_V23_Theme_AppCompat_Light 0x0
+int style Base_V26_Theme_AppCompat 0x0
+int style Base_V26_Theme_AppCompat_Light 0x0
+int style Base_V26_Widget_AppCompat_Toolbar 0x0
+int style Base_V28_Theme_AppCompat 0x0
+int style Base_V28_Theme_AppCompat_Light 0x0
+int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat 0x0
+int style Base_V7_Theme_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat_Light 0x0
+int style Base_V7_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_V7_Widget_AppCompat_EditText 0x0
+int style Base_V7_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_ActionBar 0x0
+int style Base_Widget_AppCompat_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_ActionButton 0x0
+int style Base_Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Base_Widget_AppCompat_ActionButton_Overflow 0x0
+int style Base_Widget_AppCompat_ActionMode 0x0
+int style Base_Widget_AppCompat_ActivityChooserView 0x0
+int style Base_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_Widget_AppCompat_Button 0x0
+int style Base_Widget_AppCompat_ButtonBar 0x0
+int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Borderless 0x0
+int style Base_Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Colored 0x0
+int style Base_Widget_AppCompat_Button_Small 0x0
+int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Base_Widget_AppCompat_CompoundButton_Switch 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x0
+int style Base_Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Base_Widget_AppCompat_EditText 0x0
+int style Base_Widget_AppCompat_ImageButton 0x0
+int style Base_Widget_AppCompat_Light_ActionBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_ListMenuView 0x0
+int style Base_Widget_AppCompat_ListPopupWindow 0x0
+int style Base_Widget_AppCompat_ListView 0x0
+int style Base_Widget_AppCompat_ListView_DropDown 0x0
+int style Base_Widget_AppCompat_ListView_Menu 0x0
+int style Base_Widget_AppCompat_PopupMenu 0x0
+int style Base_Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_PopupWindow 0x0
+int style Base_Widget_AppCompat_ProgressBar 0x0
+int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Base_Widget_AppCompat_RatingBar 0x0
+int style Base_Widget_AppCompat_RatingBar_Indicator 0x0
+int style Base_Widget_AppCompat_RatingBar_Small 0x0
+int style Base_Widget_AppCompat_SearchView 0x0
+int style Base_Widget_AppCompat_SearchView_ActionBar 0x0
+int style Base_Widget_AppCompat_SeekBar 0x0
+int style Base_Widget_AppCompat_SeekBar_Discrete 0x0
+int style Base_Widget_AppCompat_Spinner 0x0
+int style Base_Widget_AppCompat_Spinner_Underlined 0x0
+int style Base_Widget_AppCompat_TextView 0x0
+int style Base_Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Base_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style CalendarDatePickerDialog 0x0
+int style CalendarDatePickerStyle 0x0
+int style DialogAnimationFade 0x0
+int style DialogAnimationSlide 0x0
+int style Platform_AppCompat 0x0
+int style Platform_AppCompat_Light 0x0
+int style Platform_ThemeOverlay_AppCompat 0x0
+int style Platform_ThemeOverlay_AppCompat_Dark 0x0
+int style Platform_ThemeOverlay_AppCompat_Light 0x0
+int style Platform_V21_AppCompat 0x0
+int style Platform_V21_AppCompat_Light 0x0
+int style Platform_V25_AppCompat 0x0
+int style Platform_V25_AppCompat_Light 0x0
+int style Platform_Widget_AppCompat_Spinner 0x0
+int style RtlOverlay_DialogWindowTitle_AppCompat 0x0
+int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x0
+int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x0
+int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x0
+int style SpinnerDatePickerDialog 0x0
+int style SpinnerDatePickerStyle 0x0
+int style TextAppearance_AppCompat 0x0
+int style TextAppearance_AppCompat_Body1 0x0
+int style TextAppearance_AppCompat_Body2 0x0
+int style TextAppearance_AppCompat_Button 0x0
+int style TextAppearance_AppCompat_Caption 0x0
+int style TextAppearance_AppCompat_Display1 0x0
+int style TextAppearance_AppCompat_Display2 0x0
+int style TextAppearance_AppCompat_Display3 0x0
+int style TextAppearance_AppCompat_Display4 0x0
+int style TextAppearance_AppCompat_Headline 0x0
+int style TextAppearance_AppCompat_Inverse 0x0
+int style TextAppearance_AppCompat_Large 0x0
+int style TextAppearance_AppCompat_Large_Inverse 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Medium 0x0
+int style TextAppearance_AppCompat_Medium_Inverse 0x0
+int style TextAppearance_AppCompat_Menu 0x0
+int style TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Small 0x0
+int style TextAppearance_AppCompat_Small_Inverse 0x0
+int style TextAppearance_AppCompat_Subhead 0x0
+int style TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style TextAppearance_AppCompat_Title 0x0
+int style TextAppearance_AppCompat_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Tooltip 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_Button 0x0
+int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Widget_Switch 0x0
+int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style TextAppearance_Compat_Notification 0x0
+int style TextAppearance_Compat_Notification_Info 0x0
+int style TextAppearance_Compat_Notification_Line2 0x0
+int style TextAppearance_Compat_Notification_Time 0x0
+int style TextAppearance_Compat_Notification_Title 0x0
+int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Theme 0x0
+int style ThemeOverlay_AppCompat 0x0
+int style ThemeOverlay_AppCompat_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dark 0x0
+int style ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style ThemeOverlay_AppCompat_DayNight 0x0
+int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dialog 0x0
+int style ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style ThemeOverlay_AppCompat_Light 0x0
+int style Theme_AppCompat 0x0
+int style Theme_AppCompat_CompactMenu 0x0
+int style Theme_AppCompat_DayNight 0x0
+int style Theme_AppCompat_DayNight_DarkActionBar 0x0
+int style Theme_AppCompat_DayNight_Dialog 0x0
+int style Theme_AppCompat_DayNight_DialogWhenLarge 0x0
+int style Theme_AppCompat_DayNight_Dialog_Alert 0x0
+int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x0
+int style Theme_AppCompat_DayNight_NoActionBar 0x0
+int style Theme_AppCompat_Dialog 0x0
+int style Theme_AppCompat_DialogWhenLarge 0x0
+int style Theme_AppCompat_Dialog_Alert 0x0
+int style Theme_AppCompat_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Empty 0x0
+int style Theme_AppCompat_Light 0x0
+int style Theme_AppCompat_Light_DarkActionBar 0x0
+int style Theme_AppCompat_Light_Dialog 0x0
+int style Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Light_NoActionBar 0x0
+int style Theme_AppCompat_NoActionBar 0x0
+int style Theme_AutofillInlineSuggestion 0x0
+int style Theme_Catalyst 0x0
+int style Theme_Catalyst_LogBox 0x0
+int style Theme_Catalyst_RedBox 0x0
+int style Theme_FullScreenDialog 0x0
+int style Theme_FullScreenDialogAnimatedFade 0x0
+int style Theme_FullScreenDialogAnimatedSlide 0x0
+int style Theme_ReactNative_AppCompat_Light 0x0
+int style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen 0x0
+int style Widget_AppCompat_ActionBar 0x0
+int style Widget_AppCompat_ActionBar_Solid 0x0
+int style Widget_AppCompat_ActionBar_TabBar 0x0
+int style Widget_AppCompat_ActionBar_TabText 0x0
+int style Widget_AppCompat_ActionBar_TabView 0x0
+int style Widget_AppCompat_ActionButton 0x0
+int style Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_ActionButton_Overflow 0x0
+int style Widget_AppCompat_ActionMode 0x0
+int style Widget_AppCompat_ActivityChooserView 0x0
+int style Widget_AppCompat_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Button 0x0
+int style Widget_AppCompat_ButtonBar 0x0
+int style Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Borderless 0x0
+int style Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Colored 0x0
+int style Widget_AppCompat_Button_Small 0x0
+int style Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Widget_AppCompat_CompoundButton_Switch 0x0
+int style Widget_AppCompat_DrawerArrowToggle 0x0
+int style Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_EditText 0x0
+int style Widget_AppCompat_ImageButton 0x0
+int style Widget_AppCompat_Light_ActionBar 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x0
+int style Widget_AppCompat_Light_ActionButton 0x0
+int style Widget_AppCompat_Light_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_Light_ActionButton_Overflow 0x0
+int style Widget_AppCompat_Light_ActionMode_Inverse 0x0
+int style Widget_AppCompat_Light_ActivityChooserView 0x0
+int style Widget_AppCompat_Light_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Light_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_Light_ListPopupWindow 0x0
+int style Widget_AppCompat_Light_ListView_DropDown 0x0
+int style Widget_AppCompat_Light_PopupMenu 0x0
+int style Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_Light_SearchView 0x0
+int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_ListMenuView 0x0
+int style Widget_AppCompat_ListPopupWindow 0x0
+int style Widget_AppCompat_ListView 0x0
+int style Widget_AppCompat_ListView_DropDown 0x0
+int style Widget_AppCompat_ListView_Menu 0x0
+int style Widget_AppCompat_PopupMenu 0x0
+int style Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_PopupWindow 0x0
+int style Widget_AppCompat_ProgressBar 0x0
+int style Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Widget_AppCompat_RatingBar 0x0
+int style Widget_AppCompat_RatingBar_Indicator 0x0
+int style Widget_AppCompat_RatingBar_Small 0x0
+int style Widget_AppCompat_SearchView 0x0
+int style Widget_AppCompat_SearchView_ActionBar 0x0
+int style Widget_AppCompat_SeekBar 0x0
+int style Widget_AppCompat_SeekBar_Discrete 0x0
+int style Widget_AppCompat_Spinner 0x0
+int style Widget_AppCompat_Spinner_DropDown 0x0
+int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_Spinner_Underlined 0x0
+int style Widget_AppCompat_TextView 0x0
+int style Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Widget_AppCompat_Toolbar 0x0
+int style Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style Widget_Autofill 0x0
+int style Widget_Autofill_InlineSuggestionChip 0x0
+int style Widget_Autofill_InlineSuggestionEndIconStyle 0x0
+int style Widget_Autofill_InlineSuggestionStartIconStyle 0x0
+int style Widget_Autofill_InlineSuggestionSubtitle 0x0
+int style Widget_Autofill_InlineSuggestionTitle 0x0
+int style Widget_Compat_NotificationActionContainer 0x0
+int style Widget_Compat_NotificationActionText 0x0
+int style Widget_Support_CoordinatorLayout 0x0
+int style redboxButton 0x0
+int[] styleable ActionBar { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionBar_background 0
+int styleable ActionBar_backgroundSplit 1
+int styleable ActionBar_backgroundStacked 2
+int styleable ActionBar_contentInsetEnd 3
+int styleable ActionBar_contentInsetEndWithActions 4
+int styleable ActionBar_contentInsetLeft 5
+int styleable ActionBar_contentInsetRight 6
+int styleable ActionBar_contentInsetStart 7
+int styleable ActionBar_contentInsetStartWithNavigation 8
+int styleable ActionBar_customNavigationLayout 9
+int styleable ActionBar_displayOptions 10
+int styleable ActionBar_divider 11
+int styleable ActionBar_elevation 12
+int styleable ActionBar_height 13
+int styleable ActionBar_hideOnContentScroll 14
+int styleable ActionBar_homeAsUpIndicator 15
+int styleable ActionBar_homeLayout 16
+int styleable ActionBar_icon 17
+int styleable ActionBar_indeterminateProgressStyle 18
+int styleable ActionBar_itemPadding 19
+int styleable ActionBar_logo 20
+int styleable ActionBar_navigationMode 21
+int styleable ActionBar_popupTheme 22
+int styleable ActionBar_progressBarPadding 23
+int styleable ActionBar_progressBarStyle 24
+int styleable ActionBar_subtitle 25
+int styleable ActionBar_subtitleTextStyle 26
+int styleable ActionBar_title 27
+int styleable ActionBar_titleTextStyle 28
+int[] styleable ActionBarLayout { 0x10100b3 }
+int styleable ActionBarLayout_android_layout_gravity 0
+int[] styleable ActionMenuItemView { 0x101013f }
+int styleable ActionMenuItemView_android_minWidth 0
+int[] styleable ActionMenuView {  }
+int[] styleable ActionMode { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionMode_background 0
+int styleable ActionMode_backgroundSplit 1
+int styleable ActionMode_closeItemLayout 2
+int styleable ActionMode_height 3
+int styleable ActionMode_subtitleTextStyle 4
+int styleable ActionMode_titleTextStyle 5
+int[] styleable ActivityChooserView { 0x0, 0x0 }
+int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
+int styleable ActivityChooserView_initialActivityCount 1
+int[] styleable AlertDialog { 0x10100f2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AlertDialog_android_layout 0
+int styleable AlertDialog_buttonIconDimen 1
+int styleable AlertDialog_buttonPanelSideLayout 2
+int styleable AlertDialog_listItemLayout 3
+int styleable AlertDialog_listLayout 4
+int styleable AlertDialog_multiChoiceItemLayout 5
+int styleable AlertDialog_showTitle 6
+int styleable AlertDialog_singleChoiceItemLayout 7
+int[] styleable AnimatedStateListDrawableCompat { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable AnimatedStateListDrawableCompat_android_constantSize 0
+int styleable AnimatedStateListDrawableCompat_android_dither 1
+int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 2
+int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 3
+int styleable AnimatedStateListDrawableCompat_android_variablePadding 4
+int styleable AnimatedStateListDrawableCompat_android_visible 5
+int[] styleable AnimatedStateListDrawableItem { 0x1010199, 0x10100d0 }
+int styleable AnimatedStateListDrawableItem_android_drawable 0
+int styleable AnimatedStateListDrawableItem_android_id 1
+int[] styleable AnimatedStateListDrawableTransition { 0x1010199, 0x101044a, 0x101044b, 0x1010449 }
+int styleable AnimatedStateListDrawableTransition_android_drawable 0
+int styleable AnimatedStateListDrawableTransition_android_fromId 1
+int styleable AnimatedStateListDrawableTransition_android_reversible 2
+int styleable AnimatedStateListDrawableTransition_android_toId 3
+int[] styleable AppCompatEmojiHelper {  }
+int[] styleable AppCompatImageView { 0x1010119, 0x0, 0x0, 0x0 }
+int styleable AppCompatImageView_android_src 0
+int styleable AppCompatImageView_srcCompat 1
+int styleable AppCompatImageView_tint 2
+int styleable AppCompatImageView_tintMode 3
+int[] styleable AppCompatSeekBar { 0x1010142, 0x0, 0x0, 0x0 }
+int styleable AppCompatSeekBar_android_thumb 0
+int styleable AppCompatSeekBar_tickMark 1
+int styleable AppCompatSeekBar_tickMarkTint 2
+int styleable AppCompatSeekBar_tickMarkTintMode 3
+int[] styleable AppCompatTextHelper { 0x101016e, 0x1010393, 0x101016f, 0x1010170, 0x1010392, 0x101016d, 0x1010034 }
+int styleable AppCompatTextHelper_android_drawableBottom 0
+int styleable AppCompatTextHelper_android_drawableEnd 1
+int styleable AppCompatTextHelper_android_drawableLeft 2
+int styleable AppCompatTextHelper_android_drawableRight 3
+int styleable AppCompatTextHelper_android_drawableStart 4
+int styleable AppCompatTextHelper_android_drawableTop 5
+int styleable AppCompatTextHelper_android_textAppearance 6
+int[] styleable AppCompatTextView { 0x1010034, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTextView_android_textAppearance 0
+int styleable AppCompatTextView_autoSizeMaxTextSize 1
+int styleable AppCompatTextView_autoSizeMinTextSize 2
+int styleable AppCompatTextView_autoSizePresetSizes 3
+int styleable AppCompatTextView_autoSizeStepGranularity 4
+int styleable AppCompatTextView_autoSizeTextType 5
+int styleable AppCompatTextView_drawableBottomCompat 6
+int styleable AppCompatTextView_drawableEndCompat 7
+int styleable AppCompatTextView_drawableLeftCompat 8
+int styleable AppCompatTextView_drawableRightCompat 9
+int styleable AppCompatTextView_drawableStartCompat 10
+int styleable AppCompatTextView_drawableTint 11
+int styleable AppCompatTextView_drawableTintMode 12
+int styleable AppCompatTextView_drawableTopCompat 13
+int styleable AppCompatTextView_emojiCompatEnabled 14
+int styleable AppCompatTextView_firstBaselineToTopHeight 15
+int styleable AppCompatTextView_fontFamily 16
+int styleable AppCompatTextView_fontVariationSettings 17
+int styleable AppCompatTextView_lastBaselineToBottomHeight 18
+int styleable AppCompatTextView_lineHeight 19
+int styleable AppCompatTextView_textAllCaps 20
+int styleable AppCompatTextView_textLocale 21
+int[] styleable AppCompatTheme { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10100ae, 0x1010057, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTheme_actionBarDivider 0
+int styleable AppCompatTheme_actionBarItemBackground 1
+int styleable AppCompatTheme_actionBarPopupTheme 2
+int styleable AppCompatTheme_actionBarSize 3
+int styleable AppCompatTheme_actionBarSplitStyle 4
+int styleable AppCompatTheme_actionBarStyle 5
+int styleable AppCompatTheme_actionBarTabBarStyle 6
+int styleable AppCompatTheme_actionBarTabStyle 7
+int styleable AppCompatTheme_actionBarTabTextStyle 8
+int styleable AppCompatTheme_actionBarTheme 9
+int styleable AppCompatTheme_actionBarWidgetTheme 10
+int styleable AppCompatTheme_actionButtonStyle 11
+int styleable AppCompatTheme_actionDropDownStyle 12
+int styleable AppCompatTheme_actionMenuTextAppearance 13
+int styleable AppCompatTheme_actionMenuTextColor 14
+int styleable AppCompatTheme_actionModeBackground 15
+int styleable AppCompatTheme_actionModeCloseButtonStyle 16
+int styleable AppCompatTheme_actionModeCloseContentDescription 17
+int styleable AppCompatTheme_actionModeCloseDrawable 18
+int styleable AppCompatTheme_actionModeCopyDrawable 19
+int styleable AppCompatTheme_actionModeCutDrawable 20
+int styleable AppCompatTheme_actionModeFindDrawable 21
+int styleable AppCompatTheme_actionModePasteDrawable 22
+int styleable AppCompatTheme_actionModePopupWindowStyle 23
+int styleable AppCompatTheme_actionModeSelectAllDrawable 24
+int styleable AppCompatTheme_actionModeShareDrawable 25
+int styleable AppCompatTheme_actionModeSplitBackground 26
+int styleable AppCompatTheme_actionModeStyle 27
+int styleable AppCompatTheme_actionModeTheme 28
+int styleable AppCompatTheme_actionModeWebSearchDrawable 29
+int styleable AppCompatTheme_actionOverflowButtonStyle 30
+int styleable AppCompatTheme_actionOverflowMenuStyle 31
+int styleable AppCompatTheme_activityChooserViewStyle 32
+int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
+int styleable AppCompatTheme_alertDialogCenterButtons 34
+int styleable AppCompatTheme_alertDialogStyle 35
+int styleable AppCompatTheme_alertDialogTheme 36
+int styleable AppCompatTheme_android_windowAnimationStyle 37
+int styleable AppCompatTheme_android_windowIsFloating 38
+int styleable AppCompatTheme_autoCompleteTextViewStyle 39
+int styleable AppCompatTheme_borderlessButtonStyle 40
+int styleable AppCompatTheme_buttonBarButtonStyle 41
+int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
+int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
+int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
+int styleable AppCompatTheme_buttonBarStyle 45
+int styleable AppCompatTheme_buttonStyle 46
+int styleable AppCompatTheme_buttonStyleSmall 47
+int styleable AppCompatTheme_checkboxStyle 48
+int styleable AppCompatTheme_checkedTextViewStyle 49
+int styleable AppCompatTheme_colorAccent 50
+int styleable AppCompatTheme_colorBackgroundFloating 51
+int styleable AppCompatTheme_colorButtonNormal 52
+int styleable AppCompatTheme_colorControlActivated 53
+int styleable AppCompatTheme_colorControlHighlight 54
+int styleable AppCompatTheme_colorControlNormal 55
+int styleable AppCompatTheme_colorError 56
+int styleable AppCompatTheme_colorPrimary 57
+int styleable AppCompatTheme_colorPrimaryDark 58
+int styleable AppCompatTheme_colorSwitchThumbNormal 59
+int styleable AppCompatTheme_controlBackground 60
+int styleable AppCompatTheme_dialogCornerRadius 61
+int styleable AppCompatTheme_dialogPreferredPadding 62
+int styleable AppCompatTheme_dialogTheme 63
+int styleable AppCompatTheme_dividerHorizontal 64
+int styleable AppCompatTheme_dividerVertical 65
+int styleable AppCompatTheme_dropDownListViewStyle 66
+int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
+int styleable AppCompatTheme_editTextBackground 68
+int styleable AppCompatTheme_editTextColor 69
+int styleable AppCompatTheme_editTextStyle 70
+int styleable AppCompatTheme_homeAsUpIndicator 71
+int styleable AppCompatTheme_imageButtonStyle 72
+int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
+int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
+int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
+int styleable AppCompatTheme_listDividerAlertDialog 76
+int styleable AppCompatTheme_listMenuViewStyle 77
+int styleable AppCompatTheme_listPopupWindowStyle 78
+int styleable AppCompatTheme_listPreferredItemHeight 79
+int styleable AppCompatTheme_listPreferredItemHeightLarge 80
+int styleable AppCompatTheme_listPreferredItemHeightSmall 81
+int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
+int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
+int styleable AppCompatTheme_listPreferredItemPaddingRight 84
+int styleable AppCompatTheme_listPreferredItemPaddingStart 85
+int styleable AppCompatTheme_panelBackground 86
+int styleable AppCompatTheme_panelMenuListTheme 87
+int styleable AppCompatTheme_panelMenuListWidth 88
+int styleable AppCompatTheme_popupMenuStyle 89
+int styleable AppCompatTheme_popupWindowStyle 90
+int styleable AppCompatTheme_radioButtonStyle 91
+int styleable AppCompatTheme_ratingBarStyle 92
+int styleable AppCompatTheme_ratingBarStyleIndicator 93
+int styleable AppCompatTheme_ratingBarStyleSmall 94
+int styleable AppCompatTheme_searchViewStyle 95
+int styleable AppCompatTheme_seekBarStyle 96
+int styleable AppCompatTheme_selectableItemBackground 97
+int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
+int styleable AppCompatTheme_spinnerDropDownItemStyle 99
+int styleable AppCompatTheme_spinnerStyle 100
+int styleable AppCompatTheme_switchStyle 101
+int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
+int styleable AppCompatTheme_textAppearanceListItem 103
+int styleable AppCompatTheme_textAppearanceListItemSecondary 104
+int styleable AppCompatTheme_textAppearanceListItemSmall 105
+int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
+int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
+int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
+int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
+int styleable AppCompatTheme_textColorAlertDialogListItem 110
+int styleable AppCompatTheme_textColorSearchUrl 111
+int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
+int styleable AppCompatTheme_toolbarStyle 113
+int styleable AppCompatTheme_tooltipForegroundColor 114
+int styleable AppCompatTheme_tooltipFrameBackground 115
+int styleable AppCompatTheme_viewInflaterClass 116
+int styleable AppCompatTheme_windowActionBar 117
+int styleable AppCompatTheme_windowActionBarOverlay 118
+int styleable AppCompatTheme_windowActionModeOverlay 119
+int styleable AppCompatTheme_windowFixedHeightMajor 120
+int styleable AppCompatTheme_windowFixedHeightMinor 121
+int styleable AppCompatTheme_windowFixedWidthMajor 122
+int styleable AppCompatTheme_windowFixedWidthMinor 123
+int styleable AppCompatTheme_windowMinWidthMajor 124
+int styleable AppCompatTheme_windowMinWidthMinor 125
+int styleable AppCompatTheme_windowNoTitle 126
+int[] styleable Autofill_InlineSuggestion { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionChip 0
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionEndIconStyle 1
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionStartIconStyle 2
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionSubtitle 3
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionTitle 4
+int styleable Autofill_InlineSuggestion_isAutofillInlineSuggestionTheme 5
+int[] styleable ButtonBarLayout { 0x0 }
+int styleable ButtonBarLayout_allowStacking 0
+int[] styleable Capability { 0x0, 0x0 }
+int styleable Capability_queryPatterns 0
+int styleable Capability_shortcutMatchRequired 1
+int[] styleable CheckedTextView { 0x1010108, 0x0, 0x0, 0x0 }
+int styleable CheckedTextView_android_checkMark 0
+int styleable CheckedTextView_checkMarkCompat 1
+int styleable CheckedTextView_checkMarkTint 2
+int styleable CheckedTextView_checkMarkTintMode 3
+int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5, 0x1010647, 0x0 }
+int styleable ColorStateListItem_alpha 0
+int styleable ColorStateListItem_android_alpha 1
+int styleable ColorStateListItem_android_color 2
+int styleable ColorStateListItem_android_lStar 3
+int styleable ColorStateListItem_lStar 4
+int[] styleable CompoundButton { 0x1010107, 0x0, 0x0, 0x0 }
+int styleable CompoundButton_android_button 0
+int styleable CompoundButton_buttonCompat 1
+int styleable CompoundButton_buttonTint 2
+int styleable CompoundButton_buttonTintMode 3
+int[] styleable CoordinatorLayout { 0x0, 0x0 }
+int styleable CoordinatorLayout_keylines 0
+int styleable CoordinatorLayout_statusBarBackground 1
+int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable CoordinatorLayout_Layout_android_layout_gravity 0
+int styleable CoordinatorLayout_Layout_layout_anchor 1
+int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
+int styleable CoordinatorLayout_Layout_layout_behavior 3
+int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
+int styleable CoordinatorLayout_Layout_layout_insetEdge 5
+int styleable CoordinatorLayout_Layout_layout_keyline 6
+int[] styleable DrawerArrowToggle { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable DrawerArrowToggle_arrowHeadLength 0
+int styleable DrawerArrowToggle_arrowShaftLength 1
+int styleable DrawerArrowToggle_barLength 2
+int styleable DrawerArrowToggle_color 3
+int styleable DrawerArrowToggle_drawableSize 4
+int styleable DrawerArrowToggle_gapBetweenBars 5
+int styleable DrawerArrowToggle_spinBars 6
+int styleable DrawerArrowToggle_thickness 7
+int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamily_fontProviderAuthority 0
+int styleable FontFamily_fontProviderCerts 1
+int styleable FontFamily_fontProviderFetchStrategy 2
+int styleable FontFamily_fontProviderFetchTimeout 3
+int styleable FontFamily_fontProviderPackage 4
+int styleable FontFamily_fontProviderQuery 5
+int styleable FontFamily_fontProviderSystemFontFamily 6
+int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamilyFont_android_font 0
+int styleable FontFamilyFont_android_fontStyle 1
+int styleable FontFamilyFont_android_fontVariationSettings 2
+int styleable FontFamilyFont_android_fontWeight 3
+int styleable FontFamilyFont_android_ttcIndex 4
+int styleable FontFamilyFont_font 5
+int styleable FontFamilyFont_fontStyle 6
+int styleable FontFamilyFont_fontVariationSettings 7
+int styleable FontFamilyFont_fontWeight 8
+int styleable FontFamilyFont_ttcIndex 9
+int[] styleable Fragment { 0x10100d0, 0x1010003, 0x10100d1 }
+int styleable Fragment_android_id 0
+int styleable Fragment_android_name 1
+int styleable Fragment_android_tag 2
+int[] styleable FragmentContainerView { 0x1010003, 0x10100d1 }
+int styleable FragmentContainerView_android_name 0
+int styleable FragmentContainerView_android_tag 1
+int[] styleable GenericDraweeHierarchy { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable GenericDraweeHierarchy_actualImageScaleType 0
+int styleable GenericDraweeHierarchy_backgroundImage 1
+int styleable GenericDraweeHierarchy_fadeDuration 2
+int styleable GenericDraweeHierarchy_failureImage 3
+int styleable GenericDraweeHierarchy_failureImageScaleType 4
+int styleable GenericDraweeHierarchy_overlayImage 5
+int styleable GenericDraweeHierarchy_placeholderImage 6
+int styleable GenericDraweeHierarchy_placeholderImageScaleType 7
+int styleable GenericDraweeHierarchy_pressedStateOverlayImage 8
+int styleable GenericDraweeHierarchy_progressBarAutoRotateInterval 9
+int styleable GenericDraweeHierarchy_progressBarImage 10
+int styleable GenericDraweeHierarchy_progressBarImageScaleType 11
+int styleable GenericDraweeHierarchy_retryImage 12
+int styleable GenericDraweeHierarchy_retryImageScaleType 13
+int styleable GenericDraweeHierarchy_roundAsCircle 14
+int styleable GenericDraweeHierarchy_roundBottomEnd 15
+int styleable GenericDraweeHierarchy_roundBottomLeft 16
+int styleable GenericDraweeHierarchy_roundBottomRight 17
+int styleable GenericDraweeHierarchy_roundBottomStart 18
+int styleable GenericDraweeHierarchy_roundTopEnd 19
+int styleable GenericDraweeHierarchy_roundTopLeft 20
+int styleable GenericDraweeHierarchy_roundTopRight 21
+int styleable GenericDraweeHierarchy_roundTopStart 22
+int styleable GenericDraweeHierarchy_roundWithOverlayColor 23
+int styleable GenericDraweeHierarchy_roundedCornerRadius 24
+int styleable GenericDraweeHierarchy_roundingBorderColor 25
+int styleable GenericDraweeHierarchy_roundingBorderPadding 26
+int styleable GenericDraweeHierarchy_roundingBorderWidth 27
+int styleable GenericDraweeHierarchy_viewAspectRatio 28
+int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
+int styleable GradientColor_android_centerColor 0
+int styleable GradientColor_android_centerX 1
+int styleable GradientColor_android_centerY 2
+int styleable GradientColor_android_endColor 3
+int styleable GradientColor_android_endX 4
+int styleable GradientColor_android_endY 5
+int styleable GradientColor_android_gradientRadius 6
+int styleable GradientColor_android_startColor 7
+int styleable GradientColor_android_startX 8
+int styleable GradientColor_android_startY 9
+int styleable GradientColor_android_tileMode 10
+int styleable GradientColor_android_type 11
+int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
+int styleable GradientColorItem_android_color 0
+int styleable GradientColorItem_android_offset 1
+int[] styleable LinearLayoutCompat { 0x1010126, 0x1010127, 0x10100af, 0x10100c4, 0x1010128, 0x0, 0x0, 0x0, 0x0 }
+int styleable LinearLayoutCompat_android_baselineAligned 0
+int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 1
+int styleable LinearLayoutCompat_android_gravity 2
+int styleable LinearLayoutCompat_android_orientation 3
+int styleable LinearLayoutCompat_android_weightSum 4
+int styleable LinearLayoutCompat_divider 5
+int styleable LinearLayoutCompat_dividerPadding 6
+int styleable LinearLayoutCompat_measureWithLargestChild 7
+int styleable LinearLayoutCompat_showDividers 8
+int[] styleable LinearLayoutCompat_Layout { 0x10100b3, 0x10100f5, 0x1010181, 0x10100f4 }
+int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
+int styleable LinearLayoutCompat_Layout_android_layout_height 1
+int styleable LinearLayoutCompat_Layout_android_layout_weight 2
+int styleable LinearLayoutCompat_Layout_android_layout_width 3
+int[] styleable ListPopupWindow { 0x10102ac, 0x10102ad }
+int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
+int styleable ListPopupWindow_android_dropDownVerticalOffset 1
+int[] styleable MenuGroup { 0x10101e0, 0x101000e, 0x10100d0, 0x10101de, 0x10101df, 0x1010194 }
+int styleable MenuGroup_android_checkableBehavior 0
+int styleable MenuGroup_android_enabled 1
+int styleable MenuGroup_android_id 2
+int styleable MenuGroup_android_menuCategory 3
+int styleable MenuGroup_android_orderInCategory 4
+int styleable MenuGroup_android_visible 5
+int[] styleable MenuItem { 0x0, 0x0, 0x0, 0x0, 0x10101e3, 0x10101e5, 0x1010106, 0x101000e, 0x1010002, 0x10100d0, 0x10101de, 0x10101e4, 0x101026f, 0x10101df, 0x10101e1, 0x10101e2, 0x1010194, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MenuItem_actionLayout 0
+int styleable MenuItem_actionProviderClass 1
+int styleable MenuItem_actionViewClass 2
+int styleable MenuItem_alphabeticModifiers 3
+int styleable MenuItem_android_alphabeticShortcut 4
+int styleable MenuItem_android_checkable 5
+int styleable MenuItem_android_checked 6
+int styleable MenuItem_android_enabled 7
+int styleable MenuItem_android_icon 8
+int styleable MenuItem_android_id 9
+int styleable MenuItem_android_menuCategory 10
+int styleable MenuItem_android_numericShortcut 11
+int styleable MenuItem_android_onClick 12
+int styleable MenuItem_android_orderInCategory 13
+int styleable MenuItem_android_title 14
+int styleable MenuItem_android_titleCondensed 15
+int styleable MenuItem_android_visible 16
+int styleable MenuItem_contentDescription 17
+int styleable MenuItem_iconTint 18
+int styleable MenuItem_iconTintMode 19
+int styleable MenuItem_numericModifiers 20
+int styleable MenuItem_showAsAction 21
+int styleable MenuItem_tooltipText 22
+int[] styleable MenuView { 0x101012f, 0x101012d, 0x1010130, 0x1010131, 0x101012c, 0x101012e, 0x10100ae, 0x0, 0x0 }
+int styleable MenuView_android_headerBackground 0
+int styleable MenuView_android_horizontalDivider 1
+int styleable MenuView_android_itemBackground 2
+int styleable MenuView_android_itemIconDisabledAlpha 3
+int styleable MenuView_android_itemTextAppearance 4
+int styleable MenuView_android_verticalDivider 5
+int styleable MenuView_android_windowAnimationStyle 6
+int styleable MenuView_preserveIconSpacing 7
+int styleable MenuView_subMenuArrow 8
+int[] styleable PopupWindow { 0x10102c9, 0x1010176, 0x0 }
+int styleable PopupWindow_android_popupAnimationStyle 0
+int styleable PopupWindow_android_popupBackground 1
+int styleable PopupWindow_overlapAnchor 2
+int[] styleable PopupWindowBackgroundState { 0x0 }
+int styleable PopupWindowBackgroundState_state_above_anchor 0
+int[] styleable RecycleListView { 0x0, 0x0 }
+int styleable RecycleListView_paddingBottomNoButtons 0
+int styleable RecycleListView_paddingTopNoTitle 1
+int[] styleable SearchView { 0x10100da, 0x1010264, 0x1010220, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SearchView_android_focusable 0
+int styleable SearchView_android_imeOptions 1
+int styleable SearchView_android_inputType 2
+int styleable SearchView_android_maxWidth 3
+int styleable SearchView_closeIcon 4
+int styleable SearchView_commitIcon 5
+int styleable SearchView_defaultQueryHint 6
+int styleable SearchView_goIcon 7
+int styleable SearchView_iconifiedByDefault 8
+int styleable SearchView_layout 9
+int styleable SearchView_queryBackground 10
+int styleable SearchView_queryHint 11
+int styleable SearchView_searchHintIcon 12
+int styleable SearchView_searchIcon 13
+int styleable SearchView_submitBackground 14
+int styleable SearchView_suggestionRowLayout 15
+int styleable SearchView_voiceIcon 16
+int[] styleable SimpleDraweeView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SimpleDraweeView_actualImageResource 0
+int styleable SimpleDraweeView_actualImageScaleType 1
+int styleable SimpleDraweeView_actualImageUri 2
+int styleable SimpleDraweeView_backgroundImage 3
+int styleable SimpleDraweeView_fadeDuration 4
+int styleable SimpleDraweeView_failureImage 5
+int styleable SimpleDraweeView_failureImageScaleType 6
+int styleable SimpleDraweeView_overlayImage 7
+int styleable SimpleDraweeView_placeholderImage 8
+int styleable SimpleDraweeView_placeholderImageScaleType 9
+int styleable SimpleDraweeView_pressedStateOverlayImage 10
+int styleable SimpleDraweeView_progressBarAutoRotateInterval 11
+int styleable SimpleDraweeView_progressBarImage 12
+int styleable SimpleDraweeView_progressBarImageScaleType 13
+int styleable SimpleDraweeView_retryImage 14
+int styleable SimpleDraweeView_retryImageScaleType 15
+int styleable SimpleDraweeView_roundAsCircle 16
+int styleable SimpleDraweeView_roundBottomEnd 17
+int styleable SimpleDraweeView_roundBottomLeft 18
+int styleable SimpleDraweeView_roundBottomRight 19
+int styleable SimpleDraweeView_roundBottomStart 20
+int styleable SimpleDraweeView_roundTopEnd 21
+int styleable SimpleDraweeView_roundTopLeft 22
+int styleable SimpleDraweeView_roundTopRight 23
+int styleable SimpleDraweeView_roundTopStart 24
+int styleable SimpleDraweeView_roundWithOverlayColor 25
+int styleable SimpleDraweeView_roundedCornerRadius 26
+int styleable SimpleDraweeView_roundingBorderColor 27
+int styleable SimpleDraweeView_roundingBorderPadding 28
+int styleable SimpleDraweeView_roundingBorderWidth 29
+int styleable SimpleDraweeView_viewAspectRatio 30
+int[] styleable Spinner { 0x1010262, 0x10100b2, 0x1010176, 0x101017b, 0x0 }
+int styleable Spinner_android_dropDownWidth 0
+int styleable Spinner_android_entries 1
+int styleable Spinner_android_popupBackground 2
+int styleable Spinner_android_prompt 3
+int styleable Spinner_popupTheme 4
+int[] styleable StateListDrawable { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable StateListDrawable_android_constantSize 0
+int styleable StateListDrawable_android_dither 1
+int styleable StateListDrawable_android_enterFadeDuration 2
+int styleable StateListDrawable_android_exitFadeDuration 3
+int styleable StateListDrawable_android_variablePadding 4
+int styleable StateListDrawable_android_visible 5
+int[] styleable StateListDrawableItem { 0x1010199 }
+int styleable StateListDrawableItem_android_drawable 0
+int[] styleable SwitchCompat { 0x1010125, 0x1010124, 0x1010142, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SwitchCompat_android_textOff 0
+int styleable SwitchCompat_android_textOn 1
+int styleable SwitchCompat_android_thumb 2
+int styleable SwitchCompat_showText 3
+int styleable SwitchCompat_splitTrack 4
+int styleable SwitchCompat_switchMinWidth 5
+int styleable SwitchCompat_switchPadding 6
+int styleable SwitchCompat_switchTextAppearance 7
+int styleable SwitchCompat_thumbTextPadding 8
+int styleable SwitchCompat_thumbTint 9
+int styleable SwitchCompat_thumbTintMode 10
+int styleable SwitchCompat_track 11
+int styleable SwitchCompat_trackTint 12
+int styleable SwitchCompat_trackTintMode 13
+int[] styleable TextAppearance { 0x10103ac, 0x1010161, 0x1010162, 0x1010163, 0x1010164, 0x1010098, 0x101009a, 0x101009b, 0x1010585, 0x1010095, 0x1010097, 0x1010096, 0x0, 0x0, 0x0, 0x0 }
+int styleable TextAppearance_android_fontFamily 0
+int styleable TextAppearance_android_shadowColor 1
+int styleable TextAppearance_android_shadowDx 2
+int styleable TextAppearance_android_shadowDy 3
+int styleable TextAppearance_android_shadowRadius 4
+int styleable TextAppearance_android_textColor 5
+int styleable TextAppearance_android_textColorHint 6
+int styleable TextAppearance_android_textColorLink 7
+int styleable TextAppearance_android_textFontWeight 8
+int styleable TextAppearance_android_textSize 9
+int styleable TextAppearance_android_textStyle 10
+int styleable TextAppearance_android_typeface 11
+int styleable TextAppearance_fontFamily 12
+int styleable TextAppearance_fontVariationSettings 13
+int styleable TextAppearance_textAllCaps 14
+int styleable TextAppearance_textLocale 15
+int[] styleable Toolbar { 0x10100af, 0x1010140, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Toolbar_android_gravity 0
+int styleable Toolbar_android_minHeight 1
+int styleable Toolbar_buttonGravity 2
+int styleable Toolbar_collapseContentDescription 3
+int styleable Toolbar_collapseIcon 4
+int styleable Toolbar_contentInsetEnd 5
+int styleable Toolbar_contentInsetEndWithActions 6
+int styleable Toolbar_contentInsetLeft 7
+int styleable Toolbar_contentInsetRight 8
+int styleable Toolbar_contentInsetStart 9
+int styleable Toolbar_contentInsetStartWithNavigation 10
+int styleable Toolbar_logo 11
+int styleable Toolbar_logoDescription 12
+int styleable Toolbar_maxButtonHeight 13
+int styleable Toolbar_menu 14
+int styleable Toolbar_navigationContentDescription 15
+int styleable Toolbar_navigationIcon 16
+int styleable Toolbar_popupTheme 17
+int styleable Toolbar_subtitle 18
+int styleable Toolbar_subtitleTextAppearance 19
+int styleable Toolbar_subtitleTextColor 20
+int styleable Toolbar_title 21
+int styleable Toolbar_titleMargin 22
+int styleable Toolbar_titleMarginBottom 23
+int styleable Toolbar_titleMarginEnd 24
+int styleable Toolbar_titleMarginStart 25
+int styleable Toolbar_titleMarginTop 26
+int styleable Toolbar_titleMargins 27
+int styleable Toolbar_titleTextAppearance 28
+int styleable Toolbar_titleTextColor 29
+int[] styleable View { 0x10100da, 0x1010000, 0x0, 0x0, 0x0 }
+int styleable View_android_focusable 0
+int styleable View_android_theme 1
+int styleable View_paddingEnd 2
+int styleable View_paddingStart 3
+int styleable View_theme 4
+int[] styleable ViewBackgroundHelper { 0x10100d4, 0x0, 0x0 }
+int styleable ViewBackgroundHelper_android_background 0
+int styleable ViewBackgroundHelper_backgroundTint 1
+int styleable ViewBackgroundHelper_backgroundTintMode 2
+int[] styleable ViewStubCompat { 0x10100d0, 0x10100f3, 0x10100f2 }
+int styleable ViewStubCompat_android_id 0
+int styleable ViewStubCompat_android_inflatedId 1
+int styleable ViewStubCompat_android_layout 2
+int xml rn_dev_preferences 0x0
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/mergeReactNative60DebugJniLibFolders/merger.xml b/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/mergeReactNative60DebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..af4698f
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/mergeReactNative60DebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/jniLibs"/></dataSet><dataSet config="reactNative60" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/debug/jniLibs"/></dataSet><dataSet config="reactNative60Debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60Debug/jniLibs"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60Debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/mergeReactNative60DebugShaders/merger.xml b/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/mergeReactNative60DebugShaders/merger.xml
new file mode 100644
index 0000000..c431da8
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/mergeReactNative60DebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/shaders"/></dataSet><dataSet config="reactNative60" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/debug/shaders"/></dataSet><dataSet config="reactNative60Debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60Debug/shaders"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60Debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/packageReactNative60DebugAssets/merger.xml b/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/packageReactNative60DebugAssets/merger.xml
new file mode 100644
index 0000000..c338f73
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/packageReactNative60DebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/assets"/><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/build/intermediates/shader_assets/reactNative60Debug/out"/></dataSet><dataSet config="reactNative60" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/debug/assets"/></dataSet><dataSet config="reactNative60Debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60Debug/assets"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60Debug/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/reactNative60Debug/packageReactNative60DebugResources/compile-file-map.properties b/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/reactNative60Debug/packageReactNative60DebugResources/compile-file-map.properties
new file mode 100644
index 0000000..1f8f9cc
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/reactNative60Debug/packageReactNative60DebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Fri Jul 11 22:15:46 IST 2025
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/reactNative60Debug/packageReactNative60DebugResources/merger.xml b/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/reactNative60Debug/packageReactNative60DebugResources/merger.xml
new file mode 100644
index 0000000..ea42c0f
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/incremental/reactNative60Debug/packageReactNative60DebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/res"/><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/build/generated/res/rs/reactNative60/debug"/><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/build/generated/res/resValues/reactNative60/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/res"/><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/build/generated/res/rs/reactNative60/debug"/><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/build/generated/res/resValues/reactNative60/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="reactNative60$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="reactNative60" generated-set="reactNative60$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="reactNative60Debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60Debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="reactNative60Debug" generated-set="reactNative60Debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60Debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60Debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="variant" generated-set="variant$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/reactNative60Debug/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/BuildConfig.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/BuildConfig.class
new file mode 100644
index 0000000..ea230a7
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/BuildConfig.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/Defs.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/Defs.class
new file mode 100644
index 0000000..f8567ba
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/Defs.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/NotificationManagerCompatFacade.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/NotificationManagerCompatFacade.class
new file mode 100644
index 0000000..f96db60
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/NotificationManagerCompatFacade.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/RNNotificationsModule.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/RNNotificationsModule.class
new file mode 100644
index 0000000..e1d4e9e
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/RNNotificationsModule.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/RNNotificationsPackage.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/RNNotificationsPackage.class
new file mode 100644
index 0000000..30ef1e2
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/RNNotificationsPackage.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLaunchHelper.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLaunchHelper.class
new file mode 100644
index 0000000..ae2cf36
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLaunchHelper.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLifecycleFacade$AppVisibilityListener.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLifecycleFacade$AppVisibilityListener.class
new file mode 100644
index 0000000..dd240e6
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLifecycleFacade$AppVisibilityListener.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLifecycleFacade.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLifecycleFacade.class
new file mode 100644
index 0000000..56e0de1
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLifecycleFacade.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLifecycleFacadeHolder.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLifecycleFacadeHolder.class
new file mode 100644
index 0000000..69c8244
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/AppLifecycleFacadeHolder.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/InitialNotificationHolder.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/InitialNotificationHolder.class
new file mode 100644
index 0000000..b8033b5
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/InitialNotificationHolder.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/JsIOHelper.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/JsIOHelper.class
new file mode 100644
index 0000000..486672b
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/JsIOHelper.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/NotificationIntentAdapter.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/NotificationIntentAdapter.class
new file mode 100644
index 0000000..4aab2f8
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/NotificationIntentAdapter.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/ProxyService.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/ProxyService.class
new file mode 100644
index 0000000..3875a56
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/ProxyService.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade$1.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade$1.class
new file mode 100644
index 0000000..487496d
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade$1.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade.class
new file mode 100644
index 0000000..c52b7d1
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/INotificationChannel.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/INotificationChannel.class
new file mode 100644
index 0000000..d53c72e
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/INotificationChannel.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/INotificationsApplication.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/INotificationsApplication.class
new file mode 100644
index 0000000..de664e6
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/INotificationsApplication.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/IPushNotification$InvalidNotificationException.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/IPushNotification$InvalidNotificationException.class
new file mode 100644
index 0000000..0713afc
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/IPushNotification$InvalidNotificationException.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/IPushNotification.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/IPushNotification.class
new file mode 100644
index 0000000..2784efc
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/IPushNotification.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/NotificationChannel.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/NotificationChannel.class
new file mode 100644
index 0000000..28f6f70
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/NotificationChannel.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/NotificationChannelProps.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/NotificationChannelProps.class
new file mode 100644
index 0000000..2c51e7b
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/NotificationChannelProps.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/PushNotification$1.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/PushNotification$1.class
new file mode 100644
index 0000000..e9a3e1f
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/PushNotification$1.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/PushNotification.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/PushNotification.class
new file mode 100644
index 0000000..b771f81
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/PushNotification.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/PushNotificationProps.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/PushNotificationProps.class
new file mode 100644
index 0000000..d78f186
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notification/PushNotificationProps.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notificationdrawer/INotificationsDrawerApplication.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notificationdrawer/INotificationsDrawerApplication.class
new file mode 100644
index 0000000..52fb019
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notificationdrawer/INotificationsDrawerApplication.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notificationdrawer/IPushNotificationsDrawer.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notificationdrawer/IPushNotificationsDrawer.class
new file mode 100644
index 0000000..da34d32
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notificationdrawer/IPushNotificationsDrawer.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notificationdrawer/PushNotificationsDrawer.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notificationdrawer/PushNotificationsDrawer.class
new file mode 100644
index 0000000..bddd40b
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/core/notificationdrawer/PushNotificationsDrawer.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/FcmInstanceIdListenerService.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/FcmInstanceIdListenerService.class
new file mode 100644
index 0000000..9a35665
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/FcmInstanceIdListenerService.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/FcmInstanceIdRefreshHandlerService.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/FcmInstanceIdRefreshHandlerService.class
new file mode 100644
index 0000000..aa58708
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/FcmInstanceIdRefreshHandlerService.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/FcmToken.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/FcmToken.class
new file mode 100644
index 0000000..6d3e771
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/FcmToken.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/IFcmToken.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/IFcmToken.class
new file mode 100644
index 0000000..573455b
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/IFcmToken.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/IFcmTokenListenerApplication.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/IFcmTokenListenerApplication.class
new file mode 100644
index 0000000..fee9efb
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/IFcmTokenListenerApplication.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/INotificationsFcmApplication.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/INotificationsFcmApplication.class
new file mode 100644
index 0000000..334b768
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/javac/reactNative60Debug/classes/com/wix/reactnativenotifications/fcm/INotificationsFcmApplication.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/local_only_symbol_list/reactNative60Debug/R-def.txt b/node_modules/react-native-notifications/lib/android/app/build/intermediates/local_only_symbol_list/reactNative60Debug/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/local_only_symbol_list/reactNative60Debug/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/manifest_merge_blame_file/reactNative60Debug/manifest-merger-blame-reactNative60-debug-report.txt b/node_modules/react-native-notifications/lib/android/app/build/intermediates/manifest_merge_blame_file/reactNative60Debug/manifest-merger-blame-reactNative60-debug-report.txt
new file mode 100644
index 0000000..1e2b080
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/manifest_merge_blame_file/reactNative60Debug/manifest-merger-blame-reactNative60-debug-report.txt
@@ -0,0 +1,41 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.wix.reactnativenotifications" >
+4
+5    <uses-sdk android:minSdkVersion="24" />
+5-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+6
+7    <application>
+7-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:5:5-25:19
+8
+9<!--          A proxy-service that gives the library an opportunity to do some work before launching/resuming the actual application task. -->
+10        <service android:name="com.wix.reactnativenotifications.core.ProxyService" />
+10-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:10:9-53
+10-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:10:18-51
+11        <service
+11-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:12:9-19:19
+12            android:name="com.wix.reactnativenotifications.fcm.FcmInstanceIdListenerService"
+12-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:13:13-61
+13            android:exported="true" >
+13-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:14:13-36
+14            <intent-filter>
+14-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:15:13-18:29
+15                <action android:name="com.google.firebase.MESSAGING_EVENT" />
+15-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:16:17-78
+15-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:16:25-75
+16                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
+16-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:17:17-80
+16-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:17:25-77
+17            </intent-filter>
+18        </service>
+19        <service
+19-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:21:9-24:72
+20            android:name="com.wix.reactnativenotifications.fcm.FcmInstanceIdRefreshHandlerService"
+20-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:22:13-67
+21            android:exported="false"
+21-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:23:13-37
+22            android:permission="android.permission.BIND_JOB_SERVICE" />
+22-->/Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:24:13-69
+23    </application>
+24
+25</manifest>
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/merged_manifest/reactNative60Debug/AndroidManifest.xml b/node_modules/react-native-notifications/lib/android/app/build/intermediates/merged_manifest/reactNative60Debug/AndroidManifest.xml
new file mode 100644
index 0000000..e541a6d
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/merged_manifest/reactNative60Debug/AndroidManifest.xml
@@ -0,0 +1,25 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.wix.reactnativenotifications" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <application>
+
+<!--          A proxy-service that gives the library an opportunity to do some work before launching/resuming the actual application task. -->
+        <service android:name="com.wix.reactnativenotifications.core.ProxyService" />
+        <service
+            android:name="com.wix.reactnativenotifications.fcm.FcmInstanceIdListenerService"
+            android:exported="true" >
+            <intent-filter>
+                <action android:name="com.google.firebase.MESSAGING_EVENT" />
+                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
+            </intent-filter>
+        </service>
+        <service
+            android:name="com.wix.reactnativenotifications.fcm.FcmInstanceIdRefreshHandlerService"
+            android:exported="false"
+            android:permission="android.permission.BIND_JOB_SERVICE" />
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/navigation_json/reactNative60Debug/navigation.json b/node_modules/react-native-notifications/lib/android/app/build/intermediates/navigation_json/reactNative60Debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/navigation_json/reactNative60Debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/packaged_manifests/reactNative60Debug/output-metadata.json b/node_modules/react-native-notifications/lib/android/app/build/intermediates/packaged_manifests/reactNative60Debug/output-metadata.json
new file mode 100644
index 0000000..f4191c6
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/packaged_manifests/reactNative60Debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.wix.reactnativenotifications",
+  "variantName": "reactNative60Debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/reactNative60Debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/BuildConfig.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/BuildConfig.class
new file mode 100644
index 0000000..ea230a7
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/BuildConfig.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/Defs.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/Defs.class
new file mode 100644
index 0000000..f8567ba
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/Defs.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/NotificationManagerCompatFacade.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/NotificationManagerCompatFacade.class
new file mode 100644
index 0000000..f96db60
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/NotificationManagerCompatFacade.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsModule.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsModule.class
new file mode 100644
index 0000000..e1d4e9e
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsModule.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsPackage.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsPackage.class
new file mode 100644
index 0000000..30ef1e2
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/RNNotificationsPackage.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLaunchHelper.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLaunchHelper.class
new file mode 100644
index 0000000..ae2cf36
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLaunchHelper.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade$AppVisibilityListener.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade$AppVisibilityListener.class
new file mode 100644
index 0000000..dd240e6
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade$AppVisibilityListener.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade.class
new file mode 100644
index 0000000..56e0de1
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacade.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacadeHolder.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacadeHolder.class
new file mode 100644
index 0000000..69c8244
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/AppLifecycleFacadeHolder.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/InitialNotificationHolder.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/InitialNotificationHolder.class
new file mode 100644
index 0000000..b8033b5
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/InitialNotificationHolder.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/JsIOHelper.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/JsIOHelper.class
new file mode 100644
index 0000000..486672b
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/JsIOHelper.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/NotificationIntentAdapter.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/NotificationIntentAdapter.class
new file mode 100644
index 0000000..4aab2f8
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/NotificationIntentAdapter.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/ProxyService.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/ProxyService.class
new file mode 100644
index 0000000..3875a56
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/ProxyService.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade$1.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade$1.class
new file mode 100644
index 0000000..487496d
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade$1.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade.class
new file mode 100644
index 0000000..c52b7d1
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/ReactAppLifecycleFacade.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationChannel.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationChannel.class
new file mode 100644
index 0000000..d53c72e
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationChannel.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationsApplication.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationsApplication.class
new file mode 100644
index 0000000..de664e6
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/INotificationsApplication.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification$InvalidNotificationException.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification$InvalidNotificationException.class
new file mode 100644
index 0000000..0713afc
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification$InvalidNotificationException.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification.class
new file mode 100644
index 0000000..2784efc
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/IPushNotification.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannel.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannel.class
new file mode 100644
index 0000000..28f6f70
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannel.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannelProps.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannelProps.class
new file mode 100644
index 0000000..2c51e7b
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/NotificationChannelProps.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification$1.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification$1.class
new file mode 100644
index 0000000..e9a3e1f
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification$1.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification.class
new file mode 100644
index 0000000..b771f81
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotification.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotificationProps.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotificationProps.class
new file mode 100644
index 0000000..d78f186
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notification/PushNotificationProps.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/INotificationsDrawerApplication.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/INotificationsDrawerApplication.class
new file mode 100644
index 0000000..52fb019
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/INotificationsDrawerApplication.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/IPushNotificationsDrawer.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/IPushNotificationsDrawer.class
new file mode 100644
index 0000000..da34d32
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/IPushNotificationsDrawer.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/PushNotificationsDrawer.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/PushNotificationsDrawer.class
new file mode 100644
index 0000000..bddd40b
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/core/notificationdrawer/PushNotificationsDrawer.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdListenerService.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdListenerService.class
new file mode 100644
index 0000000..9a35665
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdListenerService.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdRefreshHandlerService.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdRefreshHandlerService.class
new file mode 100644
index 0000000..aa58708
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmInstanceIdRefreshHandlerService.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmToken.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmToken.class
new file mode 100644
index 0000000..6d3e771
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/FcmToken.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmToken.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmToken.class
new file mode 100644
index 0000000..573455b
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmToken.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmTokenListenerApplication.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmTokenListenerApplication.class
new file mode 100644
index 0000000..fee9efb
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/IFcmTokenListenerApplication.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/INotificationsFcmApplication.class b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/INotificationsFcmApplication.class
new file mode 100644
index 0000000..334b768
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/intermediates/runtime_library_classes_dir/reactNative60Debug/com/wix/reactnativenotifications/fcm/INotificationsFcmApplication.class differ
diff --git a/node_modules/react-native-notifications/lib/android/app/build/intermediates/symbol_list_with_package_name/reactNative60Debug/package-aware-r.txt b/node_modules/react-native-notifications/lib/android/app/build/intermediates/symbol_list_with_package_name/reactNative60Debug/package-aware-r.txt
new file mode 100644
index 0000000..6a24c44
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/intermediates/symbol_list_with_package_name/reactNative60Debug/package-aware-r.txt
@@ -0,0 +1,1483 @@
+com.wix.reactnativenotifications
+anim abc_fade_in
+anim abc_fade_out
+anim abc_grow_fade_in_from_bottom
+anim abc_popup_enter
+anim abc_popup_exit
+anim abc_shrink_fade_out_from_bottom
+anim abc_slide_in_bottom
+anim abc_slide_in_top
+anim abc_slide_out_bottom
+anim abc_slide_out_top
+anim abc_tooltip_enter
+anim abc_tooltip_exit
+anim btn_checkbox_to_checked_box_inner_merged_animation
+anim btn_checkbox_to_checked_box_outer_merged_animation
+anim btn_checkbox_to_checked_icon_null_animation
+anim btn_checkbox_to_unchecked_box_inner_merged_animation
+anim btn_checkbox_to_unchecked_check_path_merged_animation
+anim btn_checkbox_to_unchecked_icon_null_animation
+anim btn_radio_to_off_mtrl_dot_group_animation
+anim btn_radio_to_off_mtrl_ring_outer_animation
+anim btn_radio_to_off_mtrl_ring_outer_path_animation
+anim btn_radio_to_on_mtrl_dot_group_animation
+anim btn_radio_to_on_mtrl_ring_outer_animation
+anim btn_radio_to_on_mtrl_ring_outer_path_animation
+anim catalyst_fade_in
+anim catalyst_fade_out
+anim catalyst_push_up_in
+anim catalyst_push_up_out
+anim catalyst_slide_down
+anim catalyst_slide_up
+anim fragment_fast_out_extra_slow_in
+animator fragment_close_enter
+animator fragment_close_exit
+animator fragment_fade_enter
+animator fragment_fade_exit
+animator fragment_open_enter
+animator fragment_open_exit
+attr actionBarDivider
+attr actionBarItemBackground
+attr actionBarPopupTheme
+attr actionBarSize
+attr actionBarSplitStyle
+attr actionBarStyle
+attr actionBarTabBarStyle
+attr actionBarTabStyle
+attr actionBarTabTextStyle
+attr actionBarTheme
+attr actionBarWidgetTheme
+attr actionButtonStyle
+attr actionDropDownStyle
+attr actionLayout
+attr actionMenuTextAppearance
+attr actionMenuTextColor
+attr actionModeBackground
+attr actionModeCloseButtonStyle
+attr actionModeCloseContentDescription
+attr actionModeCloseDrawable
+attr actionModeCopyDrawable
+attr actionModeCutDrawable
+attr actionModeFindDrawable
+attr actionModePasteDrawable
+attr actionModePopupWindowStyle
+attr actionModeSelectAllDrawable
+attr actionModeShareDrawable
+attr actionModeSplitBackground
+attr actionModeStyle
+attr actionModeTheme
+attr actionModeWebSearchDrawable
+attr actionOverflowButtonStyle
+attr actionOverflowMenuStyle
+attr actionProviderClass
+attr actionViewClass
+attr activityChooserViewStyle
+attr actualImageResource
+attr actualImageScaleType
+attr actualImageUri
+attr alertDialogButtonGroupStyle
+attr alertDialogCenterButtons
+attr alertDialogStyle
+attr alertDialogTheme
+attr allowStacking
+attr alpha
+attr alphabeticModifiers
+attr arrowHeadLength
+attr arrowShaftLength
+attr autoCompleteTextViewStyle
+attr autoSizeMaxTextSize
+attr autoSizeMinTextSize
+attr autoSizePresetSizes
+attr autoSizeStepGranularity
+attr autoSizeTextType
+attr autofillInlineSuggestionChip
+attr autofillInlineSuggestionEndIconStyle
+attr autofillInlineSuggestionStartIconStyle
+attr autofillInlineSuggestionSubtitle
+attr autofillInlineSuggestionTitle
+attr background
+attr backgroundImage
+attr backgroundSplit
+attr backgroundStacked
+attr backgroundTint
+attr backgroundTintMode
+attr barLength
+attr borderlessButtonStyle
+attr buttonBarButtonStyle
+attr buttonBarNegativeButtonStyle
+attr buttonBarNeutralButtonStyle
+attr buttonBarPositiveButtonStyle
+attr buttonBarStyle
+attr buttonCompat
+attr buttonGravity
+attr buttonIconDimen
+attr buttonPanelSideLayout
+attr buttonStyle
+attr buttonStyleSmall
+attr buttonTint
+attr buttonTintMode
+attr checkMarkCompat
+attr checkMarkTint
+attr checkMarkTintMode
+attr checkboxStyle
+attr checkedTextViewStyle
+attr closeIcon
+attr closeItemLayout
+attr collapseContentDescription
+attr collapseIcon
+attr color
+attr colorAccent
+attr colorBackgroundFloating
+attr colorButtonNormal
+attr colorControlActivated
+attr colorControlHighlight
+attr colorControlNormal
+attr colorError
+attr colorPrimary
+attr colorPrimaryDark
+attr colorSwitchThumbNormal
+attr commitIcon
+attr contentDescription
+attr contentInsetEnd
+attr contentInsetEndWithActions
+attr contentInsetLeft
+attr contentInsetRight
+attr contentInsetStart
+attr contentInsetStartWithNavigation
+attr controlBackground
+attr coordinatorLayoutStyle
+attr customNavigationLayout
+attr defaultQueryHint
+attr dialogCornerRadius
+attr dialogPreferredPadding
+attr dialogTheme
+attr displayOptions
+attr divider
+attr dividerHorizontal
+attr dividerPadding
+attr dividerVertical
+attr drawableBottomCompat
+attr drawableEndCompat
+attr drawableLeftCompat
+attr drawableRightCompat
+attr drawableSize
+attr drawableStartCompat
+attr drawableTint
+attr drawableTintMode
+attr drawableTopCompat
+attr drawerArrowStyle
+attr dropDownListViewStyle
+attr dropdownListPreferredItemHeight
+attr editTextBackground
+attr editTextColor
+attr editTextStyle
+attr elevation
+attr emojiCompatEnabled
+attr expandActivityOverflowButtonDrawable
+attr fadeDuration
+attr failureImage
+attr failureImageScaleType
+attr firstBaselineToTopHeight
+attr font
+attr fontFamily
+attr fontProviderAuthority
+attr fontProviderCerts
+attr fontProviderFetchStrategy
+attr fontProviderFetchTimeout
+attr fontProviderPackage
+attr fontProviderQuery
+attr fontProviderSystemFontFamily
+attr fontStyle
+attr fontVariationSettings
+attr fontWeight
+attr gapBetweenBars
+attr goIcon
+attr height
+attr hideOnContentScroll
+attr homeAsUpIndicator
+attr homeLayout
+attr icon
+attr iconTint
+attr iconTintMode
+attr iconifiedByDefault
+attr imageButtonStyle
+attr indeterminateProgressStyle
+attr initialActivityCount
+attr isAutofillInlineSuggestionTheme
+attr isLightTheme
+attr itemPadding
+attr keylines
+attr lStar
+attr lastBaselineToBottomHeight
+attr layout
+attr layout_anchor
+attr layout_anchorGravity
+attr layout_behavior
+attr layout_dodgeInsetEdges
+attr layout_insetEdge
+attr layout_keyline
+attr lineHeight
+attr listChoiceBackgroundIndicator
+attr listChoiceIndicatorMultipleAnimated
+attr listChoiceIndicatorSingleAnimated
+attr listDividerAlertDialog
+attr listItemLayout
+attr listLayout
+attr listMenuViewStyle
+attr listPopupWindowStyle
+attr listPreferredItemHeight
+attr listPreferredItemHeightLarge
+attr listPreferredItemHeightSmall
+attr listPreferredItemPaddingEnd
+attr listPreferredItemPaddingLeft
+attr listPreferredItemPaddingRight
+attr listPreferredItemPaddingStart
+attr logo
+attr logoDescription
+attr maxButtonHeight
+attr measureWithLargestChild
+attr menu
+attr multiChoiceItemLayout
+attr navigationContentDescription
+attr navigationIcon
+attr navigationMode
+attr nestedScrollViewStyle
+attr numericModifiers
+attr overlapAnchor
+attr overlayImage
+attr paddingBottomNoButtons
+attr paddingEnd
+attr paddingStart
+attr paddingTopNoTitle
+attr panelBackground
+attr panelMenuListTheme
+attr panelMenuListWidth
+attr placeholderImage
+attr placeholderImageScaleType
+attr popupMenuStyle
+attr popupTheme
+attr popupWindowStyle
+attr preserveIconSpacing
+attr pressedStateOverlayImage
+attr progressBarAutoRotateInterval
+attr progressBarImage
+attr progressBarImageScaleType
+attr progressBarPadding
+attr progressBarStyle
+attr queryBackground
+attr queryHint
+attr queryPatterns
+attr radioButtonStyle
+attr ratingBarStyle
+attr ratingBarStyleIndicator
+attr ratingBarStyleSmall
+attr retryImage
+attr retryImageScaleType
+attr roundAsCircle
+attr roundBottomEnd
+attr roundBottomLeft
+attr roundBottomRight
+attr roundBottomStart
+attr roundTopEnd
+attr roundTopLeft
+attr roundTopRight
+attr roundTopStart
+attr roundWithOverlayColor
+attr roundedCornerRadius
+attr roundingBorderColor
+attr roundingBorderPadding
+attr roundingBorderWidth
+attr searchHintIcon
+attr searchIcon
+attr searchViewStyle
+attr seekBarStyle
+attr selectableItemBackground
+attr selectableItemBackgroundBorderless
+attr shortcutMatchRequired
+attr showAsAction
+attr showDividers
+attr showText
+attr showTitle
+attr singleChoiceItemLayout
+attr spinBars
+attr spinnerDropDownItemStyle
+attr spinnerStyle
+attr splitTrack
+attr srcCompat
+attr state_above_anchor
+attr statusBarBackground
+attr subMenuArrow
+attr submitBackground
+attr subtitle
+attr subtitleTextAppearance
+attr subtitleTextColor
+attr subtitleTextStyle
+attr suggestionRowLayout
+attr switchMinWidth
+attr switchPadding
+attr switchStyle
+attr switchTextAppearance
+attr textAllCaps
+attr textAppearanceLargePopupMenu
+attr textAppearanceListItem
+attr textAppearanceListItemSecondary
+attr textAppearanceListItemSmall
+attr textAppearancePopupMenuHeader
+attr textAppearanceSearchResultSubtitle
+attr textAppearanceSearchResultTitle
+attr textAppearanceSmallPopupMenu
+attr textColorAlertDialogListItem
+attr textColorSearchUrl
+attr textLocale
+attr theme
+attr thickness
+attr thumbTextPadding
+attr thumbTint
+attr thumbTintMode
+attr tickMark
+attr tickMarkTint
+attr tickMarkTintMode
+attr tint
+attr tintMode
+attr title
+attr titleMargin
+attr titleMarginBottom
+attr titleMarginEnd
+attr titleMarginStart
+attr titleMarginTop
+attr titleMargins
+attr titleTextAppearance
+attr titleTextColor
+attr titleTextStyle
+attr toolbarNavigationButtonStyle
+attr toolbarStyle
+attr tooltipForegroundColor
+attr tooltipFrameBackground
+attr tooltipText
+attr track
+attr trackTint
+attr trackTintMode
+attr ttcIndex
+attr viewAspectRatio
+attr viewInflaterClass
+attr voiceIcon
+attr windowActionBar
+attr windowActionBarOverlay
+attr windowActionModeOverlay
+attr windowFixedHeightMajor
+attr windowFixedHeightMinor
+attr windowFixedWidthMajor
+attr windowFixedWidthMinor
+attr windowMinWidthMajor
+attr windowMinWidthMinor
+attr windowNoTitle
+bool abc_action_bar_embed_tabs
+bool abc_config_actionMenuItemAllCaps
+color abc_background_cache_hint_selector_material_dark
+color abc_background_cache_hint_selector_material_light
+color abc_btn_colored_borderless_text_material
+color abc_btn_colored_text_material
+color abc_color_highlight_material
+color abc_decor_view_status_guard
+color abc_decor_view_status_guard_light
+color abc_hint_foreground_material_dark
+color abc_hint_foreground_material_light
+color abc_primary_text_disable_only_material_dark
+color abc_primary_text_disable_only_material_light
+color abc_primary_text_material_dark
+color abc_primary_text_material_light
+color abc_search_url_text
+color abc_search_url_text_normal
+color abc_search_url_text_pressed
+color abc_search_url_text_selected
+color abc_secondary_text_material_dark
+color abc_secondary_text_material_light
+color abc_tint_btn_checkable
+color abc_tint_default
+color abc_tint_edittext
+color abc_tint_seek_thumb
+color abc_tint_spinner
+color abc_tint_switch_track
+color accent_material_dark
+color accent_material_light
+color androidx_core_ripple_material_light
+color androidx_core_secondary_text_default_material_light
+color background_floating_material_dark
+color background_floating_material_light
+color background_material_dark
+color background_material_light
+color bright_foreground_disabled_material_dark
+color bright_foreground_disabled_material_light
+color bright_foreground_inverse_material_dark
+color bright_foreground_inverse_material_light
+color bright_foreground_material_dark
+color bright_foreground_material_light
+color button_material_dark
+color button_material_light
+color call_notification_answer_color
+color call_notification_decline_color
+color catalyst_logbox_background
+color catalyst_redbox_background
+color dim_foreground_disabled_material_dark
+color dim_foreground_disabled_material_light
+color dim_foreground_material_dark
+color dim_foreground_material_light
+color error_color_material_dark
+color error_color_material_light
+color foreground_material_dark
+color foreground_material_light
+color highlighted_text_material_dark
+color highlighted_text_material_light
+color material_blue_grey_800
+color material_blue_grey_900
+color material_blue_grey_950
+color material_deep_teal_200
+color material_deep_teal_500
+color material_grey_100
+color material_grey_300
+color material_grey_50
+color material_grey_600
+color material_grey_800
+color material_grey_850
+color material_grey_900
+color notification_action_color_filter
+color notification_icon_bg_color
+color primary_dark_material_dark
+color primary_dark_material_light
+color primary_material_dark
+color primary_material_light
+color primary_text_default_material_dark
+color primary_text_default_material_light
+color primary_text_disabled_material_dark
+color primary_text_disabled_material_light
+color ripple_material_dark
+color ripple_material_light
+color secondary_text_default_material_dark
+color secondary_text_default_material_light
+color secondary_text_disabled_material_dark
+color secondary_text_disabled_material_light
+color switch_thumb_disabled_material_dark
+color switch_thumb_disabled_material_light
+color switch_thumb_material_dark
+color switch_thumb_material_light
+color switch_thumb_normal_material_dark
+color switch_thumb_normal_material_light
+color tooltip_background_dark
+color tooltip_background_light
+dimen abc_action_bar_content_inset_material
+dimen abc_action_bar_content_inset_with_nav
+dimen abc_action_bar_default_height_material
+dimen abc_action_bar_default_padding_end_material
+dimen abc_action_bar_default_padding_start_material
+dimen abc_action_bar_elevation_material
+dimen abc_action_bar_icon_vertical_padding_material
+dimen abc_action_bar_overflow_padding_end_material
+dimen abc_action_bar_overflow_padding_start_material
+dimen abc_action_bar_stacked_max_height
+dimen abc_action_bar_stacked_tab_max_width
+dimen abc_action_bar_subtitle_bottom_margin_material
+dimen abc_action_bar_subtitle_top_margin_material
+dimen abc_action_button_min_height_material
+dimen abc_action_button_min_width_material
+dimen abc_action_button_min_width_overflow_material
+dimen abc_alert_dialog_button_bar_height
+dimen abc_alert_dialog_button_dimen
+dimen abc_button_inset_horizontal_material
+dimen abc_button_inset_vertical_material
+dimen abc_button_padding_horizontal_material
+dimen abc_button_padding_vertical_material
+dimen abc_cascading_menus_min_smallest_width
+dimen abc_config_prefDialogWidth
+dimen abc_control_corner_material
+dimen abc_control_inset_material
+dimen abc_control_padding_material
+dimen abc_dialog_corner_radius_material
+dimen abc_dialog_fixed_height_major
+dimen abc_dialog_fixed_height_minor
+dimen abc_dialog_fixed_width_major
+dimen abc_dialog_fixed_width_minor
+dimen abc_dialog_list_padding_bottom_no_buttons
+dimen abc_dialog_list_padding_top_no_title
+dimen abc_dialog_min_width_major
+dimen abc_dialog_min_width_minor
+dimen abc_dialog_padding_material
+dimen abc_dialog_padding_top_material
+dimen abc_dialog_title_divider_material
+dimen abc_disabled_alpha_material_dark
+dimen abc_disabled_alpha_material_light
+dimen abc_dropdownitem_icon_width
+dimen abc_dropdownitem_text_padding_left
+dimen abc_dropdownitem_text_padding_right
+dimen abc_edit_text_inset_bottom_material
+dimen abc_edit_text_inset_horizontal_material
+dimen abc_edit_text_inset_top_material
+dimen abc_floating_window_z
+dimen abc_list_item_height_large_material
+dimen abc_list_item_height_material
+dimen abc_list_item_height_small_material
+dimen abc_list_item_padding_horizontal_material
+dimen abc_panel_menu_list_width
+dimen abc_progress_bar_height_material
+dimen abc_search_view_preferred_height
+dimen abc_search_view_preferred_width
+dimen abc_seekbar_track_background_height_material
+dimen abc_seekbar_track_progress_height_material
+dimen abc_select_dialog_padding_start_material
+dimen abc_star_big
+dimen abc_star_medium
+dimen abc_star_small
+dimen abc_switch_padding
+dimen abc_text_size_body_1_material
+dimen abc_text_size_body_2_material
+dimen abc_text_size_button_material
+dimen abc_text_size_caption_material
+dimen abc_text_size_display_1_material
+dimen abc_text_size_display_2_material
+dimen abc_text_size_display_3_material
+dimen abc_text_size_display_4_material
+dimen abc_text_size_headline_material
+dimen abc_text_size_large_material
+dimen abc_text_size_medium_material
+dimen abc_text_size_menu_header_material
+dimen abc_text_size_menu_material
+dimen abc_text_size_small_material
+dimen abc_text_size_subhead_material
+dimen abc_text_size_subtitle_material_toolbar
+dimen abc_text_size_title_material
+dimen abc_text_size_title_material_toolbar
+dimen autofill_inline_suggestion_icon_size
+dimen compat_button_inset_horizontal_material
+dimen compat_button_inset_vertical_material
+dimen compat_button_padding_horizontal_material
+dimen compat_button_padding_vertical_material
+dimen compat_control_corner_material
+dimen compat_notification_large_icon_max_height
+dimen compat_notification_large_icon_max_width
+dimen disabled_alpha_material_dark
+dimen disabled_alpha_material_light
+dimen highlight_alpha_material_colored
+dimen highlight_alpha_material_dark
+dimen highlight_alpha_material_light
+dimen hint_alpha_material_dark
+dimen hint_alpha_material_light
+dimen hint_pressed_alpha_material_dark
+dimen hint_pressed_alpha_material_light
+dimen notification_action_icon_size
+dimen notification_action_text_size
+dimen notification_big_circle_margin
+dimen notification_content_margin_start
+dimen notification_large_icon_height
+dimen notification_large_icon_width
+dimen notification_main_column_padding_top
+dimen notification_media_narrow_margin
+dimen notification_right_icon_size
+dimen notification_right_side_padding_top
+dimen notification_small_icon_background_padding
+dimen notification_small_icon_size_as_large
+dimen notification_subtext_size
+dimen notification_top_pad
+dimen notification_top_pad_large_text
+dimen tooltip_corner_radius
+dimen tooltip_horizontal_padding
+dimen tooltip_margin
+dimen tooltip_precise_anchor_extra_offset
+dimen tooltip_precise_anchor_threshold
+dimen tooltip_vertical_padding
+dimen tooltip_y_offset_non_touch
+dimen tooltip_y_offset_touch
+drawable abc_ab_share_pack_mtrl_alpha
+drawable abc_action_bar_item_background_material
+drawable abc_btn_borderless_material
+drawable abc_btn_check_material
+drawable abc_btn_check_material_anim
+drawable abc_btn_check_to_on_mtrl_000
+drawable abc_btn_check_to_on_mtrl_015
+drawable abc_btn_colored_material
+drawable abc_btn_default_mtrl_shape
+drawable abc_btn_radio_material
+drawable abc_btn_radio_material_anim
+drawable abc_btn_radio_to_on_mtrl_000
+drawable abc_btn_radio_to_on_mtrl_015
+drawable abc_btn_switch_to_on_mtrl_00001
+drawable abc_btn_switch_to_on_mtrl_00012
+drawable abc_cab_background_internal_bg
+drawable abc_cab_background_top_material
+drawable abc_cab_background_top_mtrl_alpha
+drawable abc_control_background_material
+drawable abc_dialog_material_background
+drawable abc_edit_text_material
+drawable abc_ic_ab_back_material
+drawable abc_ic_arrow_drop_right_black_24dp
+drawable abc_ic_clear_material
+drawable abc_ic_commit_search_api_mtrl_alpha
+drawable abc_ic_go_search_api_material
+drawable abc_ic_menu_copy_mtrl_am_alpha
+drawable abc_ic_menu_cut_mtrl_alpha
+drawable abc_ic_menu_overflow_material
+drawable abc_ic_menu_paste_mtrl_am_alpha
+drawable abc_ic_menu_selectall_mtrl_alpha
+drawable abc_ic_menu_share_mtrl_alpha
+drawable abc_ic_search_api_material
+drawable abc_ic_voice_search_api_material
+drawable abc_item_background_holo_dark
+drawable abc_item_background_holo_light
+drawable abc_list_divider_material
+drawable abc_list_divider_mtrl_alpha
+drawable abc_list_focused_holo
+drawable abc_list_longpressed_holo
+drawable abc_list_pressed_holo_dark
+drawable abc_list_pressed_holo_light
+drawable abc_list_selector_background_transition_holo_dark
+drawable abc_list_selector_background_transition_holo_light
+drawable abc_list_selector_disabled_holo_dark
+drawable abc_list_selector_disabled_holo_light
+drawable abc_list_selector_holo_dark
+drawable abc_list_selector_holo_light
+drawable abc_menu_hardkey_panel_mtrl_mult
+drawable abc_popup_background_mtrl_mult
+drawable abc_ratingbar_indicator_material
+drawable abc_ratingbar_material
+drawable abc_ratingbar_small_material
+drawable abc_scrubber_control_off_mtrl_alpha
+drawable abc_scrubber_control_to_pressed_mtrl_000
+drawable abc_scrubber_control_to_pressed_mtrl_005
+drawable abc_scrubber_primary_mtrl_alpha
+drawable abc_scrubber_track_mtrl_alpha
+drawable abc_seekbar_thumb_material
+drawable abc_seekbar_tick_mark_material
+drawable abc_seekbar_track_material
+drawable abc_spinner_mtrl_am_alpha
+drawable abc_spinner_textfield_background_material
+drawable abc_star_black_48dp
+drawable abc_star_half_black_48dp
+drawable abc_switch_thumb_material
+drawable abc_switch_track_mtrl_alpha
+drawable abc_tab_indicator_material
+drawable abc_tab_indicator_mtrl_alpha
+drawable abc_text_cursor_material
+drawable abc_text_select_handle_left_mtrl
+drawable abc_text_select_handle_middle_mtrl
+drawable abc_text_select_handle_right_mtrl
+drawable abc_textfield_activated_mtrl_alpha
+drawable abc_textfield_default_mtrl_alpha
+drawable abc_textfield_search_activated_mtrl_alpha
+drawable abc_textfield_search_default_mtrl_alpha
+drawable abc_textfield_search_material
+drawable abc_vector_test
+drawable autofill_inline_suggestion_chip_background
+drawable btn_checkbox_checked_mtrl
+drawable btn_checkbox_checked_to_unchecked_mtrl_animation
+drawable btn_checkbox_unchecked_mtrl
+drawable btn_checkbox_unchecked_to_checked_mtrl_animation
+drawable btn_radio_off_mtrl
+drawable btn_radio_off_to_on_mtrl_animation
+drawable btn_radio_on_mtrl
+drawable btn_radio_on_to_off_mtrl_animation
+drawable ic_call_answer
+drawable ic_call_answer_low
+drawable ic_call_answer_video
+drawable ic_call_answer_video_low
+drawable ic_call_decline
+drawable ic_call_decline_low
+drawable notification_action_background
+drawable notification_bg
+drawable notification_bg_low
+drawable notification_bg_low_normal
+drawable notification_bg_low_pressed
+drawable notification_bg_normal
+drawable notification_bg_normal_pressed
+drawable notification_icon_background
+drawable notification_template_icon_bg
+drawable notification_template_icon_low_bg
+drawable notification_tile_bg
+drawable notify_panel_notification_icon_bg
+drawable redbox_top_border_background
+drawable test_level_drawable
+drawable tooltip_frame_dark
+drawable tooltip_frame_light
+id accessibility_action_clickable_span
+id accessibility_actions
+id accessibility_collection
+id accessibility_collection_item
+id accessibility_custom_action_0
+id accessibility_custom_action_1
+id accessibility_custom_action_10
+id accessibility_custom_action_11
+id accessibility_custom_action_12
+id accessibility_custom_action_13
+id accessibility_custom_action_14
+id accessibility_custom_action_15
+id accessibility_custom_action_16
+id accessibility_custom_action_17
+id accessibility_custom_action_18
+id accessibility_custom_action_19
+id accessibility_custom_action_2
+id accessibility_custom_action_20
+id accessibility_custom_action_21
+id accessibility_custom_action_22
+id accessibility_custom_action_23
+id accessibility_custom_action_24
+id accessibility_custom_action_25
+id accessibility_custom_action_26
+id accessibility_custom_action_27
+id accessibility_custom_action_28
+id accessibility_custom_action_29
+id accessibility_custom_action_3
+id accessibility_custom_action_30
+id accessibility_custom_action_31
+id accessibility_custom_action_4
+id accessibility_custom_action_5
+id accessibility_custom_action_6
+id accessibility_custom_action_7
+id accessibility_custom_action_8
+id accessibility_custom_action_9
+id accessibility_hint
+id accessibility_label
+id accessibility_links
+id accessibility_role
+id accessibility_state
+id accessibility_state_expanded
+id accessibility_value
+id action_bar
+id action_bar_activity_content
+id action_bar_container
+id action_bar_root
+id action_bar_spinner
+id action_bar_subtitle
+id action_bar_title
+id action_container
+id action_context_bar
+id action_divider
+id action_image
+id action_menu_divider
+id action_menu_presenter
+id action_mode_bar
+id action_mode_bar_stub
+id action_mode_close_button
+id action_text
+id actions
+id activity_chooser_view_content
+id add
+id alertTitle
+id async
+id autofill_inline_suggestion_end_icon
+id autofill_inline_suggestion_start_icon
+id autofill_inline_suggestion_subtitle
+id autofill_inline_suggestion_title
+id blocking
+id bottom
+id buttonPanel
+id catalyst_redbox_title
+id center
+id centerCrop
+id centerInside
+id checkbox
+id checked
+id chronometer
+id content
+id contentPanel
+id custom
+id customPanel
+id decor_content_parent
+id default_activity_button
+id dialog_button
+id edit_query
+id end
+id expand_activities_button
+id expanded_menu
+id fitBottomStart
+id fitCenter
+id fitEnd
+id fitStart
+id fitXY
+id focusCrop
+id forever
+id fps_text
+id fragment_container_view_tag
+id group_divider
+id home
+id icon
+id icon_group
+id image
+id info
+id italic
+id item1
+id item2
+id item3
+id item4
+id labelled_by
+id left
+id line1
+id line3
+id listMode
+id list_item
+id message
+id multiply
+id none
+id normal
+id notification_background
+id notification_main_column
+id notification_main_column_container
+id off
+id on
+id parentPanel
+id pointer_events
+id progress_circular
+id progress_horizontal
+id radio
+id react_test_id
+id right
+id right_icon
+id right_side
+id rn_frame_file
+id rn_frame_method
+id rn_redbox_dismiss_button
+id rn_redbox_line_separator
+id rn_redbox_loading_indicator
+id rn_redbox_reload_button
+id rn_redbox_report_button
+id rn_redbox_report_label
+id rn_redbox_stack
+id screen
+id scrollIndicatorDown
+id scrollIndicatorUp
+id scrollView
+id search_badge
+id search_bar
+id search_button
+id search_close_btn
+id search_edit_frame
+id search_go_btn
+id search_mag_icon
+id search_plate
+id search_src_text
+id search_voice_btn
+id select_dialog_listview
+id shortcut
+id spacer
+id special_effects_controller_view_tag
+id split_action_bar
+id src_atop
+id src_in
+id src_over
+id start
+id submenuarrow
+id submit_area
+id tabMode
+id tag_accessibility_actions
+id tag_accessibility_clickable_spans
+id tag_accessibility_heading
+id tag_accessibility_pane_title
+id tag_on_apply_window_listener
+id tag_on_receive_content_listener
+id tag_on_receive_content_mime_types
+id tag_screen_reader_focusable
+id tag_state_description
+id tag_transition_group
+id tag_unhandled_key_event_manager
+id tag_unhandled_key_listeners
+id tag_window_insets_animation_callback
+id text
+id text2
+id textSpacerNoButtons
+id textSpacerNoTitle
+id time
+id title
+id titleDividerNoCustom
+id title_template
+id top
+id topPanel
+id unchecked
+id uniform
+id up
+id view_tag_instance_handle
+id view_tag_native_id
+id view_tree_lifecycle_owner
+id view_tree_saved_state_registry_owner
+id view_tree_view_model_store_owner
+id visible_removing_fragment_view_tag
+id wrap_content
+integer abc_config_activityDefaultDur
+integer abc_config_activityShortDur
+integer cancel_button_image_alpha
+integer config_tooltipAnimTime
+integer google_play_services_version
+integer react_native_dev_server_port
+integer react_native_inspector_proxy_port
+integer status_bar_notification_info_maxnum
+interpolator btn_checkbox_checked_mtrl_animation_interpolator_0
+interpolator btn_checkbox_checked_mtrl_animation_interpolator_1
+interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0
+interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1
+interpolator btn_radio_to_off_mtrl_animation_interpolator_0
+interpolator btn_radio_to_on_mtrl_animation_interpolator_0
+interpolator fast_out_slow_in
+layout abc_action_bar_title_item
+layout abc_action_bar_up_container
+layout abc_action_menu_item_layout
+layout abc_action_menu_layout
+layout abc_action_mode_bar
+layout abc_action_mode_close_item_material
+layout abc_activity_chooser_view
+layout abc_activity_chooser_view_list_item
+layout abc_alert_dialog_button_bar_material
+layout abc_alert_dialog_material
+layout abc_alert_dialog_title_material
+layout abc_cascading_menu_item_layout
+layout abc_dialog_title_material
+layout abc_expanded_menu_layout
+layout abc_list_menu_item_checkbox
+layout abc_list_menu_item_icon
+layout abc_list_menu_item_layout
+layout abc_list_menu_item_radio
+layout abc_popup_menu_header_item_layout
+layout abc_popup_menu_item_layout
+layout abc_screen_content_include
+layout abc_screen_simple
+layout abc_screen_simple_overlay_action_mode
+layout abc_screen_toolbar
+layout abc_search_dropdown_item_icons_2line
+layout abc_search_view
+layout abc_select_dialog_material
+layout abc_tooltip
+layout autofill_inline_suggestion
+layout custom_dialog
+layout dev_loading_view
+layout fps_view
+layout notification_action
+layout notification_action_tombstone
+layout notification_template_custom_big
+layout notification_template_icon_group
+layout notification_template_part_chronometer
+layout notification_template_part_time
+layout redbox_item_frame
+layout redbox_item_title
+layout redbox_view
+layout select_dialog_item_material
+layout select_dialog_multichoice_material
+layout select_dialog_singlechoice_material
+layout support_simple_spinner_dropdown_item
+menu example_menu
+menu example_menu2
+string abc_action_bar_home_description
+string abc_action_bar_up_description
+string abc_action_menu_overflow_description
+string abc_action_mode_done
+string abc_activity_chooser_view_see_all
+string abc_activitychooserview_choose_application
+string abc_capital_off
+string abc_capital_on
+string abc_menu_alt_shortcut_label
+string abc_menu_ctrl_shortcut_label
+string abc_menu_delete_shortcut_label
+string abc_menu_enter_shortcut_label
+string abc_menu_function_shortcut_label
+string abc_menu_meta_shortcut_label
+string abc_menu_shift_shortcut_label
+string abc_menu_space_shortcut_label
+string abc_menu_sym_shortcut_label
+string abc_prepend_shortcut_label
+string abc_search_hint
+string abc_searchview_description_clear
+string abc_searchview_description_query
+string abc_searchview_description_search
+string abc_searchview_description_submit
+string abc_searchview_description_voice
+string abc_shareactionprovider_share_with
+string abc_shareactionprovider_share_with_application
+string abc_toolbar_collapse_description
+string alert_description
+string call_notification_answer_action
+string call_notification_answer_video_action
+string call_notification_decline_action
+string call_notification_hang_up_action
+string call_notification_incoming_text
+string call_notification_ongoing_text
+string call_notification_screening_text
+string catalyst_change_bundle_location
+string catalyst_copy_button
+string catalyst_debug
+string catalyst_debug_chrome
+string catalyst_debug_chrome_stop
+string catalyst_debug_connecting
+string catalyst_debug_error
+string catalyst_debug_open
+string catalyst_debug_stop
+string catalyst_devtools_open
+string catalyst_dismiss_button
+string catalyst_heap_capture
+string catalyst_hot_reloading
+string catalyst_hot_reloading_auto_disable
+string catalyst_hot_reloading_auto_enable
+string catalyst_hot_reloading_stop
+string catalyst_inspector
+string catalyst_inspector_stop
+string catalyst_loading_from_url
+string catalyst_open_flipper_error
+string catalyst_perf_monitor
+string catalyst_perf_monitor_stop
+string catalyst_reload
+string catalyst_reload_button
+string catalyst_reload_error
+string catalyst_report_button
+string catalyst_sample_profiler_disable
+string catalyst_sample_profiler_enable
+string catalyst_settings
+string catalyst_settings_title
+string combobox_description
+string common_google_play_services_unknown_issue
+string fcm_fallback_notification_channel_label
+string header_description
+string image_description
+string imagebutton_description
+string link_description
+string menu_description
+string menubar_description
+string menuitem_description
+string progressbar_description
+string radiogroup_description
+string rn_tab_description
+string scrollbar_description
+string search_menu_title
+string spinbutton_description
+string state_busy_description
+string state_collapsed_description
+string state_expanded_description
+string state_mixed_description
+string state_off_description
+string state_on_description
+string state_unselected_description
+string status_bar_notification_info_overflow
+string summary_description
+string tablist_description
+string timer_description
+string toolbar_description
+style AlertDialog_AppCompat
+style AlertDialog_AppCompat_Light
+style Animation_AppCompat_Dialog
+style Animation_AppCompat_DropDownUp
+style Animation_AppCompat_Tooltip
+style Animation_Catalyst_LogBox
+style Animation_Catalyst_RedBox
+style Base_AlertDialog_AppCompat
+style Base_AlertDialog_AppCompat_Light
+style Base_Animation_AppCompat_Dialog
+style Base_Animation_AppCompat_DropDownUp
+style Base_Animation_AppCompat_Tooltip
+style Base_DialogWindowTitleBackground_AppCompat
+style Base_DialogWindowTitle_AppCompat
+style Base_TextAppearance_AppCompat
+style Base_TextAppearance_AppCompat_Body1
+style Base_TextAppearance_AppCompat_Body2
+style Base_TextAppearance_AppCompat_Button
+style Base_TextAppearance_AppCompat_Caption
+style Base_TextAppearance_AppCompat_Display1
+style Base_TextAppearance_AppCompat_Display2
+style Base_TextAppearance_AppCompat_Display3
+style Base_TextAppearance_AppCompat_Display4
+style Base_TextAppearance_AppCompat_Headline
+style Base_TextAppearance_AppCompat_Inverse
+style Base_TextAppearance_AppCompat_Large
+style Base_TextAppearance_AppCompat_Large_Inverse
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Medium
+style Base_TextAppearance_AppCompat_Medium_Inverse
+style Base_TextAppearance_AppCompat_Menu
+style Base_TextAppearance_AppCompat_SearchResult
+style Base_TextAppearance_AppCompat_SearchResult_Subtitle
+style Base_TextAppearance_AppCompat_SearchResult_Title
+style Base_TextAppearance_AppCompat_Small
+style Base_TextAppearance_AppCompat_Small_Inverse
+style Base_TextAppearance_AppCompat_Subhead
+style Base_TextAppearance_AppCompat_Subhead_Inverse
+style Base_TextAppearance_AppCompat_Title
+style Base_TextAppearance_AppCompat_Title_Inverse
+style Base_TextAppearance_AppCompat_Tooltip
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Title
+style Base_TextAppearance_AppCompat_Widget_Button
+style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Inverse
+style Base_TextAppearance_AppCompat_Widget_DropDownItem
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Widget_Switch
+style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Title
+style Base_ThemeOverlay_AppCompat
+style Base_ThemeOverlay_AppCompat_ActionBar
+style Base_ThemeOverlay_AppCompat_Dark
+style Base_ThemeOverlay_AppCompat_Dark_ActionBar
+style Base_ThemeOverlay_AppCompat_Dialog
+style Base_ThemeOverlay_AppCompat_Dialog_Alert
+style Base_ThemeOverlay_AppCompat_Light
+style Base_Theme_AppCompat
+style Base_Theme_AppCompat_CompactMenu
+style Base_Theme_AppCompat_Dialog
+style Base_Theme_AppCompat_DialogWhenLarge
+style Base_Theme_AppCompat_Dialog_Alert
+style Base_Theme_AppCompat_Dialog_FixedSize
+style Base_Theme_AppCompat_Dialog_MinWidth
+style Base_Theme_AppCompat_Light
+style Base_Theme_AppCompat_Light_DarkActionBar
+style Base_Theme_AppCompat_Light_Dialog
+style Base_Theme_AppCompat_Light_DialogWhenLarge
+style Base_Theme_AppCompat_Light_Dialog_Alert
+style Base_Theme_AppCompat_Light_Dialog_FixedSize
+style Base_Theme_AppCompat_Light_Dialog_MinWidth
+style Base_V21_ThemeOverlay_AppCompat_Dialog
+style Base_V21_Theme_AppCompat
+style Base_V21_Theme_AppCompat_Dialog
+style Base_V21_Theme_AppCompat_Light
+style Base_V21_Theme_AppCompat_Light_Dialog
+style Base_V22_Theme_AppCompat
+style Base_V22_Theme_AppCompat_Light
+style Base_V23_Theme_AppCompat
+style Base_V23_Theme_AppCompat_Light
+style Base_V26_Theme_AppCompat
+style Base_V26_Theme_AppCompat_Light
+style Base_V26_Widget_AppCompat_Toolbar
+style Base_V28_Theme_AppCompat
+style Base_V28_Theme_AppCompat_Light
+style Base_V7_ThemeOverlay_AppCompat_Dialog
+style Base_V7_Theme_AppCompat
+style Base_V7_Theme_AppCompat_Dialog
+style Base_V7_Theme_AppCompat_Light
+style Base_V7_Theme_AppCompat_Light_Dialog
+style Base_V7_Widget_AppCompat_AutoCompleteTextView
+style Base_V7_Widget_AppCompat_EditText
+style Base_V7_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_ActionBar
+style Base_Widget_AppCompat_ActionBar_Solid
+style Base_Widget_AppCompat_ActionBar_TabBar
+style Base_Widget_AppCompat_ActionBar_TabText
+style Base_Widget_AppCompat_ActionBar_TabView
+style Base_Widget_AppCompat_ActionButton
+style Base_Widget_AppCompat_ActionButton_CloseMode
+style Base_Widget_AppCompat_ActionButton_Overflow
+style Base_Widget_AppCompat_ActionMode
+style Base_Widget_AppCompat_ActivityChooserView
+style Base_Widget_AppCompat_AutoCompleteTextView
+style Base_Widget_AppCompat_Button
+style Base_Widget_AppCompat_ButtonBar
+style Base_Widget_AppCompat_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Borderless
+style Base_Widget_AppCompat_Button_Borderless_Colored
+style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Colored
+style Base_Widget_AppCompat_Button_Small
+style Base_Widget_AppCompat_CompoundButton_CheckBox
+style Base_Widget_AppCompat_CompoundButton_RadioButton
+style Base_Widget_AppCompat_CompoundButton_Switch
+style Base_Widget_AppCompat_DrawerArrowToggle
+style Base_Widget_AppCompat_DrawerArrowToggle_Common
+style Base_Widget_AppCompat_DropDownItem_Spinner
+style Base_Widget_AppCompat_EditText
+style Base_Widget_AppCompat_ImageButton
+style Base_Widget_AppCompat_Light_ActionBar
+style Base_Widget_AppCompat_Light_ActionBar_Solid
+style Base_Widget_AppCompat_Light_ActionBar_TabBar
+style Base_Widget_AppCompat_Light_ActionBar_TabText
+style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Base_Widget_AppCompat_Light_ActionBar_TabView
+style Base_Widget_AppCompat_Light_PopupMenu
+style Base_Widget_AppCompat_Light_PopupMenu_Overflow
+style Base_Widget_AppCompat_ListMenuView
+style Base_Widget_AppCompat_ListPopupWindow
+style Base_Widget_AppCompat_ListView
+style Base_Widget_AppCompat_ListView_DropDown
+style Base_Widget_AppCompat_ListView_Menu
+style Base_Widget_AppCompat_PopupMenu
+style Base_Widget_AppCompat_PopupMenu_Overflow
+style Base_Widget_AppCompat_PopupWindow
+style Base_Widget_AppCompat_ProgressBar
+style Base_Widget_AppCompat_ProgressBar_Horizontal
+style Base_Widget_AppCompat_RatingBar
+style Base_Widget_AppCompat_RatingBar_Indicator
+style Base_Widget_AppCompat_RatingBar_Small
+style Base_Widget_AppCompat_SearchView
+style Base_Widget_AppCompat_SearchView_ActionBar
+style Base_Widget_AppCompat_SeekBar
+style Base_Widget_AppCompat_SeekBar_Discrete
+style Base_Widget_AppCompat_Spinner
+style Base_Widget_AppCompat_Spinner_Underlined
+style Base_Widget_AppCompat_TextView
+style Base_Widget_AppCompat_TextView_SpinnerItem
+style Base_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_Toolbar_Button_Navigation
+style CalendarDatePickerDialog
+style CalendarDatePickerStyle
+style DialogAnimationFade
+style DialogAnimationSlide
+style Platform_AppCompat
+style Platform_AppCompat_Light
+style Platform_ThemeOverlay_AppCompat
+style Platform_ThemeOverlay_AppCompat_Dark
+style Platform_ThemeOverlay_AppCompat_Light
+style Platform_V21_AppCompat
+style Platform_V21_AppCompat_Light
+style Platform_V25_AppCompat
+style Platform_V25_AppCompat_Light
+style Platform_Widget_AppCompat_Spinner
+style RtlOverlay_DialogWindowTitle_AppCompat
+style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem
+style RtlOverlay_Widget_AppCompat_DialogTitle_Icon
+style RtlOverlay_Widget_AppCompat_PopupMenuItem
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title
+style RtlOverlay_Widget_AppCompat_SearchView_MagIcon
+style RtlOverlay_Widget_AppCompat_Search_DropDown
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Query
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Text
+style RtlUnderlay_Widget_AppCompat_ActionButton
+style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow
+style SpinnerDatePickerDialog
+style SpinnerDatePickerStyle
+style TextAppearance_AppCompat
+style TextAppearance_AppCompat_Body1
+style TextAppearance_AppCompat_Body2
+style TextAppearance_AppCompat_Button
+style TextAppearance_AppCompat_Caption
+style TextAppearance_AppCompat_Display1
+style TextAppearance_AppCompat_Display2
+style TextAppearance_AppCompat_Display3
+style TextAppearance_AppCompat_Display4
+style TextAppearance_AppCompat_Headline
+style TextAppearance_AppCompat_Inverse
+style TextAppearance_AppCompat_Large
+style TextAppearance_AppCompat_Large_Inverse
+style TextAppearance_AppCompat_Light_SearchResult_Subtitle
+style TextAppearance_AppCompat_Light_SearchResult_Title
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Medium
+style TextAppearance_AppCompat_Medium_Inverse
+style TextAppearance_AppCompat_Menu
+style TextAppearance_AppCompat_SearchResult_Subtitle
+style TextAppearance_AppCompat_SearchResult_Title
+style TextAppearance_AppCompat_Small
+style TextAppearance_AppCompat_Small_Inverse
+style TextAppearance_AppCompat_Subhead
+style TextAppearance_AppCompat_Subhead_Inverse
+style TextAppearance_AppCompat_Title
+style TextAppearance_AppCompat_Title_Inverse
+style TextAppearance_AppCompat_Tooltip
+style TextAppearance_AppCompat_Widget_ActionBar_Menu
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionBar_Title
+style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Title
+style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse
+style TextAppearance_AppCompat_Widget_Button
+style TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style TextAppearance_AppCompat_Widget_Button_Colored
+style TextAppearance_AppCompat_Widget_Button_Inverse
+style TextAppearance_AppCompat_Widget_DropDownItem
+style TextAppearance_AppCompat_Widget_PopupMenu_Header
+style TextAppearance_AppCompat_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Widget_Switch
+style TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style TextAppearance_Compat_Notification
+style TextAppearance_Compat_Notification_Info
+style TextAppearance_Compat_Notification_Line2
+style TextAppearance_Compat_Notification_Time
+style TextAppearance_Compat_Notification_Title
+style TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style TextAppearance_Widget_AppCompat_Toolbar_Title
+style Theme
+style ThemeOverlay_AppCompat
+style ThemeOverlay_AppCompat_ActionBar
+style ThemeOverlay_AppCompat_Dark
+style ThemeOverlay_AppCompat_Dark_ActionBar
+style ThemeOverlay_AppCompat_DayNight
+style ThemeOverlay_AppCompat_DayNight_ActionBar
+style ThemeOverlay_AppCompat_Dialog
+style ThemeOverlay_AppCompat_Dialog_Alert
+style ThemeOverlay_AppCompat_Light
+style Theme_AppCompat
+style Theme_AppCompat_CompactMenu
+style Theme_AppCompat_DayNight
+style Theme_AppCompat_DayNight_DarkActionBar
+style Theme_AppCompat_DayNight_Dialog
+style Theme_AppCompat_DayNight_DialogWhenLarge
+style Theme_AppCompat_DayNight_Dialog_Alert
+style Theme_AppCompat_DayNight_Dialog_MinWidth
+style Theme_AppCompat_DayNight_NoActionBar
+style Theme_AppCompat_Dialog
+style Theme_AppCompat_DialogWhenLarge
+style Theme_AppCompat_Dialog_Alert
+style Theme_AppCompat_Dialog_MinWidth
+style Theme_AppCompat_Empty
+style Theme_AppCompat_Light
+style Theme_AppCompat_Light_DarkActionBar
+style Theme_AppCompat_Light_Dialog
+style Theme_AppCompat_Light_DialogWhenLarge
+style Theme_AppCompat_Light_Dialog_Alert
+style Theme_AppCompat_Light_Dialog_MinWidth
+style Theme_AppCompat_Light_NoActionBar
+style Theme_AppCompat_NoActionBar
+style Theme_AutofillInlineSuggestion
+style Theme_Catalyst
+style Theme_Catalyst_LogBox
+style Theme_Catalyst_RedBox
+style Theme_FullScreenDialog
+style Theme_FullScreenDialogAnimatedFade
+style Theme_FullScreenDialogAnimatedSlide
+style Theme_ReactNative_AppCompat_Light
+style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen
+style Widget_AppCompat_ActionBar
+style Widget_AppCompat_ActionBar_Solid
+style Widget_AppCompat_ActionBar_TabBar
+style Widget_AppCompat_ActionBar_TabText
+style Widget_AppCompat_ActionBar_TabView
+style Widget_AppCompat_ActionButton
+style Widget_AppCompat_ActionButton_CloseMode
+style Widget_AppCompat_ActionButton_Overflow
+style Widget_AppCompat_ActionMode
+style Widget_AppCompat_ActivityChooserView
+style Widget_AppCompat_AutoCompleteTextView
+style Widget_AppCompat_Button
+style Widget_AppCompat_ButtonBar
+style Widget_AppCompat_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Borderless
+style Widget_AppCompat_Button_Borderless_Colored
+style Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Colored
+style Widget_AppCompat_Button_Small
+style Widget_AppCompat_CompoundButton_CheckBox
+style Widget_AppCompat_CompoundButton_RadioButton
+style Widget_AppCompat_CompoundButton_Switch
+style Widget_AppCompat_DrawerArrowToggle
+style Widget_AppCompat_DropDownItem_Spinner
+style Widget_AppCompat_EditText
+style Widget_AppCompat_ImageButton
+style Widget_AppCompat_Light_ActionBar
+style Widget_AppCompat_Light_ActionBar_Solid
+style Widget_AppCompat_Light_ActionBar_Solid_Inverse
+style Widget_AppCompat_Light_ActionBar_TabBar
+style Widget_AppCompat_Light_ActionBar_TabBar_Inverse
+style Widget_AppCompat_Light_ActionBar_TabText
+style Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Widget_AppCompat_Light_ActionBar_TabView
+style Widget_AppCompat_Light_ActionBar_TabView_Inverse
+style Widget_AppCompat_Light_ActionButton
+style Widget_AppCompat_Light_ActionButton_CloseMode
+style Widget_AppCompat_Light_ActionButton_Overflow
+style Widget_AppCompat_Light_ActionMode_Inverse
+style Widget_AppCompat_Light_ActivityChooserView
+style Widget_AppCompat_Light_AutoCompleteTextView
+style Widget_AppCompat_Light_DropDownItem_Spinner
+style Widget_AppCompat_Light_ListPopupWindow
+style Widget_AppCompat_Light_ListView_DropDown
+style Widget_AppCompat_Light_PopupMenu
+style Widget_AppCompat_Light_PopupMenu_Overflow
+style Widget_AppCompat_Light_SearchView
+style Widget_AppCompat_Light_Spinner_DropDown_ActionBar
+style Widget_AppCompat_ListMenuView
+style Widget_AppCompat_ListPopupWindow
+style Widget_AppCompat_ListView
+style Widget_AppCompat_ListView_DropDown
+style Widget_AppCompat_ListView_Menu
+style Widget_AppCompat_PopupMenu
+style Widget_AppCompat_PopupMenu_Overflow
+style Widget_AppCompat_PopupWindow
+style Widget_AppCompat_ProgressBar
+style Widget_AppCompat_ProgressBar_Horizontal
+style Widget_AppCompat_RatingBar
+style Widget_AppCompat_RatingBar_Indicator
+style Widget_AppCompat_RatingBar_Small
+style Widget_AppCompat_SearchView
+style Widget_AppCompat_SearchView_ActionBar
+style Widget_AppCompat_SeekBar
+style Widget_AppCompat_SeekBar_Discrete
+style Widget_AppCompat_Spinner
+style Widget_AppCompat_Spinner_DropDown
+style Widget_AppCompat_Spinner_DropDown_ActionBar
+style Widget_AppCompat_Spinner_Underlined
+style Widget_AppCompat_TextView
+style Widget_AppCompat_TextView_SpinnerItem
+style Widget_AppCompat_Toolbar
+style Widget_AppCompat_Toolbar_Button_Navigation
+style Widget_Autofill
+style Widget_Autofill_InlineSuggestionChip
+style Widget_Autofill_InlineSuggestionEndIconStyle
+style Widget_Autofill_InlineSuggestionStartIconStyle
+style Widget_Autofill_InlineSuggestionSubtitle
+style Widget_Autofill_InlineSuggestionTitle
+style Widget_Compat_NotificationActionContainer
+style Widget_Compat_NotificationActionText
+style Widget_Support_CoordinatorLayout
+style redboxButton
+styleable ActionBar background backgroundSplit backgroundStacked contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation customNavigationLayout displayOptions divider elevation height hideOnContentScroll homeAsUpIndicator homeLayout icon indeterminateProgressStyle itemPadding logo navigationMode popupTheme progressBarPadding progressBarStyle subtitle subtitleTextStyle title titleTextStyle
+styleable ActionBarLayout android_layout_gravity
+styleable ActionMenuItemView android_minWidth
+styleable ActionMenuView
+styleable ActionMode background backgroundSplit closeItemLayout height subtitleTextStyle titleTextStyle
+styleable ActivityChooserView expandActivityOverflowButtonDrawable initialActivityCount
+styleable AlertDialog android_layout buttonIconDimen buttonPanelSideLayout listItemLayout listLayout multiChoiceItemLayout showTitle singleChoiceItemLayout
+styleable AnimatedStateListDrawableCompat android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable AnimatedStateListDrawableItem android_drawable android_id
+styleable AnimatedStateListDrawableTransition android_drawable android_fromId android_reversible android_toId
+styleable AppCompatEmojiHelper
+styleable AppCompatImageView android_src srcCompat tint tintMode
+styleable AppCompatSeekBar android_thumb tickMark tickMarkTint tickMarkTintMode
+styleable AppCompatTextHelper android_drawableBottom android_drawableEnd android_drawableLeft android_drawableRight android_drawableStart android_drawableTop android_textAppearance
+styleable AppCompatTextView android_textAppearance autoSizeMaxTextSize autoSizeMinTextSize autoSizePresetSizes autoSizeStepGranularity autoSizeTextType drawableBottomCompat drawableEndCompat drawableLeftCompat drawableRightCompat drawableStartCompat drawableTint drawableTintMode drawableTopCompat emojiCompatEnabled firstBaselineToTopHeight fontFamily fontVariationSettings lastBaselineToBottomHeight lineHeight textAllCaps textLocale
+styleable AppCompatTheme actionBarDivider actionBarItemBackground actionBarPopupTheme actionBarSize actionBarSplitStyle actionBarStyle actionBarTabBarStyle actionBarTabStyle actionBarTabTextStyle actionBarTheme actionBarWidgetTheme actionButtonStyle actionDropDownStyle actionMenuTextAppearance actionMenuTextColor actionModeBackground actionModeCloseButtonStyle actionModeCloseContentDescription actionModeCloseDrawable actionModeCopyDrawable actionModeCutDrawable actionModeFindDrawable actionModePasteDrawable actionModePopupWindowStyle actionModeSelectAllDrawable actionModeShareDrawable actionModeSplitBackground actionModeStyle actionModeTheme actionModeWebSearchDrawable actionOverflowButtonStyle actionOverflowMenuStyle activityChooserViewStyle alertDialogButtonGroupStyle alertDialogCenterButtons alertDialogStyle alertDialogTheme android_windowAnimationStyle android_windowIsFloating autoCompleteTextViewStyle borderlessButtonStyle buttonBarButtonStyle buttonBarNegativeButtonStyle buttonBarNeutralButtonStyle buttonBarPositiveButtonStyle buttonBarStyle buttonStyle buttonStyleSmall checkboxStyle checkedTextViewStyle colorAccent colorBackgroundFloating colorButtonNormal colorControlActivated colorControlHighlight colorControlNormal colorError colorPrimary colorPrimaryDark colorSwitchThumbNormal controlBackground dialogCornerRadius dialogPreferredPadding dialogTheme dividerHorizontal dividerVertical dropDownListViewStyle dropdownListPreferredItemHeight editTextBackground editTextColor editTextStyle homeAsUpIndicator imageButtonStyle listChoiceBackgroundIndicator listChoiceIndicatorMultipleAnimated listChoiceIndicatorSingleAnimated listDividerAlertDialog listMenuViewStyle listPopupWindowStyle listPreferredItemHeight listPreferredItemHeightLarge listPreferredItemHeightSmall listPreferredItemPaddingEnd listPreferredItemPaddingLeft listPreferredItemPaddingRight listPreferredItemPaddingStart panelBackground panelMenuListTheme panelMenuListWidth popupMenuStyle popupWindowStyle radioButtonStyle ratingBarStyle ratingBarStyleIndicator ratingBarStyleSmall searchViewStyle seekBarStyle selectableItemBackground selectableItemBackgroundBorderless spinnerDropDownItemStyle spinnerStyle switchStyle textAppearanceLargePopupMenu textAppearanceListItem textAppearanceListItemSecondary textAppearanceListItemSmall textAppearancePopupMenuHeader textAppearanceSearchResultSubtitle textAppearanceSearchResultTitle textAppearanceSmallPopupMenu textColorAlertDialogListItem textColorSearchUrl toolbarNavigationButtonStyle toolbarStyle tooltipForegroundColor tooltipFrameBackground viewInflaterClass windowActionBar windowActionBarOverlay windowActionModeOverlay windowFixedHeightMajor windowFixedHeightMinor windowFixedWidthMajor windowFixedWidthMinor windowMinWidthMajor windowMinWidthMinor windowNoTitle
+styleable Autofill_InlineSuggestion autofillInlineSuggestionChip autofillInlineSuggestionEndIconStyle autofillInlineSuggestionStartIconStyle autofillInlineSuggestionSubtitle autofillInlineSuggestionTitle isAutofillInlineSuggestionTheme
+styleable ButtonBarLayout allowStacking
+styleable Capability queryPatterns shortcutMatchRequired
+styleable CheckedTextView android_checkMark checkMarkCompat checkMarkTint checkMarkTintMode
+styleable ColorStateListItem alpha android_alpha android_color android_lStar lStar
+styleable CompoundButton android_button buttonCompat buttonTint buttonTintMode
+styleable CoordinatorLayout keylines statusBarBackground
+styleable CoordinatorLayout_Layout android_layout_gravity layout_anchor layout_anchorGravity layout_behavior layout_dodgeInsetEdges layout_insetEdge layout_keyline
+styleable DrawerArrowToggle arrowHeadLength arrowShaftLength barLength color drawableSize gapBetweenBars spinBars thickness
+styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery fontProviderSystemFontFamily
+styleable FontFamilyFont android_font android_fontStyle android_fontVariationSettings android_fontWeight android_ttcIndex font fontStyle fontVariationSettings fontWeight ttcIndex
+styleable Fragment android_id android_name android_tag
+styleable FragmentContainerView android_name android_tag
+styleable GenericDraweeHierarchy actualImageScaleType backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable GradientColor android_centerColor android_centerX android_centerY android_endColor android_endX android_endY android_gradientRadius android_startColor android_startX android_startY android_tileMode android_type
+styleable GradientColorItem android_color android_offset
+styleable LinearLayoutCompat android_baselineAligned android_baselineAlignedChildIndex android_gravity android_orientation android_weightSum divider dividerPadding measureWithLargestChild showDividers
+styleable LinearLayoutCompat_Layout android_layout_gravity android_layout_height android_layout_weight android_layout_width
+styleable ListPopupWindow android_dropDownHorizontalOffset android_dropDownVerticalOffset
+styleable MenuGroup android_checkableBehavior android_enabled android_id android_menuCategory android_orderInCategory android_visible
+styleable MenuItem actionLayout actionProviderClass actionViewClass alphabeticModifiers android_alphabeticShortcut android_checkable android_checked android_enabled android_icon android_id android_menuCategory android_numericShortcut android_onClick android_orderInCategory android_title android_titleCondensed android_visible contentDescription iconTint iconTintMode numericModifiers showAsAction tooltipText
+styleable MenuView android_headerBackground android_horizontalDivider android_itemBackground android_itemIconDisabledAlpha android_itemTextAppearance android_verticalDivider android_windowAnimationStyle preserveIconSpacing subMenuArrow
+styleable PopupWindow android_popupAnimationStyle android_popupBackground overlapAnchor
+styleable PopupWindowBackgroundState state_above_anchor
+styleable RecycleListView paddingBottomNoButtons paddingTopNoTitle
+styleable SearchView android_focusable android_imeOptions android_inputType android_maxWidth closeIcon commitIcon defaultQueryHint goIcon iconifiedByDefault layout queryBackground queryHint searchHintIcon searchIcon submitBackground suggestionRowLayout voiceIcon
+styleable SimpleDraweeView actualImageResource actualImageScaleType actualImageUri backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable Spinner android_dropDownWidth android_entries android_popupBackground android_prompt popupTheme
+styleable StateListDrawable android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable StateListDrawableItem android_drawable
+styleable SwitchCompat android_textOff android_textOn android_thumb showText splitTrack switchMinWidth switchPadding switchTextAppearance thumbTextPadding thumbTint thumbTintMode track trackTint trackTintMode
+styleable TextAppearance android_fontFamily android_shadowColor android_shadowDx android_shadowDy android_shadowRadius android_textColor android_textColorHint android_textColorLink android_textFontWeight android_textSize android_textStyle android_typeface fontFamily fontVariationSettings textAllCaps textLocale
+styleable Toolbar android_gravity android_minHeight buttonGravity collapseContentDescription collapseIcon contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation logo logoDescription maxButtonHeight menu navigationContentDescription navigationIcon popupTheme subtitle subtitleTextAppearance subtitleTextColor title titleMargin titleMarginBottom titleMarginEnd titleMarginStart titleMarginTop titleMargins titleTextAppearance titleTextColor
+styleable View android_focusable android_theme paddingEnd paddingStart theme
+styleable ViewBackgroundHelper android_background backgroundTint backgroundTintMode
+styleable ViewStubCompat android_id android_inflatedId android_layout
+xml rn_dev_preferences
diff --git a/node_modules/react-native-notifications/lib/android/app/build/outputs/logs/manifest-merger-reactNative60-debug-report.txt b/node_modules/react-native-notifications/lib/android/app/build/outputs/logs/manifest-merger-reactNative60-debug-report.txt
new file mode 100644
index 0000000..0a8e247
--- /dev/null
+++ b/node_modules/react-native-notifications/lib/android/app/build/outputs/logs/manifest-merger-reactNative60-debug-report.txt
@@ -0,0 +1,55 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:2:1-27:12
+INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:2:1-27:12
+INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:2:1-27:12
+	package
+		INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:2:1-27:12
+		INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:3:5-63
+application
+ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:5:5-25:19
+service#com.wix.reactnativenotifications.core.ProxyService
+ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:10:9-53
+	android:name
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:10:18-51
+service#com.wix.reactnativenotifications.fcm.FcmInstanceIdListenerService
+ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:12:9-19:19
+	android:exported
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:14:13-36
+	android:name
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:13:13-61
+intent-filter#action:name:com.google.firebase.INSTANCE_ID_EVENT+action:name:com.google.firebase.MESSAGING_EVENT
+ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:15:13-18:29
+action#com.google.firebase.MESSAGING_EVENT
+ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:16:17-78
+	android:name
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:16:25-75
+action#com.google.firebase.INSTANCE_ID_EVENT
+ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:17:17-80
+	android:name
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:17:25-77
+service#com.wix.reactnativenotifications.fcm.FcmInstanceIdRefreshHandlerService
+ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:21:9-24:72
+	android:exported
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:23:13-37
+	android:permission
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:24:13-69
+	android:name
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml:22:13-67
+uses-sdk
+INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/Documents/Projects/RN/Follo/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-notifications/lib/android/app/build/tmp/compileReactNative60DebugJavaWithJavac/previous-compilation-data.bin b/node_modules/react-native-notifications/lib/android/app/build/tmp/compileReactNative60DebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..d1a8d09
Binary files /dev/null and b/node_modules/react-native-notifications/lib/android/app/build/tmp/compileReactNative60DebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml b/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
index 24cd226..45c25ad 100644
--- a/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
+++ b/node_modules/react-native-notifications/lib/android/app/src/main/AndroidManifest.xml
@@ -1,7 +1,6 @@
 <?xml version="1.0" encoding="utf-8"?>
 <manifest
-    xmlns:android="http://schemas.android.com/apk/res/android"
-    package="com.wix.reactnativenotifications">
+    xmlns:android="http://schemas.android.com/apk/res/android">
 
     <application>
 
diff --git a/node_modules/react-native-notifications/lib/android/build.gradle b/node_modules/react-native-notifications/lib/android/build.gradle
index 29d6dca..015c651 100644
--- a/node_modules/react-native-notifications/lib/android/build.gradle
+++ b/node_modules/react-native-notifications/lib/android/build.gradle
@@ -38,6 +38,7 @@ subprojects {
     afterEvaluate { project ->
         if ((project.plugins.hasPlugin('android') || project.plugins.hasPlugin('android-library'))) {
             android {
+    namespace "com.wix.reactnativenotifications"
                 compileSdkVersion rootProject.ext.androidSdkVersion
             }
         }
