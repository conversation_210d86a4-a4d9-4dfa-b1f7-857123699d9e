diff --git a/node_modules/react-native-reanimated/lib/module/reanimated2/UpdateProps.js b/node_modules/react-native-reanimated/lib/module/reanimated2/UpdateProps.js
index 723afbd..95cd40f 100644
--- a/node_modules/react-native-reanimated/lib/module/reanimated2/UpdateProps.js
+++ b/node_modules/react-native-reanimated/lib/module/reanimated2/UpdateProps.js
@@ -2,7 +2,7 @@
 
 import { processColorsInProps } from './Colors';
 import { _updatePropsJS } from './js-reanimated';
-import { isFabric, isJest, shouldBeUseWeb } from './PlatformChecker';
+import { isFabric, isChromeDebugger, isJest, shouldBeUseWeb } from './PlatformChecker';
 import { runOnUIImmediately } from './threads';
 let updateProps;
 if (shouldBeUseWeb()) {
@@ -87,7 +87,7 @@ if (shouldBeUseWeb()) {
   const maybeThrowError = () => {
     // Jest attempts to access a property of this object to check if it is a Jest mock
     // so we can't throw an error in the getter.
-    if (!isJest()) {
+    if (!isJest() && !isChromeDebugger()) {
       throw new Error('[Reanimated] `UpdatePropsManager` is not available on non-native platform.');
     }
   };
diff --git a/node_modules/react-native-reanimated/lib/module/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.js b/node_modules/react-native-reanimated/lib/module/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.js
index b9774ef..bf0251d 100644
--- a/node_modules/react-native-reanimated/lib/module/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.js
+++ b/node_modules/react-native-reanimated/lib/module/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.js
@@ -6,7 +6,7 @@ function _toPrimitive(input, hint) { if (typeof input !== "object" || input ===
 import { runOnUIImmediately } from '../../threads';
 import { registerEventHandler, unregisterEventHandler } from '../../core';
 import { Platform } from 'react-native';
-import { isJest, shouldBeUseWeb } from '../../PlatformChecker';
+import { isChromeDebugger, isJest, shouldBeUseWeb } from '../../PlatformChecker';
 const IS_ANDROID = Platform.OS === 'android';
 export class ProgressTransitionManager {
   constructor() {
@@ -191,7 +191,7 @@ if (shouldBeUseWeb()) {
   const maybeThrowError = () => {
     // Jest attempts to access a property of this object to check if it is a Jest mock
     // so we can't throw an error in the getter.
-    if (!isJest()) {
+    if (!isJest() && !isChromeDebugger()) {
       throw new Error('[Reanimated] `ProgressTransitionRegister` is not available on non-native platform.');
     }
   };
diff --git a/node_modules/react-native-reanimated/lib/module/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.js.map b/node_modules/react-native-reanimated/lib/module/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.js.map
index 05519a3..b3b8eea 100644
--- a/node_modules/react-native-reanimated/lib/module/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.js.map
+++ b/node_modules/react-native-reanimated/lib/module/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.js.map
@@ -1 +1 @@
-{"version":3,"names":["_defineProperty","obj","key","value","_toPropertyKey","Object","defineProperty","enumerable","configurable","writable","arg","_toPrimitive","String","input","hint","prim","Symbol","toPrimitive","undefined","res","call","TypeError","Number","runOnUIImmediately","registerEventHandler","unregisterEventHandler","Platform","isJest","shouldBeUseWeb","IS_ANDROID","OS","ProgressTransitionManager","constructor","isRegistered","onTransitionProgress","onAppear","onDisappear","onSwipeDismiss","addProgressAnimation","viewTag","progressAnimation","global","ProgressTransitionRegister","registerEventHandlers","removeProgressAnimation","unregisterEventHandlers","_sharedElementCount","eventHandler","_eventHandler","eventPrefix","lastProgressValue","event","progress","frame","onTransitionEnd","onAndroidFinishTransitioning","createProgressTransitionRegister","progressAnimations","Map","snapshots","currentTransitions","Set","toRemove","skipCleaning","isTransitionRestart","progressTransitionManager","size","set","add","onTransitionStart","snapshot","get","removeViews","arguments","length","clear","_notifyAboutEnd","delete","maybeThrowError","Error","Proxy"],"sources":["ProgressTransitionManager.ts"],"sourcesContent":["'use strict';\nimport { runOnUIImmediately } from '../../threads';\nimport type {\n  ProgressAnimation,\n  SharedTransitionAnimationsValues,\n} from '../animationBuilder/commonTypes';\nimport { registerEventHandler, unregisterEventHandler } from '../../core';\nimport { Platform } from 'react-native';\nimport { isJest, shouldBeUseWeb } from '../../PlatformChecker';\n\ntype TransitionProgressEvent = {\n  closing: number;\n  goingForward: number;\n  eventName: string;\n  progress: number;\n  target: number;\n};\n\nconst IS_ANDROID = Platform.OS === 'android';\n\nexport class ProgressTransitionManager {\n  private _sharedElementCount = 0;\n  private _eventHandler = {\n    isRegistered: false,\n    onTransitionProgress: -1,\n    onAppear: -1,\n    onDisappear: -1,\n    onSwipeDismiss: -1,\n  };\n\n  public addProgressAnimation(\n    viewTag: number,\n    progressAnimation: ProgressAnimation\n  ) {\n    runOnUIImmediately(() => {\n      'worklet';\n      global.ProgressTransitionRegister.addProgressAnimation(\n        viewTag,\n        progressAnimation\n      );\n    })();\n    this.registerEventHandlers();\n  }\n\n  public removeProgressAnimation(viewTag: number) {\n    this.unregisterEventHandlers();\n    runOnUIImmediately(() => {\n      'worklet';\n      global.ProgressTransitionRegister.removeProgressAnimation(viewTag);\n    })();\n  }\n\n  private registerEventHandlers() {\n    this._sharedElementCount++;\n    const eventHandler = this._eventHandler;\n    if (!eventHandler.isRegistered) {\n      eventHandler.isRegistered = true;\n      const eventPrefix = IS_ANDROID ? 'on' : 'top';\n      let lastProgressValue = -1;\n      eventHandler.onTransitionProgress = registerEventHandler(\n        (event: TransitionProgressEvent) => {\n          'worklet';\n          const progress = event.progress;\n          if (progress === lastProgressValue) {\n            // During screen transition, handler receives two events with the same progress\n            // value for both screens, but for modals, there is only one event. To optimize\n            // performance and avoid unnecessary worklet calls, let's skip the second event.\n            return;\n          }\n          lastProgressValue = progress;\n          global.ProgressTransitionRegister.frame(progress);\n        },\n        eventPrefix + 'TransitionProgress'\n      );\n      eventHandler.onAppear = registerEventHandler(() => {\n        'worklet';\n        global.ProgressTransitionRegister.onTransitionEnd();\n      }, eventPrefix + 'Appear');\n\n      if (IS_ANDROID) {\n        // onFinishTransitioning event is available only on Android and\n        // is used to handle closing modals\n        eventHandler.onDisappear = registerEventHandler(() => {\n          'worklet';\n          global.ProgressTransitionRegister.onAndroidFinishTransitioning();\n        }, 'onFinishTransitioning');\n      } else if (Platform.OS === 'ios') {\n        // topDisappear event is required to handle closing modals on iOS\n        eventHandler.onDisappear = registerEventHandler(() => {\n          'worklet';\n          global.ProgressTransitionRegister.onTransitionEnd(true);\n        }, 'topDisappear');\n        eventHandler.onSwipeDismiss = registerEventHandler(() => {\n          'worklet';\n          global.ProgressTransitionRegister.onTransitionEnd();\n        }, 'topGestureCancel');\n      }\n    }\n  }\n\n  private unregisterEventHandlers(): void {\n    this._sharedElementCount--;\n    if (this._sharedElementCount === 0) {\n      const eventHandler = this._eventHandler;\n      eventHandler.isRegistered = false;\n      if (eventHandler.onTransitionProgress !== -1) {\n        unregisterEventHandler(eventHandler.onTransitionProgress);\n        eventHandler.onTransitionProgress = -1;\n      }\n      if (eventHandler.onAppear !== -1) {\n        unregisterEventHandler(eventHandler.onAppear);\n        eventHandler.onAppear = -1;\n      }\n      if (eventHandler.onDisappear !== -1) {\n        unregisterEventHandler(eventHandler.onDisappear);\n        eventHandler.onDisappear = -1;\n      }\n      if (eventHandler.onSwipeDismiss !== -1) {\n        unregisterEventHandler(eventHandler.onSwipeDismiss);\n        eventHandler.onSwipeDismiss = -1;\n      }\n    }\n  }\n}\n\nfunction createProgressTransitionRegister() {\n  'worklet';\n  const progressAnimations = new Map<number, ProgressAnimation>();\n  const snapshots = new Map<\n    number,\n    Partial<SharedTransitionAnimationsValues>\n  >();\n  const currentTransitions = new Set<number>();\n  const toRemove = new Set<number>();\n\n  let skipCleaning = false;\n  let isTransitionRestart = false;\n\n  const progressTransitionManager = {\n    addProgressAnimation: (\n      viewTag: number,\n      progressAnimation: ProgressAnimation\n    ) => {\n      if (currentTransitions.size > 0) {\n        // there is no need to prevent cleaning on android\n        isTransitionRestart = !IS_ANDROID;\n      }\n      progressAnimations.set(viewTag, progressAnimation);\n    },\n    removeProgressAnimation: (viewTag: number) => {\n      if (currentTransitions.size > 0) {\n        // there is no need to prevent cleaning on android\n        isTransitionRestart = !IS_ANDROID;\n      }\n      // Remove the animation config after the transition is finished\n      toRemove.add(viewTag);\n    },\n    onTransitionStart: (\n      viewTag: number,\n      snapshot: Partial<SharedTransitionAnimationsValues>\n    ) => {\n      skipCleaning = isTransitionRestart;\n      snapshots.set(viewTag, snapshot);\n      currentTransitions.add(viewTag);\n      // set initial style for re-parented components\n      progressTransitionManager.frame(0);\n    },\n    frame: (progress: number) => {\n      for (const viewTag of currentTransitions) {\n        const progressAnimation = progressAnimations.get(viewTag);\n        if (!progressAnimation) {\n          continue;\n        }\n        const snapshot = snapshots.get(\n          viewTag\n        )! as SharedTransitionAnimationsValues;\n        progressAnimation!(viewTag, snapshot, progress);\n      }\n    },\n    onAndroidFinishTransitioning: () => {\n      if (toRemove.size > 0) {\n        // it should be ran only on modal closing\n        progressTransitionManager.onTransitionEnd();\n      }\n    },\n    onTransitionEnd: (removeViews = false) => {\n      if (currentTransitions.size === 0) {\n        toRemove.clear();\n        return;\n      }\n      if (skipCleaning) {\n        skipCleaning = false;\n        isTransitionRestart = false;\n        return;\n      }\n      for (const viewTag of currentTransitions) {\n        _notifyAboutEnd(viewTag, removeViews);\n      }\n      currentTransitions.clear();\n      if (isTransitionRestart) {\n        // on transition restart, progressAnimations should be saved\n        // because they potentially can be used in the next transition\n        return;\n      }\n      snapshots.clear();\n      if (toRemove.size > 0) {\n        for (const viewTag of toRemove) {\n          progressAnimations.delete(viewTag);\n          _notifyAboutEnd(viewTag, removeViews);\n        }\n        toRemove.clear();\n      }\n    },\n  };\n  return progressTransitionManager;\n}\n\nif (shouldBeUseWeb()) {\n  const maybeThrowError = () => {\n    // Jest attempts to access a property of this object to check if it is a Jest mock\n    // so we can't throw an error in the getter.\n    if (!isJest()) {\n      throw new Error(\n        '[Reanimated] `ProgressTransitionRegister` is not available on non-native platform.'\n      );\n    }\n  };\n  global.ProgressTransitionRegister = new Proxy(\n    {} as ProgressTransitionRegister,\n    {\n      get: maybeThrowError,\n      set: () => {\n        maybeThrowError();\n        return false;\n      },\n    }\n  );\n} else {\n  runOnUIImmediately(() => {\n    'worklet';\n    global.ProgressTransitionRegister = createProgressTransitionRegister();\n  })();\n}\n\nexport type ProgressTransitionRegister = ReturnType<\n  typeof createProgressTransitionRegister\n>;\n"],"mappings":"AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,GAAA,QAAAR,GAAA,GAAAS,YAAA,CAAAD,GAAA,2BAAAR,GAAA,gBAAAA,GAAA,GAAAU,MAAA,CAAAV,GAAA;AAAA,SAAAS,aAAAE,KAAA,EAAAC,IAAA,eAAAD,KAAA,iBAAAA,KAAA,kBAAAA,KAAA,MAAAE,IAAA,GAAAF,KAAA,CAAAG,MAAA,CAAAC,WAAA,OAAAF,IAAA,KAAAG,SAAA,QAAAC,GAAA,GAAAJ,IAAA,CAAAK,IAAA,CAAAP,KAAA,EAAAC,IAAA,2BAAAK,GAAA,sBAAAA,GAAA,YAAAE,SAAA,4DAAAP,IAAA,gBAAAF,MAAA,GAAAU,MAAA,EAAAT,KAAA;AACb,SAASU,kBAAkB,QAAQ,eAAe;AAKlD,SAASC,oBAAoB,EAAEC,sBAAsB,QAAQ,YAAY;AACzE,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAU9D,MAAMC,UAAU,GAAGH,QAAQ,CAACI,EAAE,KAAK,SAAS;AAE5C,OAAO,MAAMC,yBAAyB,CAAC;EAAAC,YAAA;IAAAhC,eAAA,8BACP,CAAC;IAAAA,eAAA,wBACP;MACtBiC,YAAY,EAAE,KAAK;MACnBC,oBAAoB,EAAE,CAAC,CAAC;MACxBC,QAAQ,EAAE,CAAC,CAAC;MACZC,WAAW,EAAE,CAAC,CAAC;MACfC,cAAc,EAAE,CAAC;IACnB,CAAC;EAAA;EAEMC,oBAAoBA,CACzBC,OAAe,EACfC,iBAAoC,EACpC;IACAjB,kBAAkB,CAAC,MAAM;MACvB,SAAS;;MACTkB,MAAM,CAACC,0BAA0B,CAACJ,oBAAoB,CACpDC,OAAO,EACPC,iBAAiB,CAClB;IACH,CAAC,CAAC,EAAE;IACJ,IAAI,CAACG,qBAAqB,EAAE;EAC9B;EAEOC,uBAAuBA,CAACL,OAAe,EAAE;IAC9C,IAAI,CAACM,uBAAuB,EAAE;IAC9BtB,kBAAkB,CAAC,MAAM;MACvB,SAAS;;MACTkB,MAAM,CAACC,0BAA0B,CAACE,uBAAuB,CAACL,OAAO,CAAC;IACpE,CAAC,CAAC,EAAE;EACN;EAEQI,qBAAqBA,CAAA,EAAG;IAC9B,IAAI,CAACG,mBAAmB,EAAE;IAC1B,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa;IACvC,IAAI,CAACD,YAAY,CAACd,YAAY,EAAE;MAC9Bc,YAAY,CAACd,YAAY,GAAG,IAAI;MAChC,MAAMgB,WAAW,GAAGpB,UAAU,GAAG,IAAI,GAAG,KAAK;MAC7C,IAAIqB,iBAAiB,GAAG,CAAC,CAAC;MAC1BH,YAAY,CAACb,oBAAoB,GAAGV,oBAAoB,CACrD2B,KAA8B,IAAK;QAClC,SAAS;;QACT,MAAMC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;QAC/B,IAAIA,QAAQ,KAAKF,iBAAiB,EAAE;UAClC;UACA;UACA;UACA;QACF;QACAA,iBAAiB,GAAGE,QAAQ;QAC5BX,MAAM,CAACC,0BAA0B,CAACW,KAAK,CAACD,QAAQ,CAAC;MACnD,CAAC,EACDH,WAAW,GAAG,oBAAoB,CACnC;MACDF,YAAY,CAACZ,QAAQ,GAAGX,oBAAoB,CAAC,MAAM;QACjD,SAAS;;QACTiB,MAAM,CAACC,0BAA0B,CAACY,eAAe,EAAE;MACrD,CAAC,EAAEL,WAAW,GAAG,QAAQ,CAAC;MAE1B,IAAIpB,UAAU,EAAE;QACd;QACA;QACAkB,YAAY,CAACX,WAAW,GAAGZ,oBAAoB,CAAC,MAAM;UACpD,SAAS;;UACTiB,MAAM,CAACC,0BAA0B,CAACa,4BAA4B,EAAE;QAClE,CAAC,EAAE,uBAAuB,CAAC;MAC7B,CAAC,MAAM,IAAI7B,QAAQ,CAACI,EAAE,KAAK,KAAK,EAAE;QAChC;QACAiB,YAAY,CAACX,WAAW,GAAGZ,oBAAoB,CAAC,MAAM;UACpD,SAAS;;UACTiB,MAAM,CAACC,0BAA0B,CAACY,eAAe,CAAC,IAAI,CAAC;QACzD,CAAC,EAAE,cAAc,CAAC;QAClBP,YAAY,CAACV,cAAc,GAAGb,oBAAoB,CAAC,MAAM;UACvD,SAAS;;UACTiB,MAAM,CAACC,0BAA0B,CAACY,eAAe,EAAE;QACrD,CAAC,EAAE,kBAAkB,CAAC;MACxB;IACF;EACF;EAEQT,uBAAuBA,CAAA,EAAS;IACtC,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,IAAI,CAACA,mBAAmB,KAAK,CAAC,EAAE;MAClC,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa;MACvCD,YAAY,CAACd,YAAY,GAAG,KAAK;MACjC,IAAIc,YAAY,CAACb,oBAAoB,KAAK,CAAC,CAAC,EAAE;QAC5CT,sBAAsB,CAACsB,YAAY,CAACb,oBAAoB,CAAC;QACzDa,YAAY,CAACb,oBAAoB,GAAG,CAAC,CAAC;MACxC;MACA,IAAIa,YAAY,CAACZ,QAAQ,KAAK,CAAC,CAAC,EAAE;QAChCV,sBAAsB,CAACsB,YAAY,CAACZ,QAAQ,CAAC;QAC7CY,YAAY,CAACZ,QAAQ,GAAG,CAAC,CAAC;MAC5B;MACA,IAAIY,YAAY,CAACX,WAAW,KAAK,CAAC,CAAC,EAAE;QACnCX,sBAAsB,CAACsB,YAAY,CAACX,WAAW,CAAC;QAChDW,YAAY,CAACX,WAAW,GAAG,CAAC,CAAC;MAC/B;MACA,IAAIW,YAAY,CAACV,cAAc,KAAK,CAAC,CAAC,EAAE;QACtCZ,sBAAsB,CAACsB,YAAY,CAACV,cAAc,CAAC;QACnDU,YAAY,CAACV,cAAc,GAAG,CAAC,CAAC;MAClC;IACF;EACF;AACF;AAEA,SAASmB,gCAAgCA,CAAA,EAAG;EAC1C,SAAS;;EACT,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,EAA6B;EAC/D,MAAMC,SAAS,GAAG,IAAID,GAAG,EAGtB;EACH,MAAME,kBAAkB,GAAG,IAAIC,GAAG,EAAU;EAC5C,MAAMC,QAAQ,GAAG,IAAID,GAAG,EAAU;EAElC,IAAIE,YAAY,GAAG,KAAK;EACxB,IAAIC,mBAAmB,GAAG,KAAK;EAE/B,MAAMC,yBAAyB,GAAG;IAChC3B,oBAAoB,EAAEA,CACpBC,OAAe,EACfC,iBAAoC,KACjC;MACH,IAAIoB,kBAAkB,CAACM,IAAI,GAAG,CAAC,EAAE;QAC/B;QACAF,mBAAmB,GAAG,CAACnC,UAAU;MACnC;MACA4B,kBAAkB,CAACU,GAAG,CAAC5B,OAAO,EAAEC,iBAAiB,CAAC;IACpD,CAAC;IACDI,uBAAuB,EAAGL,OAAe,IAAK;MAC5C,IAAIqB,kBAAkB,CAACM,IAAI,GAAG,CAAC,EAAE;QAC/B;QACAF,mBAAmB,GAAG,CAACnC,UAAU;MACnC;MACA;MACAiC,QAAQ,CAACM,GAAG,CAAC7B,OAAO,CAAC;IACvB,CAAC;IACD8B,iBAAiB,EAAEA,CACjB9B,OAAe,EACf+B,QAAmD,KAChD;MACHP,YAAY,GAAGC,mBAAmB;MAClCL,SAAS,CAACQ,GAAG,CAAC5B,OAAO,EAAE+B,QAAQ,CAAC;MAChCV,kBAAkB,CAACQ,GAAG,CAAC7B,OAAO,CAAC;MAC/B;MACA0B,yBAAyB,CAACZ,KAAK,CAAC,CAAC,CAAC;IACpC,CAAC;IACDA,KAAK,EAAGD,QAAgB,IAAK;MAC3B,KAAK,MAAMb,OAAO,IAAIqB,kBAAkB,EAAE;QACxC,MAAMpB,iBAAiB,GAAGiB,kBAAkB,CAACc,GAAG,CAAChC,OAAO,CAAC;QACzD,IAAI,CAACC,iBAAiB,EAAE;UACtB;QACF;QACA,MAAM8B,QAAQ,GAAGX,SAAS,CAACY,GAAG,CAC5BhC,OAAO,CAC6B;QACtCC,iBAAiB,CAAED,OAAO,EAAE+B,QAAQ,EAAElB,QAAQ,CAAC;MACjD;IACF,CAAC;IACDG,4BAA4B,EAAEA,CAAA,KAAM;MAClC,IAAIO,QAAQ,CAACI,IAAI,GAAG,CAAC,EAAE;QACrB;QACAD,yBAAyB,CAACX,eAAe,EAAE;MAC7C;IACF,CAAC;IACDA,eAAe,EAAE,SAAAA,CAAA,EAAyB;MAAA,IAAxBkB,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAvD,SAAA,GAAAuD,SAAA,MAAG,KAAK;MACnC,IAAIb,kBAAkB,CAACM,IAAI,KAAK,CAAC,EAAE;QACjCJ,QAAQ,CAACa,KAAK,EAAE;QAChB;MACF;MACA,IAAIZ,YAAY,EAAE;QAChBA,YAAY,GAAG,KAAK;QACpBC,mBAAmB,GAAG,KAAK;QAC3B;MACF;MACA,KAAK,MAAMzB,OAAO,IAAIqB,kBAAkB,EAAE;QACxCgB,eAAe,CAACrC,OAAO,EAAEiC,WAAW,CAAC;MACvC;MACAZ,kBAAkB,CAACe,KAAK,EAAE;MAC1B,IAAIX,mBAAmB,EAAE;QACvB;QACA;QACA;MACF;MACAL,SAAS,CAACgB,KAAK,EAAE;MACjB,IAAIb,QAAQ,CAACI,IAAI,GAAG,CAAC,EAAE;QACrB,KAAK,MAAM3B,OAAO,IAAIuB,QAAQ,EAAE;UAC9BL,kBAAkB,CAACoB,MAAM,CAACtC,OAAO,CAAC;UAClCqC,eAAe,CAACrC,OAAO,EAAEiC,WAAW,CAAC;QACvC;QACAV,QAAQ,CAACa,KAAK,EAAE;MAClB;IACF;EACF,CAAC;EACD,OAAOV,yBAAyB;AAClC;AAEA,IAAIrC,cAAc,EAAE,EAAE;EACpB,MAAMkD,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA;IACA,IAAI,CAACnD,MAAM,EAAE,EAAE;MACb,MAAM,IAAIoD,KAAK,CACb,oFAAoF,CACrF;IACH;EACF,CAAC;EACDtC,MAAM,CAACC,0BAA0B,GAAG,IAAIsC,KAAK,CAC3C,CAAC,CAAC,EACF;IACET,GAAG,EAAEO,eAAe;IACpBX,GAAG,EAAEA,CAAA,KAAM;MACTW,eAAe,EAAE;MACjB,OAAO,KAAK;IACd;EACF,CAAC,CACF;AACH,CAAC,MAAM;EACLvD,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACTkB,MAAM,CAACC,0BAA0B,GAAGc,gCAAgC,EAAE;EACxE,CAAC,CAAC,EAAE;AACN"}
\ No newline at end of file
+{"version":3,"names":["_defineProperty","obj","key","value","_toPropertyKey","Object","defineProperty","enumerable","configurable","writable","arg","_toPrimitive","String","input","hint","prim","Symbol","toPrimitive","undefined","res","call","TypeError","Number","runOnUIImmediately","registerEventHandler","unregisterEventHandler","Platform","isJest","shouldBeUseWeb","IS_ANDROID","OS","ProgressTransitionManager","constructor","isRegistered","onTransitionProgress","onAppear","onDisappear","onSwipeDismiss","addProgressAnimation","viewTag","progressAnimation","global","ProgressTransitionRegister","registerEventHandlers","removeProgressAnimation","unregisterEventHandlers","_sharedElementCount","eventHandler","_eventHandler","eventPrefix","lastProgressValue","event","progress","frame","onTransitionEnd","onAndroidFinishTransitioning","createProgressTransitionRegister","progressAnimations","Map","snapshots","currentTransitions","Set","toRemove","skipCleaning","isTransitionRestart","progressTransitionManager","size","set","add","onTransitionStart","snapshot","get","removeViews","arguments","length","clear","_notifyAboutEnd","delete","maybeThrowError","Error","Proxy"],"sources":["ProgressTransitionManager.ts"],"sourcesContent":["'use strict';\nimport { runOnUIImmediately } from '../../threads';\nimport type {\n  ProgressAnimation,\n  SharedTransitionAnimationsValues,\n} from '../animationBuilder/commonTypes';\nimport { registerEventHandler, unregisterEventHandler } from '../../core';\nimport { Platform } from 'react-native';\nimport { isJest, shouldBeUseWeb } from '../../PlatformChecker';\n\ntype TransitionProgressEvent = {\n  closing: number;\n  goingForward: number;\n  eventName: string;\n  progress: number;\n  target: number;\n};\n\nconst IS_ANDROID = Platform.OS === 'android';\n\nexport class ProgressTransitionManager {\n  private _sharedElementCount = 0;\n  private _eventHandler = {\n    isRegistered: false,\n    onTransitionProgress: -1,\n    onAppear: -1,\n    onDisappear: -1,\n    onSwipeDismiss: -1,\n  };\n\n  public addProgressAnimation(\n    viewTag: number,\n    progressAnimation: ProgressAnimation\n  ) {\n    runOnUIImmediately(() => {\n      'worklet';\n      global.ProgressTransitionRegister.addProgressAnimation(\n        viewTag,\n        progressAnimation\n      );\n    })();\n    this.registerEventHandlers();\n  }\n\n  public removeProgressAnimation(viewTag: number) {\n    this.unregisterEventHandlers();\n    runOnUIImmediately(() => {\n      'worklet';\n      global.ProgressTransitionRegister.removeProgressAnimation(viewTag);\n    })();\n  }\n\n  private registerEventHandlers() {\n    this._sharedElementCount++;\n    const eventHandler = this._eventHandler;\n    if (!eventHandler.isRegistered) {\n      eventHandler.isRegistered = true;\n      const eventPrefix = IS_ANDROID ? 'on' : 'top';\n      let lastProgressValue = -1;\n      eventHandler.onTransitionProgress = registerEventHandler(\n        (event: TransitionProgressEvent) => {\n          'worklet';\n          const progress = event.progress;\n          if (progress === lastProgressValue) {\n            // During screen transition, handler receives two events with the same progress\n            // value for both screens, but for modals, there is only one event. To optimize\n            // performance and avoid unnecessary worklet calls, let's skip the second event.\n            return;\n          }\n          lastProgressValue = progress;\n          global.ProgressTransitionRegister.frame(progress);\n        },\n        eventPrefix + 'TransitionProgress'\n      );\n      eventHandler.onAppear = registerEventHandler(() => {\n        'worklet';\n        global.ProgressTransitionRegister.onTransitionEnd();\n      }, eventPrefix + 'Appear');\n\n      if (IS_ANDROID) {\n        // onFinishTransitioning event is available only on Android and\n        // is used to handle closing modals\n        eventHandler.onDisappear = registerEventHandler(() => {\n          'worklet';\n          global.ProgressTransitionRegister.onAndroidFinishTransitioning();\n        }, 'onFinishTransitioning');\n      } else if (Platform.OS === 'ios') {\n        // topDisappear event is required to handle closing modals on iOS\n        eventHandler.onDisappear = registerEventHandler(() => {\n          'worklet';\n          global.ProgressTransitionRegister.onTransitionEnd(true);\n        }, 'topDisappear');\n        eventHandler.onSwipeDismiss = registerEventHandler(() => {\n          'worklet';\n          global.ProgressTransitionRegister.onTransitionEnd();\n        }, 'topGestureCancel');\n      }\n    }\n  }\n\n  private unregisterEventHandlers(): void {\n    this._sharedElementCount--;\n    if (this._sharedElementCount === 0) {\n      const eventHandler = this._eventHandler;\n      eventHandler.isRegistered = false;\n      if (eventHandler.onTransitionProgress !== -1) {\n        unregisterEventHandler(eventHandler.onTransitionProgress);\n        eventHandler.onTransitionProgress = -1;\n      }\n      if (eventHandler.onAppear !== -1) {\n        unregisterEventHandler(eventHandler.onAppear);\n        eventHandler.onAppear = -1;\n      }\n      if (eventHandler.onDisappear !== -1) {\n        unregisterEventHandler(eventHandler.onDisappear);\n        eventHandler.onDisappear = -1;\n      }\n      if (eventHandler.onSwipeDismiss !== -1) {\n        unregisterEventHandler(eventHandler.onSwipeDismiss);\n        eventHandler.onSwipeDismiss = -1;\n      }\n    }\n  }\n}\n\nfunction createProgressTransitionRegister() {\n  'worklet';\n  const progressAnimations = new Map<number, ProgressAnimation>();\n  const snapshots = new Map<\n    number,\n    Partial<SharedTransitionAnimationsValues>\n  >();\n  const currentTransitions = new Set<number>();\n  const toRemove = new Set<number>();\n\n  let skipCleaning = false;\n  let isTransitionRestart = false;\n\n  const progressTransitionManager = {\n    addProgressAnimation: (\n      viewTag: number,\n      progressAnimation: ProgressAnimation\n    ) => {\n      if (currentTransitions.size > 0) {\n        // there is no need to prevent cleaning on android\n        isTransitionRestart = !IS_ANDROID;\n      }\n      progressAnimations.set(viewTag, progressAnimation);\n    },\n    removeProgressAnimation: (viewTag: number) => {\n      if (currentTransitions.size > 0) {\n        // there is no need to prevent cleaning on android\n        isTransitionRestart = !IS_ANDROID;\n      }\n      // Remove the animation config after the transition is finished\n      toRemove.add(viewTag);\n    },\n    onTransitionStart: (\n      viewTag: number,\n      snapshot: Partial<SharedTransitionAnimationsValues>\n    ) => {\n      skipCleaning = isTransitionRestart;\n      snapshots.set(viewTag, snapshot);\n      currentTransitions.add(viewTag);\n      // set initial style for re-parented components\n      progressTransitionManager.frame(0);\n    },\n    frame: (progress: number) => {\n      for (const viewTag of currentTransitions) {\n        const progressAnimation = progressAnimations.get(viewTag);\n        if (!progressAnimation) {\n          continue;\n        }\n        const snapshot = snapshots.get(\n          viewTag\n        )! as SharedTransitionAnimationsValues;\n        progressAnimation!(viewTag, snapshot, progress);\n      }\n    },\n    onAndroidFinishTransitioning: () => {\n      if (toRemove.size > 0) {\n        // it should be ran only on modal closing\n        progressTransitionManager.onTransitionEnd();\n      }\n    },\n    onTransitionEnd: (removeViews = false) => {\n      if (currentTransitions.size === 0) {\n        toRemove.clear();\n        return;\n      }\n      if (skipCleaning) {\n        skipCleaning = false;\n        isTransitionRestart = false;\n        return;\n      }\n      for (const viewTag of currentTransitions) {\n        _notifyAboutEnd(viewTag, removeViews);\n      }\n      currentTransitions.clear();\n      if (isTransitionRestart) {\n        // on transition restart, progressAnimations should be saved\n        // because they potentially can be used in the next transition\n        return;\n      }\n      snapshots.clear();\n      if (toRemove.size > 0) {\n        for (const viewTag of toRemove) {\n          progressAnimations.delete(viewTag);\n          _notifyAboutEnd(viewTag, removeViews);\n        }\n        toRemove.clear();\n      }\n    },\n  };\n  return progressTransitionManager;\n}\n\nif (shouldBeUseWeb()) {\n  const maybeThrowError = () => {\n    // Jest attempts to access a property of this object to check if it is a Jest mock\n    // so we can't throw an error in the getter.\n    if (!isJest() && !isChromeDebugger()) {\n      throw new Error(\n        '[Reanimated] `ProgressTransitionRegister` is not available on non-native platform.'\n      );\n    }\n  };\n  global.ProgressTransitionRegister = new Proxy(\n    {} as ProgressTransitionRegister,\n    {\n      get: maybeThrowError,\n      set: () => {\n        maybeThrowError();\n        return false;\n      },\n    }\n  );\n} else {\n  runOnUIImmediately(() => {\n    'worklet';\n    global.ProgressTransitionRegister = createProgressTransitionRegister();\n  })();\n}\n\nexport type ProgressTransitionRegister = ReturnType<\n  typeof createProgressTransitionRegister\n>;\n"],"mappings":"AAAA,YAAY;;AAAC,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA,IAAAD,GAAA,GAAAE,cAAA,CAAAF,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAAI,MAAA,CAAAC,cAAA,CAAAL,GAAA,EAAAC,GAAA,IAAAC,KAAA,EAAAA,KAAA,EAAAI,UAAA,QAAAC,YAAA,QAAAC,QAAA,oBAAAR,GAAA,CAAAC,GAAA,IAAAC,KAAA,WAAAF,GAAA;AAAA,SAAAG,eAAAM,GAAA,QAAAR,GAAA,GAAAS,YAAA,CAAAD,GAAA,2BAAAR,GAAA,gBAAAA,GAAA,GAAAU,MAAA,CAAAV,GAAA;AAAA,SAAAS,aAAAE,KAAA,EAAAC,IAAA,eAAAD,KAAA,iBAAAA,KAAA,kBAAAA,KAAA,MAAAE,IAAA,GAAAF,KAAA,CAAAG,MAAA,CAAAC,WAAA,OAAAF,IAAA,KAAAG,SAAA,QAAAC,GAAA,GAAAJ,IAAA,CAAAK,IAAA,CAAAP,KAAA,EAAAC,IAAA,2BAAAK,GAAA,sBAAAA,GAAA,YAAAE,SAAA,4DAAAP,IAAA,gBAAAF,MAAA,GAAAU,MAAA,EAAAT,KAAA;AACb,SAASU,kBAAkB,QAAQ,eAAe;AAKlD,SAASC,oBAAoB,EAAEC,sBAAsB,QAAQ,YAAY;AACzE,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAU9D,MAAMC,UAAU,GAAGH,QAAQ,CAACI,EAAE,KAAK,SAAS;AAE5C,OAAO,MAAMC,yBAAyB,CAAC;EAAAC,YAAA;IAAAhC,eAAA,8BACP,CAAC;IAAAA,eAAA,wBACP;MACtBiC,YAAY,EAAE,KAAK;MACnBC,oBAAoB,EAAE,CAAC,CAAC;MACxBC,QAAQ,EAAE,CAAC,CAAC;MACZC,WAAW,EAAE,CAAC,CAAC;MACfC,cAAc,EAAE,CAAC;IACnB,CAAC;EAAA;EAEMC,oBAAoBA,CACzBC,OAAe,EACfC,iBAAoC,EACpC;IACAjB,kBAAkB,CAAC,MAAM;MACvB,SAAS;;MACTkB,MAAM,CAACC,0BAA0B,CAACJ,oBAAoB,CACpDC,OAAO,EACPC,iBAAiB,CAClB;IACH,CAAC,CAAC,EAAE;IACJ,IAAI,CAACG,qBAAqB,EAAE;EAC9B;EAEOC,uBAAuBA,CAACL,OAAe,EAAE;IAC9C,IAAI,CAACM,uBAAuB,EAAE;IAC9BtB,kBAAkB,CAAC,MAAM;MACvB,SAAS;;MACTkB,MAAM,CAACC,0BAA0B,CAACE,uBAAuB,CAACL,OAAO,CAAC;IACpE,CAAC,CAAC,EAAE;EACN;EAEQI,qBAAqBA,CAAA,EAAG;IAC9B,IAAI,CAACG,mBAAmB,EAAE;IAC1B,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa;IACvC,IAAI,CAACD,YAAY,CAACd,YAAY,EAAE;MAC9Bc,YAAY,CAACd,YAAY,GAAG,IAAI;MAChC,MAAMgB,WAAW,GAAGpB,UAAU,GAAG,IAAI,GAAG,KAAK;MAC7C,IAAIqB,iBAAiB,GAAG,CAAC,CAAC;MAC1BH,YAAY,CAACb,oBAAoB,GAAGV,oBAAoB,CACrD2B,KAA8B,IAAK;QAClC,SAAS;;QACT,MAAMC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;QAC/B,IAAIA,QAAQ,KAAKF,iBAAiB,EAAE;UAClC;UACA;UACA;UACA;QACF;QACAA,iBAAiB,GAAGE,QAAQ;QAC5BX,MAAM,CAACC,0BAA0B,CAACW,KAAK,CAACD,QAAQ,CAAC;MACnD,CAAC,EACDH,WAAW,GAAG,oBAAoB,CACnC;MACDF,YAAY,CAACZ,QAAQ,GAAGX,oBAAoB,CAAC,MAAM;QACjD,SAAS;;QACTiB,MAAM,CAACC,0BAA0B,CAACY,eAAe,EAAE;MACrD,CAAC,EAAEL,WAAW,GAAG,QAAQ,CAAC;MAE1B,IAAIpB,UAAU,EAAE;QACd;QACA;QACAkB,YAAY,CAACX,WAAW,GAAGZ,oBAAoB,CAAC,MAAM;UACpD,SAAS;;UACTiB,MAAM,CAACC,0BAA0B,CAACa,4BAA4B,EAAE;QAClE,CAAC,EAAE,uBAAuB,CAAC;MAC7B,CAAC,MAAM,IAAI7B,QAAQ,CAACI,EAAE,KAAK,KAAK,EAAE;QAChC;QACAiB,YAAY,CAACX,WAAW,GAAGZ,oBAAoB,CAAC,MAAM;UACpD,SAAS;;UACTiB,MAAM,CAACC,0BAA0B,CAACY,eAAe,CAAC,IAAI,CAAC;QACzD,CAAC,EAAE,cAAc,CAAC;QAClBP,YAAY,CAACV,cAAc,GAAGb,oBAAoB,CAAC,MAAM;UACvD,SAAS;;UACTiB,MAAM,CAACC,0BAA0B,CAACY,eAAe,EAAE;QACrD,CAAC,EAAE,kBAAkB,CAAC;MACxB;IACF;EACF;EAEQT,uBAAuBA,CAAA,EAAS;IACtC,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,IAAI,CAACA,mBAAmB,KAAK,CAAC,EAAE;MAClC,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa;MACvCD,YAAY,CAACd,YAAY,GAAG,KAAK;MACjC,IAAIc,YAAY,CAACb,oBAAoB,KAAK,CAAC,CAAC,EAAE;QAC5CT,sBAAsB,CAACsB,YAAY,CAACb,oBAAoB,CAAC;QACzDa,YAAY,CAACb,oBAAoB,GAAG,CAAC,CAAC;MACxC;MACA,IAAIa,YAAY,CAACZ,QAAQ,KAAK,CAAC,CAAC,EAAE;QAChCV,sBAAsB,CAACsB,YAAY,CAACZ,QAAQ,CAAC;QAC7CY,YAAY,CAACZ,QAAQ,GAAG,CAAC,CAAC;MAC5B;MACA,IAAIY,YAAY,CAACX,WAAW,KAAK,CAAC,CAAC,EAAE;QACnCX,sBAAsB,CAACsB,YAAY,CAACX,WAAW,CAAC;QAChDW,YAAY,CAACX,WAAW,GAAG,CAAC,CAAC;MAC/B;MACA,IAAIW,YAAY,CAACV,cAAc,KAAK,CAAC,CAAC,EAAE;QACtCZ,sBAAsB,CAACsB,YAAY,CAACV,cAAc,CAAC;QACnDU,YAAY,CAACV,cAAc,GAAG,CAAC,CAAC;MAClC;IACF;EACF;AACF;AAEA,SAASmB,gCAAgCA,CAAA,EAAG;EAC1C,SAAS;;EACT,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,EAA6B;EAC/D,MAAMC,SAAS,GAAG,IAAID,GAAG,EAGtB;EACH,MAAME,kBAAkB,GAAG,IAAIC,GAAG,EAAU;EAC5C,MAAMC,QAAQ,GAAG,IAAID,GAAG,EAAU;EAElC,IAAIE,YAAY,GAAG,KAAK;EACxB,IAAIC,mBAAmB,GAAG,KAAK;EAE/B,MAAMC,yBAAyB,GAAG;IAChC3B,oBAAoB,EAAEA,CACpBC,OAAe,EACfC,iBAAoC,KACjC;MACH,IAAIoB,kBAAkB,CAACM,IAAI,GAAG,CAAC,EAAE;QAC/B;QACAF,mBAAmB,GAAG,CAACnC,UAAU;MACnC;MACA4B,kBAAkB,CAACU,GAAG,CAAC5B,OAAO,EAAEC,iBAAiB,CAAC;IACpD,CAAC;IACDI,uBAAuB,EAAGL,OAAe,IAAK;MAC5C,IAAIqB,kBAAkB,CAACM,IAAI,GAAG,CAAC,EAAE;QAC/B;QACAF,mBAAmB,GAAG,CAACnC,UAAU;MACnC;MACA;MACAiC,QAAQ,CAACM,GAAG,CAAC7B,OAAO,CAAC;IACvB,CAAC;IACD8B,iBAAiB,EAAEA,CACjB9B,OAAe,EACf+B,QAAmD,KAChD;MACHP,YAAY,GAAGC,mBAAmB;MAClCL,SAAS,CAACQ,GAAG,CAAC5B,OAAO,EAAE+B,QAAQ,CAAC;MAChCV,kBAAkB,CAACQ,GAAG,CAAC7B,OAAO,CAAC;MAC/B;MACA0B,yBAAyB,CAACZ,KAAK,CAAC,CAAC,CAAC;IACpC,CAAC;IACDA,KAAK,EAAGD,QAAgB,IAAK;MAC3B,KAAK,MAAMb,OAAO,IAAIqB,kBAAkB,EAAE;QACxC,MAAMpB,iBAAiB,GAAGiB,kBAAkB,CAACc,GAAG,CAAChC,OAAO,CAAC;QACzD,IAAI,CAACC,iBAAiB,EAAE;UACtB;QACF;QACA,MAAM8B,QAAQ,GAAGX,SAAS,CAACY,GAAG,CAC5BhC,OAAO,CAC6B;QACtCC,iBAAiB,CAAED,OAAO,EAAE+B,QAAQ,EAAElB,QAAQ,CAAC;MACjD;IACF,CAAC;IACDG,4BAA4B,EAAEA,CAAA,KAAM;MAClC,IAAIO,QAAQ,CAACI,IAAI,GAAG,CAAC,EAAE;QACrB;QACAD,yBAAyB,CAACX,eAAe,EAAE;MAC7C;IACF,CAAC;IACDA,eAAe,EAAE,SAAAA,CAAA,EAAyB;MAAA,IAAxBkB,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAvD,SAAA,GAAAuD,SAAA,MAAG,KAAK;MACnC,IAAIb,kBAAkB,CAACM,IAAI,KAAK,CAAC,EAAE;QACjCJ,QAAQ,CAACa,KAAK,EAAE;QAChB;MACF;MACA,IAAIZ,YAAY,EAAE;QAChBA,YAAY,GAAG,KAAK;QACpBC,mBAAmB,GAAG,KAAK;QAC3B;MACF;MACA,KAAK,MAAMzB,OAAO,IAAIqB,kBAAkB,EAAE;QACxCgB,eAAe,CAACrC,OAAO,EAAEiC,WAAW,CAAC;MACvC;MACAZ,kBAAkB,CAACe,KAAK,EAAE;MAC1B,IAAIX,mBAAmB,EAAE;QACvB;QACA;QACA;MACF;MACAL,SAAS,CAACgB,KAAK,EAAE;MACjB,IAAIb,QAAQ,CAACI,IAAI,GAAG,CAAC,EAAE;QACrB,KAAK,MAAM3B,OAAO,IAAIuB,QAAQ,EAAE;UAC9BL,kBAAkB,CAACoB,MAAM,CAACtC,OAAO,CAAC;UAClCqC,eAAe,CAACrC,OAAO,EAAEiC,WAAW,CAAC;QACvC;QACAV,QAAQ,CAACa,KAAK,EAAE;MAClB;IACF;EACF,CAAC;EACD,OAAOV,yBAAyB;AAClC;AAEA,IAAIrC,cAAc,EAAE,EAAE;EACpB,MAAMkD,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA;IACA,IAAI,CAACnD,MAAM,EAAE,EAAE;MACb,MAAM,IAAIoD,KAAK,CACb,oFAAoF,CACrF;IACH;EACF,CAAC;EACDtC,MAAM,CAACC,0BAA0B,GAAG,IAAIsC,KAAK,CAC3C,CAAC,CAAC,EACF;IACET,GAAG,EAAEO,eAAe;IACpBX,GAAG,EAAEA,CAAA,KAAM;MACTW,eAAe,EAAE;MACjB,OAAO,KAAK;IACd;EACF,CAAC,CACF;AACH,CAAC,MAAM;EACLvD,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACTkB,MAAM,CAACC,0BAA0B,GAAGc,gCAAgC,EAAE;EACxE,CAAC,CAAC,EAAE;AACN"}
\ No newline at end of file
diff --git a/node_modules/react-native-reanimated/src/reanimated2/UpdateProps.ts b/node_modules/react-native-reanimated/src/reanimated2/UpdateProps.ts
index 597c153..ab5912d 100644
--- a/node_modules/react-native-reanimated/src/reanimated2/UpdateProps.ts
+++ b/node_modules/react-native-reanimated/src/reanimated2/UpdateProps.ts
@@ -5,7 +5,7 @@ import type { ShadowNodeWrapper, SharedValue, StyleProps } from './commonTypes';
 import type { AnimatedStyle } from './helperTypes';
 import type { Descriptor } from './hook/commonTypes';
 import { _updatePropsJS } from './js-reanimated';
-import { isFabric, isJest, shouldBeUseWeb } from './PlatformChecker';
+import { isChromeDebugger, isFabric, isJest, shouldBeUseWeb } from './PlatformChecker';
 import type { ViewRefSet } from './ViewDescriptorsSet';
 import { runOnUIImmediately } from './threads';
 
@@ -120,7 +120,7 @@ if (shouldBeUseWeb()) {
   const maybeThrowError = () => {
     // Jest attempts to access a property of this object to check if it is a Jest mock
     // so we can't throw an error in the getter.
-    if (!isJest()) {
+    if (!isJest() && !isChromeDebugger()) {
       throw new Error(
         '[Reanimated] `UpdatePropsManager` is not available on non-native platform.'
       );
diff --git a/node_modules/react-native-reanimated/src/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts b/node_modules/react-native-reanimated/src/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts
index b4c436a..1d11d83 100644
--- a/node_modules/react-native-reanimated/src/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts
+++ b/node_modules/react-native-reanimated/src/reanimated2/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts
@@ -6,7 +6,7 @@ import type {
 } from '../animationBuilder/commonTypes';
 import { registerEventHandler, unregisterEventHandler } from '../../core';
 import { Platform } from 'react-native';
-import { isJest, shouldBeUseWeb } from '../../PlatformChecker';
+import { isChromeDebugger, isJest, shouldBeUseWeb } from '../../PlatformChecker';
 
 type TransitionProgressEvent = {
   closing: number;
@@ -219,7 +219,7 @@ if (shouldBeUseWeb()) {
   const maybeThrowError = () => {
     // Jest attempts to access a property of this object to check if it is a Jest mock
     // so we can't throw an error in the getter.
-    if (!isJest()) {
+    if (!isJest() && !isChromeDebugger()) {
       throw new Error(
         '[Reanimated] `ProgressTransitionRegister` is not available on non-native platform.'
       );
