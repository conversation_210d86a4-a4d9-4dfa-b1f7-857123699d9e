#!/bin/bash
# Unified Package Compatibility Fixes
# This patch applies all necessary fixes for package compatibility after npm install

echo "🔧 Applying unified package compatibility fixes..."

# Function to safely add namespace to build.gradle
add_namespace_safely() {
    local gradle_file="$1"
    local namespace="$2"
    local package_name="$3"
    
    if [ -f "$gradle_file" ]; then
        if ! grep -q "namespace" "$gradle_file"; then
            echo "📦 Adding namespace to $package_name"
            # Create a temporary file with the namespace added
            awk '/android {/ { print; print "    namespace \"'$namespace'\""; next } 1' "$gradle_file" > "$gradle_file.tmp"
            mv "$gradle_file.tmp" "$gradle_file"
        else
            echo "✅ $package_name already has namespace"
        fi
        
        # Also ensure BuildConfig is enabled for Firebase packages
        if [[ "$package_name" == *"firebase"* ]] && ! grep -q "buildConfig true" "$gradle_file"; then
            echo "📦 Enabling BuildConfig for $package_name"
            awk '/android {/ { print; print "  buildFeatures {"; print "    buildConfig true"; print "  }"; next } 1' "$gradle_file" > "$gradle_file.tmp"
            mv "$gradle_file.tmp" "$gradle_file"
        fi
        

        # Fix react-native-maps missing ListenableFuture dependency
        if [[ "$package_name" == "react-native-maps" ]] && ! grep -q "com.google.guava:guava" "$gradle_file"; then
            echo "📦 Adding Guava dependency for $package_name"
            sed -i.bak '/dependencies {/a\
    implementation "com.google.guava:guava:31.1-android"
' "$gradle_file"
        fi
        
        # Fix react-native-orientation-locker API 33+ constants for API 31
        if [[ "$package_name" == "react-native-orientation-locker" ]]; then
            echo "📦 Fixing API 33+ constants for $package_name"
            orientation_file="$module_dir/src/main/java/org/wonday/orientation/OrientationModule.java"
            if [ -f "$orientation_file" ] && grep -q "Context.RECEIVER_EXPORTED" "$orientation_file"; then
                # Replace the API 33+ constants with 0 for compatibility with API 31
                sed -i.bak 's/exported ? Context\.RECEIVER_EXPORTED : Context\.RECEIVER_NOT_EXPORTED/0/g' "$orientation_file"
            fi
        fi
    else
        echo "⚠️  $package_name build.gradle not found"
    fi
}

# Apply namespace fixes to key packages
echo "🔧 Fixing Android namespaces for packages..."

add_namespace_safely "node_modules/@react-native-community/blur/android/build.gradle" "com.cmcewen.blurview" "@react-native-community/blur"
add_namespace_safely "node_modules/@react-native-community/datetimepicker/android/build.gradle" "com.reactcommunity.rndatetimepicker" "@react-native-community/datetimepicker"
add_namespace_safely "node_modules/@react-native-community/netinfo/android/build.gradle" "com.reactnativecommunity.netinfo" "@react-native-community/netinfo"
add_namespace_safely "node_modules/@react-native-community/masked-view/android/build.gradle" "org.reactnative.maskedview" "@react-native-community/masked-view"
add_namespace_safely "node_modules/react-native-notifications/lib/android/build.gradle" "com.wix.reactnativenotifications" "react-native-notifications"
add_namespace_safely "node_modules/react-native-gesture-handler/android/build.gradle" "com.swmansion.gesturehandler" "react-native-gesture-handler"
add_namespace_safely "node_modules/react-native-reanimated/android/build.gradle" "com.swmansion.reanimated" "react-native-reanimated"
add_namespace_safely "node_modules/@intercom/intercom-react-native/android/build.gradle" "com.intercom.reactnative" "@intercom/intercom-react-native"
add_namespace_safely "node_modules/mixpanel-react-native/android/build.gradle" "com.mixpanel.reactnative" "mixpanel-react-native"
add_namespace_safely "node_modules/react-native-maps/lib/android/build.gradle" "com.airbnb.android.react.maps" "react-native-maps"
add_namespace_safely "node_modules/react-native-image-picker/android/build.gradle" "com.imagepicker" "react-native-image-picker"
add_namespace_safely "node_modules/react-native-document-picker/android/build.gradle" "com.reactnativedocumentpicker" "react-native-document-picker"
add_namespace_safely "node_modules/react-native-branch/android/build.gradle" "io.branch.rnbranch" "react-native-branch"
add_namespace_safely "node_modules/@react-native-firebase/app/android/build.gradle" "io.invertase.firebase.app" "@react-native-firebase/app"
add_namespace_safely "node_modules/@react-native-firebase/analytics/android/build.gradle" "io.invertase.firebase.analytics" "@react-native-firebase/analytics"
add_namespace_safely "node_modules/@react-native-firebase/messaging/android/build.gradle" "io.invertase.firebase.messaging" "@react-native-firebase/messaging"
add_namespace_safely "node_modules/@react-native-firebase/crashlytics/android/build.gradle" "io.invertase.firebase.crashlytics" "@react-native-firebase/crashlytics"
add_namespace_safely "node_modules/react-native-pdf/android/build.gradle" "org.wonday.pdf" "react-native-pdf"
add_namespace_safely "node_modules/react-native-file-viewer/android/build.gradle" "com.vinzscam.reactnativefileviewer" "react-native-file-viewer"
add_namespace_safely "node_modules/react-native-svg/android/build.gradle" "com.horcrux.svg" "react-native-svg"
add_namespace_safely "node_modules/react-native-blob-util/android/build.gradle" "com.ReactNativeBlobUtil" "react-native-blob-util"
add_namespace_safely "node_modules/react-native-code-push/android/app/build.gradle" "com.microsoft.codepush.react" "react-native-code-push"

echo "✅ Android namespace fixes completed!"

# Fix PackageList.java autolinking issues
echo "🔧 Fixing PackageList.java autolinking issues..."
packagelist_file="android/app/build/generated/rncli/src/main/java/com/facebook/react/PackageList.java"
if [ -f "$packagelist_file" ]; then
    # Fix undefined clipboard import
    if grep -q "import undefined.ClipboardPackage;" "$packagelist_file"; then
        echo "📦 Fixing undefined ClipboardPackage import"
        sed -i.bak 's/import undefined\.ClipboardPackage;/import com.reactnativecommunity.clipboard.ClipboardPackage;/g' "$packagelist_file"
    fi
fi


# Fix BlurViewManager implementation for @react-native-community/blur
if [ -f "node_modules/@react-native-community/blur/android/src/main/java/com/cmcewen/blurview/BlurViewManager.java" ]; then
    if grep -q "setBlurAlgorithm\|setHasFixedTransformationMatrix" "node_modules/@react-native-community/blur/android/src/main/java/com/cmcewen/blurview/BlurViewManager.java"; then
        echo "📦 Fixing BlurViewManager implementation - simplifying setup chain"
        # Replace the complex setup chain with the simplified version
        perl -i.bak -pe 'BEGIN{undef $/;} s/blurView\.setupWith\(rootView\)\s*\.setFrameClearDrawable\(windowBackground\)\s*\.setBlurAlgorithm\(new RenderScriptBlur\(ctx\)\)\s*\.setBlurRadius\(defaultRadius\)\s*\.setHasFixedTransformationMatrix\(false\);/blurView.setupWith(rootView)\n            .setFrameClearDrawable(windowBackground)\n            .setBlurRadius(defaultRadius);/gs' "node_modules/@react-native-community/blur/android/src/main/java/com/cmcewen/blurview/BlurViewManager.java"
    else
        echo "✅ BlurViewManager already has correct simplified implementation"
    fi
fi

# iOS fixes (only if files exist and need fixing)
echo "🔧 Applying iOS fixes..."

# Fix boost.podspec URL if needed
if [ -f "node_modules/react-native/third-party-podspecs/boost.podspec" ]; then
    if grep -q "boostorg.jfrog.io" "node_modules/react-native/third-party-podspecs/boost.podspec"; then
        echo "📦 Fixing boost.podspec URL"
        sed -i.bak 's|https://boostorg.jfrog.io/artifactory/main/release/|https://archives.boost.io/release/|g' node_modules/react-native/third-party-podspecs/boost.podspec
    else
        echo "✅ boost.podspec already fixed"
    fi
fi


echo "✅ iOS fixes completed!"
echo "🎉 All package compatibility fixes applied successfully!" 