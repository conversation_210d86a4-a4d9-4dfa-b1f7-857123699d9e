module.exports = {
  dependencies: {
    // React Native 0.68.0 compatible autolinking configuration
    'react-native-vector-icons': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-vector-icons/android',
          packageImportPath: 'import com.oblador.vectoricons.VectorIconsPackage;',
        },
      },
    },
    '@react-native-community/blur': {
      platforms: {
        android: {
          sourceDir: '../node_modules/@react-native-community/blur/android',
          packageImportPath: 'import com.cmcewen.blurview.BlurViewPackage;',
        },
      },
    },
    '@react-native-community/netinfo': {
      platforms: {
        android: {
          sourceDir: '../node_modules/@react-native-community/netinfo/android',
          packageImportPath: 'import com.reactnativecommunity.netinfo.NetInfoPackage;',
        },
      },
    },
    '@react-native-firebase/analytics': {
      platforms: {
        android: {
          sourceDir: '../node_modules/@react-native-firebase/analytics/android',
          packageImportPath: 'import io.invertase.firebase.analytics.ReactNativeFirebaseAnalyticsPackage;',
        },
      },
    },
    'react-native-file-viewer': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-file-viewer/android',
          packageImportPath: 'import com.vinzscam.reactnativefileviewer.RNFileViewerPackage;',
        },
      },
    },
    'react-native-image-crop-picker': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-image-crop-picker/android',
          packageImportPath: 'import com.reactnative.ivpusic.imagepicker.PickerPackage;',
        },
      },
    },
    'react-native-notifications': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-notifications/lib/android',
          packageImportPath: 'import com.wix.reactnativenotifications.RNNotificationsPackage;',
        },
      },
    },
    'react-native-permissions': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-permissions/android',
          packageImportPath: 'import com.zoontek.rnpermissions.RNPermissionsPackage;',
        },
      },
    },
    'react-native-safe-area-context': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-safe-area-context/android',
          packageImportPath: 'import com.th3rdwave.safeareacontext.SafeAreaContextPackage;',
        },
      },
    },
    'react-native-share': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-share/android',
          packageImportPath: 'import cl.json.RNSharePackage;',
        },
      },
    },
    'react-native-svg': {
      platforms: {
        android: {
          sourceDir: '../node_modules/react-native-svg/android',
          packageImportPath: 'import com.horcrux.svg.SvgPackage;',
        },
      },
    },
    '@react-native-clipboard/clipboard': {
      platforms: {
        android: {
          sourceDir: '../node_modules/@react-native-clipboard/clipboard/android',
          packageImportPath: 'import com.reactnativecommunity.clipboard.ClipboardPackage;',
        },
      },
    },
  },
  project: {
    android: {
      sourceDir: './android',
      appName: 'app',
    },
  },
};


