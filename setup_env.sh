#!/bin/bash
ENVIRONMENT=$1
DIR=$(dirname "$0")

echo "FOLLO - SETUP ENVIRONMENT"

echo "Copy files to locations"
cp "${DIR}/environment/${ENVIRONMENT}/AndroidManifest.xml" android/app/src/main/AndroidManifest.xml
cp "${DIR}/environment/${ENVIRONMENT}/Configs.json" src/configs/Configs.json
cp "${DIR}/environment/${ENVIRONMENT}/google-services.json" android/app/google-services.json
cp "${DIR}/environment/${ENVIRONMENT}/GoogleService-Info.plist" ios/FolloIT/GoogleService-Info.plist
cp "${DIR}/environment/${ENVIRONMENT}/Info.plist" ios/FolloIT/Info.plist
cp "${DIR}/environment/${ENVIRONMENT}/strings.xml" android/app/src/main/res/values/strings.xml