# must be unique in a given SonarQube instance

# sonar.projectKey=folloit-reactnative

# this is the name and version displayed in the SonarQube UI. Was mandatory prior to SonarQube 6.1.

# sonar.projectName=folloit-reactnative

sonar.projectVersion=1.0

sonar.host.url=https://sonarqube.optisolbusiness.com

sonar.sources=src

sonar.exclusions=**/node_modules/**,babel.config.js,android/**/*.java


sonar.test.inclusions=**/*.test.js

sonar.typescript.lcov.reportPaths=coverage/lcov.info

# sonar.coverage.exclusions=**/database/**/*.*

sonar.cpd.inclusions=src

# sonar.cpd.exclusions=**/database/**/*.*

# sonar.login=****************************************
# sonar.login=sqp_1004a0292fb6479b013ce1afb01b521837169082

# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.

# This property is optional if sonar.modules is set.

# sonar.sources=src
# sonar.exclusions=src/redux/**/*
# sonar.cpd.exclusions=src/utils/**/*

# Encoding of the source code. Default is default system encoding

sonar.sourceEncoding=UTF-8

# Authentication credentials

# sonar.login=admin

# sonar.password=Optisol@200111