import analytics from "@react-native-firebase/analytics";
import {Reports} from "../configs/Configs.json";
const screenAnalytics = [
  {
    screenName: "Landing Screen",
    screen_class: "Landing Screen",
  },
  {
    screenName: "Login",
    screen_class: "Login",
  },
  {
    screenName: "SignUp",
    screen_class: "SignUp",
  },
  {
    screenName: "DashBoard",
    screen_class: "DashBoard",
  },
  {
    screenName: "Notifications",
    screen_class: "Notifications",
  },
  {
    screenName: "Setting",
    screen_class: "Setting",
  },
  {
    screenName: "Members",
    screen_class: "Members",
  },
  {
    screenName: "Invite Member",
    screen_class: "Invite Member",
  },
  {
    screenName: "Edit Member",
    screen_class: "Edit Member",
  },
  {
    screenName: "Companies",
    screen_class: "Companies",
  },
  {
    screenName: "Add Company",
    screen_class: "Add Company",
  },
  {
    screenName: "Edit Company",
    screen_class: "Edit Company",
  },
  {
    screenName: "Gates",
    screen_class: "Gates",
  },
  {
    screenName: "Add Gate",
    screen_class: "Add Gate",
  },
  {
    screenName: "Edit Gate",
    screen_class: "Edit Gate",
  },
  {
    screenName: "Equipments",
    screen_class: "Equipments",
  },
  {
    screenName: "New Equipment",
    screen_class: "New Equipment",
  },
  {
    screenName: "Edit Equipment",
    screen_class: "Edit Equipment",
  },
  {
    screenName: "Definable Fetaures of Work",
    screen_class: "Definable Fetaures of Work",
  },
  {
    screenName: "Add Definable Fetaures of Work",
    screen_class: "Add Definable Fetaures of Work",
  },
  {
    screenName: "Edit Definable Fetaures of Work",
    screen_class: "Edit Definable Fetaures of Work",
  },
  {
    screenName: "Void List",
    screen_class: "Void List",
  },
  {
    screenName: "Profile",
    screen_class: "Profile",
  },
  {
    screenName: "Deliveries",
    screen_class: "Deliveries",
  },
  {
    screenName: "Crane",
    screen_class: "Crane",
  },
  {
    screenName: "Concrete",
    screen_class: "Concrete",
  },
  {
    screenName: "Delivery Details",
    screen_class: "Delivery Details",
  },
  {
    screenName: "Crane Details",
    screen_class: "Crane Details",
  },
  {
    screenName: "Concrete Details",
    screen_class: "Concrete Details",
  },
  {
    screenName: "New Delivery Request",
    screen_class: "New Delivery Request",
  },
  {
    screenName: "New Pick Request",
    screen_class: "New Pick Request",
  },
  {
    screenName: "New Concrete Request",
    screen_class: "New Concrete Request",
  },
  {
    screenName: "Edit Delivery Request",
    screen_class: "Edit Delivery Request",
  },
  {
    screenName: "Edit Pick Request",
    screen_class: "Edit Pick Request",
  },
  {
    screenName: "Edit Concrete Request",
    screen_class: "Edit Concrete Request",
  },
  {
    screenName: "Delivery Calendar",
    screen_class: "Delivery Calendar",
  },
  {
    screenName: "Crane Calendar",
    screen_class: "Crane Calendar",
  },
  {
    screenName: "Concrete Calendar",
    screen_class: "Concrete Calendar",
  },
];


export const trackScreen = async (props) => {
  const SData = screenAnalytics.filter((ele) => ele.screenName == props);
 
  if (!Object.keys(SData).length) return;
  if (Reports.analytics) {
    await analytics().logScreenView({
      screen_name: SData[0].screenName,
      screen_class: SData[0].screen_class,
    });
  }
 
};

export const trackEvent = async props => {
  if (Reports.analytics) {
  await analytics().logEvent(props, {
    Date:new Date().toUTCString()
  });
}
  };
