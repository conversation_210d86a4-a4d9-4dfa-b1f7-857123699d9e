import { Mixpanel } from "mixpanel-react-native";
import { Platform } from "react-native";
import { Reports } from "../configs/Configs.json";

export const mixPanelTrackEvent = async (eventName, prop) => {
  if (Reports.analytics) {
    let param = {...prop, isMobile: true, platform: Platform.OS}
    const mixpanel = new Mixpanel("a7f3fd49a6dfdbbe81727f3beadcd383");
    mixpanel.track(eventName, param);
  }
};

export const mixpanelLogin = async (param) => {
  if (Reports.analytics) {
    const data = param.userDetails;
    const mixpanel = new Mixpanel("a7f3fd49a6dfdbbe81727f3beadcd383");
    mixpanel.identify(`${data.email}`);
    mixpanel.getPeople().set({
      $first_name: `${data.firstName}`,
      $last_name: `${data.firstName}`,
      $name: `${data.firstName}`,
      $email: `${data.email}`,
      $phone: `${data.phoneNumber}`,
      $avatar: `${data.profilePic}`,
    });
  }
};
