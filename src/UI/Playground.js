import React, { Component } from "react";
import {
  TextInput,
  View,
  FlatList,
  Text,
  TouchableOpacity,

} from "react-native";
import axios from "axios";

const googleAutoCompleteAPI = (apiKey, keyWord) =>
  `https://maps.googleapis.com/maps/api/place/autocomplete/json?key=${apiKey}&input=${keyWord}`;

const API_KEY = "AIzaSyB2EHXZ7yQZ3KItOpshW2F4DJ5ewmagOWU";

const Places = ({ item }) => {
  return (
    <TouchableOpacity
      style={{ width: "90%", alignSelf: "center", padding: 10 }}
    >
      <Text style={{ fontSize: 15 }}>{item.description}</Text>
    </TouchableOpacity>
  );
};

const PlaceList = ({ source, onSelectPlace }) => {

  return (
    <FlatList
      data={source}
      renderItem={({ item }) => {
       
        return <Places item={item} />;
      }}
    />
  );
};

export default class Playground extends Component {
  constructor(props) {
    super(props);
    this.state = {
      key: "",
      predictList: [],
    };
  }



  getPlacePrediction = async (keyword) => {
    let placeURL = googleAutoCompleteAPI(API_KEY, keyword);
    let placeResponse = await axios.get(placeURL);
    this.setState({
      key: keyword,
      predictList: placeResponse.data.predictions,
    });
  };

  render() {
    return (
      <View>
        <TextInput
          style={{ backgroundColor: "lightgray", margin: 10, marginTop: 50 }}
          onChangeText={(txt) => this.getPlacePrediction(txt)}
        />
        {this.state.key.length > 0 && (
          <PlaceList source={this.state.predictList} />
        )}
      </View>
    );
  }
}
