import axios from "axios";
import {
  <PERSON><PERSON><PERSON><PERSON>_SUCCESS,
  UPDATE_DATA,
  STORE_ROLE_ID,
  GO_TO_VOID,
  STORE_ROLE,
  SEARCH,
  LOG_OUT,
  EDIT_<PERSON>ATA,
  CAME_BACK,
  STORE_LAST_ID,
  <PERSON><PERSON><PERSON><PERSON>_ADD,
  CH<PERSON>K_NOTIFICATION,
  COUNTRY_LIST,
  STATE_LIST,
  CITY_LIST,
  PROJECT_LIST,
  SELECTED_PROJECT,
  USER_DETAILS,
  NOTIFICATION_DETAILS,
  SET_PAGE,
  <PERSON><PERSON><PERSON>_TAB,
  SHOW_MENU,
  DETAILS_PAGE,
  DELIVERY_DETAILS_ID,
  INSPECTION_DETAILS_ID,
  <PERSON><PERSON><PERSON>_COUNT,
  <PERSON><PERSON>NUP_DATA,
  UPDATE_LIST,
  REFRESH_PAGE,
  REFRESH_DASHBOARD,
  REFRESH_DELIVERY,
  SHOW_TOASTER,
  GET_MEMBERS,
  PROJECT_SWITCHED,
  UPDATE_DR_LIST,
  UPDATE_SETTING_SCREEN,
  UPDATE_INVITE_MEMBER,
  DR_RESPOSIBLE_PERSON,
  COMAPANY_LIST,
  SELECTED_COMPANY,
  ADD_ALL_REQUEST_TOGGLE,
  LAST_CRANE_ID,
  SHOW_CRANE_REQUEST_ID,
  EDIT_CRANE_REQUEST,
  EDIT_INS,
  ADD_CALENDAR_TOGGLE,
  EDIT_ASSOCIATED_WITH_CRANE,
  REFRESH_CALENDAR,
  REFRESH_CALENDAR_SETTINGS,
  EVENT_DISPLAY_PAGE,
  EVENT_DISPLAY_DATA,
  CONCRETE_DETAILS,
  CONCRETE_DETAILS_ID,
  SHOW_CONCRETE_REQUEST_ID,
  EDIT_CONCRETE_REQUEST,
  LAST_CONCRETE_ID,
  SELECTED_CONCRETE_LOCATIONS,
  SELECTED_CONCRETE_PLACEMENTS,
  SELECTED_CONCRETE_MIXDESIGNS,
  SELECTED_CONCRETE_PUMPSIZES,
  EDIT_EVENT_DATA,
  REFRESH_EVENT_DISPLAY,
  ENABLE_OPTION_EVENTS,
  DR_LIST_PAGE,
  SET_SELECTED_CALENDAR_DATE,
} from "./types";

export const storeUserid = (data) => {
  return (dispatch) => {
    dispatch({
      type: LOGIN_SUCCESS,
      payload: data,
    });
  };
};

export const onTapDetail = (data) => {
  console.log('data------->',data)
  return (dispatch) => {
    dispatch({
      type: DETAILS_PAGE,
      payload: data,
    });
  };
};
export const onPressConcreteDetail = (data) => {
  return (dispatch) => {
    dispatch({
      type: CONCRETE_DETAILS,
      payload: data,
    });
  };
};
export const getUserDetails = (data) => {
  return (dispatch) => {
    dispatch({
      type: USER_DETAILS,
      payload: data,
    });
  };
};

export const tappedNotificationDetails = (data) => {
  return (dispatch) => {
    dispatch({
      type: NOTIFICATION_DETAILS,
      payload: data,
    });
  };
};

export const changeTab = (data) => {
  return (dispatch) => {
    dispatch({
      type: CHANGE_TAB,
      payload: data,
    });
  };
};

export const updateData = (data) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_DATA,
      payload: data,
    });
  };
};

export const updateList = (data) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_LIST,
      payload: data,
    });
  };
};

export const refreshPage = (data) => {
  return (dispatch) => {
    dispatch({
      type: REFRESH_PAGE,
      payload: data,
    });
  };
};

export const refreshDashboard = (data,param) => {
  return (dispatch) => {
    dispatch({
      type: REFRESH_DASHBOARD,
      payload: data,
    });
  };
};

export const cameBack = (data) => {
  return (dispatch) => {
    dispatch({
      type: CAME_BACK,
      payload: data,
    });
  };
};

export const editData = (data) => {
  return (dispatch) => {
    dispatch({
      type: EDIT_DATA,
      payload: data,
    });
  };
};

export const selectedProjectDetails = (data) => {
  return (dispatch) => {
    dispatch({
      type: SELECTED_PROJECT,
      payload: data,
    });
  };
};

export const projectList = (data) => {
  return (dispatch) => {
    dispatch({
      type: PROJECT_LIST,
      payload: data,
    });
  };
};

export const setPage = (data) => {
  return (dispatch) => {
    dispatch({
      type: SET_PAGE,
      payload: data,
    });
  };
};

export const clickAdd = (data) => {
  return (dispatch) => {
    dispatch({
      type: CLICK_ADD,
      payload: data,
    });
  };
};

export const checkNotification = (data) => {
  return (dispatch) => {
    dispatch({
      type: CHECK_NOTIFICATION,
      payload: data,
    });
  };
};

export const onTapSearch = (data) => {
  return (dispatch) => {
    dispatch({
      type: SEARCH,
      payload: data,
    });
  };
};

export const storeRole = (data) => {
  return (dispatch) => {
    dispatch({
      type: STORE_ROLE,
      payload: data,
    });
  };
};

export const storeLastid = (data) => {
  return (dispatch) => {
    dispatch({
      type: STORE_LAST_ID,
      payload: data,
    });
  };
};

export const countryList = (data) => {
  return (dispatch) => {
    dispatch({
      type: COUNTRY_LIST,
      payload: data,
    });
  };
};

export const stateList = (data) => {
  return (dispatch) => {
    dispatch({
      type: STATE_LIST,
      payload: data,
    });
  };
};

export const cityList = (data) => {
  return (dispatch) => {
    dispatch({
      type: CITY_LIST,
      payload: data,
    });
  };
};

export const showSideMenu = (data) => {
  return (dispatch) => {
    dispatch({
      type: SHOW_MENU,
      payload: data,
    });
  };
};

export const logout = (data) => {
  return (dispatch) => {
    dispatch({
      type: LOG_OUT,
      payload: data,
    });
  };
};

export const goToVoid = (data) => {
  return (dispatch) => {
    dispatch({
      type: GO_TO_VOID,
      payload: data,
    });
  };
};

export const storeProjectRole = (data) => {
  return (dispatch) => {
    dispatch({
      type: STORE_ROLE_ID,
      payload: data,
    });
  };
};

export const showDeliverdetailsid = (data) => {
  console.log('data----------------------->',data)
  return (dispatch) => {
    dispatch({
      type: DELIVERY_DETAILS_ID,
      payload: data,
    });
  };
};

// export const showInspectiondetailsid = (data) => {
//   console.log('data----------------------->',data)
//   return (dispatch) => {
//     dispatch({
//       type: INSPECTION_DETAILS_ID,
//       payload: data,
//     });
//   };
// };

export const showBadgecount = (data) => {
  return (dispatch) => {
    dispatch({
      type: BADGE_COUNT,
      payload: data,
    });
  };
};

export const refreshDeliveryList = (data,loged) => {
  return (dispatch) => {
    dispatch({
      type: REFRESH_DELIVERY,
      payload: data,
    });
  };
};

export const showSignupparams = (name, email, mobile, countryCode) => {
  let signupdet = {};
  signupdet.name = name;
  signupdet.email = email;
  signupdet.mobile = mobile;
  signupdet.countryCode = countryCode;
  return (dispatch) => {
    dispatch({
      type: SIGNUP_DATA,
      payload: signupdet,
    });
  };
};

export const showToaster = (data) => {
  return (dispatch) => {
    dispatch({
      type: SHOW_TOASTER,
      payload: data,
    });
  };
};

export const _getMemberList = (url, params) => {

  return (dispatch) => {
    axios.post(url, params).then((response) => {
      let members = response.data.data.rows.length;
      if (!members) {
        dispatch({
          type: GET_MEMBERS,
          payload: members,
        });
      } else {
        dispatch({
          type: GET_MEMBERS,
          payload: [],
        });
      }
    });
  };
};

export const  projectSwitched= (data) => {
  return (dispatch) => {
    dispatch({
      type: PROJECT_SWITCHED,
      payload: data,
    });
  };
};

export const updateDrList= (data) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_DR_LIST,
      payload: data,
    });
  };
};

export const afterProjectCreated= (data) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_SETTING_SCREEN,
      payload: data,
    });
  };
};

export const updateInviteMember= (data) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_INVITE_MEMBER,
      payload: data,
    });
  };
};

export const drResponsiblePerson= (data) => {
  return (dispatch) => {
    dispatch({
      type: DR_RESPOSIBLE_PERSON,
      payload: data,
    });
  };
};

export const companyList= (data) => {
  return (dispatch) => {
    dispatch({
      type: COMAPANY_LIST,
      payload: data,
    });
  };
};
export const selectedCompany= (data) => {
  return (dispatch) => {
    dispatch({
      type: SELECTED_COMPANY,
      payload: data,
    });
  };
};

export const toggleAddAllRequest= (data) => {
  return (dispatch) => {
    dispatch({
      type: ADD_ALL_REQUEST_TOGGLE,
      payload: data,
    });
  };
};
export const toggleAddCalendar= (data) => {
  return (dispatch) => {
    dispatch({
      type: ADD_CALENDAR_TOGGLE,
      payload: data,
    });
  };
};

export const lastCraneId= (data) => {
  return (dispatch) => {
    dispatch({
      type: LAST_CRANE_ID,
      payload: data,
    });
  };
};

export const showCraneRequestId= (data) => {
  return (dispatch) => {
    dispatch({
      type: SHOW_CRANE_REQUEST_ID,
      payload: data,
    });
  };
};

export const editCraneRequest= (data) => {
  return (dispatch) => {
    dispatch({
      type: EDIT_CRANE_REQUEST,
      payload: data,
    });
  };
};

export const editINS= (data) => {
  return (dispatch) => {
    dispatch({
      type: EDIT_INS,
      payload: data,
    });
  };
};

export const toggleAssociatedWithCrane= (data) => {
  return (dispatch) => {
    dispatch({
      type: EDIT_ASSOCIATED_WITH_CRANE,
      payload: data,
    });
  };
};

export const refreshCalendar= (data) => {
  return (dispatch) => {
    dispatch({
      type: REFRESH_CALENDAR,
      payload: data,
    });
  };
};

export const refreshCalendarSettings= (data) => {
  return (dispatch) => {
    dispatch({
      type: REFRESH_CALENDAR_SETTINGS,
      payload: data,
    });
  };
};
export const concreteDetailsID= (data) => {
  return (dispatch) => {
    dispatch({
      type: CONCRETE_DETAILS_ID,
      payload: data,
    });
  };
};

export const showConcreteRequestId= (data) => {
  return (dispatch) => {
    dispatch({
      type: SHOW_CONCRETE_REQUEST_ID,
      payload: data,
    });
  };
};

export const lastConcreteId= (data) => {
  return (dispatch) => {
    dispatch({
      type: LAST_CONCRETE_ID,
      payload: data,
    });
  };
};

export const editConcreteRequest= (data) => {
  return (dispatch) => {
    dispatch({
      type: EDIT_CONCRETE_REQUEST,
      payload: data,
    });
  };
};

export const selectedConcreteLocationsData= (data) => {
  return (dispatch) => {
    dispatch({
      type: SELECTED_CONCRETE_LOCATIONS,
      payload: data,
    });
  };
};
export const eventDisplayPage= (data) => {
  return (dispatch) => {
    dispatch({
      type: EVENT_DISPLAY_PAGE,
      payload: data,
    });
  };
};

export const selectedConcretePlacementsData= (data) => {
  return (dispatch) => {
    dispatch({
      type: SELECTED_CONCRETE_PLACEMENTS,
      payload: data,
    });
  };
};
export const eventDisplayData= (data) => {
  return (dispatch) => {
    dispatch({
      type: EVENT_DISPLAY_DATA,
      payload: data,
    });
  };
};

export const selectedConcreteMixDesignsData= (data) => {
  return (dispatch) => {
    dispatch({
      type: SELECTED_CONCRETE_MIXDESIGNS,
      payload: data,
    });
  };
};

export const selectedConcretePumpSizesData= (data) => {
  return (dispatch) => {
    dispatch({
      type: SELECTED_CONCRETE_PUMPSIZES,
      payload: data,
    });
  };
};
export const editEventData = (data) => {
  return (dispatch) => {
    dispatch({
      type: EDIT_EVENT_DATA,
      payload: data,
    });
  };
};
export const refreshEventDisplay = (data) => {
  return (dispatch) => {
    dispatch({
      type: REFRESH_EVENT_DISPLAY,
      payload: data,
    });
  };
};

export const enableEditEvents = (data) => {
  return (dispatch) => {
    dispatch({
      type: ENABLE_OPTION_EVENTS,
      payload: data,
    });
  };
};

export const getDrPage = (data) => {
  return (dispatch) => {
    dispatch({
      type: DR_LIST_PAGE,
      payload: data,
    });
  };
};

export const setSelectedCalendarDate = (data) => {
  return (dispatch) => {
    dispatch({
      type: SET_SELECTED_CALENDAR_DATE,
      payload: data,
    });
  };
};