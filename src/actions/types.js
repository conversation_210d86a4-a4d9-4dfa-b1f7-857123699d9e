export const LOGIN_SUCCESS = "login_success";
export const LOGIN_FAILED = "login_failed";
export const POST_LIST_SUCCESS = "post_list_success";
export const POST_LIST_FAILURE = "post_list_failure";
export const CHANGE_TAB = "change_tab";
export const SHOW_MENU = "show_menu";
export const SET_PAGE = "set_page";
export const USER_DETAILS = "user_details";
export const SELECTED_PROJECT = "selected_project";
export const PROJECT_LIST = "project_list";
export const COUNTRY_LIST = "country_list";
export const CLICK_ADD = "click_add";
export const STORE_LAST_ID = "store_last_id";
export const CAME_BACK = "came_back";
export const EDIT_DATA = "edit_data";
export const EDIT_INS = "edit_inspection_data";
export const STORE_USER = "store_user";
export const LOG_OUT = "log_out";
export const SEARCH = "search_tap";
export const STORE_ROLE = "store_role";
export const DETAILS_PAGE = "details_page";
export const GO_TO_VOID = "go_to_void";
export const STORE_ROLE_ID = "store_role_id";
export const UPDATE_DATA = "update_data";
export const DELIVERY_DETAILS_ID = "delivery_details_id";
export const INSPECTION_DETAILS_ID = "inspection_details_id";
export const BADGE_COUNT = "badge_count";
export const NOTIFICATION_DETAILS = "notification_details";
export const CHECK_NOTIFICATION = "check_notification";
export const SIGNUP_DATA = "signup_data";
export const UPDATE_LIST = "update_list";
export const REFRESH_PAGE = "refresh_page";
export const REFRESH_DASHBOARD = "refresh_dashboard";
export const REFRESH_DELIVERY = "refresh_delivery";
export const REFRESH_CALENDAR="refresh_calendar";
//redux state for api
export const SHOW_TOASTER = "show_toaster";

//Members list page
export const GET_MEMBERS = "get_members";

//project switched
export const PROJECT_SWITCHED = "project_switched";

//project switched
export const UPDATE_DR_LIST = "update_dr_list";
export const UPDATE_SETTING_SCREEN="update_setting_screen";

//Addcompany from InviteMember
export const UPDATE_INVITE_MEMBER='update_invite_member';

export const DR_RESPOSIBLE_PERSON='dr_responsible_person_data'

//Company List and Selected Company
export const COMAPANY_LIST="company_list";
export const SELECTED_COMPANY="selected_company";
export const LAST_CRANE_ID='last_crane_id';
export const SHOW_CRANE_REQUEST_ID="show_crane_request_id";
export const EDIT_CRANE_REQUEST="edit_crane_request";

//Selection Toggle for Adding either crane or delivery
export const ADD_ALL_REQUEST_TOGGLE='add_all_request_toggle';
export const ADD_CALENDAR_TOGGLE='add_calendar_toggle';
export const EDIT_ASSOCIATED_WITH_CRANE='edit_associated_with_crane';

export const REFRESH_CALENDAR_SETTINGS="refresh_calendar_settings";
export const EVENT_DISPLAY_PAGE='evetn_display_page';
export const EVENT_DISPLAY_DATA='event_display_data';
export const EDIT_EVENT_DATA = "edit_event_data";
export const CONCRETE_DETAILS='concrete_details';
export const CONCRETE_DETAILS_ID='concrete_details_id';
export const SHOW_CONCRETE_REQUEST_ID="show_concrete_request_id";
export const EDIT_CONCRETE_REQUEST="edit_concrete_request";
export const LAST_CONCRETE_ID='last_concrete_id';
export const SELECTED_CONCRETE_LOCATIONS='selected_concrete_locations';
export const SELECTED_CONCRETE_PLACEMENTS='selected_concrete_placements';
export const SELECTED_CONCRETE_MIXDESIGNS='selected_concrete_mix_design';
export const SELECTED_CONCRETE_PUMPSIZES='selected_concrete_pump_sizes';
export const REFRESH_EVENT_DISPLAY='refresh_event_dispay';
export const ENABLE_OPTION_EVENTS='enable_option-events';
 // Network connection
 export const CURRENT_TAB ="current_tab";
 export const DR_LIST_PAGE = "DR_LIST_PAGE";
 export const SET_SELECTED_CALENDAR_DATE = "set_selected_calendar_date";