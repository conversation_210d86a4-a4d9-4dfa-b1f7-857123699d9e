import axios from "axios";
import { BASE_URL } from "api/Constants";
import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from "moment";
//////
import { USER_DETAILS, GET_COMPANY_PROJECT_LIST, GET_ACCOUNT_PROJECT } from "./Constants";


function getAxiosInstance() {
return axios.create({
    baseURL: BASE_URL,
    timeout: 30000,

    // auth: {
    //     username: 'sunbox',
    //     password: 'sunbox'
    // }
  });
}
let timezoneoffset=`${moment().utcOffset()}`;
let timehours=`${moment(moment().utcOffset()).format('hh:mm')}`;

export function post(url, params = {}, config = {}, data = {}) {
  var urlValue = `${BASE_URL}${url}`;



  return axios.post(urlValue, params, config);
}

export function get(url, config = {}, data = {}, params = {}) {

  return getAxiosInstance().get(url, config, params);
}

export async function del(url, config = {}, data = {}) {
  return getAxiosInstance().del(url, config);
}

export async function postAxios(
  url,
  params = {},
  headers = {},
  initialCallback,
  onCompletionCallBack
) {
  axios.defaults.headers['timezoneoffset'] = timezoneoffset;
  axios.defaults.headers['timezoneoffsetInHours'] = timehours;
  if (initialCallback) {
    initialCallback();
  }

  var urlValue = `${BASE_URL}${url}`;
  await axios
    .post(urlValue, params, { headers: headers })
    .then(function (response) {
 
      onCompletionCallBack(response);
    })
    .catch(function (error) {
      onCompletionCallBack(error.response ? error.response : error);
    });
}

// GET METHOD
export function getAxios(
  url,
  params = {},
  headers = {},
  initialCallback,
  onCompletionCallBack
) {
  axios.defaults.headers['timezoneoffset'] = timezoneoffset;
  axios.defaults.headers['timezoneoffsetInHours'] = timehours;
  if (initialCallback) {
    initialCallback();
  }
  var urlValue = `${BASE_URL}${url}`;
  axios
    .get(urlValue, params, { headers: headers }, { timeout: 30000 })
    .then(function (response) {
      onCompletionCallBack(response);
    })
    .catch(function (error) {
      onCompletionCallBack(error);
    });
}

export function getAxioss(
  url,
  params = {},
  headerss,
  initialCallback,
  onCompletionCallBack
) {
  axios.defaults.headers['timezoneoffset'] = timezoneoffset;
  axios.defaults.headers['timezoneoffsetInHours'] = timehours;
  if (initialCallback) {
    initialCallback();
  }
  var urlValue = `${BASE_URL}${url}`;
  axios
    .get(urlValue, params, { headers: {} }, { timeout: 30000 })
    .then(function (response) {
      onCompletionCallBack(response);
    })
    .catch(function (error) {
      onCompletionCallBack(error);
    });
}

//PUT METHOD
export function putAxios(
  url,
  params = {},
  headers = {},
  initialCallback,
  onCompletionCallBack
) {
  axios.defaults.headers['timezoneoffset'] = timezoneoffset;
  axios.defaults.headers['timezoneoffsetInHours'] = timehours;
  if (initialCallback) {
    initialCallback();
  }

  var urlValue = `${BASE_URL}${url}`;
  axios
    .put(urlValue, params, { headers: headers })
    .then(function (response) {
      onCompletionCallBack(response);
    })
    .catch(function (error) {
      onCompletionCallBack(error);
    });
}

//DELETE METHOD
export function deleteAxios(
  url,
  params = {},
  headersAuthorization = {},
  initialCallback,
  onCompletionCallBack,
  onCompletionErrorCallBack
) {
  axios.defaults.headers['timezoneoffset'] = timezoneoffset;
  axios.defaults.headers['timezoneoffsetInHours'] = timehours;
  if (initialCallback) {
    initialCallback();
  }

  var urlValue = `${BASE_URL}${url}`;


  axios
    .delete(urlValue, { data: params, headers: headersAuthorization })
    .then(function (response) {
      onCompletionCallBack(response);
    })
    .catch(function (error) {
      onCompletionErrorCallBack(error);
    });
}

export function getJsonHeader(AccessToken) {
  return{
    "Content-Type": "application/json",
     Authorization: AccessToken,
  };
}

export function getAuthorizationHeader(AccessToken) {
  return {
     Authorization: AccessToken,
  };
}

export function getFormDataHeader() {
  return {
    "Content-Type": "application/json",
  };
}

export function getContentJsonHeader() {
 return{
    "Content-Type": "application/json",
  };
}

//FORGOT PASSWORD
export function forgotPassword(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//SIGNUP
export function signup(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//LOGIN
export function login(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//GET PROJECT LIST
export function getProjectList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET COUNTRY LIST
export function getCountryList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

export function getNewUser(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//GET STATE LIST
export function getStateList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET CITY LIST
export function getCityList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}
//DASHBOARD UPCOMING DELIVERIES
export function getUpcomingDeliveries(url,params,initialCallback,onCompletionCallBack){
  getAxios(url,{},{},initialCallback,onCompletionCallBack);
}
//CHECK EMAIL EXITS
export function checkEmail(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//ADD MEMEBER
export function addMemeber(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//EDIT MEMEBER
export function editMember(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//DELETED MEMEBER
export function deleteMember(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//DELETED GATE
export function deleteGate(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//GET MEMBERS LIST
export function getMemberList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//GET ALL MEMBERS LIST OF PROJECT
export function getAllMemberList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET ROLES
export function getRoles(url, params, initialCallback, onCompletionCallBack) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET COMPANY LIST
export function getCompanyList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//DELETE COMPANY
export function deleteCompany(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//GET NEW COMPAINED
export function getNewCompanyList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

export function getEquipTypeList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET GATE LIST
export function getGateList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//GET EQUIPMENT LIST
export async function getEquipList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  await postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//DELETE EQUIPMENT
export function deleteEquipment(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//GET ROLES
export function getRole(url, params, initialCallback, onCompletionCallBack) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}
export function getUnReadCount(url, params, initialCallback, onCompletionCallBack) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}
//GET USER DETAILS
export function getuserDetails(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET USER DETAILS ( main thread to redux )

export function _getUserDetails() {
  /* 
   - fetch user details from url
  */
  var urlValue = `${BASE_URL}${USER_DETAILS}`;
  return axios.get(urlValue).then((response) => {
    return response;
  });
}

//GET COMPANY PROJECT ( main thread to redux )
export function _getCompanyProjectList() {

  var urlValue = `${BASE_URL}${GET_COMPANY_PROJECT_LIST}`

  return axios.get(urlValue).then((response) => {
    return response;
  });
}

export function _getData(url) {
  var urlValue = `${BASE_URL}${url}`
  return axios.get(urlValue).then((response) => {
    return response;
  });

}

//GET PROJECT LIST ( main thread to redux )
export function _getProjectList(company_id) {

  var urlValue = `${BASE_URL}${GET_ACCOUNT_PROJECT}${company_id}`
  
  return axios.get(urlValue).then((response) => {

    return response;
  });
}

//GET Notification count ( main thread to redux )

//GET PROJECT ROLE
export function getprojectRole(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

export function getDefinableFeature(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//ADD NEW GATE
export function addGate(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

// SEARCH RESPONSIBLE MEMBER
export function searchMember(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

// RESET TOKEN
export function checkResetToken(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//reset password
export function resetPasswordEmail(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}


//UPDATE NEW GATE
export function updateGate(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//ADD EQUIPMENT
export function addEquip(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//EDIT EQUIPMENT
export function updateEquip(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//ADD PROJECT
export function addProject(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//ADD COMPANY
export function addCompany(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//EDIT COMPANY
export function editCompany(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//CHANGE PROJECT
export function changePassword(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//GET PLANS AND PROJECTS
export function getPlansAndProject(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//UPGRADE PLAN
export function upgradePlan(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//UPDATE PROFILE
export function updateProfile(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//CANCEL PROJECT SUBSCRIPTION
export function cancelProject(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET OVER VIEW DETAIL
export function getOverViewDetail(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET EQUIPMENT TYPE LIST
export function getEquipmentTypeList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET DEFINABLE LIST
export function getDefinableList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

export function CompanyGetDefinable(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}
export function presetEquipmentType(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET VOID LIST
export function getVoidList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//RESTORE VOID
export function restoreVoid(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//DELETE DFOW
export function deleteDfow(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//IMPORT DFOW
export function importDfow(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//ADD DFOW
export function addDfow(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//ADD NEW DELIVERY REQUEST
export function addNDR(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//ADD NEW INSPECTION REQUEST
export function addNIN(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}


//ADD DR TO VOID
export function addVoid(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//Get INSPECTION
export function  getInspectionDetails(
  url,
  params,
  initialCallback,
  onCompletionCallBack
)
{
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}



//GET DELIVERY
export function getDeliveryDetails(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}


//GET HISTORY
export function getHistoryDetails(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET ATTACHMENT
export function getAttachmentDetails(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET COMMENT
export function getCommentDetails(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//ADD COMMENT
export function createComment(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//STATUS UPDATE
export function updateStatus(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}
//ADD ATTACHMENT
export async function addAttachment(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  // postAxios(url, params, {}, initialCallback, onCompletionCallBack)
  var urlValue = `${BASE_URL}${url}`;

  axios({
    url: urlValue,
    method: "POST",
    data: params,
    headers: {
      //  Accept: 'application/x-www-form-urlencoded',
      Accept: "application/json",
      "content-type": `multipart/form-data`,
      Authorization: "JWT " + (await AsyncStorage.getItem("AccessToken")),
    },
  })
    .then(function (response) {
      
      onCompletionCallBack(response);
    })
    .catch(function (error) {
    
      onCompletionCallBack(error.response ? error.response : error);
    });
}
//REMOVE ATTACHMENT
export function removeAttachement(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

export function eventNDR(url, params,timezoneoffset,
  timehours, initialCallback, onCompletionCallBack) {
    axios.defaults.headers['timezoneoffset'] = timezoneoffset;
    axios.defaults.headers['timezoneoffsetInHours'] = timehours;
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}


export function eventINS(url, params,timezoneoffset,
  timehours, initialCallback, onCompletionCallBack) {
    axios.defaults.headers['timezoneoffset'] = timezoneoffset;
    axios.defaults.headers['timezoneoffsetInHours'] = timehours;
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//CREATE VOID
export function createVoid(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//UPLOAD IMAGE
export function updateImage(
  url,
  params,
  headers,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, headers, initialCallback, onCompletionCallBack);
}

//NOTIFICATION LIST
export function getNotificationList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}
//DELETE NOTIFICATION
export function deleteNotification(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}
//EDIT NOTIFICATION
export function readNotification(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}
//SET DEVICE TOKEN
export function setdevicetoken(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}
//CLEAR TOKLEN
export function cleardevicetoken(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}
//GET COMPANY LIST
export function getCompanyprojectlist(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET DASHBOARD DETAIL
export function getprojectadmindetail(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

//GET EXPORT DFOW
export function getExportDfow(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//INVITE MEMBER
export function invite(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}
//RESEND LINK
export function resend(url,params,initialCallback,onCompletionCallBack){
  postAxios(url,params,{},initialCallback,onCompletionCallBack);
}

export function updateMember(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

//CHECK MEMBER
export function check(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}
/**
 * Fetching Crane Request List
 * @param {string} url 
 * @param {string} params - which contains payload
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */
export function getCraneCalendarList(

  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

/**
 * 
 * @param {string} url 
 * @param {string} params -which contains payload
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */

export function getCraneDeliveryList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

/**
 * 
 * @param {string} url 
 * @param {string} params -which contains payload
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */

 export function addCraneRequest(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}


/**
 * 
 * @param {string} url 
 * @param {string} params -which contains payload
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */

 export function getSingleCraneRequest(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

/**
 * 
 * @param {string} url 
 * @param {string} params -which contains payload
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */

 export function getLastCraneRequestId(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

/**
 * 
 * @param {string} url 
 * @param {string} params -which contains payload
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */

 export function getCraneEquipmentList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}
/**
 * 
 * @param {String} url 
 * @param {String} params 
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */
export function getCraneUpcomingList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

/**
 * 
 * @param {String} url 
 * @param {String} params 
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */
 export function getCalendarSettings(  url,
  params,
  initialCallback,
  onCompletionCallBack
){  getAxios(url, params, {}, initialCallback, onCompletionCallBack);
}
/**
 * @param {string} url 
 * @param {string} params -which contains payload
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */

 export function addConcreteRequest(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {

  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

/**
 * 
 * @param {String} url 
 * @param {String} params 
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */
 export function getCalendarSettingsMonth( url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, params, {}, initialCallback, onCompletionCallBack);
}
 export function getConcreteList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}
/**
 * 
 * @param {String} url 
 * @param {String} params 
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */
 export function getSingleEvent (  url,
  params,
  timezoneoffset,
  timehours,
  initialCallback,
  onCompletionCallBack
) {
  
  axios.defaults.headers['timezoneoffset'] = timezoneoffset;
  axios.defaults.headers['timezoneoffsetInHours'] = timehours;
  getAxioss(url, params,  {}, initialCallback, onCompletionCallBack);
}

 export function addVoidConcrete(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

/**
 * 
 * @param {string} url 
 * @param {string} params -which contains payload
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */

 export function addEvent( url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}
 export function getConcreteDropDownDetails(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}
/**
 * 
 * @param {String} url 
 * @param {String} params 
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */
 export function deleteConcreteList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

/**
 * 
 * @param {String} url 
 * @param {String} params 
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */
 export function putDeleteEvent(  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  putAxios(url, params, {}, initialCallback, onCompletionCallBack);
}
 export function calendarConcreteList(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {

  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}


export function getTimeZone(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url,{}, {}, initialCallback, onCompletionCallBack);
}

/**
 * 
 * @param {String} url 
 * @param {String} params 
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */
 export function putEditEvent(  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  putAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

/**
 * 
 * @param {String} url 
 * @param {object} params 
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */
export function getMarkAllRead(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url,{}, {}, initialCallback, onCompletionCallBack);
}


/**
 * 
 * @param {String} url 
 * @param {object} params 
 * @param {function} initialCallback 
 * @param {function} onCompletionCallBack 
 */
 export function getEvent(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url,{}, {}, initialCallback, onCompletionCallBack);
}

/**  Get CurrentApp Version
 @param {String} url, @param {function} onCompletionCallBack 
 */
 export function getAppVersion(url, onCompletionCallBack) {
  getAxios(url, {}, {}, ()=> {}, onCompletionCallBack);
}

/** Update LastestApp Version
@param {String} url, @param {object} params, @param {function} initialCallback, @param {function} onCompletionCallBack 
*/
export function updateAppVersion(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

export function getSlotsDetail(url, params, initialCallback, onCompletionCallBack) {
  postAxios(url, params, {}, initialCallback, onCompletionCallBack);
}

export function getProjectSettings(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}


export function getProjectSettingsDetails(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}

export function getLocationSettings(
  url,
  params,
  initialCallback,
  onCompletionCallBack
) {
  getAxios(url, {}, {}, initialCallback, onCompletionCallBack);
}