import { Server } from "../configs/Configs.json";

export const BASE_URL = Server.BaseUrl;

export const SIGN_UP = "auth/register";
export const LOGIN = "auth/login";
export const FORGOT_PASSWORD = "auth/forgot_password";
export const GET_COUNTRY = "address/get_country";
export const GET_STATE = "address/get_state/";
export const GET_CITY = "address/get_city/";
export const CHECK_EMAIl = "auth/exist_user";
export const RESET_TOKEN = "auth/check_reset_token/";
export const RESET_PASSWORD = "auth/reset_password_email/";

//USER RELATED API
export const USER_DETAILS = "user/authenticated_user";
export const CHANGE_PASSWORD = "user/change_password";
export const UPDATE_IMAGE = "user/upload_profile/";
export const GET_SINGLE_PROJECT='project/get_single_project';
//MEMBERS RELATED API
export const MEMBERS_LIST = "member/list_member/";
export const ADD_MEMBER = "member/add_member";
export const INVITE_MEMBER = "member/invite_member";
export const UPDATE_MEMBER = "member/update_member";
export const CHECK_MEMBER = "member/check_user";
export const NEW_MEMBER = "member/get_user_detail";
export const RESEND_LINK="member/resend_invite_link";

export const DELETE_MEMBER = "member/delete_member";
export const GET_ROLE = "member/get_roles";
export const LIST_ALL_MEMBER = "member/list_all_member/";
export const EDIT_MEMBER = "member/edit_member";
export const GET_OVERVIEW_DETAIL = "member/get_overview_detail/";
export const UPDATE_PROFILE = "member/update_profile";
export const SEARCH_MEMBER = "member/search_member/";

//COMPANY RELATED API
export const GET_COMPANY_LIST = "company/get_companies/";
export const ADD_COMPANY = "company/add_company";
export const EDIT_COMPANY = "company/edit_company";
export const DELETE_COMPANY = "company/delete_company";
export const GET_NEW_COMPANIES = "company/get_newcompanies/";
export const DEFINABLE_FEATURE = "company/get_definable_work/";
export const UPDATE_COMPANY_IMAGE = "company/upload_logo/";

//GATE RELATED API
export const GET_GATE_LIST = "gates/gate_list/";
export const DELETE_GATES = "gates/delete_gates";
export const UPDATE_GATES = "gates/update_gates";
export const ADD_GATES = "gates/add_gates";

//EQUIPMENT RELATED API
export const GET_EQUIP_LIST = "equipment/equipment_list/";
export const DELETE_EQUIP = "equipment/delete_equipments";
export const UPDATE_EQUIP = "equipment/update_equipment";
export const ADD_EQUIP = "equipment/add_equipment";
export const GET_EQUIPMENT_TYPE_LIST = "equipment/equipment_type_list/";
export const GET_PRESET_EQUIPMENT_TYPE_LIST='equipment/preset_equipment_type_list';
//PROJECT
export const CREATE_PROJECT = "project/create_project";
export const EXIST_PROJECT = "project/exist_project"; //Check whether the project name exist or not
export const UPGRADE_PLAN = "project/upgrade_plan";
export const GET_PROJECT = "project/get_project";
export const OVER_RIDE_REQUEST = "overRide/apply_override";
export const GET_PROJECT_ROLE = "delivery/get_user_role/";
export const GET_PLAN_PROJECT = "project/get_plans_projects/";
export const CANCEL_SUBSCRIPTION = "payment/cancel_subscription/";

//DFOW
export const GET_DEFINABLE = "definable/get_definable/";
export const DELETE_DFOW = "definable/delete_definable";
export const UPDATE_DFOW = "definable/update_definable/";
export const ADD_DFOW = "definable/add_definable/";
export const IMPORT_DFOW = "definable/create_definable/";
export const EXPORT_DFOW = "definable/export_definable/";
export const SAMPLE_DFOW = "definable/sample_dfow_excel_download/";

//IN
 export const ADD_IN = "inspection/new_request";
 export const UPDATE_STATUS_INS = "inspection/update_status";
export const GET_SINGLE_INS = "inspection/get_single_NDR/";
// export const EDIT_REQUEST_IN= "inspection/edit_request/";
 export const EDIT_IN = "inspection/edit_request/";
export const GET_IN_LIST = "inspection/list_NDR/";
export const GET_HISTORY_INS = "history/get_inspection_history/";
export const GET_ATTACHMENT_INS = "attachement/get_inspection_attachement/"
export const GET_COMMENT_INS = "comment/get_inspection_comment/"
export const CREATE_COMMENT_INS = "comment/create_inspection_comment/"
export const ADD_ATTACHMENT_INS = "attachement/add_inspection_attachement/"
export const CREATE_VOID_INS ="void/create_inspection_void"
export const REMOVE_ATTACHMENT_INS = "attachement/remove_inspection_attachement/"


//IN DETAIL
// export const GET_HISTORY_IN = "history/get_history/";


//DR
export const ADD_DR = "delivery/new_request";
export const UPDATE_DR = "delivery/update_status";
export const GET_SINGLE_DR = "delivery/get_single_NDR/";
export const EDIT_REQUEST = "delivery​/edit_request/";
export const EDIT_DR = "delivery/edit_request/";
export const GET_DR_LIST = "delivery/list_NDR/";

//DR DETAIL
export const GET_SINGLE_NDR = "delivery/get_single_NDR/";
export const GET_HISTORY_NDR = "history/get_history/";
export const GET_ATTACHMENT_NDR = "attachement/get_attachement/";
export const GET_COMMENT_NDR = "comment/get_comment/";
export const CREATE_COMMENT_NDR = "comment/create_comment";
export const UPDATE_STATUS_NDR = "delivery/update_status";
export const ADD_ATTACHEMENT = "attachement/add_attachement/";
export const REMOVE_ATTACHEMENT = "attachement/remove_attachement/";
export const CREATE_VOID = "void/create_void";

//VOID
export const VOID_DR = "void/create_void";
export const RESTORE_VOID = "void/remove_void";

//CALENDER
export const GET_EVENT_NDR = "calendar/event_NDR/";
export const GET_EVENT_INS = "calendar/get_inspection_request/";


//NOTIFICATION
export const GET_NOTIFICATION_LIST = "notification/list_notification/";
export const DELETE_NOTIFICATION = "notification/delete_notification/";
export const READ_NOTIFICATION = "notification/read_notification?";
export const GET_UNREAD_COUNT="notification/unread_count/?"
export const GET_MARK_ALL_READ="delivery/markall_notification/"
//GET COMPANY LIST
export const GET_COMPANY_PROJECT_LIST = "project/get_accounts_company";

//GET DASHBOARD DETAIL
export const GET_PROJECTADMIN_DETAIL = "dashboard/get_projectadmin_detail/";
export const GET_UPCOMING_DELIVERIES='dashboard/get_upcoming_delivery';

//DEVICE TOKEN
export const CLEAR_DEVICETOKEN = "devicetoken/clear_device_token";
export const SET_DEVICETOKEN = "devicetoken/set_device_token";
export const GET_ACCOUNT_PROJECT = "project/get_account_projects/";

//geocoder api
//fetch('https://maps.googleapis.com/maps/api/geocode/json?address=' + myLat + ',' + myLon + '&key=' + myApiKey)

export const GEOCODER_API = (myLat, myLon, myApiKey) =>
  "https://maps.googleapis.com/maps/api/geocode/json?address=" +
  myLat +
  "," +
  myLon +
  "&key=" +
  myApiKey;


  //CRANE API
  export const CRANE_CALENDER='calendar/get_crane_associated_request/';
  export const CRANE_DELIVERY_LIST='crane_request/get_crane_request_list/';
  export const ADD_CRANE="crane_request/create_crane_request";
  export const EDIT_CRANE="crane_request/edit_crane_request";
  export const GET_SINGLE_CRANE="crane_request/get_single_crane_request/";
  export const GET_LAST_CRANE_ID="crane_request/get_last_crane_request_id/";
  export const CRANE_EQUIPMENT_LIST='equipment/crane_equipment_list/';
  export const ADD_VOID_CRANE='void/add_crane_request_to_void';
  
  //CRANE DETAILS 
  export const GET_COMMENT_CRANE="crane_request_comment/get_crane_request_comments/";
  export const CREATE_COMMENT_CRANE="crane_request_comment/create_crane_request_comment";
  export const CRANE_HISTORY="crane_request_history/get_crane_request_histories/";
  export const GET_ATTACHMENT_CRANE="crane_request_attachment/get_crane_request_attachements/";
  export const REMOVE_ATTACHEMENT_CRANE="crane_request_attachment​/remove_crane_request_attachement​/";
  export const ADD_ATTACHEMENT_CRANE="crane_request_attachment/add_crane_request_attachement/";
  export const UPDATE_CRANE_STATUS="crane_request/update_crane_request_status";

  //Crane UPCOMING
  export const GET_CRANE_UPCOMING="crane_request/upcoming_crane_request";

  //CALENDAR SETTINGS
  export const GET_CALERNDAR_SETTINGS="calendar_settings/get_calendar_events";
  export const GET_CALERNDAR_SETTINGS_MONTH="calendar_settings/get_calendar_month_events";
  export const ADD_EVENT="calendar_settings/add_event";
  export const EDIT_EVENT="calendar_settings/edit_event";
  export const GET_SINGLE_EVENT="calendar_settings/get_event";
  export const PUT_DELETE_EVENT="calendar_settings/delete_event";
  export const GET_TIMEZONE='timezone_list/get_timezone_list';

  //Concrete
  export const ADD_CONCRETE="concrete_request/create_concrete_request";
  export const EDIT_CONCRETE="concrete_request/edit_concrete_request";
  export const CONCRETE_DROPDOWN_DETAILS="concrete_request/concrete_dropdown_detail";
  export const GET_CONCRETE_REQUEST="concrete_request/get_concrete_request_list";
  export const ADD_VOID_CONCRETE="void/add_concrete_request_to_void";
  export const DELETE_CONCRETE="concrete_request/delete_concrete_request";
  export const GET_HISTORY_CONCRETE="concrete_request_history/get_concrete_request_histories";
  export const GET_COMMENT_CONCRETE="concrete_request_comment/get_concrete_request_comments";
  export const CREATE_COMMENT_CONCRETE="concrete_request_comment/create_concrete_request_comment";
  export const ADD_ATTACHEMENT_CONCRETE='concrete_request_attachment/add_concrete_request_attachment';
  export const GET_ATTACHMENT_CONCRETE="concrete_request_attachment/get_concrete_request_attachments";
  export const REMOVE_ATTACHEMENT_CONCRETE="concrete_request_attachment/remove_concrete_request_attachment";
  export const GET_SINGLE_CONCRETE='concrete_request/get_single_Concrete_request';
  export const UPDATE_STATUS_CONCRETE='concrete_request/update_concrete_request_status';
  export const GET_VOID_DETAILS='void/get_void_list';
  export const GET_CALENDAR_CONCRETE='calendar/get_concrete_request';

   // APP VERSION
   export const GET_APP_VERSION = "notification/current_version";
   export const UPDATE_APP_VERSION = "notification/version_update";

 //Project Setting
//  export const GET_PROJECT_SETTING = "project_settings?";
 export const GET_PROJECT_SETTING_DETAILS="project_settings/get_projectDetails";

  //Get Location
  export const GET_LOCATION_DETAILS = 'location/get_locations';
  export const GET_TIME_SLOTS = 'location/available_timeslots';

  export const NO_EQUIPMENT_NEEDED =  {
    id: 0,
    equipmentName: 'No Equipment Needed',
    isCrane: false,
    value: 'No Equipment Needed',
    name: 'No Equipment Needed',
    label: 'No Equipment Needed',
    selected: false,
  };