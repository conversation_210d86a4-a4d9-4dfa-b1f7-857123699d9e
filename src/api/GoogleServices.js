/**
 * API Keys to access Google APIs
 */

export const ApiKeys = {
  android: "AIzaSyB2EHXZ7yQZ3KItOpshW2F4DJ5ewmagOWU",
  ios: "AIzaSyDnpmHj2Uqaod01D2jF6aDwq0sGgBKMp3o",
};

/**
 * Google API ROOT
 */
const ROOT = "https://maps.googleapis.com/maps/api";

/**
 * Google Places Autocomplete api
 */
export const googleAutoCompleteAPI = (apiKey, keyWord) => {
  return `${ROOT}/place/autocomplete/json?key=${apiKey}&input=${keyWord}`;
};

export const geoCodeAdrressAPI=(address,apiKey)=>{
return `${ROOT}/geocode/json?address=${address}&key=${apiKey}`;
}
/**
 * Google Map Geocoder API
 */
export const geoCoderAPI = (lat, long, apiKey) => {
  return `${ROOT}/geocode/json?address=${lat},${long}&key=${apiKey}`;
};

