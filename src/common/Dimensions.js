import {
    widthPercentageToDP as wp,
    heightPercentageToDP as hp,
  } from 'react-native-responsive-screen';
  import {Dimensions, Platform, PixelRatio} from 'react-native';
  
  //need to add listener based on the device rotation
  export const scaleWidth = (width: number) => {
    return wp(width);
  };
  
  export const scaleHeight = (height: number) => {
    return hp(height);
  };
  
  export const scaleFont = (size: number) => {
    return hp(size);
  };
  
  export const getDeviceHeight = () => {
    return Dimensions.get('window').height;
  };
  
  export const getDeviceWidth = () => {
    return Dimensions.get('window').width;
  };
  
  // based on iphone 5s's scale
  const scale = getDeviceWidth() / 400;
  
  export function normalizeFont(size: number) {
    const newSize = size * scale;
    if (Platform.OS === 'ios') {
      return Math.round(PixelRatio.roundToNearestPixel(newSize));
    } else {
      return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2;
    }
  }
  
  export default {
    scaleHeight,
    scaleWidth,
    scaleFont,
    getDeviceHeight,
    getDeviceWidth,
    normalizeFont
  };
  