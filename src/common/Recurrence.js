export const listOfRecurrence = [
  { name: "Does Not Repeat" },
  { name: "Daily" },
  { name: "Weekly" },
  { name: "Monthly" },
  { name: "Yearly" },
];

export const inspection_type = [
  { type: "Material" },
  { type: "Quality Control" },
  { type: "Special Inspection" },
  { type: "Equipment" },
  { type: "Safety" },
  { type: "Other" },
]


export const recurrenceList = [
  {
    id: 1,
    value: "Day",
    label: "Day",
    selected: false,
  },
  {
    id: 2,
    value: "Week",
    label: "Week",
    selected: false,
  },
  { id: 3, value: "Month", label: "Month", selected: false },
  {
    id: 4,
    value: "Year",
    label: "Year",
    selected: false,
  },
];


export const repetRecurrenceList = [
  {
    id: 1,
    value: "Days",
    label: "Days",
    selected: false,
  },
  {
    id: 2,
    value: "Weeks",
    label: "Weeks",
    selected: false,
  },
  { id: 3, value: "Months", label: "Months", selected: false },
  {
    id: 4,
    value: "Years",
    label: "Years",
    selected: false,
  },
];

export const editSeriesOption1 =[
  {id:1,name:"This event"},
  {id:2,name:"This and all following events"},
  // {id:3,name:"All events in the series"}
]

export const editSeriesOption2 =[
  {id:1,name:"This event"},
]