import {StyleSheet} from 'react-native';

// import {
//   Dimensions,
//   Fonts,
//   Colors
// } from './index';
import  Dimensions from '../common/Dimensions';
import Fonts from '../common/fonts';
import Colors from '../common/color';
const Styles = StyleSheet.create({
  header: {
    width: Dimensions.scaleWidth(78),
    fontSize: Dimensions.scaleFont(2.65),
    textAlign: "center",
    fontFamily: Fonts.montserratSemiBold,
  },
  questionHeader: {
    color: Colors.placeholder,
    fontSize: Dimensions.scaleFont(1.7),
    fontFamily: Fonts.montserratMedium,
  },
  container: {
      flex: 1,
      marginHorizontal: Dimensions.scaleWidth(5),
  },
  dropDownItem: {
    justifyContent: "flex-start",
    fontSize: Dimensions.scaleFont(1.8),
    color:'#fff',
  },
  inputText: {
    color: Colors.black,
    fontSize: 14,
  },
  selctizeInput: {
    color: Colors.black,
    fontSize: Dimensions.scaleFont(1.8),
    left: Dimensions.scaleWidth(2.5),
    fontFamily: Fonts.montserratMedium,
  },
  greyTextContainer: {
    width: Dimensions.scaleWidth(90),
    height: Dimensions.scaleHeight(5),
    backgroundColor: Colors.greyContainer,
    marginTop: Dimensions.scaleWidth(1),
    padding: Dimensions.scaleHeight(1),
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: Dimensions.scaleFont(1.8),
    paddingLeft: 15
  },
  datePickerOkContainer: {
    width:  Dimensions.scaleWidth(100),
    alignItems: "center",
    backgroundColor: Colors.white,
    paddingBottom: Dimensions.scaleHeight(2),
  },
  datePickerOkLabel: {
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 6,
    paddingBottom: 6,
    fontFamily: Fonts.montserratBold,
  },
  rowContainer: {
    width: Dimensions.scaleWidth(90),
    alignSelf: "center",
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  mediumHeaderText: {
    color: Colors.black,
    fontSize: Dimensions.scaleFont(2),
    fontFamily: Fonts.montserratBold,
  },
  lineStyle: {
    flex: 1,
    height: Dimensions.scaleWidth(0.5),
    backgroundColor: Colors.greyContainer,
    marginLeft: Dimensions.scaleWidth(2),
  },
  confirmDateText: {
    color: Colors.blackShade,
    paddingTop: Dimensions.scaleHeight(2),
    fontSize: Dimensions.scaleFont(1.6)
  },
  bottomContainer: {
    width: Dimensions.scaleWidth(90),
    flexDirection: "row",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: Dimensions.scaleHeight(4),
    marginTop: Dimensions.scaleHeight(8),
  },
  cancel: {
    width: Dimensions.scaleWidth(35),
    height: Dimensions.scaleHeight(7),
    backgroundColor: Colors.shadowColor,
    marginRight: Dimensions.scaleWidth(3),
    borderRadius: Dimensions.scaleHeight(3.5),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: Dimensions.scaleWidth(35),
    height: Dimensions.scaleHeight(7),
    backgroundColor: Colors.themeOpacity,
    marginLeft: Dimensions.scaleWidth(3),
    borderRadius: Dimensions.scaleHeight(3.5),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: "#757575",
    fontFamily: Fonts.montserratSemiBold,
    fontSize: Dimensions.scaleFont(2),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: Dimensions.scaleFont(2),
  },
  hoursModalContainer: {
    // flex: 1,
    width: Dimensions.getDeviceWidth(),
    height: Dimensions.scaleHeight(35),
    backgroundColor: '#fff',
    paddingTop: Dimensions.scaleWidth(5),
    paddingBottom: Dimensions.scaleWidth(5),
    alignItems: 'center',
    justifyContent: 'center',
    borderTopLeftRadius: 30,
    borderTopEndRadius: 30,
  },
  textContainer: {
    padding: Dimensions.scaleWidth(3),
    // flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    width: Dimensions.getDeviceWidth(),
    height: 40,
    marginTop: 5,
    // marginBottom: Dimensions.scaleWidth(1),
  }
});

export default Styles;
