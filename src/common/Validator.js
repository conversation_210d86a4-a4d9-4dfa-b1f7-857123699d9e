export const isValidEmail = text => {
    let reg = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
    return !reg.test(text);
};

export const isEmpty = text => {
    return (text === undefined || text.length === 0);
};

export const isValidPassword = text => {
    // Regex to allow one capital one small one number and min 6 to max 15 with optional special characters
    let reg = /^(?=.*\d)(?=.*[a-zA-Z0-9])[\w~@#$%^&*+=`|{}:;!.?\"()\[\]-]{6,15}$/
    return !reg.test(text);
};

 export const isValidName = text => {
     let reg =  /^[a-zA-Z]+$/;
     return !reg.test(text);
 };

 export const isValidPostal = text => {
    let reg =  /^[a-zA-Z0-9]+$/;
    return !reg.test(text);
 }

 export const isValidNumber = text => {
    let reg =  /^[0-9]+$/;
    return !reg.test(text);
 };

export const isName = text => {
    let reg =  /^[A-Za-z\s]{1,}[\.]{0,1}[A-Za-z\s]{0,}$/;
    return !reg.test(text);
};

export const isNamee = text => {
    let reg =  /^[A-Za-z\s\d,]{1,}[\.]{0,1}[A-Za-z\s]{0,}$/;
    return !reg.test(text);
};


export const isWebsite = text => {
    var reg = /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/;
    return !reg.test(text);
}