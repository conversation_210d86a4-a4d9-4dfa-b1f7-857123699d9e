const Images = {
  logoFollo: require("../assets/images/logoFollo.png"),
  logoArr: require("../assets/images/logoArrow.png"),
  follo: require("../assets/images/Follo.png"),
  constImg: require("../assets/images/const.png"),
  backButton: require("../assets/images/back.png"),
  path: require("../assets/images/Path85.png"),
  path2: require("../assets/images/Path86.png"),
  path3: require("../assets/images/Path87.png"),
  downArr: require("../assets/images/down.png"),
  map: require("../assets/images/map.png"),
  whiteForward: require("../assets/images/whiteForward.png"),
  blackTick: require("../assets/images/blackTick.png"),
  unhide: require("../assets/images/unhide.png"),
  hide: require("../assets/images/hide.png"),
  homeun: require("../assets/images/home.png"),
  homesele: require("../assets/images/home_active.png"),
  calun: require("../assets/images/cal.png"),
  calsele: require("../assets/images/cal_active.png"),
  calGray: require("../assets/images/calGray.png"),
  plus: require("../assets/images/plus.png"),
  notiun: require("../assets/images/notif.png"),
  notisele: require("../assets/images/notification_active.png"),
  menuun: require("../assets/images/settings.png"),
  menusele: require("../assets/images/settings.png"),
  profile: require("../assets/images/profile.png"),
  close: require("../assets/images/close.png"),
  company: require("../assets/images/company.png"),
  member: require("../assets/images/members.png"),
  gate: require("../assets/images/gate.png"),
  dr: require("../assets/images/dr.png"),
  equipment: require("../assets/images/equipment.png"),
  df: require("../assets/images/work.png"),
  settings: require("../assets/images/settings.png"),
  upArrow: require("../assets/images/upArrow.png"),
  newpro: require("../assets/images/newpro.png"),
  filter: require("../assets/images/filter.png"),
  search: require("../assets/images/search.png"),
  backArrow: require("../assets/images/backArrow.png"),
  placeholder: require("../assets/images/placholder.png"),
  camera: require("../assets/images/camera.png"),
  dotmenu: require("../assets/images/dot.png"),
  check: require("../assets/images/checked_check_box.png"),
  uncheck: require("../assets/images/unchecked_check_box.png"),
  delete: require("../assets/images/delete.png"),
  deleteBin:require("../assets/images/delete_bin.png"),
  edit: require("../assets/images/edit.png"),
  delete1: require("../assets/images/delete.png"),
  edit1: require("../assets/images/edit.png"),
  camRound: require("../assets/images/camRound.png"),
  companyPlace: require("../assets/images/companyPlace.png"),
  companyHolder: require("../assets/images/noun_company_3654353.png"),
  logout: require("../assets/images/logout.png"),
  lock: require("../assets/images/lock.png"),
  closeBlack: require("../assets/images/closeBlack.png"),
  searchGray: require("../assets/images/searchGray.png"),
  import: require("../assets/images/import.png"),
  export: require("../assets/images/export.png"),
  save: require("../assets/images/save.png"),
  crossred: require("../assets/images/cross-red.png"),
  edit_blue: require("../assets/images/edit-blue.png"),
  dotmen_gray: require("../assets/images/dotmen-gray.png"),
  plus_black: require("../assets/images/Plus-black.png"),
  plus_attach: require("../assets/images/plus-attach.png"),
  clock: require("../assets/images/clock.png"),
  editBlue: require("../assets/images/editBlue.png"),
  void: require("../assets/images/void.png"),
  sort: require("../assets/images/sort.png"),
  doc_place: require("../assets/images/doc-placeholder.png"),
  pdf_place: require("../assets/images/pdf-placeholder.png"),
  calendar_edit: require("../assets/images/calendar_edit.png"),
  arrow_left: require("../assets/images/Arrow-left.png"),
  arrow_right: require("../assets/images/Arrow-right.png"),
  list_click: require("../assets/images/list-click.png"),
  list_unclick: require("../assets/images/list-unclick.png"),
  arrow_down: require("../assets/images/arrow-down.png"),
  arrow_up: require("../assets/images/arrow-up.png"),
  company_address: require("../assets/images/company_address.png"),
  company_desc: require("../assets/images/company_desc.png"),
  mail: require("../assets/images/noun_Mail_6101.png"),
  phone: require("../assets/images/noun_Phone_3612568.png"),
  map_marker:require('../assets/images/map_marker.png'),
  edit2: require("../assets/images/edit2.png"),  
  Resend: require("../assets/images/Resend.png"), 
  completed: require("../assets/images/completed.png"), 
  pending:require("../assets/images/pending.png"), 
  dotBell:require("../assets/images/dotbell.png"),
  arrowDown:require('../assets/images/downArrow.png'),
  refresh:require('../assets/images/refresh.png'),
  Search1: require("../assets/images/Search1.png"),
  membersCount: require("../assets/images/group1.png"),
  editNew:require("../assets/images/EditNew.png"),
  deleteNew:require("../assets/images/DeleteNew.png"),
  resendNew:require("../assets/images/Resend1.png"),
  ham:require("../assets/images/ham.png"),
  calendarSettings:require("../assets/images/CalendarSettings.png"),
  editBlack:require("../assets/images/editBlack.png"),
  clockGrey:require("../assets/images/clockGrey.png"),
  deleteOrange:require("../assets/images/deleteOrange.png"),
  phoneRes:require("../assets/images/phoneRes.png"),
  mailRes:require("../assets/images/mail.png"),
  allday:require('../assets/images/allday.png'),
  nocompany:require('../assets/images/nocompany.png'),
  noequipment:require('../assets/images/noequipment.png'),
  nomember:require('../assets/images/nomember.png'),
  nodelivery:require('../assets/images/nodelivery.png'),
  plusdelivery:require('../assets/images/plusdelivery.png'),
  pluscrane:require('../assets/images/pluscrane.png'),
  plusconcrete:require('../assets/images/plusconcrete.png'),
  plusinspection:require('../assets/images/Inspection_cal.png'),
  plusclose:require('../assets/images/plusclose.png'),
  companylogo:require('../assets/images/companyLogo.png'),
  alert:require('../assets/images/alert.png'),
  more:require('../assets/images/more.png'),
  help:require('../assets/images/help.png'),
  voidList:require('../assets/images/voidList.png'),
  expand:require('../assets/images/expand.png'),
  copyLink:require('../assets/images/copyLink.png'),
  download:require('../assets/images/download.png'),
  passtick:require('../assets/images/pass_tick.png'),
  crossdetails:require('../assets/images/cross_details.png'),
  close_icon:require('../assets/images/close_icon.png'),
}

export default Images;