const Strings = {
  landing: {
    appDescText1: "Reimagine",
    appDescText2: "the jobsite logistics & collaboration",
  },
  dashboard: {
    lorem: "Lorem ipsum dolor sit amet",
    consectetuer: "consectetuer",
    signin: "Sign In",
    signup: "Sign Up",
    login: "Login",
    createAccount: "Create an account",
    dashboard: "Dashboard",
    upcomingDeliveries: "Upcoming Deliveries",
    upcomingCraneDeliveries:"Upcoming Cranes",
    upcomingConcreteDeliveries:"Upcoming Concrete",
    viewall: "View All",
    Deliveries_Assigned_to_me: "Deliveries Assigned To Me",
    no_concrete: "No Upcoming Concrete Bookings Found",
    no_crane: "No Upcoming Crane Bookings Found",
    logistics_plan: "Site Plan",
  },
  bottomMenuLabels: {
    home: "Home",
    calendar: "Calendar",
    Notification: "Notifications",
  },
  step1: {
    welcome: "Welcome to Follo",
    next: "Next",
    step: "Step",
    //final: " of 4",
    final : " of 3"
  },
  step2: {
    company: "Your company details",
  },
  step3: {
    projectDetails: "Project Details",
    signUp: "Sign Up",
    add: "Add",
    next: "Next",
  },
  step4: {
    choose: "Choose the appropriate \n plan for you",
    month: "Billed monthly",
    year: "Billed annually",
    trailDes: "Enjoy Free 15 days Trial period to access the Follo",
    projectDes: "Lorem ipsum dolor sit amet, consectetuer",
    enterpriseDes:
      "Please contact our Sales to get your Enterprise plan on an exciting prize !",
  },
  creditCard: {
    cardDetails: "Card Details",
    payable: "Payable Amount",
    month: "month",
    year: "Year",
    cancel: "Cancel",
  },
  subsriptionThanks: {
    desc:
      "Thank you for the subscription with Follo. We have sent the link with login credentials to your email id. \n\n Please start accessing your project from the link shared.",
    close: "Close",
  },
  login: {
    welcomeBack: "Welcome Back",
    forgotPassword: "Forgot Password?",
    signIn: "Sign In",
    login: "Login",
    submit: "Submit",
    enterEmail: "Enter the email address associated with your account.",
    resetPassword: " Reset Password",
  },
  menu: {
    members: "Members",
    editMembers: "editMembers",
    company: "Companies",
    gates: "Gates",
    dr: "Delivery Requests",
    equip: "Equipments",
    df: "Definable Feature of Work",
    dfow: "DFOW",
    settings: "Settings",
    project: "Project",
    addNew: "Add New Project",
    logout: "Logout",
    projects: "Projects",
    addNewDr: "Add New Delivery Booking",
    addNewIn: "Add New Inspection Booking",
    voidlist: "Void List",
    specification:"Specification Section",
    spec:"Spec",
    addCompany:"Add Company",
    allRequest:'All Bookings',
    allItems:"All Items",
    addNewCrane:"Add New Crane",
    calendarSettings:"Calendar Settings",
    addNewConcrete:"Add New Concrete",
    Help:"Help"
  },
  addMember: {
    add: "Edit Member",
    id: "Member ID",
    submit: "Submit",
    cancel: "Cancel",
    company: "Company",
    email: "Email",
    phone: "Phone No",
    update: "Update",
    invite: "Invite",
    chooseCode: "Choose Country Code",
    chooseCompany: "Choose Company",
    chooseRole: "Choose Role",
    chooseProject: "Choose Project",
    reset: "Reset",
    skip:'Skip',
  },
  inviteMember: {
    title1: "Invite Member",
    title2: "Add and Invite multiple users to your project with their emails.",
    title3: "Enter The Email Id Here",
    notes: '* Note: Please press "return key" after an email to save it',
    id: "Memeber ID",
    submit: "Submit",
    cancel: "Cancel",
    company: "Company",
    email: "Email",
    phone: "Phone No",
    update: "Update",
    chooseCode: "Choose Country Code",
    chooseCompany: "Choose Company",
    chooseRole: "Choose Role",
    chooseProject: "Choose Project",
    reset: "Reset",
  },
  addDR: {
    ndr: "New Delivery Booking",
    edit:"Edit Delivery Booking",
    info:
      "The delivery needs to be approved by Project Administrator to be added to the calendar. Please click on submit to send the request for approval.",
    escortNeeded: "Escort Needed?",
    responsible: "Responsible Person",
    deliveryDate: "Delivery Date",
    void: "Void List",
    dateTime: "Date and Time",
    approvedBy: "Approved By",
    status: "Status",
    restore: "Restore",
    company:"Responsible Company",
    nib: "New Inspection Booking",
    editin:"Edit Inspection Booking",
    origin: "Origination Address",
    vehicleType: "Vehicle Type",
    inspectionType: "Inspection Type",
    originTitle: "Origin Address",
    inspectionStatus: "Inspection Status", 

  },
  // addIN: {
  //   nib: "New Inspection Booking",
  //   edit:"Edit Inspection Booking",
  // },
  addNewEvent:{
    title:"Add New Event",
    edit:"Edit New Event",
    fromDate:"From Date",
    toDate:"To Date",
    allDay:"All Day",
    eventApplicableto:"Event Applicable To",
    deliveryCalendar:"Delivery",
    craneCalendar:"Crane",
    concreteCalendar:"Concrete",
    inspectionEvent:'Inspection',
    recurrence:"Recurrence",
    inspection:"Inspection Type",
    repeat:"Repeat Every",
    timezone:'Time Zone',
    endDate:"End Date",
    occursDay:"Occurs on day",
    occursEveryDayUntil:'Occurs every day until',
    occursEvery:"Occurs every",
    occurs:"Occurs on the",
    until:"until",
    mandatory:"*",
    timeZoneSearch:"Search",
    times:'Times',
    onDay:"On day",
    onThe:"On the"
  },
  dsiplayEvent:{
    title:"Event Details",
  },
  addCraneRequest:{
    info:
      "The crane delivery needs to be approved by Project Administrator to be added to the calendar. Please click on submit to send the request for approval.",
    tittle:'New Crane Pick Booking',
    edit:'Edit Crane Pick Booking',
    pickId:'Pick ID',
    crane:'Crane',
    pickingFrom:'Picking From',
    pickingTo:'Picking To',
    pickDate: 'Pick Date',
  },
  addConcrete:{
    tittle:"New Concrete Booking",
    edit:"Edit Concrete Booking",
    info:
    "The concrete needs to be approved by Project Administrator to be added to the calendar. Please click on submit to send the request for approval.",
    placement: "Placement",
    concreteId: "Concrete ID",
    date: "Date",
    fromTime: "From Time",
    toTime: "To Time",
    concreteSupplier: "Concrete Supplier",
    mixDesign: "Mix Design",
    orderNo: "Order Number",
    slump: "Slump",
    truckSpacing: "Truck Spacing",
    quantity: "Quantity",
    quantityOrdered:'Quantity Ordered (CY)',
    primerForPump: "Primer Ordered For The Pump",
    primer: "Primer For Pump",
    concreteConfirmed: "Concrete Confirmed",
    selectHrs: "Select Hrs",
    selectMins: "Select Mins",
    truckSpacingHrs: "truck spacing Hrs",
    truckSpacingMins: "truck spacing Mins",
    completePlacementHrs: "Completion Time",
    completePlacementMins: "complete placement Mins",
    pumpRequired: "Pump Required",
    pumpSize: "Pump Size",
    pumpOrdered: "Pump Ordered",
    pumpLocation: "Pump Location",
    pumpShowUp: "Pump Showup",
    pumpConfirmed: "Pump Confirmed",
    hrsCompletePlacement: "Hours to complete placement",
    cubicYardsTotal: "Total Cubic Yards Placed",
    pumpDetails: "Pump Details",
    otherDetails: "Other Details",
  },
  concreteCard:{
    concreteSupplier:"Concrete Supplier",
    pimpSize:"Pump Size",
  },
  filter: {
    title: "Filter",
    apply: "Apply",
    pickFrom:"Pick From",
    pickTO:"Pick To",
  },
  addGate: {
    add: "Add Gate",
    addNew: "Add New",
    edit:'Edit Gate',
  },
  addDFOW:{
    add: "Add DFOW",
    edit:'Edit DFOW',
  },
  addEquipment: {
    new: "New Equipment",
    id: "Equipment ID",
    name: "Equipment Name",
    type: "Equipment Type",
    contact: "Contact Person",
    edit: "Edit Equipment",
  },
  addCompany: {
    add: "Add Company",
    chooseCity: "Choose City",
    chooseCountry: "Choose Country",
    chooseState: "Choose State",
    chooseLocation: "Choose Location",
    edit:"Edit Company",
  },
  gates: {
    id: "ID",
    gateName: "Gate Name",
    action: "Action",
    gateId: "Gate ID",
    
  },
  equip: {
    resp: "Responsible Subcontractor",
    type: "Equipment Type",
    id: "ID",
    contact: "Contact Person",
    equipment:'Equipment',
    company:"Responsible Company",
  },
  settings: {
    override: "Override Request",
    changePassword: "Change Password",
    settings: "Settings",
  },
  profile: {
    profile: "Profile",
    submit: "Submit",
    update: "Update",
    ChooseOption: "Choose From",
    Choose: "Choose",
    Are:'Please enter actual\nconcrete placement details'
  },
  search: {
    title: "Search",
  },
  void: {
    voidList: "Void List",
  },
  placeholders: {
    name: "Name",
    firstname: "First Name",
    lastname: "Last Name",
    email: "Email ID",
    mobile: "Mobile Number",
    companyName: "Company Name",
    companyWebsite: "Company Website",
    streetName: "Street Name",
    companyAddress: "Company Address",
    city: "City",
    state: "State",
    country: "Country",
    zipcode: "Zip Code",
    ProjectName: "Project Name",
    recurrenceType:"Recurrence",
    location: "Location",
    cardName: "Name On Card",
    cardNumber: "Card Number",
    expiry: "Expiry Date (MM/YYYY)",
    cvv: "(CVC/CVV)",
    cardCode: "Zipcode",
    password: "Password",
    role: "Role",
    assignProject: "Assign Projects",
    gateName: "Gate Name",
    currentPassword: "Current Password",
    newPassword: "New Password",
    confirmPassword: "Confirm Password",
    addressline1: "Address Line 1",
    addressline2: "Address Line 2",
    additionalNotes: "Additional Notes (Optional)",
    additional: "Additional Notes",
    website: "Website",
    SearchHere: "Search Here",
    definable: "Definable Feature of Work (Scope)",
    company: "Company",
    equipmentName: "Equipment Name",
    id: "ID",
    workEmail: "Work Email",
    description: "Description",
    deliveryId: "Delivery ID",
    craneId:"Crane Request ID",
    responsibleCompany: "Responsible Company",
    dfow: "Definable Feature of Work",
    escort: "Escort Needed?",
    responisblePerson: "Responsible Person",
    deliveryDate: "Delivery Date",
    startTime: "Start Time",
    endTime: "End Time",
    equip: "Equipment",
    gate: "Gate",
    deliveryVehical: "Delivery Vehicle Details",
    type: "Type",
    contactPerson: "Contact Person",
    controlledBy: "Controlled By",
    selectstatus: "Select Status",
    vechicleDetails:'Vehicle Details',
    chooseType:"Choose Type",
    select: "Select",
    status: 'Status',
    supplier:'Select Concrete Supplier',
    mixDesign:'Mix Design',
    orderNumber:'Order Number',
    selectStatus:'Select Status',
    placementStartTime:'Placement Start Time',
    anticipatedCompletionTime:'Anticipated Completion Time',
    pumpShowUpTime:'Pump Show Up Time',
    pumpcompletionTime:'Pump Completion Time',
    notes:'Notes',
    timeZone:'Time Zone',
    completed:'Completed',
    pending:'Pending',
    chooseTimezone:"Choose TimeZone",
    notificationDate:"Notification Date",
    additional_location: 'Additional Location Details',
    Inspection_Type: 'Inspection Type',
    Inspection_Id : 'Inspection ID',
    informationOnlyEvent: 'Information Only Event',
    informationOnlyEventText: 'ℹ️ Enabling this toggle makes this event informational only. Location, Equipment, and Gate selections are disabled. It will not overlap with other bookings in all calendars.'
  },
  errors: {
    emptyName: "Enter Name",
    enterFirst: "Enter first name",
    enterlast: "Enter last name",
    emptyEmail: "Enter email address",
    emptyPassword: "Enter password",
    emptyMobile: "Enter Mobile number",
    emptyCompany: "Enter company name",
    emptyWebsite: "Enter company website",
    emptyStreet: "Enter street name",
    emptyCountry: "Please select country",
    addressline1: "Please enter address line 1",
    emptyCompanyAddress: "Enter company address",
    emptyState: "Please select state",
    emptyCity: "Please select city",
    emptyZipcode: "Enter Zipcode",
    emptyProject: "Enter Project Name",
    validProjectname: "Please enter valid project name",
    emptyLocation: "Please enter location",
    emptyCardname: "Enter card name",
    emptyCardNumber: "Enter Card Number",
    emptyCVV: "Enter CVV",
    emptyPickFrom:'Please enter Picking From',
    emptyPickTo:"Please enter Picking To",
    emptyExpiry: "Enter Expiry date",
    validMonth: "Enter valid expiry month",
    validYear: "Enter valid expiry year",
    emptyMonth: "Enter Expiry Month",
    emptyYear: "Enter Expiry Year",
    validEmail: "Please enter valid email",
    validZipCode: "Please enter valid zipcode",
    validPassword: "Please enter valid password",
    validCurre: "Invalid Current Password",
    validConfirm: "Invalid Confirm Password",
    validName: "Please enter valid name",
    validFirst: "Please enter valid first name",
    validlast: "Please enter valid last name",
    validMobile: "Please enter valid mobile number",
    validCompany: "Please enter valid company name",
    validwebsite: "Please enter valid company website",
    minLength: "length must be at least 3 characters long",
    validationFailed: "Validation Failed",
    validCardNumber: "Please enter valid card number",
    validEquipName: "Please enter valid equipment name",
    validEquipType: "Please enter valid equipment type",
    projectListFailed: "Failed to get project details",
    selectState: "Please select state first",
    selectCountry: "Please select country first",
    exists: "Email/Phone Number already exists",
    noData: "No Data Found",
    emptyGateName: "Enter gate name",
    failed: "Failed to get data",
    emptyEquipName: "Please enter equipment name.",
    emptyEquipType: "Please enter equipment type.",
    emptyEquipContactPerson: "Please select contact person.",
    emptyRole: "Please select role.",
    emptyAssign: "Please select project to Assign.",
    emptyCurrentPassword: "Please enter current password.",
    emptyNew: "Please enter new password.",
    emptyconfirm: "Please enter confirm password.",
    validCurrent: "Please enter valid Password.",
    validNew:
      "New Password must contain 7 to 20 characters, including letters and at least 1 number and 1 symbol ($, %, !, @, #, &, ?)",
    mismatch: "New Password and Confirm Password mismatch.",
    timeout: "Error: timeout of 15000ms exceeded.",
    checkInternet: "Please check your internet connectivity.",
    noRecords: "No Record found",
    emptyDescription: "Please enter description",
    emptyRespCompany: "Please select responsible company",
    emptyResponsiblePerson: "Please select responsible person",
    validResponsiblePerson: "Please select valid responsible person",
    emptyDefinabel: "Please select definable feature of work",
    emptydeliverData: "Please select delivery date",
    emptyStartTime: "Please select start time",
    emptyEndTime: "Please select end time",
    emptyEquip: "Please select equipment type",
    emptyGate: "Please select gate",
    empty_type: "Please select inspection type",
    emptyVehicleDetails: "Please enter vehicle Details",
    validEndDate: "Please select Future end date",
    notValidTime: "Please Enter Start time Lesser than End time",
    notValidSameTime:"Start time and End time Should not be the same",
    pumpNotValidTime: "Please Enter Pump Show Up time Lesser than Pump Completion time",
    startTimeAndEnd: "Start time and End time should not less then 1 hour",
    pumpStartTimeAndEnd: "Pump Show Up time and Pump Completion time should not less then 1 hour",
    pumpSameTime:"Pump Show Up time and Pump Completion time should not be the same",
    validWebsite: "Please enter valid website url",
    emptyDfow: "Enter DFOW",
    lengthEquipName: "Equipment name must be at least 3 character long",
    lengthEquiptype: "Equipment type must be at least 3 character long",
    validgate: "Please enter valid gate name",
    validDfow: "Please enter valid Definable Fetaure of Work",
    lengthError: "length must be at least 3 characters long",
    emptySpecification:"Enter Specification Section",
    something:'Something Went Wrong',
    selectCalendar:"Please select applicable calendar(s)",
    recurrence:"Recurrence is required",
    futureDate:"Please select Future Date",
    emptyConcreteSuppliers: "Please select concrete suppliers",
    emptyPlacement: "Please enter placement",
    emptyDate: "Please select Date",
    emptyFromTime: "Please select From time",
    emptyToTime: "Please select To time",
    emptySupplier: "Please select concrete supplier",
    emptyMixDesign: "Please select mix design",
    emptyOrderNo: "Please enter order number",
    emptyPumpSize: "Please enter pump size",
    emptyPumpOrderedDate: "Please select Pump Ordered Date",
    emptyPumpLocation: "Please select Pump Location",
    emptyPumpShowUp: "Please select Pump Show Up",
    emptySlump: "Please enter slump",
    emptyQuantity: "Please enter quantity ordered",
    emptyPrimerForPump: "Please enter primer ordered for the pump",
    emptyCubicYardsTotal: "Please enter cubic yards total",
    emptyTruckSpacing:'Please enter Truck Spacing',
    entervalues:'Please fill atleast one value',
    errorDecimalNumeric:'Please Enter only Numbers  for Order Number',
    errorEnddate:'End date must be greater than start date',
    errorEndTime:'End time must be greater than start time',
    emptyResponseValue:"Please select an active member,the existing member is deactivated.",
    emptyEquipValue:"Please select an active equipment, the existing equipment is deactivated.",
    emptyGateValue:"Please select an active gate,the existing gate is deactivated.",
    emptyEquipGateResp:"Are you sure? The gate,equipment, member mapped to the request is deactivated, do you still want to deliver the request?",
    emptyEquipGate:"Are you sure? The gate,equipment mapped to the request is deactivated, do you still want to deliver the request?",
    emptyEquipResp:"Are you sure? The equipment,member mapped to the request is deactivated, do you still want to deliver the request?",
    emptyGateResp:"Are you sure? The gate,member mapped to the request is deactivated, do you still want to deliver the request?",
    emptyEquipDetails:"Are you sure? The Equipment mapped to the request is deactivated, do you still want to deliver the request?",
    emptyGateDetails:"Are you sure? The gate mapped to the request is deactivated, do you still want to deliver the request?",
    emptyRespoDetails:"Are you sure? The responsible person mapped to the request is deactivated, do you still want to deliver the request?",
    emptyResponsiblePersonDetails:"Are you sure? The responsible person mapped to the request is deactivated, do you still want to deliver the request?",
    emptyDeliveryDate:"Please enter Future start or end date.",
    projectSetting:"Booking not allowed to edit. Please contact the project administrator to edit this booking",
    futureTime:"Cannot select a past time. Please select a future time.",

  },
  popup: {
    success: "Success!",
    forgotSuccess: "Reset password email sent successfully",
    loginSuccess: "Login Successfully.",
    contaceSales: "Please contact sales team",
    gateUpdate: "Gate Updated successfully.",
    updateFailed: "Failed to update data",
    getMemEqui: "Member listed Successfully.",
    equipCreated: "Equipment added successfully.",
    memberCreated: "Member Created Successfully.",
    memberInvite: "Member Invite Successfully.",
    changepasswordSuccess: "Password has been changed succesfully.",
    delete: `${'Are you sure'}\n${' you want to delete ?'}`,
    restore: "Are you sure you want to restore ?",
    logout: `${'Are you sure'}\n${'you want to logout ?'}`,
    cancelSub: "Are you sure you want to cancel the subscription ?",
    createcomment: "Comment added successfully.",
    approvedstatus: "Approved Successfully.",
    declinestatus: "Declined Successfully.",
    deliveredStatus: "Delivered Successfully.",
    downloadStatus: "Downloaded Successfully",
    beforeDeliveryPopUp:"Are you sure have attached the invoice receipt?",
    cancel:`${'Are you sure'}\n${'you want to cancel ?'}`
  },
  deliverydetails: {
    deliverydetails: "Delivery Details",
    craneDetails:"Crane Request Details",
    inspectiondetails:"Inspection Details",
    itemdescription: "Description",
    deliveryid: "Delivery ID",
    inspectionid : "Inspection ID",
    craneId:"Crane Request ID",
    timeanddate: "Time & Date",
    responsiblecompany: "Responsible Company",
    gateno: "Gate",
    equipmentneeded: "Equipment Needed",
    featureofwork: "DFOW",
    deliveryvehicledetail: "Delivery Vehicle Detail",
    appliedby: "Applied by",
    note: "Note",
    addattachment: "Add attachment",
    entercomments: "Enter your comments here",
    done: "Done",
    files: "Files",
    responsiblePerson:"Responsible Person",
    deliveryStatus:"Delivery Status",
    pickFrom:"Picking From",
    pickTo:"Picking To",
  },
  concreteDetails:{
    title:'Concrete Details',
    concreteId:'Concrete ID',
    location:'Location',
    Inspection_type:'Inspection Type',
    placement:'Placement',
    concreteSupplier:'Concrete Supplier',
    mixDesign:'Mix Design',
    orderNumber:'Order Number',
    slump:'Slump',
    truckSpacing:'Truck Spacing',
    concreteRequestStatus:'Concrete Request Status',
    quantityOrdered:'Quantity Ordered',
    primerForPump:'Primer Ordered for the Pump',
    concreteConfirmed:'Concrete Confirmed',
    pumpSize:'Pump Size',
    pumpOrdered:'Pump Ordered',
    pumpLocation:'Pump Location',
    pumpShowUpTime:'Pump Show Up Time',
    pumpConfirmed:'Pump Confirmed',
    hoursToCompletePlacement:'Hours To Complete Placement',
    totalCubicYardPlaced:'Total Cubic Yard Placed',
    date:'Date',
    time:'Time',
    pumpDetails:'Pump Details',
    otherDetails:'Other Details',
    notes:'Notes',
  },


  calender: {
    deliveryCalendar: "Delivery Calendar",
    craneCalendar:"Crane Calendar",
    concreteCalendar:'Concrete Calendar',
    inspectionCalender:'Inspection Calendar',
    selectLocation:"Select Location",
    selectMixPanel:"Select Mix Design",

    
  },
  notification: {
    notification: "Notifications",
    markAllRead:"Mark All As Read",
    action:"Action",
  },
  passwordValidationMessage: {
    title: "oops..!",
    errorMessage:
      "Password must be eight characters including one uppercase letter, one special character and alphanumeric characters",
    tokenExpired: "Password reset link expired",
  },
  passwordMissmatch: {
    title: "oops..!",
    errorMessage: "Password Miss Matched",
  },
  passwordChangeSuccess: {
    title: " ",
    message: "Password reset successfully",
  },
  //map component
  map: {
    title: "Select The Location",
    pinLabel: "Project Address",
    buttonText:"Continue"
  },
  autoCompletePlaces: {
    emptyList : "No Places Found"
  },
  timeLine:{
    allDay:'All Day',
  },

  concrete: {
    concreteDetails: "Concrete Details",
  },
  mixpanel:{
    addCalendarEvent:"Added Calendar Event" ,
    editCalendarEvent:"Edited Calendar Event",
    deleteCalendarEvent:"Deleted Calendar Event",
  },
  platforms:{
    ios:'ios',
    android:'android',
  },
  datePicker:{
    set:"set",
    dismissed:'dismissed',
    inline:'inline',
    light:'light',
    default:'default',
  },

  deleteError:{
    oops:'Oops!'
  },
  events:{
    calendarEvent:"calendarEvent",
    week:'week',
    weeks:'weeks',
    deliveryRequestWithCrane:"deliveryRequestWithCrane",
    craneRequest:"craneRequest"
  },
  forceUpdate:{
    new:'New',
    whatNew:"What's new",
    learnMore:'Learn More',
    updateNow:'Update now',
    skipNow:'Skip for now',
    updateAvailable:'Update available!'
  },
  toast:{
    success:"success",
    error:"error",
  },
  calendarSettings:{
    doseNotRepeat:"Does Not Repeat",
    daily:"Daily",
    weekly:"Weekly",
    yearly:"Yearly",
    monthly:"Monthly",
    month:"month",
    day:"Day",
    week:"Week",
    upperCaseMonth:"Month",
    year:"Year",
    first:"First",
    second:"Second",
    third:"Third",
    fourth:"Fourth",
    last:"Last",
  },
  keyboardTypes:{
    numeric:"numeric",
  },
  versionName:{
    version:"version",
    iosVersion:"iosVersion"
  },
  noInternet:{
    connection:"No Internet Connection",
    checkConnection:"Please check your connection and",
    tryAgain:"try again",
    refresh:"Refresh"
  },
  deliveryStatusName:{
    expired:"Expired"
  },
  permissions: {
  camera_permission: '"Follo" would like to access your Camera',
  files_media_permission: '"Follo" would like to access your External Storage',
  no_thanks: 'No thanks',
  go_to_settings: 'Go to Settings',
  }
};

export default Strings;