export const isValidEmail = text => {
    let reg = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
    return !reg.test(text);
};

export const isEmpty = text => {
    return (text === undefined || text === null|| text.length === 0);
};

export const isValidPassword = text => {
    let reg = /^((?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%?=*&]).{8,20})$/;
    return !reg.test(text);
};

export const getuniqId = () => {

    var S4 = function() {
       return (((1+Math.random())*0x10000)|0).toString(16).substring(1);
    };

    return (S4()+S4()+"-"+S4()+"-"+S4()+"-"+S4()+"-"+S4()+S4()+S4());
}


 export const isValidName = text => {
     let reg =  /^[a-zA-Z]+$/;
     return !reg.test(text);
 };
 
export const isName = text => {
    let reg =  /^[A-Za-z\s]{1,}[\.]{0,1}[A-Za-z\s]{0,}$/;
    return !reg.test(text);
};

export const isAlphaNumeric = text => {
    let reg =  /^[A-Za-z0-9\s]{1,}[\.]{0,1}[A-Za-z0-9\s]{0,}$/;
    return !reg.test(text);
}

export const isCompany = text => {
    let reg =  /^[A-Za-z0-9&\s]{1,}[\.]{0,1}[A-Za-z&\s]{0,}$/;
    return !reg.test(text);
}

export const isAddress = text => {
    let reg =  /^[A-Za-z#,\s]{1,}[\.]{0,1}[A-Za-z#,\s]{0,}$/;
    return !reg.test(text);
}

export const isNumeric = text => {
    let reg =  /^[0-9]*$/;
    return !reg.test(text);
}
export const isEmptyString =text =>{
    let  reg = /^\s+$/;
    return !reg.test(text);
}