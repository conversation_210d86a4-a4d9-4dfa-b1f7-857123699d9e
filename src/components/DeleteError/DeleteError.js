import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
} from "react-native";
import Modal from "react-native-modal";
import { Images, Strings, Fonts, Colors } from "../../common";
class DeleteError extends Component {
  constructor(props) {
    super(props);
  }
  render() {
    return (
      <Modal
        isVisible={true}
        hasBackdrop={true}
        onBackdropPress={this.props.close}
        animationIn="fadeIn"
        animationOut="fadeOut"
      >
        <View style={styles.mainContainer}>
          <Image source={Images.alert} style={styles.image} />
          <Text style={styles.firstText}>{Strings.deleteError.oops}</Text>
          <Text style={styles.secondText}>{this.props.message}</Text>
        </View>
      </Modal>
    );
  }
}
const styles = StyleSheet.create({
  mainContainer: {
    alignSelf: "center",
    alignItems: "center",
    alignContent: "center",
    height: "25%",
    width: "100%",
    backgroundColor: Colors.white,
    padding: 20,
    borderRadius: 10,
  },
  image: { height: 50, width: 55 },
  firstText: {
    fontFamily: Fonts.montserratSemiBold,
    fontSize: 18,
    color: Colors.black,
    marginTop: 10,
  },
  secondText: {
    fontFamily: Fonts.montserratMedium,
    fontSize: 14,
    color: Colors.black,
    marginTop: 10,
    textAlign: "center",
  },
});
export default DeleteError;
