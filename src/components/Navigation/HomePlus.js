import React, { Component } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Text,Platform,
  Image,
} from "react-native";
import Modal from "react-native-modal";
import {
  heightPercentageToDP as hp
} from "react-native-responsive-screen";
import { BlurView } from "@react-native-community/blur";
import { Colors, Fonts, Images } from "../../common";
class HomePlus extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isModal: false,
    };
  }
  render() {
    return (
      <Modal
        isVisible={this.props.isModal}
        hasBackdrop={false}
        style={Styles.modalContainer}
        animationIn={"fadeIn"}
        animationOut="fadeOut"
        backdropColor="white"
        backdropOpacity={0.1}
      >
        <BlurView
          style={Styles.blurViewStyle}
          blurRadius={0.1}
          blurType={"light"}
          onPress={() =>{this.props.close()}}
          reducedTransparencyFallbackColor='white'
          downsampleFactor={0}
          overlayColor='white'
        />
        <View style={Styles.container}>
        <TouchableOpacity
            onPress={() => this.props.onPress(1)}
            style={Styles.innerContainer}
          >
            <Image source={Images.plusinspection} style={Styles.image} />
            <Text style={Styles.text}>Inspection Booking</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => this.props.onPress(2)}
            style={Styles.innerContainer}
          >
            <Image source={Images.plusconcrete} style={Styles.image} />
            <Text style={Styles.text}>Concrete Booking</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => this.props.onPress(3)}
            style={Styles.innerContainer}
          >
            <Image source={Images.pluscrane} style={Styles.image} />
            <Text style={Styles.text}>Crane Booking</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => this.props.onPress(4)}
            style={Styles.innerContainer}
          >
            <Image source={Images.plusdelivery} style={Styles.image} />
            <Text style={Styles.text}>Delivery Booking</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={Styles.plusContainer}
            onPress={() => this.props.close()}
          >
            <Image source={Images.plusclose} style={Styles.closeimage} />
          </TouchableOpacity>
        </View>
      </Modal>
    );
  }
}
export default HomePlus;
const Styles = StyleSheet.create({
  modalContainer: {
    margin: 0,
    justifyContent: "flex-end",
    alignItems: "center",
    backgroundColor: Platform.OS=='android'?'rgba(255, 255, 255, 0.9)': 'rgba(255, 255, 255, 0.4)'
  },
  text: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: 14,
    alignSelf: "center",
  },
  container: {
    alignContent: "center",
    justifyContent: "center",
    marginLeft: hp("15.5"),
    bottom: Platform.OS=='android'?hp('4.3'):70,
  },
  innerContainer: {
    flexDirection: "row",
  },
  blurViewStyle: {
    position: Platform.OS=='android'?'relative':"absolute",
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  },
  plusContainer: {
    marginLeft: Platform.OS=='android'?8:-2,
  },
  image: {
    height: 50,
    width: 50,
  },
  closeimage: {
    height: 55,
    width: 55,
  },
});