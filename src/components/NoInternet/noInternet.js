    import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View, Image,Platform} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Colors from '../../common/color';
import Strings from '../../common/string';
import Fonts from '../../common/fonts';

export default function NoInternet(props) {
    return(
        <View style={styles.mainContainer}>
            <View style={styles.subContainer}>
            <Image
            source={require('../../assets/images/nointernet.gif')}
            style={styles.imageStyle}
            />
            </View>
            <Text style={styles.connectionText}>{Strings.noInternet.connection}</Text>
            <Text style={styles.checkConnectionText}>{Strings.noInternet.checkConnection}</Text>
            <Text style={styles.tryAgainText}>{Strings.noInternet.tryAgain}</Text>
            <TouchableOpacity onPress={props.Refresh} style={styles.buttonContainer}>
                <Text style={styles.refreshText}>{Strings.noInternet.refresh}</Text>
            </TouchableOpacity>
        </View>
    )
}

const styles = StyleSheet.create({
    mainContainer:{
        flex:1,
        backgroundColor:Colors.white,
        justifyContent:'center',
        alignItems:'center',
        marginHorizontal:5
    },
    subContainer:{
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
        padding: 3,
        marginBottom:10
    },
    imageStyle:{
        width: Platform.OS == "android"? wp("91%") : wp("89%"),
        height:Platform.OS == "android"? hp("45%"): hp("41%"),
    },
    connectionText:{
        color: Colors.internetText,
        fontSize: 20,
        fontFamily: Fonts.montserratBold,
        margin:10
    },
    checkConnectionText:{
        color: Colors.skipNowText,
        fontSize: 18,
        fontFamily: Fonts.montserratRegular,
        marginTop:10
    },
    tryAgainText:{
        color: Colors.skipNowText,
        fontSize: 18,
        fontFamily: Fonts.montserratRegular,
        marginBottom:10
    },
    refreshText:{
        color: Colors.white,
        fontSize: 18,
        fontFamily: Fonts.montserratRegular,
        margin:5
    },
    buttonContainer:{
        backgroundColor: Colors.internetText,
        width: wp("30%"),
        height: hp("6%"),
        borderRadius: hp("5%"),
        justifyContent: "center",
        alignItems: "center",
        margin:10
    }
})