import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  Platform,
  Keyboard,
} from "react-native";

import { TextField } from "../textinput/addMemberTextinput";
import { listOfRecurrence,Colors, recurrenceList, repetRecurrenceList, Strings, Fonts, Images } from "../../common";

import Dropdown from "../dropdown/dropdown";

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import DropDownPicker from "../dropdown/DropDownPicker";
import { Avatar } from "react-native-paper";
import Modal from "react-native-modal";
import DateTimePicker from "@react-native-community/datetimepicker";
import moment from "moment";
import { stat } from "react-native-fs";

class RecurrenceComponent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedDayCheck: this.props.selectedDayCheck,
      editDayList : this.props.selectedDay ?? [],
      selectedRecur: "",
      endDate: moment(new Date()).format("MM/DD/YYYY"),
      selectedEndDate: new Date(),
      isEndDate: false,
      isYearFirstCheck: true,
      isYearSecondCheck: false,
      isYearThirdCheck: false,
      recurrence:Strings.calendarSettings.doseNotRepeat,
      isRecurrence:false,
      recurenceEndDate:new Date(),
      daysList: [
        { key: 1, name: "Sunday", label: "S", selected: true },
        { key: 2, name: "Monday", label: "M", selected: true },
        { key: 3, name: "Tuesday", label: "T", selected: true },
        { key: 4, name: "Wednesday", label: "W", selected: true },
        { key: 5, name: "Thursday", label: "T", selected: true },
        { key: 6, name: "Friday", label: "F", selected: true },
        { key: 7, name: "Saturday", label: "S", selected: true },
      ],
    };
  }

  
  // componentDidUpdate(prevProps) {
  //   if (prevProps.recurranceEdit !== this.props.recurranceEdit) {
  //     this.setState({ iseditable: this.props.recurranceEdit });
  //   }
  //   if (prevProps.selectedDay !== this.props.selectedDay) {
  //     this.setState({ editDayList: this.props.selectedDay });
  //   }
  //   if (prevProps.selectedDay !== this.props.selectedDay) {
  //     const updatedDaysList = this.state.daysList.map((day) => ({
  //       ...day,
  //       selected: this.props.selectedDay.includes(day.name),
  //     }));
  
  //     console.log("Updated daysList:", updatedDaysList); 
  
  //     this.setState({
  //       editDayList: this.props.selectedDay,
  //       daysList: updatedDaysList,
  //     });
  //   }
  // }

  componentDidUpdate(prevProps) {
    console.log("componentDidUpdate prevProps.selectedDay", prevProps.selectedDay);
  
    // Ensure selectedDay is always an array
    const selectedDay = Array.isArray(this.props.selectedDay) ? this.props.selectedDay : [];
  
    if (prevProps.selectedDay !== this.props.selectedDay) {
      const updatedDaysList = this.state.daysList.map((day) => ({
        ...day,
        selected: selectedDay.includes(day.name), // Use the safe selectedDay array
      }));
  
      console.log("Updated daysList:", updatedDaysList);
  
      this.setState({
        editDayList: updatedDaysList,
        daysList: updatedDaysList,
      });
  
      // Collect selected days for callback
      let selectedDaysArray = updatedDaysList
        .filter(day => day.selected)
        .map(day => day.name);
  
      let selectedDaysString = selectedDaysArray.join(", ");
      
      const { onChangeSelectedDay } = this.props;
      if (onChangeSelectedDay) {
        onChangeSelectedDay(selectedDaysString);
      }
    }
  }
  
  
  componentDidMount() {
    this.onMonthChanged();
  }
  componentWillReceiveProps=(nextProps)=>{
    if(this.props.isFromDateChanged){
      this.onMonthChanged();
    }
    this.setState({
      recurenceEndDate: new Date(this.props.endDateCheck)
    })
  }
  onMonthChanged = () => {
    const {fromDate}= this.props;
    const { onChangeMonthDay, onChangeMonthLastDay }=this.props
    const startDate = moment(fromDate).format("YYYY-MM");
    const chosenDay = moment(fromDate).format("dddd");
    const day = moment(startDate, "YYYY-MM")
      .startOf(Strings.calendarSettings.month)
      .day(chosenDay);
    const getAllDays = [];
    if (day.date() > 7) day.add(7, "d");
    const month = day.month();
    while (month === day.month()) {
      getAllDays.push(day.toString());
      day.add(7, "d");
    }
    let week;
    let extraOption;
    getAllDays.forEach((element, i) => {
      if (
        moment(fromDate).format("YYYY-MM-DD") ===
        moment(element).format("YYYY-MM-DD")
      ) {
        const number = i + 1;
        if (number === 1) {
          week = Strings.calendarSettings.first;
        }
        if (number === 2) {
          week = Strings.calendarSettings.second;
        }
        if (number === 3) {
          week = Strings.calendarSettings.third;
        }
        if (number === 4) {
          extraOption = Strings.calendarSettings.last;
          week = Strings.calendarSettings.fourth;
        }
        if (number === 5) {
          week = Strings.calendarSettings.last;
        }
        if (number === 6) {
          week = Strings.calendarSettings.last;
        }
      }
    });
    let monthlyDayOfWeek = `${week} ${chosenDay}`;
    onChangeMonthDay(monthlyDayOfWeek)
    onChangeMonthLastDay(extraOption)
    this.props.onFromDateChanged(false)
  };
  onchangeEndDate = (tevent, selectedValue) => {
    const {onChangeEndDate,onChangeSelectedEndDate} = this.props
    if(Platform.OS == "android"){
    if (
      tevent.type == Strings.datePicker.set ||
      tevent.type == Strings.datePicker.dismissed
    ) {
      this.setState({
        isEndDate: false,
      });
    }
   }

    if (
      Platform.OS == Strings.platforms.ios ||
      tevent.type == Strings.datePicker.set
    ) {
      const fullYear = selectedValue.getFullYear();
      const fullMonth = selectedValue.getMonth();
      const date = selectedValue.getDate();
      let sampleDate = `${fullMonth + 1}/${date}/${fullYear}`;

      onChangeEndDate(moment(sampleDate).format("MM/DD/YYYY"))
      onChangeSelectedEndDate(selectedValue)

      this.setState(
        {
          selectedEndDate: selectedValue,
        },
        () => this.onMonthChanged()
      );
    }
  };
  onDatePickerEndDonePressed() {
    this.setState({ isEndDate: false });
  }
  updateDay = () => {
    let updateList = this.state.daysList;
    for (let [index] of updateList.entries()) {
      updateList[index].selected = true;
    }
    this.setState({ daysList: updateList });
  };
  updateWeek = () => {
    let updateList = this.state.daysList;
    for (let [index] of updateList.entries()) {
      if (index == 1) {
        updateList[1].selected = true;
      } else {
        updateList[index].selected = false;
      }
    }
    this.setState({ daysList: updateList });
  };

  setDay = (item, index) => {
    const { onChangeSelectedDay, onChangeSelectedDayArray } = this.props;
  
    // Toggle selection for clicked day
    let updatedList = this.state.daysList.map((day, i) => 
      i === index ? { ...day, selected: !day.selected } : day
    );
  
    // Count the number of selected days
    let countTrue = updatedList.filter(day => day.selected).length;
    let countFalse = updatedList.length - countTrue;
  
    // Set recurrence based on selection
    if (countTrue === 7) {
      this.setState({
        selectedRecur: Strings.calendarSettings.day,
        selectedRecurrence: Strings.calendarSettings.daily,
      });
      this.props.onChangeRecrrenceName(Strings.calendarSettings.daily);
    } else {
      this.setState({
        selectedRecur: Strings.calendarSettings.week,
        selectedRecurrence: Strings.calendarSettings.weekly,
      });
      this.props.onChangeRecrrenceName(Strings.calendarSettings.weekly);
    }
  
    // Prevent deselecting all days (ensure at least one remains selected)
    if (countFalse === 7) {
      updatedList = this.state.daysList.map((day, i) => 
        i === index ? { ...day, selected: true } : day
      );
    }
  
    this.setState({ daysList: updatedList });
  
    // Collect selected days for callback
    let selectedDaysArray = updatedList
      .filter(day => day.selected)
      .map(day => day.name);
  
    let selectedDaysString = selectedDaysArray.join(", ");
  
    onChangeSelectedDay(selectedDaysString);
    onChangeSelectedDayArray(selectedDaysArray);
  };
  

  renderDay = ({ item, index }) => {
    console.log("days for edit---->",this.state.editDayList);
    return (
      <TouchableOpacity onPress={() => this.setDay(item, index)}>
        <Avatar.Text
          size={Platform.OS === Strings.platforms.android ? 35 : 40}
          label={item.label}
          color={item.selected ? Colors.white : Colors.lightGrey}
          style={[
            Styles.avatarIcon,
            {
              backgroundColor: item.selected
                ? Colors.themeColor
                : Colors.lightOrange,
            },
          ]}
        />
      </TouchableOpacity>
    );
  };
  

  // Month Recurrence Occurs radion-button Option Change (FirstCheck,SecondCheck, ThirdCheck)
  onChangeMonthThreeOptions = (isCheckOne = true, isCheckTwo = false, isCheckThird = false, value='') => {
    const { onChangeMonthFirstCheck, onChangeMonthSecondCheck, onChangeMonthThirdCheck,onChangeListName } = this.props;
    onChangeMonthFirstCheck(isCheckOne);
    onChangeMonthSecondCheck(isCheckTwo);
    onChangeMonthThirdCheck(isCheckThird);
    onChangeListName(value)
  }

  // Year Recurrence Occurs radion-button Option Change (FirstCheck,SecondCheck, ThirdCheck)
  onChangeYearThreeOptions = (isCheckOne = true, isCheckTwo = false, isCheckThird = false, value='') => {
    const { onChangeYearlyFirstCheck, onChangeYearlySecondCheck, onChangeYearlyThirdCheck,onChangeYearListName } = this.props;
    onChangeYearlyFirstCheck(isCheckOne);
    onChangeYearlySecondCheck(isCheckTwo);
    onChangeYearlyThirdCheck(isCheckThird);
    onChangeYearListName(value)
  }

 

  render() {
    const {fromDate,
      recurrenceName, onChangeRecrrenceName, timeValue, onChangeTimeValue, monthFirstCheck,
      monthSecondCheck, monthThirdCheck, onMonthDay, onMonthLastDay, yearlyFirstCheck,recurrenceTypeData,
      yearlySecondCheck, yearlyThirdCheck, endDateCheck, selectedDayCheck, onChangeSelectedDay, onChangeSelectedDayArray,selectedEndDate,
    } = this.props;
    let isShowDays = recurrenceName == Strings.calendarSettings.daily && timeValue > 1 ? false : true;
    let lastMonthName = onMonthLastDay + " "+ moment(fromDate).format("dddd")
    let secondYearList =  onMonthDay
    let thirdYearList =  onMonthLastDay +" "+moment(fromDate).format("dddd")
    console.log('recurrenceName',recurrenceName)
    console.log("selectedDayCheck",selectedDayCheck)
    return (
      <>
      <TextField
        attrName={Strings.addNewEvent.recurrence}
        title={Strings.addNewEvent.recurrence}
        value={recurrenceName}
        updateMasterState={()=>{}}
        mandatory={true}
        textInputStyles={recurrenceTypeData ? Styles.textStyleEdited : Styles.textStyle}
        showButton={true}
        onPress={() => {
          Keyboard.dismiss()
          if(recurrenceTypeData){
          this.setState({ isRecurrence: false });
          } else {
            this.setState({ isRecurrence: true });
          }
        }}
        imageSource={recurrenceTypeData == false && Images.downArr}
      />
             
        {recurrenceName !=
          Strings.calendarSettings.doseNotRepeat && (
          <>
            <View>
            {recurrenceTypeData == false ? 
            <>
              {recurrenceName != Strings.calendarSettings.yearly && (
              <>
                <Text style={[Styles.escortText, { marginLeft: 20 }]}>
                  {Strings.addNewEvent.repeat}
                </Text>
                
                <View style={[Styles.innerContainer, Platform.OS == "ios" && {zIndex: 10000}]}>
                  <TextField
                    attrName={Strings.addNewEvent.times}
                    value={timeValue}
                    updateMasterState={(key, value) => {
                      let time = value.toString();
                      onChangeTimeValue(time);
                    }}
                    paddingBottom={hp("1%")}
                    defaultValue="1"
                    keyboardType={Strings.keyboardTypes.numeric}
                    textInputStyles={Styles.textInput}
                    container={Styles.textContainer}
                    progressWidth={Styles.widthContainer}
                    buttonContainer={Styles.textBottomContainer}
                  />
                  

                  <DropDownPicker
                  
                    items={timeValue <= 1 ? recurrenceList : repetRecurrenceList}
                    defaultValue={timeValue <= 1 ? this.state.selectedRecur : `${this.state.selectedRecur}s`}
                    placeholderStyle={Styles.dropDownPlaceHolder}
                    placeholder={
    recurrenceName === "Daily" ? "Day" :
    recurrenceName === "Weekly" ? "Week" :
    recurrenceName === "Monthly" ? "Month" :
    recurrenceName === "Yearly" ? "Year" :
    ''
  }
                    containerStyle={Styles.dropDownContainer}
                    style={Styles.dropDownStyle}
                    itemStyle={Styles.dropdownValue}
                    globalTextStyle={Styles.marginContainer}
                    customArrowUp={(size) => (
                      <Image source={Images.downArr} style={[ Styles.dropDownImage,{ width: size, height: size }]} />
                    )}
                    customArrowDown={(size) => (
                      <Image source={Images.downArr} style={[ Styles.dropDownImage, { width: size, height: size }]} />
                    )}
                    dropDownStyle={Styles.DropDownPicker}
                    onChangeItem={(item) => {
                      let dropdownValue;
                      if (item.id == 1) {
                        
                        this.updateDay();
                        dropdownValue = Strings.calendarSettings.daily;
                      } else if (item.id == 3) {
                        dropdownValue = Strings.calendarSettings.monthly;
                        if (monthFirstCheck) {
                          this.onChangeMonthThreeOptions(true, false, false, moment(endDateCheck).format("DD"));
                        } else if (monthSecondCheck) {
                          this.onChangeMonthThreeOptions(false, true, false,onMonthDay);
                        } else if (monthThirdCheck) {
                          this.onChangeMonthThreeOptions(false, false, true, lastMonthName);
                        }
                      } else if (item.id == 2) {
                        this.updateWeek();
                        dropdownValue = Strings.calendarSettings.weekly;
                        onChangeSelectedDayArray(["Monday"])
                        onChangeSelectedDay("Monday")
                      } else if (item.id == 4) {
                        dropdownValue = Strings.calendarSettings.yearly;
                        if (monthFirstCheck) {
                          this.onChangeYearThreeOptions(true, false, false,moment(endDateCheck).format("DD"));                      
                        } else if (monthSecondCheck) {
                          this.onChangeYearThreeOptions(false, true, false,secondYearList);
                        } else if (monthThirdCheck) {
                          this.onChangeYearThreeOptions(false, false, true, thirdYearList);
                        }
                      }
                      onChangeRecrrenceName(dropdownValue);
                      let recur;
                      if (timeValue <= 1) {
                        recur = item.value;
                      } else {
                        recur = item.value.substring(0, item.value.length - 1);
                      }
                      this.setState({
                        selectedRecur: recur,
                        selectedRecurrence: dropdownValue,
                      });
                    }}
                    selectedLabelStyle={Styles.dropDownSelectedValue}
                    zIndex={5000}
                  />
                </View>
              </>)}

              {(recurrenceName === Strings.calendarSettings.daily ||
                recurrenceName === Strings.calendarSettings.weekly) && isShowDays && (
                <>
                  <View style={Styles.weeklyContainer}>
                    <FlatList
                     data={this.state.daysList}
                      renderItem={this.renderDay}
                      horizontal={true}
                      scrollEnabled={false}
                      extraData={this.state}
                    />
                  </View>
                </>
              )}
              {recurrenceName ===
                Strings.calendarSettings.monthly && (
                <>
                  <View style={Styles.monthlyContainer}>
                    <TouchableOpacity
                      onPress={() => { if (!monthFirstCheck) { this.onChangeMonthThreeOptions(true, false, false, moment(endDateCheck).format("DD")); } }}
                      style={[
                        Styles.circleSelect,
                        {
                          backgroundColor: monthFirstCheck
                            ? Colors.themeColor
                            : Colors.white,
                        },
                      ]}
                    ></TouchableOpacity>
                    <Text style={Styles.escortText}>
                      {" "}
                      {Strings.addNewEvent.onDay}{" "}
                      {moment(fromDate).format("DD")}
                    </Text>
                  </View>
                  <View style={Styles.monthlyInsideContainer}>
                    <TouchableOpacity
                      onPress={() => { if (!monthSecondCheck) { this.onChangeMonthThreeOptions(false, true, false, onMonthDay); } }}
                      style={[
                        Styles.circleSelect,
                        {
                          backgroundColor: monthSecondCheck
                            ? Colors.themeColor
                            : Colors.white,
                        },
                      ]}
                    ></TouchableOpacity>
                    <Text style={Styles.escortText}>
                      {" "}
                      {Strings.addNewEvent.onThe} {onMonthDay}
                    </Text>
                  </View>
                  {onMonthLastDay != "" &&
                  onMonthLastDay != undefined && (
                      <View style={Styles.yearlySecondContainer}>
                      <TouchableOpacity
                        onPress={() => { if (!monthThirdCheck) { this.onChangeMonthThreeOptions(false, false, true, lastMonthName); } }}
                          style={[
                            Styles.circleSelect,
                            {
                              backgroundColor: monthThirdCheck
                                ? Colors.themeColor
                                : Colors.white,
                            },
                          ]}
                        ></TouchableOpacity>
                        <Text style={Styles.escortText}>
                          {" "}
                          {Strings.addNewEvent.onThe}{" "}
                          {onMonthLastDay}{" "}
                          {moment(fromDate).format("dddd")}
                        </Text>
                      </View>
                    )}
                </>
              )}

              {recurrenceName ==
                Strings.calendarSettings.yearly && (
                <>
                  <View style={Styles.yearlyContainer}>
                    <TouchableOpacity
                      onPress={() => { if (!yearlyFirstCheck) { this.onChangeYearThreeOptions(true, false, false,moment(endDateCheck).format("DD")); } }}
                      style={[
                        Styles.circleSelect,
                        {
                          backgroundColor: yearlyFirstCheck
                            ? Colors.themeColor
                            : Colors.white,
                        },
                      ]}
                    ></TouchableOpacity>
                    <Text style={Styles.escortText}>
                      {" "}
                      {Strings.addNewEvent.onDay}{" "}
                      {moment(fromDate).format("DD")} 
                    </Text>
                  </View>
                  <View style={Styles.yearlySecondContainer}>
                    <TouchableOpacity
                      onPress={() => { if (!yearlySecondCheck) { this.onChangeYearThreeOptions(false, true, false, secondYearList ); } }}
                      style={[
                        Styles.circleSelect,
                        {
                          backgroundColor: yearlySecondCheck
                            ? Colors.themeColor
                            : Colors.white,
                        },
                      ]}
                    ></TouchableOpacity>
                    <Text style={Styles.escortText}>
                      {" "}
                      {Strings.addNewEvent.onThe} {onMonthDay} of {moment(fromDate).format("MMMM")}
                    </Text>
                  </View>
                  {onMonthLastDay != "" &&
                  onMonthLastDay != undefined && (
                      <View style={Styles.yearlySecondContainer}>
                      <TouchableOpacity
                        onPress={() => { if (!yearlyThirdCheck) { this.onChangeYearThreeOptions(false, false, true, thirdYearList); } }}
                          style={[
                            Styles.circleSelect,
                            {
                              backgroundColor: yearlyThirdCheck
                                ? Colors.themeColor
                                : Colors.white,
                            },
                          ]}
                        ></TouchableOpacity>
                        <Text style={Styles.escortText}>
                          {" "}
                          {Strings.addNewEvent.onThe}{" "}
                          {onMonthLastDay}{" "}
                          {moment(fromDate).format("dddd")} of {moment(fromDate).format("MMMM")}
                        </Text>
                      </View>
                    )}
                </>
              )}
              </>
               :
            null }

              {recurrenceName !=
                Strings.calendarSettings.doseNotRepeat && (
                <>
                  <TextField
                    attrName={Strings.addNewEvent.endDate}
                    title={Strings.addNewEvent.endDate}
                    value={endDateCheck.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={true}
                    textInputStyles={Styles.textInput}
                    showButton={true}
                    onPress={() => {
                      Keyboard.dismiss();
                      this.setState({
                        isEndDate: true,
                      });
                    }}
                    container={{}}
                    imageSource={Images.calGray}
                  />
                  <View style={Styles.occursContainer}>
                    <Text
                      style={[Styles.escortText, { color: Colors.themeColor }]}
                    >
                      {Strings.addNewEvent.mandatory}{" "}
                    </Text>
                    {recurrenceName ==
                      Strings.calendarSettings.daily && (
                      <>
                        {timeValue == 1 && (
                        <>
                          <Text style={Styles.occursText}>
                            {Strings.addNewEvent.occursEveryDayUntil}{" "}
                            {moment(endDateCheck).format("MMMM DD, YYYY")}
                          </Text>
                        </>
                        )}
                        {timeValue == 2 && (
                        <>
                          <Text style={Styles.occursText}>
                            {Strings.addNewEvent.occursEveryOtherDayUntil}{" "}
                            {moment(endDateCheck).format("MMMM DD, YYYY")}
                          </Text>
                        </>
                        )}
                        {timeValue > 2 && (
                        <>
                          <Text style={Styles.occursText}>
                            {Strings.addNewEvent.occursEvery} {timeValue} {Strings.addNewEvent.daysUntil}{" "}
                            {moment(endDateCheck).format("MMMM DD, YYYY")}
                          </Text>
                        </>
                        )}
                      </>
                    )}
                    {recurrenceName ==
                      Strings.calendarSettings.weekly && (
                      <>
                        {timeValue == 1 && (
                        <>
                          <Text style={Styles.occursText}>
                            {Strings.addNewEvent.occursEvery}{" "}
                            {selectedDayCheck}{" "}
                            {Strings.addNewEvent.until}{" "}
                            {moment(endDateCheck).format("MMMM DD, YYYY")}
                          </Text>
                        </>
                        )}
                        {timeValue == 2 && (
                        <>
                          <Text style={Styles.occursText}>
                            {Strings.addNewEvent.occursEvery}{" "}
                            {Strings.addNewEvent.other}{" "}
                            {selectedDayCheck}{", "}
                            {Strings.addNewEvent.until}{" "}
                            {moment(endDateCheck).format("MMMM DD, YYYY")}
                          </Text>
                        </>
                        )}
                        {timeValue > 2 && (
                        <>
                          <Text style={Styles.occursText}>
                            {Strings.addNewEvent.occursEvery}{" "}
                            {timeValue}{" "}
                            {Strings.addNewEvent.weekOn}{" "}
                            {selectedDayCheck}{", "}
                            {Strings.addNewEvent.until}{" "}
                            {moment(endDateCheck).format("MMMM DD, YYYY")}
                          </Text>
                        </>
                        )}
                      </>
                    )}
                    {recurrenceName ==
                      Strings.calendarSettings.monthly && (
                      <>
                        {monthFirstCheck && (
                          <>
                            <Text style={Styles.occursText}>
                              {Strings.addNewEvent.occursDay}{" "}
                              {moment(fromDate).format("DD")}{" "}
                              {Strings.addNewEvent.until}{" "}
                              {moment(endDateCheck).format(
                                "MMMM DD, YYYY"
                              )}
                            </Text>
                          </>
                        )}
                        {monthSecondCheck && (
                          <>
                            <Text style={Styles.occursText}>
                              {Strings.addNewEvent.occurs}{" "}
                              {onMonthDay}{" "}
                              {Strings.addNewEvent.until}{" "}
                              {moment(endDateCheck).format(
                                "MMMM DD, YYYY"
                              )}
                            </Text>
                          </>
                        )}
                        {monthThirdCheck && (
                          <>
                            <Text style={Styles.occursText}>
                              {Strings.addNewEvent.occurs}{" "}
                              {onMonthLastDay}{" "}
                              {moment(fromDate).format("dddd")}{" "}
                              {Strings.addNewEvent.until}{" "}
                              {moment(endDateCheck).format(
                                "MMMM DD, YYYY"
                              )}
                            </Text>
                          </>
                        )}
                      </>
                    )}
                    {recurrenceName ==
                      Strings.calendarSettings.yearly && (
                      <>
                        {yearlyFirstCheck&& (
                          <>
                            <Text style={Styles.occursText}>
                              {Strings.addNewEvent.occursDay}{" "}
                              {moment(fromDate).format("DD")}{" "}
                              {Strings.addNewEvent.until}{" "}
                              {moment(endDateCheck).format(
                                "MMMM DD, YYYY"
                              )}
                            </Text>
                          </>
                        )}
                        {yearlySecondCheck && (
                          <>
                            <Text style={Styles.occursText}>
                              {Strings.addNewEvent.occurs}{" "}
                              {onMonthDay}{" "}
                              {Strings.addNewEvent.until}{" "}
                              {moment(endDateCheck).format(
                                "MMMM DD, YYYY"
                              )}
                            </Text>
                          </>
                        )}
                        {yearlyThirdCheck && (
                          <>
                            <Text style={Styles.occursText}>
                              {Strings.addNewEvent.occurs}{" "}
                              {onMonthLastDay}{" "}
                              {moment(fromDate).format("dddd")}{" "}
                              {Strings.addNewEvent.until}{" "}
                              {moment(endDateCheck).format(
                                "MMMM DD, YYYY"
                              )}
                            </Text>
                          </>
                        )}
                      </>
                    )}
                  </View>
                </>
              )}
            </View>
            {/* From Calender iOS */}
            {Platform.OS == Strings.platforms.ios && (
              <Modal
                isVisible={this.state.isEndDate}
                onBackdropPress={() => {
                  this.setState({
                    isEndDate: false,
                  });
                }}
                animationInTiming={500}
                style={Styles.datePickerContainer}
              >
                <DateTimePicker
                  value={recurrenceTypeData ? new Date(endDateCheck) : new Date(selectedEndDate)}
                  style={Styles.datePickerStyle}
                  display={Strings.datePicker.inline}
                  themeVariant={Strings.datePicker.light}
                  accentColor={Colors.themeColor}
                  onChange={(time, date) => {
                    this.onchangeEndDate(time, date);
                  }}
                />
                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={Styles.datePickerOkContainer}
                    onPress={() => {
                      this.onDatePickerEndDonePressed();
                    }}
                  >
                    <Text style={Styles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* From Calender Android */}

            {Platform.OS == Strings.platforms.android && this.state.isEndDate && (
              <DateTimePicker
                value={recurrenceTypeData ? new Date(endDateCheck) : new Date(selectedEndDate)}
                style={Styles.datePickerStyle}
                onChange={(time, date) => {
                  this.onchangeEndDate(time, date);
                }}
              />
            )}
          </>
        )}

          <Modal transparent={true}
          style={Styles.modalStyle} backdropColor={Colors.bgTransparent}
          isVisible={this.state.isRecurrence}
            animationInTiming={500}>
            <Dropdown
              data={listOfRecurrence}
              title={Strings.addNewEvent.recurrence}
              value={""}
              closeBtn={() => this.setState({ isRecurrence: false })}
              onPress={(item) => {
                let recurrenceSelect;
                if (item.name == "Does Not Repeat") {
                  onChangeRecrrenceName(item.name);
                  this.setState({ isRecurrence: false });
                } 
                else {

                if (item.name == "Daily") {
                  this.updateDay();
                  recurrenceSelect = "Day";
                } else if (item.name == "Monthly") {
                  this.onChangeMonthThreeOptions(true, false, false,moment(endDateCheck).format("DD"));
                  recurrenceSelect = "Month";
                } else if (item.name == "Weekly") {
                  this.updateWeek();
                  recurrenceSelect = "Week";
                  onChangeSelectedDay('Monday')
                  onChangeSelectedDayArray(['Monday'])
                } else if (item.name == "Yearly") {
                  this.onChangeYearThreeOptions(true, false, false,moment(endDateCheck).format("DD"));
                  recurrenceSelect = "Year";

                }
                onChangeRecrrenceName(item.name);
                onChangeTimeValue("1");
                this.setState({
                  selectedRecur: recurrenceSelect,
                  isRecurrence: false,
                });
              }

              }}
              visible={this.state.isRecurrence}
              onbackPress={() => this.setState({ isRecurrence: false })}
              container={{ alignItems: "center" }}
              textContainer={Styles.textContainerStyle}
            />
          </Modal>
      </>
    );
  }
}

const Styles = StyleSheet.create({
  innerContainer: {
    flexDirection: "row",
  },
  textContainer: {
    width: wp("20%"),
    alignSelf: "flex-start",
    marginLeft: 20,
    marginTop: -5,
  },
  textBottomContainer: {},
  textInput: {
    color: Colors.black,
    fontSize: 14,
  },
  escortText: {
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
  },
  dropDownPlaceHolder: {
    color: 'black',
    fontSize: 14,
    marginTop: 10,
  },
  dropDownContainer: {
    height: hp("6%"),
    marginTop: 3,
  },
  dropDownStyle: {
    backgroundColor: Colors.white,
    width: wp("30%"),
    borderColor: Colors.white,
    borderBottomColor: Colors.placeholder,
    alignSelf: "center",
    height: hp("5%"),
    marginLeft: 20,
  },
  dropdownValue: {
    justifyContent: "flex-start",
    marginTop:-12,
  },
  dropDownImage: {
    alignSelf: "flex-end",
  },
  DropDownPicker: {
  backgroundColor:Colors.white,
    width: "85%",
    justifyContent: "center",
    alignItems: "center",
    height: Platform.OS == "android" ? hp("13%") : hp("10%"),
    marginLeft: 20,
  },
  dropDownSelectedValue: { color: Colors.black },
  weeklyContainer: {
    marginTop: 10,
    alignItems: "center",
    width: "95%",
    marginRight: 10,
  },
  dailyContainer: {
    flexDirection: "row",
    width: wp("90"),
    marginLeft: 20,
  },
  avatarIcon: { marginLeft: 10 },
  occursStar: {
    marginTop: 20,
    color: Colors.themeColor,
  },

  occursText: {
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
  },
  occursContainer: {
    flexDirection: "row",
    width: wp("90"),
    marginLeft: 20,
  },
  monthlyContainer: { flexDirection: "row", marginLeft: 20, marginTop: 10 },
  yearlyContainer: {
    flexDirection: "row",
    marginLeft: 20,
  },
  yearlySecondContainer: {
    flexDirection: "row",
    marginLeft: 20,
    marginTop: 10,
  },
  monthlyInsideContainer: {
    flexDirection: "row",
    marginLeft: 20,
    marginTop: 10,
  },
  circleSelect: {
    height: 15,
    width: 15,
    borderRadius: 30,
    borderColor: Colors.lightGrey,
    borderWidth: 1,
    marginRight: 10,
  },
  datePickerOkContainer: {
    width: "100%",
    alignItems: "center",
    backgroundColor: Colors.white,
    paddingBottom: hp("2%"),
  },
  datePickerOkLabel: {
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 6,
    paddingBottom: 6,
    fontFamily: Fonts.montserratBold,
  },
  datePickerContainer: {
    paddingTop: 45,
    margin: 0,
    justifyContent: "flex-end",
  },
  datePickerStyle: {
    backgroundColor: Colors.white,
    width:'100%',
  },
  modalStyle:{
    margin:0
  },
  textStyle:{
    color: Colors.black,
    fontSize: 14,
  },
  textStyleEdited:{
    color: Colors.inlineGrey,
    fontSize: 14,
  },
  textContainerStyle:{
    textAlign: "center",
    marginRight: 20,
    fontSize: 14 
  },
  marginContainer:{
    marginTop: 10,
    // fontSize: wp("4%"),
  },
  widthContainer:wp("20%")
});

export default RecurrenceComponent;