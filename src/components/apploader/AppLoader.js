import React, { Component } from "react";
import { InteractionManager, StyleSheet } from "react-native";
import { BlurView } from "@react-native-community/blur";
import Loader from "../loader/Loader";

export default class AppLoader extends Component {
  constructor(props) {
    super(props);
    this.state = {
      ready: false,
    };
    this.timer = null;
  }

  componentDidMount() {
    this.timer = setTimeout(() => {
      this.setState({ ready: true });
    }, 100);
  }

  componentWillUnmount() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
  }

  render() {
    return (
      <>
     {
     this.state.ready &&
     this.props.viewRef &&
      <BlurView
        style={[styles.containerStyle, { ...this.props.style }]}
        viewRef={true}
        blurType="light"
        blurAmount={10}
        reducedTransparencyFallbackColor="white"
      >
        <Loader />
      </BlurView>
      }
      </>
    );
  }
}

const styles = StyleSheet.create({
  containerStyle: {
    position: "absolute",
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
   
  },
});