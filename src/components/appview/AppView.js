import React, { Component } from "react";
import { SafeAreaView, StyleSheet } from "react-native";
import Colors from "../../common/color";

export default class AppView extends Component {
  constructor(props) {
    super(props);
  }

  render() {
    return (
      <SafeAreaView style={[styles.containerStyle, { ...this.props.style }]}>
        {this.props.children}
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  containerStyle: {
    flex: 1,
    backgroundColor: Colors.white,
  },
});
