import React from "react";
import {

  View,
  FlatList,
  Text,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { Fonts, Colors } from "../../common";
import {
  widthPercentageToDP as wp
} from "react-native-responsive-screen";

const googleAutoCompleteAPI = (apiKey, keyWord) =>
  `https://maps.googleapis.com/maps/api/place/autocomplete/json?key=${apiKey}&input=${keyWord}`;

const API_KEY = "AIzaSyB2EHXZ7yQZ3KItOpshW2F4DJ5ewmagOWU";

const renderSeperator = () => {
  return <View style={styles.seperator} />;
};

const renderEmptyList = () => (
  <Text style={styles.emptyText}>No Places Found</Text>
);

const Places = ({ item, selectPlace }) => {
  return (
    <TouchableOpacity style={styles.listTile} onPress={selectPlace}>
      <Text style={styles.placeText}>{item.description}</Text>
    </TouchableOpacity>
  );
};

export default function AutoCompleteList({ source, onSelectPlace }) {
  return (
    <FlatList
      style={styles.listContainer}
      ItemSeparatorComponent={() => renderSeperator()}
      ListEmptyComponent={renderEmptyList}
      ListFooterComponent={() => <View style={styles.seperator} />}
      data={source}
      renderItem={({ item }) => {
        return <Places item={item} selectPlace={() => onSelectPlace(item)} />;
      }}
    />
  );
}

const styles = StyleSheet.create({
  listContainer: { width: wp("90%"), alignSelf: "center" },
  listTile: {
    width: "100%",
    alignSelf: "center",
    padding: 10,
    flexDirection: "row",
    alignItems: "center",
  },
  placeText: {
    color: Colors.black,
    fontSize: 15,
    fontFamily: Fonts.montserratRegular,
  },
  seperator: { borderWidth: 0.3, borderColor: "lightgrey" },
  emptyText: {
    //alignSelf: "center",
    padding: 10,
    fontFamily: Fonts.montserratRegular,
  },
});
