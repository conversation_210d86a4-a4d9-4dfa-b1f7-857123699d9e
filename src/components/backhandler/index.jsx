import React, { Component } from 'react';
import { BackHandler } from 'react-native';

// HOC for BackHandler management
const withBackHandler = (WrappedComponent) => {
  return class extends Component {
    constructor(props) {
      super(props);
      this.backHandlerSubscription = null;
      this.focusListener = null;
      this.blurListener = null;
      this.isScreenFocused = true;
    }

    componentDidMount() {
      // Set up focus/blur listeners
      this.focusListener = this.props.navigation.addListener('focus', () => {
        this.isScreenFocused = true;
        this.setupBackHandler();
      });

      this.blurListener = this.props.navigation.addListener('blur', () => {
        this.isScreenFocused = false;
        this.removeBackHandler();
      });

      // Initial setup if screen is focused
      if (this.props.navigation.isFocused()) {
        this.setupBackHandler();
      }
    }

    componentWillUnmount() {
      this.removeBackHandler();
      
      if (this.focusListener) {
        this.focusListener();
      }
      if (this.blurListener) {
        this.blurListener();
      }
    }

    setupBackHandler = () => {
      if (!this.backHandlerSubscription) {
        this.backHandlerSubscription = BackHandler.addEventListener(
          'hardwareBackPress',
          this.handleBackPress
        );
      }
    };

    removeBackHandler = () => {
      if (this.backHandlerSubscription) {
        this.backHandlerSubscription.remove();
        this.backHandlerSubscription = null;
      }
    };

    handleBackPress = () => {
      // Only handle if screen is focused
      if (!this.isScreenFocused) {
        return false;
      }

      // Check if wrapped component has a custom back handler
      if (this.wrappedComponentRef && this.wrappedComponentRef.onBackPress) {
        return this.wrappedComponentRef.onBackPress();
      }

      // Default behavior
      return false;
    };

    render() {
      return (
        <WrappedComponent
          {...this.props}
          ref={(ref) => (this.wrappedComponentRef = ref)}
        />
      );
    }
  };
};

export default withBackHandler;
