/* 
Screen : Companies
*/

import React from "react";
import { View, Text, StyleSheet,  Image } from "react-native";
import { Images, Fonts, Colors } from "../../common";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import ModalDropdown from "react-native-modal-dropdown";

const renderParentRow = (option) => {
  return (
    <View style={styles.parentRow}>
      <Image
        resizeMode={"center"}
        source={option.image}
        style={{
          width: option.id == "Edit" ? wp("5%") : wp("6%"),
          height: option.id == "Edit" ? hp("4%") : hp("5%"),
          marginLeft: 10,
        }}
      />
      <Text style={styles.parentRowText}>{option.id}</Text>
    </View>
  );
};

const renderSeparators = () => {
  return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
};

const renderRow = (option) => {
  return (
    <View style={styles.rowView}>
      <Image
        resizeMode={"center"}
        source={option.image}
        style={{
          width: option.id == "Edit" ? wp("5%") : wp("6%"),
          height: option.id == "Edit" ? hp("4%") : hp("5%"),
          marginLeft: 10,
        }}
      />
      <Text style={styles.optionText}>{option.id}</Text>
    </View>
  );
};

const row = (isParent, option) => {
  return isParent ? renderParentRow(option) : renderRow(option);
};

export default function CompanyCard(props) {
  const { item, onSelect, roleId, options } = props;

  return (
    <View style={styles.flatlistContainer}>
      <View>
        <View style={{ width: wp("95%") }}>
          <View style={[styles.nameContainer]}>
            <View style={styles.cardBorder} />

            <View>
              <View
                style={[
                  styles.detailContainer,
                  {
                    flexDirection: "row",
                    width: "95%",
                    marginLeft: 0,
                    marginTop: 15,
                  },
                ]}
              >
                <Text
                  style={[
                    styles.nameText,
                    { width: wp("72%"), marginLeft: 20 },
                  ]}
                  numberOfLines={2}
                >
                  {item.companyName}
                </Text>

                <ModalDropdown
                  saveScrollPosition={false}
                  style={styles.customDropdownStyle}
                  dropdownStyle={styles.customOptionsStyle}
                  dropdownTextStyle={styles.customOptionsTextStyle}
                  //options={DROPDOWNOPTIONS}
                  options={options}
                  renderRow={(rowItem) =>  renderParentRow(rowItem)}
                  renderSeparator={renderSeparators}
                  showsVerticalScrollIndicator={false}
                  onSelect={(option) => onSelect(option)}
                  defaultValue=""
                  dropdownListProps={{}}             
                >
                  {roleId == 2 && (
                    <View style={styles.imageContainer}>
                      <Image source={Images.dotmenu} style={ styles.dotMenu}/>
                    </View>
                  )}
                </ModalDropdown>
              </View>

              <View style={{ flexDirection: "row" }}>
                <View style={{ width: wp("50%") }}>
                  <View
                    style={{
                      flexDirection: "row",
                      marginLeft: 15,
                      alignItems: "center",
                      marginTop: 10,
                    }}
                  >
                    <Image source={Images.company_address} />

                    <Text
                      style={[
                        styles.companyText,
                        { marginLeft: 10, marginTop: 0 },
                      ]}
                    >{`${item.address.trim()}`}</Text>
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      marginLeft: 15,
                      marginTop: 15,
                      marginBottom: hp("3%"),
                      alignItems: "center",
                    }}
                  >
                    <Image source={Images.company_desc} />

                    <Text style={[styles.companyText, { marginLeft: 10 }]}>
                      {item.scope ? item.scope : "---"}
                    </Text>
                  </View>
                </View>

                <View
                  style={{
                    width: wp("40%"),
                    justifyContent: "center",
                    alignItems: "flex-end",
                  }}
                >
                  <Image
                    source={
                      item.logo ? { uri: item.logo } : Images.companyPlace
                    }
                    resizeMode={"contain"}
                    style={{
                      width: wp("35%"),
                      height: hp("5%"),
                      alignSelf: "flex-end",
                    }}
                  />
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  flatlistContainer: {
    width: wp("90%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    alignSelf: "center",
    borderRadius: wp("2%"),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  nameContainer: {
    minHeight: hp("14%"),
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  nameText: {
    color: Colors.black,
    fontSize: 15,
    fontFamily: Fonts.montserratSemiBold,
  },
  detailContainer: {
    marginLeft: 30,
    justifyContent: "center",
    backgroundColor: Colors.white,
  },
  cardBorder: {
    width: wp("2%"),
    backgroundColor: Colors.cardBorder,
    borderTopLeftRadius: wp("4%"),
    borderBottomLeftRadius: wp("4%"),
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    padding: 8,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    //outlineProvider: 'bounds'
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: 0,
  },
  companyText: {
    color: "#5B5B5B",
    fontSize: 11,
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  dotMenu: {
    height: 8,
    width: 25,
  },
});

const styles1 = StyleSheet.create({
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  nameContainer: {
    minHeight: hp("14%"),
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  detailContainer: {
    marginLeft: 30,
    justifyContent: "center",
    backgroundColor: Colors.white,
  },
  nameText: {
    color: Colors.black,
    fontSize: wp("5%"),
    fontFamily: Fonts.montserratSemiBold,
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("10%"),
    marginLeft: 10,
    height: 40,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    //outlineProvider: 'bounds'
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: 0,
  },
  companyText: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  containerBorder: {
    width: wp("2%"),
    //backgroundColor: Colors.themeColor,
    backgroundColor: Colors.cardBorder,
    borderTopLeftRadius: wp("4%"),
    borderBottomLeftRadius: wp("4%"),
  },
  detailContainer2: {
    flexDirection: "row",
    width: "95%",
    marginLeft: 0,
    marginTop: 15,
  },
  addressView: {
    flexDirection: "row",
    marginLeft: 15,
    alignItems: "center",
    marginTop: 10,
  },
  addressSubview: {
    flexDirection: "row",
    marginLeft: 15,
    marginTop: 15,
    marginBottom: hp("3%"),
    alignItems: "center",
  },
  imageView: {
    width: wp("40%"),
    justifyContent: "center",
    alignItems: "flex-end",
  },
  imageStyle: {
    width: wp("35%"),
    height: hp("5%"),
    alignSelf: "flex-end",
  },
  rowView: {
    width: wp("40%"),
    flexDirection: "row",
    alignItems: "center",
    height: hp("7%"),
    backgroundColor: "white",
  },
  optionText: {
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
    color: Colors.planDesc,
    marginLeft: 10,
  },
  parentRow: {
    width: wp("40%"),
    flexDirection: "row",
    alignItems: "center",
    height: hp("7%"),
    backgroundColor: "white",
  },
  /*  parentRowImage: {
    width: option.id == "Edit" ? wp("5%") : wp("6%"),
    height: option.id == "Edit" ? hp("4%") : hp("5%"),
    marginLeft: 10,
  }, */
  parentRowText: {
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
    color: Colors.planDesc,
    marginLeft: 10,
  },
});
