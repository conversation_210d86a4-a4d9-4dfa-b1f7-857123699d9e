/* 
Screen : Drlist
*/

import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, Image } from "react-native";
import { Images, Fonts, Colors, Strings } from "../../common";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import moment from "moment";
import ModalDropdown from "react-native-modal-dropdown";

let DROPDOWNOPTIONS = [
  { id: "Edit", image: Images.edit_blue, name: "Edit" },
  { id: "Void", image: Images.void, name: "Void" },
  {id:"Delete", image:Images.deleteOrange, name:"Delete"}
];
let DROPDOWNOPTIONSSC=[
  { id: "Void", image: Images.void, name: "Void" },
  {id:"Delete", image:Images.deleteOrange, name:"Delete"},
]


const renderRow = (option, index, isSelected) => {

  return (
    <View style={styles.rowMainView}>
      <Image
        source={option.image}
        style={styles.imageRow}
      />
      <Text style={styles.optionText}>{option.id}</Text>
    </View>
  );
};

const renderSeparator = () => {
  return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
};

export default function ConcreteCard(props) {
  const { item, onPress, onSelect,isAccessData,textColor,defaultColor} = props;
  let colorobj = {
    Approved: "#11FF00",
    Pending: "#F45E28",
    Declined: "red",
    Expired: "#BEBEBE",
    Delivered: "blue",
  };
  
  !defaultColor && textColor.map((data)=>{
    if(data.status == 'approved') {
     colorobj.Approved = data.fontColor
    } else if(data.status == 'pending') {
     colorobj.Pending = data.fontColor
    } else if(data.status == 'delivered') {
     colorobj.Delivered = data.backgroundColor
    } else if(data.status == 'rejected') {
     colorobj.Declined = data.fontColor
    } else if(data.status == 'expired') {
     colorobj.Expired = data.fontColor
    }
 })
  let dropDownOptions=[];
  let date = moment.parseZone(item.concretePlacementStart).format("lll");
  date.toString();
  let status = item.status;
  let color = "#1E1E1E";
  if (status == "Approved") {
    color = colorobj.Approved;
  } else if (status == "Tentative") {
    color = colorobj.Pending;
  }else if (status == "Pending") {
    color = colorobj.Pending;
  } else if (status == "Delivered"||status=="Completed") {
    color = colorobj.Delivered;
  }else if(status == "Pump Confirmed") {
    color='orange'
  } else if(status == 'Declined') {
    color = colorobj.Declined;
  }
  if((!isAccessData)){
    dropDownOptions=DROPDOWNOPTIONSSC
  }
  else{
    dropDownOptions=DROPDOWNOPTIONS
   }
   let concreteSupplier="";
   if(item.concreteSupplierDetails){
     for(let [index,data] of item.concreteSupplierDetails.entries()){
       if(data.Company!=null){
         if(index==item.concreteSupplierDetails.length-1){
          concreteSupplier+=data.Company.companyName;
         }else{
          concreteSupplier+=data.Company.companyName+", ";
         }
         
       }
     }
   }else{
     concreteSupplier="---"
   }
   let pumpsize="";
   if(item.pumpSizeDetails){
    for(let [index,data] of item.pumpSizeDetails.entries() ){
      if(data.ConcretePumpSize!=null){
        if(index==item.pumpSizeDetails.length-1){
          pumpsize+=data.ConcretePumpSize.pumpSize;
        }else{
          pumpsize+=data.ConcretePumpSize.pumpSize+", ";
        }
      }
    }
   }


  return (
    <View style={styles.flatlistContainer}>
      <TouchableOpacity onPress={onPress}>
        <View>
          <View style={[styles.nameContainer, { width: wp("95%") }]}>
            <Text style={styles.nameText} numberOfLines={4}>
              {item.description}
            </Text>

            <View>
            {(isAccessData) &&
              <ModalDropdown
                saveScrollPosition={false}
                style={styles.customDropdownStyle}
                dropdownStyle={[
                  styles.customOptionsStyle,
                  { height: hp("14%") },
                ]}
                dropdownTextStyle={styles.customOptionsTextStyle}
                options={dropDownOptions}
                //renderRow={this.renderRow}
                renderRow={(option) => renderRow(option)}
                renderSeparator={renderSeparator}
                showsVerticalScrollIndicator={false}
                /*  onSelect={(options) =>
                  this.onSelectDropdown(options, index, item)
                } */
                onSelect={(options) => {
                  onSelect(options);
                }}
                defaultValue=""
                dropdownListProps={{}}
              >
                <View style={styles.imageContainer}>
                  <Image
                    style={{ width: 30, height: 10 }}
                    source={Images.dotmenu}
                  />
                </View>
              </ModalDropdown>
            }
            </View>
          </View>

          <View style={{ width: wp("96%") }}>
            <View style={styles.subContainer}>
              <Text style={styles.subtext}>{Strings.addDR.dateTime}</Text>
              <Text style={styles.subtext}>{Strings.addDR.status}</Text>
            </View>

            <View style={[styles.subContainer, { marginTop: 5 }]}>
              <Text
                style={[styles.subAnsText, {fontSize: 14 }]}
              >
               {date}
              </Text>

              <Text
                style={[styles.subAnsText, {color: color, fontSize: 14 }]}
                numberOfLines={3}
              >
              {status}
              </Text>
            </View>

            <View style={[styles.subContainer, { marginTop: 20 }]}>
              <Text style={styles.subtext}>{Strings.concreteCard.concreteSupplier}</Text>
              <Text style={styles.subtext}>{Strings.concreteCard.pimpSize}</Text>
            </View>

            <View
              style={[styles.subContainer, { marginTop: 5, marginBottom: 15 }]}
            >
              <Text
                style={[styles.subAnsText, { color: "#1E1E1E", fontSize: 14 }]}
                numberOfLines={3}
              >
               {(concreteSupplier!= '' && concreteSupplier != null) ? concreteSupplier : '---'}
              </Text>
              <Text
                style={[
                  styles.subAnsText,
                  {
                    
                    fontSize: 14,
                  },
                ]}
              >
              {(pumpsize!= '' && pumpsize != null) ? pumpsize : '---'}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    alignSelf: "center",
    borderRadius: wp("2%"),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 6,
  },
  nameContainer: {
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  nameText: {
    color: Colors.black,
    fontSize: 15,
    fontFamily: Fonts.montserratBold,
    marginTop: 10,
    marginLeft: 10,
    width: "85%",
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderWidth:3,
    borderColor: Colors.borderCardColor,
    marginRight: wp("20%"),
    alignSelf: "center",
    borderRadius: wp("1.5%")
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: wp("20%"),
    height:25,
    width:40,
    justifyContent: "center",
  },
  subContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 5,
  },
  subtext: {
    width: "45%",
    color: "#5B5B5B",
    fontSize: 14,
    marginLeft: 10,
    fontFamily: Fonts.montserratRegular,
  },
  imageRow:{
    marginLeft: 15,
    height:15,
    width:15,
  },
  subAnsText:{
    width: "45%",
    color: Colors.drListSubText,
    fontSize: 14,
    marginLeft: 10,
    fontFamily: Fonts.montserratSemiBold,
  },
  rowMainView: {
    width: wp("40%"),
    flexDirection: "row",
    alignItems: "center",
    height: hp("4.5%"),
    backgroundColor: "white",
  },
  optionText: {
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
    color: Colors.planDesc,
    marginLeft: 10,
  },
});

const styles1 = StyleSheet.create({
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  nameContainer: {
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    marginRight: wp("20%"),
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    //outlineProvider: 'bounds'
  },
  nameText: {
    color: Colors.black,
    fontSize: wp("5%"),
    fontFamily: Fonts.montserratSemiBold,
    marginTop: 10,
    marginLeft: 10,
    width: "85%",
  },
  imageContainer: {
    marginRight: wp("20%"),
    height:25,
    width:40,
    justifyContent: "center",
  },
  subContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 5,
  },
  subtext: {
    width: "45%",
    color: "#5b5b5b",
    fontSize: wp("4%"),
    marginLeft: 10,
    fontFamily: Fonts.montserratRegular,
  },
  rowMainView: {
    width: wp("40%"),
    flexDirection: "row",
    alignItems: "center",
    height: hp("7%"),

  },
  optionText: {
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
    color: Colors.planDesc,
    marginLeft: 10,
  },
});
