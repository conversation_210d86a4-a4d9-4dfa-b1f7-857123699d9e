/* 
Screen : Drlist
*/

import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, Image, Platform } from "react-native";
import { Images, Fonts, Colors, Strings } from "../../common";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import moment from "moment";
import ModalDropdown from "react-native-modal-dropdown";
import { Avatar } from 'react-native-paper';
import { INVITE_MEMBER } from "../../api/Constants";
let DROPDOWNOPTIONS = [
  { id: "Edit", image: Images.edit_blue, name: "Edit" },
  { id: "Void", image: Images.void, name: "Void" },
];
let DROPDOWNOPTIONSSC = [
  { id: "Void", image: Images.void, name: "Void" },
]


const renderRow = (option, index, isSelected) => {
  return (
    <View style={styles.rowMainView}>
      <Image
        source={option.image}
        style={{
          marginLeft: 10,
        }}
      />
      <Text style={styles.optionText}>{option.id}</Text>
    </View>
  );
};

const renderSeparator = () => {
  return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
};
const renderItem = ({ item, index }) => {
  if (index < 3) {
    return (
      <Avatar.Text size={24} label={item.label} color="white" theme="grey" style={{ backgroundColor: 'grey', marginLeft: 5 }} />)
  }
};
export default function DRCard(props) {
  const { item, onPress, onSelect, isAccessData, textColor, defaultColor } = props;

  let dropDownOptions = [];
  let date = moment.parseZone(
    item.requestType === "deliveryRequest" || item.requestType === "deliveryRequestWithCrane"
      ? item.deliveryStart
      : item.requestType === "inspectionRequest"
        ? item.inspectionStart
        : item.craneDeliveryStart
  ).format("lll");
  date.toString();
  let status = item.status;
  let inspectionStatus = item.inspectionStatus;

  let color = "#1E1E1E";
  let colorobj = {
    Approved: "#11FF00",
    Pending: "#F45E28",
    Declined: "red",
    Expired: "#BEBEBE",
    Delivered: "blue",
  };

  if (!defaultColor) {
    textColor.map((data) => {
      if (data.status === 'approved') {
        colorobj.Approved = data.fontColor;
      } else if (data.status === 'pending') {
        colorobj.Pending = data.fontColor;
      } else if (data.status === 'delivered') {
        colorobj.Delivered = data.backgroundColor;
      } else if (data.status === 'rejected') {
        colorobj.Declined = data.fontColor;
      } else if (data.status === 'expired') {
        colorobj.Expired = data.fontColor;
      }
    });
  }


  if ((inspectionStatus === "Pass" || inspectionStatus === "Fail") && status === "Approved") {
    status = "Completed";
    color = colorobj.Delivered;
  } else {
    if (status === "Approved") {
      color = colorobj.Approved;
    } else if (status === "Declined") {
      color = colorobj.Declined;
    } else if (status === "Pending") {
      color = colorobj.Pending;
    } else if (status === "Delivered" || status === "Completed") {
      color = colorobj.Delivered;
    } else if (status === "Expired") {
      color = colorobj.Expired;
    }
  }
  if ((!isAccessData)) {
    dropDownOptions = DROPDOWNOPTIONSSC
  }
  else {
    dropDownOptions = DROPDOWNOPTIONS
  }
  let companyNames = '';
  if (item.companyDetails) {
    for (let [index, sampleData] of item.companyDetails.entries()) {
      if (sampleData.Company) {
        if (index == item.companyDetails.length - 1) {
          companyNames += sampleData.Company.companyName;
        } else {
          companyNames += sampleData.Company.companyName + ", ";
        }
      }
    }
  }
  return (

    <View style={styles.flatlistContainer}>
      <TouchableOpacity onPress={onPress}>
        <View>
          <View style={[styles.nameContainer, { width: wp("95%") }]}>
            <Text style={styles.nameText} numberOfLines={4}>
              {item.description}
            </Text>

            <View>
              {(isAccessData) &&
                <ModalDropdown
                  saveScrollPosition={false}
                  style={styles.customDropdownStyle}
                  dropdownStyle={[
                    styles.customOptionsStyle,
                    { height: hp("14%") },
                  ]}
                  dropdownTextStyle={styles.customOptionsTextStyle}
                  options={dropDownOptions}
                  //renderRow={this.renderRow}
                  renderRow={(option) => renderRow(option)}
                  renderSeparator={renderSeparator}
                  showsVerticalScrollIndicator={false}
                  /*  onSelect={(options) =>
                    this.onSelectDropdown(options, index, item)
                  } */
                  onSelect={(options) => {
                    onSelect(options);
                  }}
                  defaultValue=""
                  dropdownListProps={{}}
                >
                  <View style={styles.imageContainer}>
                    <Image
                      style={{ width: 30, height: 10 }}
                      source={Images.dotmenu}
                    />
                  </View>
                </ModalDropdown>
              }
            </View>
          </View>

          <View style={{ width: wp("96%") }}>
            <View style={styles.subContainer}>
              <Text style={styles.subtext}>{Strings.addDR.dateTime}</Text>
              <Text style={styles.subtext}>{Strings.addDR.company}</Text>
            </View>

            <View style={{ flexDirection: 'row', marginTop: 5, }}>
              <Text
                style={[styles.subAnsText, { fontSize: 14 }]}
              >
                {date.toString()}
              </Text>
              <Text
                style={[styles.subAnsText, { color: "#1E1E1E", fontSize: 14, marginLeft: Platform.OS == 'ios' ? 27 : 30, width: '40%' }]}
                numberOfLines={3}
              >
                {companyNames}
              </Text>
            </View>

            <View style={[styles.subContainer, { marginTop: 20 }]}>
              <Text style={styles.subtext}>{Strings.equip.equipment}</Text>
              <Text style={styles.subtext}>{Strings.addDR.status}</Text>
            </View>

            <View
              style={[styles.subContainer, { marginTop: 5, marginBottom: 15 }]}
            >
              <Text
                style={[styles.subAnsText, { color: "#1E1E1E", fontSize: 14 }]}
                numberOfLines={3}
              >
                {item.equipmentDetails && (item.equipmentDetails.length > 0)
                  ? (() => {
                    const firstEquipment = item.equipmentDetails[0].Equipment?.equipmentName.trim() || '';
                    const remainingCount = item.equipmentDetails.length - 1;
                    return firstEquipment + (remainingCount > 0 ? ` (+${remainingCount})` : '');
                  })()
                  : '---'}
              </Text>
              <Text
                style={[
                  styles.subAnsText,
                  {
                    color: color,
                    fontSize: 14,
                  },
                ]}
              >
                {status}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    alignSelf: "center",
    borderRadius: wp("2%"),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 6,
  },
  nameContainer: {
    width: wp("95%"),
    alignSelf: "center",
    flexDirection: "row",
  },
  nameText: {
    color: Colors.black,
    fontSize: 15,
    fontFamily: Fonts.montserratBold,
    marginTop: 10,
    marginLeft: 10,
    width: "85%",
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
    borderRadius: wp("10%"),
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderWidth: 3,
    borderColor: Colors.borderCardColor,
    marginRight: wp("20%"),
    alignSelf: "center",
    borderRadius: wp("1.5%")
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: wp("20%"),
    height: 25,
    width: 40,
    justifyContent: "center",

  },
  subContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 5,
  },
  subtext: {
    width: "45%",
    color: "#5B5B5B",
    fontSize: 14,
    marginLeft: 10,
    fontFamily: Fonts.montserratRegular,
  },
  subAnsText: {
    width: "45%",
    color: Colors.drListSubText,
    fontSize: 14,
    marginLeft: 10,
    fontFamily: Fonts.montserratSemiBold,
  },
  rowMainView: {
    width: wp("40%"),
    flexDirection: "row",
    alignItems: "center",
    height: hp("7%"),
    backgroundColor: "white",
  },
  optionText: {
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
    color: Colors.planDesc,
    marginLeft: 10,
  },
});

const styles1 = StyleSheet.create({
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  nameContainer: {
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    marginRight: wp("20%"),
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    //outlineProvider: 'bounds'
  },
  nameText: {
    color: Colors.black,
    fontSize: wp("5%"),
    fontFamily: Fonts.montserratSemiBold,
    marginTop: 10,
    marginLeft: 10,
    width: "85%",
  },
  imageContainer: {
    marginRight: wp("20%"),
  },
  subContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 5,
  },
  subtext: {
    width: "45%",
    color: "#5b5b5b",
    fontSize: wp("4%"),
    marginLeft: 10,
    fontFamily: Fonts.montserratRegular,
  },
  rowMainView: {
    width: wp("40%"),
    flexDirection: "row",
    alignItems: "center",
    height: hp("7%"),
    backgroundColor: "white",
  },
  optionText: {
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
    color: Colors.planDesc,
    marginLeft: 10,
  },
});