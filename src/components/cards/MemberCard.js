/* 
Screen : Member
*/
import React from "react";
import { View, Text, StyleSheet, Image,Linking,TouchableOpacity } from "react-native";
import { Images, Fonts, Colors } from "../../common";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import ModalDropdown from "react-native-modal-dropdown";

const renderRow = (option, index, isSelected) => {
  return (
    <View
      style={{
        width: wp("40%"),
        flexDirection: "row",
        alignItems: "center",
        height: hp("7%"),
        backgroundColor: "white",
      }}
    >
      <Image
        resizeMode={"center"}
        source={option.image}
        style={{
          width: wp("4.5%"),
          height:  hp("4%"),
          marginLeft: 10,
        }}
      />
      <Text
        style={{
          fontSize: wp("4%"),
          fontFamily: Fonts.montserratMedium,
          color: Colors.planDesc,
          marginLeft: 10,
        }}
      >
        {option.id}
      </Text>
    </View>
  );
};

const renderSeparator = () => {
  return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
};
const phoneNumber=(code,num)=>{
  if(code!=null && num!=null){
  let phoneNumber='';
  let number=code+num;
  if (Platform.OS === 'android') {
    phoneNumber = `tel:${number}`;} else {
    phoneNumber = `telprompt:${number}`;}
    Linking.openURL(phoneNumber);}
}

export default function MemberCard(props) {
  const { onSelect, options, item, userDetails, projectRoleId } = props;
  return (
    <View  style={styles.flatlistContainer}>
      <View style={styles.main}>
        <View style={styles.view1}>
          <View style={styles.imgContainer}>
            <Image
              source={
                item.User.profilePic
                  ? { uri: item.User.profilePic }
                  : Images.placeholder
              }
              style={styles.profAvatar}
            />
          </View>

          <View style={styles.roleContainer}>
            <View style={{ alignContent: "flex-end",width:wp('55') }}>
              <Text style={styles.nameText} numberOfLines={2}>
                {item.User.firstName == null ? "-" : item.User.firstName+' '+item.User.lastName}
              </Text>
            </View>

            <View style={{}}>
              <Text
                style={[
                  styles.companyText,
                  {
                    fontSize: wp("3.5%"),
                    fontFamily: Fonts.montserratRegular,
                  },
                ]}
              >
                {item.Role.roleName}
              </Text>
            </View>
          </View>

          <View style={styles.menuContainer}>
            <View style={styles.dotMenu}>
              <ModalDropdown
                style={styles.customDropdownStyle}
                saveScrollPosition={false}
                dropdownStyle={[
                  styles.customOptionsStyle,
                  {
                    height:
                      userDetails.email == item.User.email
                        ? hp("7%")
                        : hp("14%"),
                  
                  },
                ]}
                dropdownTextStyle={styles.customOptionsTextStyle}
                options={options}
                renderRow={renderRow}
                renderSeparator={renderSeparator}
                showsVerticalScrollIndicator={false}
                onSelect={(option) => onSelect(option)}
                defaultValue=""
                dropdownListProps={{}}
              >
                {(projectRoleId == 2|| projectRoleId == 3) && (
                  <View style={[styles.imageContainer]}>
                    <Image
                      // resizeMode={"center"}
                      style={styles.menuIcon}
                      source={Images.dotmenu}
                    />
                  </View>
                )}
              </ModalDropdown>
            </View>
          </View>
        </View>

        <View style={styles.middleContainer}>
          <View style={{ flex: 1 }}>
            <View style={{}}>
              <Image source={Images.companyHolder} />
            </View>

            <View style={{ justifyContent: "center" }}>
              <Text style={[styles.companyText]} numberOfLines={2}>{`${
                item.Company ? item.Company.companyName : "-"
              }`}</Text>
            </View>
          </View>

          <View style={{justifyContent:'flex-end' }}>
            <View style={{}}>
              <Image source={Images.phone} />
            </View>

            <TouchableOpacity style={{ justifyContent: "center" }} onPress={()=>phoneNumber(item.phoneCode,item.phoneNumber)}>
              <Text style={[styles.companyText,{color:item.phoneNumber == null ?Colors.black:Colors.blue,textDecorationLine:item.phoneNumber != null ? 'underline':"none"}]} numberOfLines={2}>
                {item.phoneCode == null ? "-" : item.phoneCode}{" "}
                {item.phoneNumber == null ? "-" : item.phoneNumber}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.cardFooter}>
          <View style={styles.mailIconContainer}>
            <View style={{}}>
              <Image source={Images.mail} />
            </View>
            <View style={styles.footerContainer}>
              <TouchableOpacity style={styles.emailContainer} onPress={()=>Linking.openURL(`mailto:${item.User.email}`)}>
                <Text style={[styles.companyText,{color:Colors.blue,textDecorationLine: 'underline'}]} numberOfLines={2}>
                  {item.User.email}
                </Text>
              </TouchableOpacity>

              <View style={styles.statusContainer}>
                <Image
                style={styles.statusImage}
                source={item.status=="completed"?Images.completed:Images.pending}
                resizeMode='contain'/>

                <Text style={[styles.statusText,{color:item.status=="completed"?Colors.green:Colors.red}]}>
                  {item.status=="completed"?"Completed":"Pending"}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  flatlistContainer: {
    width: wp("90%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: Colors.memberCardShadowCard,
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  imgContainer: {
    flex: 1,
    maxWidth: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    borderColor: Colors.borderGreyColor,
    borderWidth: 0.5,
    margin:5
  },
  view1: {
    flex: 1,
    margin: 5,
    marginBottom: 10,
    flexDirection: "row",
  },
  roleContainer: {
    flex: 1,
    marginLeft: 10,
    marginRight: 5,
    justifyContent: "center",
  },
  cardFooter: {
    flex: 1,
    marginBottom: 0,
    flexDirection: "row",
    borderTopColor: Colors.shadowColor,
    borderTopWidth: 1,
    width: wp("90%"),
  },
  menuIcon: {
    height: 10,
    width: 30,
    backgroundColor: Colors.white,
  },
  menuContainer: { flex: 1, maxWidth: 45, height: 50, top: 0 },
  main: { width: wp("95%"), marginTop: 5, marginBottom: 5 },
  middleContainer: {
     flex: 1, 
     margin: 10, 
     flexDirection: "row" ,
     width:wp('85%'),
    },
  footerContainer:{
    flexDirection:"row",
    justifyContent:"space-between",
    alignContent:'space-between',
    paddingRight:'2%',
  },
  emailContainer: { marginBottom: 10, justifyContent: "center",width:'65%' },
  mailIconContainer: { flex: 1, marginTop: 10, marginLeft: 10 },
  nameText: {
    color: Colors.black,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratSemiBold,
  },
  statusContainer:{
    marginTop:10,
    justifyContent: "flex-end", 
    marginRight:10,
    flexDirection:'row',
    //backgroundColor:'pink'
  },
  statusImage:{
    marginTop:5,
    // height:'20%',
    // width:"20%",
    justifyContent:'center',
    marginRight:5,
  },
  statusText:{
    fontFamily:Fonts.montserratRegular,
    fontSize:wp('3.1%'),
  },
  companyText: {
    color: "#1E1E1E",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
    marginTop: hp("1%"),
  },
  dotMenu: {
    // marginTop: hp('2%')
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("40%"),
    marginLeft: 5,
    borderColor: Colors.white,
    marginRight: wp("30%"),
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: Colors.memberCardShadowCard,
    alignSelf: "center",
    borderRadius: wp("5%")
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: wp("30%"),
    
  },
  profAvatar: { width: "100%", height: "100%", borderRadius: 30 },
});
