/* 
Screen : Notifications
*/

import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, Image } from "react-native";
import { Images, Fonts, Colors, Strings } from "../../common";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp
} from "react-native-responsive-screen";
import moment from "moment";

export default function NotificationCard(props) {
  const { item, onPress, onDelete } = props;
  
 let seen=item.DeliveryNotification[0].seen;
 let id="";
 let idTitle='';
 if(item.requestType=='concreteRequest'){
   if(item.ConcreteRequest){
   id=item.ConcreteRequest.ConcreteRequestId;
   idTitle='Concrete Request ID'
  }
 }else if(item.requestType=='craneRequest'){
   if(item.CraneRequest!=null){
   id=item.CraneRequest.CraneRequestId;
   idTitle='Crane Request ID'
  }
 }else{
   if(item.DeliveryRequest!=null)
  {
    idTitle='Delivery Request ID'
  id= item.DeliveryRequest.DeliveryId
  }
 }
  return (
    <View
      style={[styles.flatlistContainer,{    backgroundColor: seen ? Colors.white : Colors.notificationBackground,}]}
    >
      <TouchableOpacity onPress={onPress}>
        <View>
          <View style={[styles.nameContainer, { width: wp("95%") }]}>
            <Text style={styles.nameText} numberOfLines={4}>
              {item.title}
            </Text>
            <View>
              <TouchableOpacity
                onPress={onDelete}
                style={styles.customDropdownStyle}
              >
                <Image source={Images.closeBlack} style={styles.image} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={{ width: wp("96%"), marginTop: 8 }}>
            <View style={styles.subContainer}>
              <Text style={styles.subtext}>
                {Strings.placeholders.ProjectName}
              </Text>
              <Text style={[styles.subtext,{width:'50%',}]}>
                {idTitle}
              </Text>
            </View>

            <View style={[styles.subContainer, { marginTop: 5 }]}>
              <Text
                style={[
                  styles.subtext,
                  { color: "#292529", fontSize: hp("1.65%") },
                ]}
              >
                {item.Project.projectName}
              </Text>
              {(item.DeliveryRequest|| item.CraneRequest || item.ConcreteRequest) &&
              <Text
                style={[
                  styles.subtext,
                  { color: "#292529", fontSize: hp("1.65%") },
                ]}
              >{id}
                {/* {item.DeliveryRequest==null?item.CraneRequest.CraneRequestId:item.DeliveryRequest.DeliveryId} */}
              </Text>
              }
            </View>
            {item.recurrenceType && 
                <View
                style={[ { marginTop: 10, width: wp("95%") }]}>
                  <Text style={[styles.subtext,{width:'90%'}]}>
                  {Strings.placeholders.recurrenceType}
                  </Text>
                  <Text
                    style={[
                      styles.desctext,
                      { color: "#292529",marginTop:5 },
                    ]}
                  >
                    {item.recurrenceType}
                  </Text>
                </View>
            }
            <View
              style={[styles.subContainer, { marginTop: 10, width: wp("95%") }]}
            >
              <Text
                style={[
                  styles.desctext,
                  { color: "#292529" },
                ]}
              >
                {item.description}
              </Text>
            </View>
            <View style={{ marginTop: 10, marginBottom: 20, width: wp("95%") }}>
              <Text
                style={[
                  styles.datetext,
                  {
                    color: "#A8B2B9",
                    fontSize: wp("3.5%"),
                    textAlign: "right",
                    marginRight: 20,
                  },
                ]}
              >
                {moment(item.createdAt).format("lll").toString()}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
}
const styles = StyleSheet.create({
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("3%"),
  },
  nameContainer: {
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  nameText: {
    color: Colors.themeColor,
    fontSize: hp("2%"),
    fontFamily: Fonts.montserratSemiBold,
    marginTop: 10,
    marginLeft: 10,
    width: "75%",
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
    marginTop: 5,
  },
  image:{
    width:wp('4%'),
    height:hp('2%'),
  },
  subContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 5,
  },
  subtext: {
    width: "45%",
    color: "#A8B2B9",
    fontSize: hp("1.8%"),
    marginLeft: 10,
    fontFamily: Fonts.montserratMedium,
  },
  desctext: {
    width: wp("85%"),
    color: "#5b5b5b",
    fontSize: hp("1.76%"),
    marginLeft: 10,
    fontFamily: Fonts.montserratMedium,
  },
  datetext: {
    color: "#5b5b5b",
    fontSize: wp("4%"),
    marginLeft: 10,
    fontFamily: Fonts.montserratMedium,
  },
});
