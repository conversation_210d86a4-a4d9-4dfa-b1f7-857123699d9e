/* 
Screen : Dashboard
*/

import React from "react";
import { View, Text, StyleSheet, TouchableOpacity,Image } from "react-native";
import { Fonts, Colors, } from "../../common";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

export default function OverviewCard(props) {
  const { onPress, item } = props;
  let width=0;
  let height=0;
  if(item.title=='Members' ||item.title=='Companies'){
    height=16;width=17;
  }else if(item.title=='Deliveries'){
    height=15;width=20;
  }else{
    height=20;width=12;
  }
  return (
    <View style={styles.flatlistContainer}>
      <TouchableOpacity onPress={onPress}>
        <View>
          <View style={{ height: hp("15%") }}>
            <View style={[styles.nameContainer, { width: wp("40%")}]}>
              <Image source={item.image} style={{height:height,width:width,marginLeft:10,marginTop:10}}/>
              <Text style={styles.nameText} numberOfLines={1}>
                {item.title}
              </Text>
            </View>
            <View style={[styles.nameContainer, { width: wp("40%"),marginTop:10,}]}>
              <Text style={styles.amountText} numberOfLines={1}>
                {item.total}
              </Text>
            </View>
          </View>
          {/* <View style={styles.bottomView}>
            <View style={[styles.nameContainer, { width: wp("40%") }]}>
              <Image
                resizeMode={"contain"}
                source={
                  item.difftotal <= 0 ? Images.arrow_down : Images.arrow_up
                }
                style={[styles.arrowImg]}
              ></Image>
              <Text
                style={item.difftotal <= 0 ? styles.downText : styles.upText}
                numberOfLines={1}
              >
                {Math.abs(item.difftotal)}
              </Text>
            </View>
            <View style={[styles.nameContainer, { width: wp("40%") }]}>
              <Text style={styles.lastmonthText} numberOfLines={1}>
                {"Since Last Month"}
              </Text>
            </View>
          </View> */}
        </View>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  flatlistContainer: {
    width: wp("40%"),
    height: hp("11%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
    margin: wp("1%"),
  },
  nameContainer: {
    width: wp("35%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  nameText: {
    color: "#2E3039",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratMedium,
    marginTop: hp("1%"),
    marginLeft: wp("2.5%"),
    width: wp("35%"),
  },
  amountText: { 
    color: "#242E42",
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratBold,
    marginTop: hp("1%"),
    marginLeft: wp("2.5%"),
    width: wp("35%"),
  },
  arrowImg: {
    width: wp("3%"),
    height: hp("3%"),
    marginLeft: hp("2%"),
    // marginTop: wp('6%')
  },
  downText: {
    color: "#F52D56",
    fontSize: wp("3%"),
    fontFamily: Fonts.montserratRegular,
    marginLeft: 5,
    marginTop: hp("0.5%"),
    width: wp("30%"),
  },
  upText: {
    color: "#0CDF78",
    fontSize: wp("3%"),
    fontFamily: Fonts.montserratRegular,
    marginLeft: 5,
    marginTop: hp("0.5%"),
    width: wp("30%"),
  },
  lastmonthText: {
    color: "#54617A",
    fontSize: wp("2%"),
    fontFamily: Fonts.montserratRegular,
    marginLeft: 15,
    width: wp("30%"),
  },
  bottomView: {
    position: "absolute",
    bottom: 0,
    flex: 1,
    justifyContent: "flex-end",
  },
});
