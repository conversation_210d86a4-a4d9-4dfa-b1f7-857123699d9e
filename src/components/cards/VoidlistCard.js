/* 
screen : Voidlist.js
*/

import React from "react";
import { View, Text, StyleSheet, TouchableOpacity} from "react-native";
import { Fonts, Colors, Strings } from "../../common";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import moment from "moment";

export default function VoidlistCard(props) {
  const { onPressRestore, item } = props;

  let date = moment(item.deliveryStart).format("lll");
  date.toString();

  let userDetails = item.approverDetails;

  return (
    <View style={styles.flatlistContainer}>
      <View>
        <View style={[styles.nameContainer, { width: wp("95%") }]}>
          <Text style={styles.nameText} numberOfLines={4}>
            {item.description}
          </Text>
          <View></View>
        </View>

        <View style={{ width: wp("96%") }}>
          <View style={styles.subContainer}>
            <Text style={styles.subtext}>{Strings.addDR.dateTime}</Text>
            <Text style={styles.subtext}>{Strings.addDR.approvedBy}</Text>
          </View>

          <View style={[styles.subContainer, { marginTop: 5 }]}>
            <Text
              style={[
                styles.subtext,
                { color: "#1E1E1E", fontSize: wp("4.5%") },
              ]}
            >
              {date.toString()}
            </Text>
            <Text
              style={[
                styles.subtext,
                { color: "#1E1E1E", fontSize: wp("4.5%") },
              ]}
            >
              {userDetails ? item.approverDetails.User.firstName : "----"}
            </Text>
          </View>

          <View style={[styles.subContainer, { marginTop: 5 }]}>
            <Text style={styles.subtext}>{Strings.menu.equip}</Text>
            <View style={{ width: wp("45%") }}>
              <TouchableOpacity
                /*   onPress={() => {
                  this.setState({
                    showDelete: true,
                    selectedItem: item,
                    selectedIndex: index,
                  });
                }} */
                onPress={onPressRestore}
                style={styles.button}
              >
                <Text
                  style={{
                    fontSize: wp("4%"),
                    marginRight: 10,
                    fontFamily: Fonts.montserratRegular,
                    marginLeft: 10,
                    color: Colors.themeColor,
                  }}
                >
                  {Strings.addDR.restore}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View
            style={[styles.subContainer, { marginTop: 5, marginBottom: 15 }]}
          >
            <Text
              style={[styles.subtext, { color: "#1E1E1E", fontSize: wp("5%") }]}
            >
              {item.equipmentDetails[0].Equipment.equipmentName}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  nameContainer: {
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  nameText: {
    color: Colors.black,
    fontSize: wp("5%"),
    fontFamily: Fonts.montserratSemiBold,
    marginTop: 10,
    marginLeft: 10,
    width: "85%",
  },
  subContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 5,
  },
  subtext: {
    width: "45%",
    color: "#5b5b5b",
    fontSize: wp("4%"),
    marginLeft: 10,
    fontFamily: Fonts.montserratRegular,
  },
  button: {
    width: wp("25%"),
    height: hp("4%"),
    borderRadius: hp("2%"),
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.themeOpacity,
  },
});
