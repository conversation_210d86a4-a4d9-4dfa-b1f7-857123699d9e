import React from "react";
import {
  View,
  Text,
  TouchableWithoutFeedback,
  Image,
  StyleSheet,
} from "react-native";
import { Images, Fonts } from "../../common";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

const itemType = {
  name: "",
  selected: "",
  id: "",
};

const listType = ["DFOW", "Gates"];

export default function CheckableListItem(props) {
  const { selectList, editList, deleteList, item, type } = props;
  let _item = {};

  if (type === listType[0]) {
    _item["name"] = item.DFOW;
    _item["id"] = item.autoId;
    _item["selected"] = item.selected;
  } else if (type === listType[1]) {
    _item["name"] = item.gateName;
    _item["id"] = item.gateAutoId;
    _item["selected"] = item.selected;
  }

  return (
    <View>
      <View style={styles.flHeader}>
        <View style={styles.checkbox}>
          <TouchableWithoutFeedback onPress={selectList}>
            <Image
              resizeMode={"contain"}
              source={_item.selected == true ? Images.check : Images.uncheck}
              style={{ width: wp("9%"), height: hp("4%") }}
            />
          </TouchableWithoutFeedback>
        </View>

        <View style={[styles.checkbox, { width: wp("15%") }]}>
          <Text style={styles.flatlistTitle}>{_item.id}</Text>
        </View>

        <View
          style={[
            styles.checkbox,
            { width: wp("50%"), marginBottom: hp("1%") },
          ]}
        >
          <Text style={styles.flatlistTitle} numberOfLines={2}>
            {_item.name}
          </Text>
        </View>

        <View style={[styles.checkbox, styles.actionview]}>
          <TouchableWithoutFeedback onPress={editList}>
            <Image
              resizeMode={"contain"}
              source={Images.edit}
              style={{ width: wp("7%") }}
            />
          </TouchableWithoutFeedback>

          <TouchableWithoutFeedback onPress={deleteList}>
            <Image
              resizeMode={"contain"}
              source={Images.delete}
              style={{ width: wp("7%") }}
            />
          </TouchableWithoutFeedback>

          {/* <Text style={styles.flatlistTitle}>{Strings.gates.action}</Text> */}
        </View>
      </View>

      <View style={styles.emptybgview} />
    </View>
  );
}

const styles = StyleSheet.create({
  flHeader: {
    width: wp("96%"),
    //   height: hp('6%'),
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "center",
    marginTop: hp("1%"),
    marginBottom: hp("1%"),
  },
  checkbox: {
    width: wp("13%"),
    // height: hp('6%'),
    justifyContent: "center",
    alignItems: "center",
  },
  flatlistTitle: {
    color: "#292529",
    fontSize: wp("4.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  actionview: {
    width: wp("18%"),
    flexDirection: "row",
    justifyContent: "space-around",
  },
  emptybgview: {
    backgroundColor: "#EFEFEF",
    height: hp("0.3%"),
    width: wp("96%"),
    alignSelf: "center",
  },
});
