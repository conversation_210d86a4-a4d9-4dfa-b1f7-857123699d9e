import React from "react";
import {
  View,
  Text,
  TouchableWithoutFeedback,
  Image,
  StyleSheet,
} from "react-native";
import { Images, Fonts} from "../../common";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

export default function DFOWListItem(props) {
  const { selectList, editList, deleteList, item } = props;

  return (
    <View>
      <View style={styles.flHeader}>
        <View style={styles.checkbox}>
          <TouchableWithoutFeedback
            /*  onPress={() => {
              this.selectGate(item, index);
            }} */
            onPress={selectList}
          >
            <Image
              resizeMode={"contain"}
              source={item.selected == true ? Images.check : Images.uncheck}
              style={{ width: wp("9%"), height: hp("4%") }}
            />
          </TouchableWithoutFeedback>
        </View>

        <View style={[styles.checkbox, { width: wp("15%") }]}>
          <Text style={styles.flatlistTitle}>{item.gateAutoId}</Text>
        </View>

        <View
          style={[
            styles.checkbox,
            { width: wp("50%"), marginBottom: hp("1%") },
          ]}
        >
          <Text style={styles.flatlistTitle} numberOfLines={2}>
            {item.gateName}
          </Text>
        </View>

        <View style={[styles.checkbox, styles.actionview]}>
          <TouchableWithoutFeedback
            /* onPress={() => this.editGate(item, index)} */
            onPress={editList}
          >
            <Image
              resizeMode={"contain"}
              source={Images.edit}
              style={{ width: wp("7%") }}
            />
          </TouchableWithoutFeedback>

          <TouchableWithoutFeedback
            /* onPress={() =>
              this.setState({
                showDelete: true,
                selectedGate: item,
                selectedIndex: index,
              })
            } */
            onPress={deleteList}
          >
            <Image
              resizeMode={"contain"}
              source={Images.delete}
              style={{ width: wp("7%") }}
            />
          </TouchableWithoutFeedback>

          {/* <Text style={styles.flatlistTitle}>{Strings.gates.action}</Text> */}
        </View>
      </View>

      <View style={styles.emptybgview} />
    </View>
  );
}

const styles = StyleSheet.create({
  flHeader: {
    width: wp("96%"),
    //   height: hp('6%'),
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "center",
    marginTop: hp("1%"),
    marginBottom: hp("1%"),
  },
  checkbox: {
    width: wp("13%"),
    // height: hp('6%'),
    justifyContent: "center",
    alignItems: "center",
  },
  flatlistTitle: {
    color: "#292529",
    fontSize: wp("4.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  actionview: {
    width: wp("18%"),
    flexDirection: "row",
    justifyContent: "space-around",
  },
  emptybgview: {
    backgroundColor: "#EFEFEF",
    height: hp("0.3%"),
    width: wp("96%"),
    alignSelf: "center",
  },
});
