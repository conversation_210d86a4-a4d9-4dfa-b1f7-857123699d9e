import { View, Text } from 'react-native'
import React from 'react'

const DateModal = ({showDateModal, onCloseDatePicker, minimumDate, calSelectedDate}) => {
  
    onchangeDate = (tevent, selectedValue) => {
        if (tevent.type == "set" || tevent.type == "dismissed") {
            this.setState({
            showDateModal: false,
            });
        }

        if (Platform.OS == "ios" || tevent.type == "set") {
            const fullYear = selectedValue.getFullYear();
            const fullMonth = selectedValue.getMonth();
            const date = selectedValue.getDate();

            this.setState({
            selectedDate: `${fullMonth + 1}/${date}/${fullYear}`,
            // calSelectedDate: selectedValue,
            // selectedStartTime: "",
            // selectedEndTime: "",
            });

            // selectedDeliveryDate = selectedValue;
            // selectedStartDate = "";
            // selectedEndDate = "";
        }
    };
  
    return (
    <View>
     {Platform.OS == "ios" && (
          <Modal
            isVisible={showDateModal}
            onBackdropPress={() => onCloseDatePicker()}
            style={{
              paddingTop: 45,
              margin: 0,
              justifyContent: "flex-end",
            }}
          >
            <DateTimePicker
              testID="datePicker"
              // timeZoneOffsetInMinutes={0}
              // minuteInterval={interval}
              minimumDate={minimumDate}
              value={calSelectedDate}
              //value={}
              style={{
                backgroundColor: "#fff",
                width:'100%',
                // backgroundColor: "black",
              }}
              display={"spinner"}
              textColor="black"
              // mode={mode}
              onChange={(time, date) => {
                this.onchngeDate(time, date);
              }}
            />
            <View>
              <TouchableOpacity
                activeOpacity={0.5}
                style={styles.datePickerOkContainer}
                onPress={() => {
                  this.onDatePickerDonePressed();
                }}
              >
                <Text style={styles.datePickerOkLabel}>Done</Text>
              </TouchableOpacity>
            </View>
          </Modal>
        )}

        {/* Calender Android */}

        {Platform.OS == "android" && this.state.showDateModal && (
          <DateTimePicker
            testID="datePicker"
            // timeZoneOffsetInMinutes={0}
            // minuteInterval={interval}
            minimumDate={this.state.minimumDate}
            value={this.state.calSelectedDate}
            style={{
              backgroundColor: "#fff",
              width:'100%',
            }}
            //mode={mode}
            onChange={(time, date) => {
              this.onchngeDate(time, date);
            }}
          />
        )}
    </View>
  )
}

export default DateModal;