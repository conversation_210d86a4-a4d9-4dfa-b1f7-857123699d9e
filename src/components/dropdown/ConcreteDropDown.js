import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Fonts from "../../common/fonts";
import Images from "../../common/images";
import { Strings, CommonStyles, Dimensions } from "../../common";
import { TextField } from "../textinput/Textinput";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import Modal from "react-native-modal";
import HoursModal from "../../components/time/HoursModal";
const range = (start, end) => {
  return Array(end - start + 1)
    .fill()
    .map((_, idx) => start + idx);
};
let hoursData = range(0, 12);
let minutesData = range(0, 60);

class ConcreteDropDown extends Component {
  constructor(props) {
    super(props);
    this.state = {
      completePlacementHrs: "",
      completePlacementMins: "",
      cubicYardsTotal: "",
      showToaster: false,
      toastType: "",
      toastMessage: "",
      hoursModal: false,
      minsModal: false,
      showCompletePlacementHrsModal: false,
      showCompletePlacementMinsModal: false,
    };
  }
  showToaster = (type, message) => {
    //  Keyboard.dismiss();
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showToaster: false,
          });
        }, 2000);
      }
    );
  };
  updateMasterState = (key, value) => {
    if (key == Strings.addConcrete.cubicYardsTotal) {
      this.setState({
        cubicYardsTotal: value,
      });
    } else if (key == Strings.addConcrete.completePlacementHrs) {
      this.setState({
        completePlacementHrs: value,
      });
    } else if (key == Strings.addConcrete.completePlacementMins) {
      this.setState({
        completePlacementMins: value,
      });
    }
  };
  bottomContainer = () => {
    return (
      <View style={styles.bottomContainer}>
        <TouchableOpacity
          onPress={() => {
            this.props.skip("skip", "", this.state.cubicYardsTotal);
            this.props.onbackPress();
          }}
        >
          <View style={styles.cancel}>
            <Text style={styles.cancelText}>{Strings.addMember.skip}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            // if(this.state.completePlacementHrs==='')
            // {
            //   if(this.state.completePlacementMins!=''||this.state.completePlacementMins==0){
            //   this.showToaster("error", 'Please Select Hours');
            // }
            // }
            // else if(this.state.completePlacementMins==='')
            // {
            //   if(this.state.completePlacementHrs!==''||this.state.completePlacementHrs==0){
            //   this.showToaster("error", 'Please Select Mintues');
            // }
            // }
            if (
              this.state.completePlacementMins == 0 &&
              this.state.completePlacementHrs == 0
            ) {
              this.showToaster("error", "Please provide Valid Completion Time");
            } else if (
              this.state.completePlacementHrs != "" ||
              this.state.cubicYardsTotal != "" ||
              this.state.completePlacementMins != ""
            ) {
              this.props.submit(
                "submit",
                this.state.completePlacementHrs,
                this.state.completePlacementMins,
                this.state.cubicYardsTotal
              );
              this.props.onbackPress();
            } else {
              this.showToaster("error", Strings.errors.entervalues);
            }
          }}
        >
          <View style={styles.submit}>
            <Text style={styles.submitText}>{Strings.addMember.submit}</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };
  //RENDER DATA

  onSelectPicker = (item) => {
    if (this.state.showCompletePlacementHrsModal) {
      this.setState({ completePlacementHrs: item });
    } else if (this.state.showCompletePlacementMinsModal) {
      this.setState({ completePlacementMins: item });
    } else {
      this.onCloseModals();
    }
    this.onCloseModals();
  };

  onCloseModals = () => {
    this.setState({
      hoursModal: false,
      minsModal: false,
      showTruckSpacingHrsModal: false,
      showTruckSpacingMinsModal: false,
      showCompletePlacementHrsModal: false,
      showCompletePlacementMinsModal: false,
    });
  };
  render() {
    if (this.props.visible == true) {
      return (
        <TouchableOpacity
          onPress={this.props.onbackPress}
          style={[
            styles.mainContainer,
            { height: hp("100%"), backgroundColor: "#0000" },
          ]}
        >
          {/* <KeyboardAwareScrollView> */}
          <View style={{ width: "100%", height: "100%" }}>
            <TouchableWithoutFeedback>
              <View style={styles.mainContainer}>
                <View style={styles.headerCont}>
                  <Text style={styles.title} numberOfLines={3}>
                    {this.props.title}
                  </Text>
                  <TouchableOpacity
                    style={styles.closeBtn}
                    onPress={this.props.closeBtn}
                  >
                    <Image source={Images.closeBlack} />
                  </TouchableOpacity>
                </View>
                {/* <TextField
              attrName={Strings.addConcrete.completePlacementHrs}
              title={Strings.addConcrete.completePlacementHrs}
              value={this.state.completePlacement}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={false}
              textInputStyles={CommonStyles.inputText}
              /> */}
                {/* hours to complete placement */}
                <Text
                  style={[
                    styles.idTitle,
                    {
                      marginLeft: 4,
                      fontSize: wp("4%"),
                      marginTop: hp("2%"),
                      width: "90%",
                      alignSelf: "center",
                    },
                  ]}
                >
                  {Strings.addConcrete.completePlacementHrs}
                </Text>
                <View style={CommonStyles.rowContainer}>
                  {/* complete placement spacing Hrs */}
                  <TextField
                    attrName={Strings.addConcrete.completePlacementHrs}
                    value={
                      this.state.completePlacementHrs
                        ? `${this.state.completePlacementHrs.toString()}` +
                          (this.state.completePlacementHrs == "1"
                            ? " Hour"
                            : " Hours")
                        : this.state.completePlacementHrs == "0"
                        ? `${"0" + " Hour"}`
                        : ""
                    }
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={false}
                    textInputStyles={CommonStyles.inputText}
                    showButton={true}
                    onPress={() => {
                      this.setState({
                        hoursModal: true,
                        showCompletePlacementHrsModal: true,
                      });
                    }}
                    container={{
                      width: Dimensions.scaleWidth(42),
                      height: Dimensions.scaleHeight(4),
                      alignSelf: "flex-start",
                    }}
                    imageSource={Images.clock}
                    placeholder={Strings.addConcrete.selectHrs}
                    progressWidth={Dimensions.scaleWidth(42)}
                    buttonContainer={{
                      width: Dimensions.scaleWidth(22),
                      backgroundColor: "pink",
                    }}
                  />

                  {/* complete placement Time  */}
                  <TextField
                    attrName={Strings.addConcrete.completePlacementMins}
                    value={
                      this.state.completePlacementMins
                        ? `${this.state.completePlacementMins.toString()}` +
                          (this.state.completePlacementMins == "1"
                            ? " Min"
                            : " Mins")
                        : this.state.completePlacementMins == "0"
                        ? `${"0" + " Min"}`
                        : ""
                    }
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={false}
                    textInputStyles={CommonStyles.inputText}
                    showButton={true}
                    onPress={() => {
                      this.setState({
                        minsModal: true,
                        showCompletePlacementMinsModal: true,
                      });
                    }}
                    container={{
                      width: Dimensions.scaleWidth(42),
                      height: Dimensions.scaleHeight(4),
                      alignSelf: "flex-start",
                    }}
                    imageSource={Images.clock}
                    placeholder={Strings.addConcrete.selectMins}
                    progressWidth={Dimensions.scaleWidth(42)}
                    buttonContainer={
                      {
                        // width: Dimensions.scaleWidth(42),
                      }
                    }
                  />
                </View>
                <TextField
                  attrName={Strings.addConcrete.cubicYardsTotal}
                  title={Strings.addConcrete.cubicYardsTotal}
                  value={this.state.cubicYardsTotal}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={false}
                  textInputStyles={CommonStyles.inputText}
                />
                {this.bottomContainer()}

                {this.state.showToaster && (
                  <Toastpopup
                    backPress={() => this.setState({ showToaster: false })}
                    toastMessage={this.state.toastMessage}
                    type={this.state.toastType}
                    container={{ marginBottom: hp("12%") }}
                  />
                )}
                <Modal
                  isVisible={this.state.hoursModal || this.state.minsModal}
                  onBackdropPress={() => this.onCloseModals()}
                  style={{
                    paddingTop: 45,
                    margin: 0,
                    justifyContent: "flex-end",
                    // backgroundColor: '#fff',
                  }}
                >
                  <HoursModal
                    data={this.state.hoursModal ? hoursData : minutesData}
                    isHours={this.state.hoursModal ? true : false}
                    Clear={() => {
                      this.onCloseModals();
                      this.setState({
                        completePlacementHrs: "",
                        completePlacementMins: "",
                      });
                    }}
                    onSelected={this.onSelectPicker}
                  />
                </Modal>
              </View>
            </TouchableWithoutFeedback>
          </View>
          {/* </KeyboardAwareScrollView> */}
        </TouchableOpacity>
      );
    } else {
      return null;
    }
  }
}

export default ConcreteDropDown;

const styles = StyleSheet.create({
  mainContainer: {
    height: hp("50%"),
    width: wp("100%"),
    position: "absolute",
    backgroundColor: Colors.white,
    bottom: 0,
    zIndex: 999,
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0.8,
    // shadowOffset: {width: 10, height: 10},
    shadowOpacity: 1,
    elevation: 200,
    shadowColor: "rgba(0,0,0,0.14)",
    borderTopEndRadius: hp("5%"),
    borderTopStartRadius: hp("5%"),
  },
  flatlistContainer: {
    width: wp("100%"),
    // height: hp('5%'),
    // marginTop: hp('1%')
  },
  text: {
    marginLeft: wp("15%"),
    fontSize: 14,
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
    marginLeft: 4,
  },
  seperator: {
    height: 1,
    backgroundColor: Colors.placeholder,
    width: wp("100%"),
  },
  headerCont: {
    height: hp("9%"),
    width: "90%",
    alignSelf: "center",
    marginTop: hp("3%"),
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratBold,
    alignSelf: "center",
    textAlign: "center",
  },
  closeBtn: {
    position: "absolute",
    right: 15,
    alignSelf: "center",
    top: 5,
  },
  bottomContainer: {
    width: wp("90%"),
    flexDirection: "row",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: hp("4%"),
  },
  cancel: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: "#757575",
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
});
