import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Linking
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Fonts from "../../common/fonts";
import Images from "../../common/images";

class ResDropdown extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  phoneNumber=(number)=>{
    if (Platform.OS === 'android') {
      phoneNumber = `tel:${number}`;}
       else {
      phoneNumber = `telprompt:${number}`;
    }
      Linking.openURL(phoneNumber);
  }
  //RENDER DATA
  
  renderData = ({ item, index }) => {
    if(item.name!=null){
    return (
      <View style={[styles.flatlistContainer.width,this.props.renderStyle]}>
        <TouchableWithoutFeedback
          onPress={() => this.props.onPress(item, index)}
        >
          <View
            style={[
              styles.flatlistContainer,
             {  height: hp("5%"),flexDirection:"row",},
              this.props.container,
            ]}
          >
            <View style={{width:wp('70'),}}>
              <Text
                style={[
                  styles.text,
                  {
                    color:
                      this.props.value == item.name
                        ? Colors.themeColor
                        : Colors.black,
                  },
                  this.props.textContainer,
                ]}
              >
                {item.name ? item.name.trim() : ""}
              </Text>
              </View>
              <View style={{flexDirection:'row',}}>
                {item.phoneNumber!=null &&
             <TouchableOpacity onPress={()=>{this.phoneNumber(item.phoneNumber)}}>
              <Image source={Images.phoneRes} style={styles.image} />
              </TouchableOpacity>
              }
              <TouchableOpacity  style={{marginLeft:item.phoneNumber==null?60:40}}onPress={()=>{Linking.openURL(`mailto:${item.email}`)}}>
              <Image source={Images.mailRes} style={styles.imageMail} />
              </TouchableOpacity>
              </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }
  };

  render() {
    if (this.props.visible == true) {
      return (
        <TouchableOpacity
          onPress={this.props.onbackPress}
          style={[
            styles.mainContainer,
            { height: hp("100%"), backgroundColor: "#0000" },
          ]}
        >
          <View style={{ width: "100%", height: "100%" }}>
            <TouchableWithoutFeedback >
              <View style={styles.mainContainer}>
                <View style={styles.headerCont}>
                  <Text style={styles.title}>{this.props.title}</Text>
                  <TouchableOpacity
                    style={styles.closeBtn}
                    onPress={this.props.closeBtn}
                  >
                    <Image source={Images.closeBlack} />
                  </TouchableOpacity>
                </View>
                <FlatList
                  data={this.props.data}
                  renderItem={this.renderData}
                  keyExtractor={(item, index) => index.toString()}
                  nestedScrollEnabled={true}
                  style={this.props.flatListContainer}
                />
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableOpacity>
      );
    } else {
      return null;
    }
  }
}

export default ResDropdown;

const styles = StyleSheet.create({
  mainContainer: {
    height: hp("30%"),
    width: wp("100%"),
    position: "absolute",
    backgroundColor: Colors.white,
    bottom: 0,
    zIndex: 999,
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0.8,
    // shadowOffset: {width: 10, height: 10},
    shadowOpacity: 1,
    elevation: 200,
    shadowColor: "rgba(0,0,0,0.14)",
    borderTopEndRadius: hp("5%"),
    borderTopStartRadius: hp("5%"),
  },
  flatlistContainer: {
    width: wp("100%"),
    // height: hp('5%'),
    // marginTop: hp('1%')
  },
  text: {
    marginLeft: wp("5%"),
    fontSize: 14,
  },
  seperator: {
    height: 1,
    backgroundColor: Colors.placeholder,
    width: wp("100%"),
  },
  headerCont: {
    height: hp("5%"),
    width: "90%",
    alignSelf: "center",
    marginTop: hp("3%"),
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratBold,
    alignSelf: "center",
  },
  closeBtn: {
    position: "absolute",
    right: 15,
    alignSelf: "center",
    top: 5,
  },
  image:{
    height:20,
    width:20,
  },
  imageMail:{
    height:20,
    width:24,
  }
});
