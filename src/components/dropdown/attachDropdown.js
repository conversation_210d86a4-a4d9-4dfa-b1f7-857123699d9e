import React, { Component } from 'react';
import { View, Text, StyleSheet, FlatList, Image, TouchableOpacity, TouchableWithoutFeedback } from 'react-native';
import {
    widthPercentageToDP as wp,
    heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Colors from '../../common/color';
import Fonts from '../../common/fonts';
import Images from '../../common/images';
import Strings from "../../common/string";

class AttachDropdown extends Component {
    constructor(props) {
        super(props);
        this.state = {};
    }

    //RENDER DATA
    renderData = ({ item, index }) => {
        return (
            <View style={[styles.flatlistContainer, { flex: 1, flexDirection: 'row', alignSelf: 'center', minHeight: hp('10%') }]}>
                {item.type.includes('doc')&&
                <Image  resizeMode={'contain'} source={Images.doc_place} style={{
                    width: wp('8%'),
                    height: wp('8%'),
                    borderRadius: wp('1%'),
                    marginLeft: wp('6%'),
                }}></Image>
            }
             {item.type.includes('pdf')&&
                <Image  resizeMode={'contain'} source={Images.pdf_place} style={{
                    width: wp('8%'),
                    height: wp('8%'),
                    borderRadius: wp('1%'),
                    marginLeft: wp('6%'),
                }}></Image>
            }
             {(item.type.includes('png') || item.type.includes('jpg') || item.type.includes('jpeg'))&&
                <Image  resizeMode={'contain'} source={{uri:item.uri}} style={{
                    width: wp('8%'),
                    height: wp('8%'),
                    borderRadius: wp('1%'),
                    marginLeft: wp('6%'),
                }}></Image>
            }
                <View style={{ width: wp('65%'), marginLeft: 10,marginTop:5 }}>
                    <Text style={[styles.text,{ color: '#292529',fontSize: wp('4%'),fontFamily: Fonts.montserratSemiBold, }]}>{item.name==undefined?item.fileName:item.name}</Text>
                    {/* <View style={styles.seperator} /> */}
                </View>
                <View style={{ right: 0, marginTop: 5 }}>
                    <TouchableWithoutFeedback 
                    onPress={() =>  this.props.onRemove(index)}>
                        <Image source={Images.delete} ></Image>
                    </TouchableWithoutFeedback>
                </View>
            </View>


        );
    };

    render() {
        if (this.props.visible) {
            return (
               // <TouchableOpacity onPress={this.props.onbackPress} style={[styles.mainContainer, { height: hp('100%'), backgroundColor: '#0000' }]}>
                  <View style={[styles.mainContainer, { height: hp('100%'), backgroundColor: '#0000' }]}>
                  <View style={{ width: '100%', height: '100%' }}>
                        <View style={styles.mainContainer}>
                            <View style={styles.headerCont}>
                                <Text style={styles.title}>{this.props.title}</Text>
                                <TouchableOpacity style={styles.closeBtn} onPress={this.props.closeBtn}>
                                    <Image source={Images.closeBlack} />
                                </TouchableOpacity>
                            </View>
                            <FlatList
                                data={this.props.data}
                                renderItem={this.renderData}
                                keyExtractor={(item, index) => index.toString()}
                                nestedScrollEnabled={true}
                            />
                            <TouchableWithoutFeedback
                              onPress={() =>{ 
                                  this.props.onDone(this.props.data);}}
                            >
                                <View
                                    style={{
                                        width: wp("50%"),
                                        alignSelf: "center",
                                        height: hp("5%"),
                                        marginBottom: hp("5%"),
                                        backgroundColor: Colors.themeOpacity,
                                        borderRadius: hp("3.5%"),
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontSize: wp("5%"),
                                            color: Colors.themeColor,
                                            fontFamily: Fonts.montserratSemiBold,
                                        }}
                                    >
                                        {Strings.deliverydetails.done}
                                    </Text>
                                </View>
                            </TouchableWithoutFeedback>
                        </View>
                    </View>
                    </View>
             //   </TouchableOpacity>
            );
        } else {
            return null
        }
    }
}

export default AttachDropdown;

const styles = StyleSheet.create({
    mainContainer: {
        height: hp('40%'),
        width: wp('100%'),
        position: 'absolute',
        backgroundColor: Colors.white,
        bottom: 0,
        zIndex: 999,
        borderColor: Colors.shadowColor,
        borderWidth: Platform.OS == 'ios' ? 1 : 0.8,
        // shadowOffset: {width: 10, height: 10},
        shadowOpacity: 1,
        elevation: 200,
        shadowColor: 'rgba(0,0,0,0.14)',
        borderTopEndRadius: hp('5%'),
        borderTopStartRadius: hp('5%')
    },
    flatlistContainer: {
        width: wp('100%'),
       // height: hp('6%'),
        marginTop: hp('1%'),

    },
    text: {
        marginLeft: 10,
    },
    seperator: {
        height: 1,
        backgroundColor: Colors.placeholder,
        width: wp('100%'),
    },
    headerCont: {
        height: hp('5%'),
        width: '90%',
        alignSelf: 'center',
        marginTop: hp('3%')
    },
    title: {
        color: Colors.black,
        fontSize: wp('5.5%'),
        fontFamily: Fonts.montserratBold,
        alignSelf: 'center'
    },
    closeBtn: {
        position: 'absolute',
        right: 15,
        alignSelf: 'center',
        top: 5
    }
});