import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Fonts from "../../common/fonts";
import Images from "../../common/images";

class dropdown extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  //RENDER DATA
  renderData = ({ item, index }) => {
    if(item.name!=null){
    return (
      <View style={[styles.flatlistContainer.width,this.props.renderStyle]}>
        <TouchableWithoutFeedback
          onPress={() => this.props.onPress(item, index)}
        >
          <View
            style={[
              styles.flatlistContainer,
             { justifyContent: "center", height: hp("5%") },
              this.props.container,
            ]}
          >
            <Text
              style={
                [styles.text,
                {
                  color:
                    this.props.value == item.name
                      ? Colors.themeColor
                      : Colors.black,
                  marginLeft: wp("5%"),
                  width: wp("85%"),
                },
                this.props.textContainer]
              }
              numberOfLines={2}
            >
              {item.dialCode ? item.dialCode : ""}
              <Text
                style={[
                  styles.text,
                  {
                    color:
                      this.props.value == item.name
                        ? Colors.themeColor
                        : Colors.black,                   
                  },
                  this.props.equipTextContainer,
                ]}
              >
                {" "}
                {item.name ? item.name.trim() : ""}
              </Text>
            </Text>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }
  };

  render() {
    if (this.props.visible == true) {
      return (
        <TouchableOpacity
          onPress={this.props.onbackPress}
          style={[
            styles.mainContainer,
            { height: hp("100%"), backgroundColor: "#0000" },
            this.props.customMainContainer
          ]}
        >
          <View style={{ width: "100%", height: "100%" }}>
            <TouchableWithoutFeedback >
              <View style={styles.mainContainer}>
                <View style={styles.headerCont}>
                
                  <Text style={[styles.title,this.props.customTextTitle]}>{this.props.title}</Text>
                
                  <TouchableOpacity
                    style={[styles.closeBtn,this.props.closeBtnStyle]}
                    onPress={this.props.closeBtn}
                  >
                    <Image source={Images.closeBlack} />
                  </TouchableOpacity>
                </View>
                  <View style={styles.inlineStyle} />
                <FlatList
                  data={this.props.data}
                  renderItem={this.renderData}
                  keyExtractor={(item, index) => index.toString()}
                  nestedScrollEnabled={true}
                  style={this.props.flatListContainer}
                />
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableOpacity>
      );
    } else {
      return null;
    }
  }
}

export default dropdown;

const styles = StyleSheet.create({
  mainContainer: {
    height: hp("35%"),
    width: wp("100%"),
    position: "absolute",
    backgroundColor: Colors.white,
    bottom: 0,
    zIndex: 999,
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0.8,
    // shadowOffset: {width: 10, height: 10},
    shadowOpacity: 1,
    elevation: 200,
    shadowColor: "rgba(0,0,0,0.14)",
    borderTopEndRadius: hp("5%"),
    borderTopStartRadius: hp("5%"),
  },
  flatlistContainer: {
     width: wp("100%"),
  },
  text: {
    marginLeft: wp("15%"),
    fontSize: 14,
    textAlign:"center"
  },
  seperator: {
    height: 1,
    backgroundColor: Colors.placeholder,
    width: wp("100%"),
  },
  headerCont: {
    height: hp("5%"),
    width: "90%",
    alignSelf: "center",
    marginTop: hp("3%"),
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratBold,
    alignSelf: "center",
  },
  closeBtn: {
    position: "absolute",
    right: 15,
    alignSelf: "center",
    top: 5, 
  },
  inlineStyle:{
    backgroundColor: Colors.inlineGrey,
    height: 0.5,
  }
});
