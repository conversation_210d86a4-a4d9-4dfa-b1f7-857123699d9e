import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Linking,
  FlatList
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Fonts from "../../common/fonts";
import Modal from "react-native-modal";
import Strings from "../../common/string";
import { getAppVersion } from "../../api/Api";
import { GET_APP_VERSION } from "../../api/Constants";
class ForceUpdateDropdown extends Component{

    constructor(props) {
        super(props);
        this.state = {
            isModal:false,
            dashboardOverview: []
        };
      }

    learnMoreLink() {
        Linking.openURL('https://www.follo.co/blog')
    }

    componentDidMount(){
        this.onFetchAppVersion();
    }

    onFetchAppVersion = () => {  
        getAppVersion(GET_APP_VERSION,
          (response) => {
          if (response?.status == 200) {
            this.setState({
                dashboardOverview:response?.data?.data.releasenote
            })
          } else {
            console.error(response.data.message);
          }
        })
      }

    renderFlatListItem = ({ item}) => {
        return (
            <Text style={styles.notesText}>• {item.message}</Text>
        )
      };

    render(){
        return(
        <Modal
            animationType="slide"
            transparent={true}
            isVisible={this.props.isModal}
            style={styles.modalContainer}
            backdropOpacity={0.1}>

            <View style={styles.mainContainer}>
              <View style={styles.headerCont}>
                <View style={styles.newButtonView}>
                    <Text style={styles.buttonText}>{Strings.forceUpdate.new}</Text>
                </View>

                <View style={styles.updateView}>
                    <Text style={styles.title}>{Strings.forceUpdate.updateAvailable}</Text>
                </View>
             </View>

                <View style={styles.flatlistContainer}>
                  <Text style={styles.whatsNewText}>{Strings.forceUpdate.whatNew}</Text>
                </View>

             <View contentContainerStyle={{paddingBottom:10,}} style={styles.updateTextView}>
               <FlatList
                    data={this.state.dashboardOverview}
                    renderItem={this.renderFlatListItem}
                    showsHorizontalScrollIndicator={false}
                    keyExtractor={(index) => index.toString()}
                    onEndReachedThreshold={0}
                  />
                <TouchableOpacity onPress={()=>this.learnMoreLink()} style={{margin:5}}>
                    <Text style={styles.learnMoreText}>{Strings.forceUpdate.learnMore}</Text>
                </TouchableOpacity>
             </View>

                <TouchableOpacity onPress={this.props.updateButton}  style={styles.updateNowBtn}>
                    <Text style={styles.updateNowText}>{Strings.forceUpdate.updateNow}</Text>
                </TouchableOpacity>

                <TouchableOpacity onPress={this.props.skipButton} style={styles.skipNowView}>
                   <Text style={styles.skipNowText}>{Strings.forceUpdate.skipNow}</Text>
                </TouchableOpacity>
         
              </View>
          </Modal>
        )
    }
}
export default ForceUpdateDropdown;

const styles= StyleSheet.create({
    mainContainer:{
        height: hp("50%"),
        width: wp("100%"),
        position: "absolute",
        backgroundColor: Colors.white,
        bottom: 0,
        zIndex: 999,
        shadowOpacity: 1,
        elevation: 200,
        shadowColor: "rgba(0,0,0,0.14)",
        borderTopEndRadius: hp("5%"),
        borderTopStartRadius: hp("5%"),
        paddingBottom:30
    },
    flatlistContainer: {
        width: "95%",
        alignSelf: "center",
    },
    headerCont: {
        height: hp("5%"),
        width: "90%",
        alignSelf: "center",
        marginTop: hp("3%"),
        flexDirection:'row',
    },
    newButtonView:{
        width: wp("20%"),
        height: hp("4%"),
        borderRadius: hp("0.5%"),
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: Colors.newUpdate,
    },
    title: {
        color: Colors.black,
        fontSize: wp("5%"),
        fontFamily: Fonts.montserratSemiBold,
        alignSelf: "center",
        justifyContent: "center",
        alignItems: "center",
        marginLeft: 10,
    },
    updateView:{
        justifyContent: "center",
        alignItems: "center", height: hp("4%"),
    },
    buttonText:{
        fontSize: wp("3.3%"),
        marginRight: 10,
        fontFamily: Fonts.montserratBold,
        marginLeft: 10,
        color: Colors.white,
    },
    whatsNewText:{
        color: Colors.black,
        fontSize: wp("5%"),
        fontFamily: Fonts.montserratSemiBold,
        marginLeft: 15,
    },
    updateTextView:{
        marginHorizontal:wp("8%"),
        margin:5,
        height:hp('25%'),
    },
    notesText:{
        fontSize: wp("4%"),
        margin: 3,
        fontFamily:Fonts.montserratRegular
    },
    updateNowBtn:{
        width:'85%',
        backgroundColor:Colors.themeColor,
        borderRadius: hp("5%"),
        height: hp("5%"),
        justifyContent: "center",
        alignItems: "center",
        marginLeft:25,
    },
    updateNowText:{
        fontSize:wp("4.2%"),
        fontFamily:Fonts.montserratMedium,
        color:Colors.white,
    },
    skipNowView:{
        justifyContent: "center",
        alignItems: "center",
        marginTop:10,
    },
    skipNowText:{
        fontSize:wp("3.5%"),
        fontFamily:Fonts.montserratMedium,
        color:Colors.skipNowText,
        textDecorationLine: 'underline',
        textDecorationColor: Colors.skipNowText,
    },
    modalContainer:{
        margin: 0,
        justifyContent: "flex-end",
        backgroundColor:'rgba(0,0,0,0.5)',
    },
    learnMoreText:{
        color:Colors.learnMore,
        fontSize: wp("4%"),
        fontFamily:Fonts.montserratRegular
    }
})