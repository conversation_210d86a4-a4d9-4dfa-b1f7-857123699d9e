import React, { Component } from "react";
import {
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  Text,
  Platform,
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Images from "../../common/images";
import Fonts from "../../common/fonts";
import Colors from "../../common/color";
import ModalDropdown from "react-native-modal-dropdown";
import {toggleAddCalendar} from "../../actions/postAction"
import { connect } from "react-redux";
const dropOption = [
  { id: "1", value: "Delivery Calendar", label: "Delivery Calendar" },
  { id: "2", value: "Crane Calendar", label: "Crane Calendar" },
  { id: "3", value: "Concrete Calendar", label: "Concrete Calendar" },
 { id: "4", value: "Inspection Calendar", label: "Inspection Calendar" }, 
];

class CalendarHeader extends Component {
  constructor(props) {
    super(props);
    if(this.props.currentCalendar=='Delivery Calendar'){
      this.props.toggleAddCalendar('Delivery');
    }
    
    this.state = {
      selectedText: this.props.currentCalendar,
      isDropDownPressed: false,
    };
  }

  onPressEvent = (item) => {
    if (this.state.selectedText != dropOption[item].value) {
      this.setState({ selectedText: dropOption[item].value });
      this.props.swtichCalendar(dropOption[item].value);
    }
  };
  renderParentRow = (option, index, isSelected) => {
   
    return (
      <View style={styles.dropTextContainer}>
        <Text style={styles.dropTextInside}>{option.value}</Text>
      </View>
    );
  };
  render() {
    // return (
    //   <View style={styles.container}>
    //     {/* <TouchableOpacity style={styles.arrowLeftView} onPress={this.props.onLeftArrowPressed}>
    //       <Image source={Images.arrow_left} style={styles.imageLeft} />
    //     </TouchableOpacity> */}
    //     <Text style={styles.monthText}> {this.props.monthName}</Text>
    //     <TouchableOpacity style={styles.arrowRightView} onPress={this.props.onRightArrowPressed}>
    //       <Image source={Images.arrow_right} style={styles.imageLeft} />
    //     </TouchableOpacity>
    //     <ModalDropdown
    //       style={styles.customDropdownStyle}
    //       dropdownStyle={styles.customOptionsStyle}
    //       options={dropOption}
    //       renderRow={this.renderParentRow}
    //       onSelect={(options) => this.onPressEvent(options)}
    //       defaultValue=""
    //       saveScrollPosition={false}
    //       dropdownListProps={{}}
    //     >
    //       <View style={styles.imageContainer}>
    //         <Text style={styles.selectedText}>{this.state.selectedText}</Text>
    //         <Image source={Images.arrowDown} style={styles.arrowDown} />
    //       </View>
    //     </ModalDropdown>
    //   </View>
    // );
    return (
      <View style={styles.container}>
        <Text style={styles.monthText}> {this.props.monthName}</Text>
        <ModalDropdown
          style={styles.customDropdownStyle}
          dropdownStyle={styles.customOptionsStyle}
          options={dropOption}
          renderRow={this.renderParentRow}
          onSelect={(options) => this.onPressEvent(options)}
          defaultValue=""
          saveScrollPosition={false}
          dropdownListProps={{}}
        >
          <View style={styles.imageContainer}>
            <Text style={styles.selectedText}>{this.state.selectedText}</Text>
            <Image source={Images.arrowDown} style={styles.arrowDown} />
          </View>
        </ModalDropdown>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    paddingTop: hp("1"),
    paddingBottom: hp("1"),
    justifyContent: "center",
    alignItems: "center",
    //height:hp("5.1%")
  },
  arrowLeftView: {
    marginRight: wp("3"),
  },
  arrowRightView: {
    marginRight: wp("4"),
    marginLeft: wp("3"),
  },
  arrowDown: {
    alignSelf: "center",
    marginRight: wp("1.5"),
  },
  imageLeft: {
    width: wp("2.4"),
    height: hp("2.4"),
  },
  monthText: {
    fontFamily: Fonts.montserratSemiBold,
    fontSize: 12,
     marginRight:10
  },
  selectedText: {
    fontFamily: Fonts.montserratSemiBold,
    fontSize: 15,
    marginRight: hp("1.5"),
    marginLeft: hp("1.5"),
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    zIndex: 999,
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0.5,
    shadowOffset: { width: 10, height: 10 },
    shadowOpacity: 1,
    elevation: 200,
    shadowColor: "rgba(0,0,0,0.14)",
    borderRadius: 200,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("12"),
    marginLeft: 5,
    borderColor: Colors.white,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 200,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
  },
  imageContainer: {
    flexDirection: "row",
  },
  dropTextContainer: {
    width: wp("45%"),
    flexDirection: "row",
    alignItems: "center",
    height: hp("3%"),
    backgroundColor: "white",
  },
  dropTextInside: {
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratMedium,
    color: Colors.planDesc,
    marginLeft: 10,
  },
});

export default connect(null,{toggleAddCalendar}) (CalendarHeader);