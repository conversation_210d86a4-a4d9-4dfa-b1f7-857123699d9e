import React from "react";
import { StyleSheet, View, Image, TouchableOpacity, Text } from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Images from "../../common/images";
import Fonts from "../../common/fonts";
import Colors from "../../common/color";
import HeaderAnimation from "../logoAnimation/logoAnimation";

export default function Header(props) {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.backButtonContainer}
        onPress={props.backPress}
      >
        <View style={styles.backButton}>
          <Image source={Images.backButton} />
        </View>
      </TouchableOpacity>

      <View style={styles.subContainer}>
        {props.title ? (
          <Text style={styles.title}>{props.title}</Text>
        ) : (
          <HeaderAnimation />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    marginTop: hp("2%"),
    alignItems: "center",
    width: "100%"
  },
  subContainer: {
    width: wp("80%"),
    justifyContent: "center",
    alignItems: "center",
  },
  backButtonContainer: {
    padding: 6,
    marginLeft: wp("5%"),
  },
  backButton: {
   
  },
  title: {
    fontSize: 20,
    fontFamily: Fonts.montserratSemiBold,
    color: Colors.black,
  },
});
