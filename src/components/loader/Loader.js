import React, { Component } from "react";
import { View, StyleSheet, Animated, Image } from "react-native";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import Images from "../../common/images";
import { View as AnimmatableView } from "react-native-animatable";

const rotation = {
  from: {
    rotate: "0deg",
  },
  to: {
    rotate: "360deg",
  },
};

export default class Loader extends Component {
  constructor(props) {
    super(props);
    this.fade_In = new Animated.Value(0);
        this.state = {
      visible: false,
      fadeIn: new Animated.Value(0),
    };
  }

  componentDidMount() {
    this.fadeIn();
  }
  componentWillUnmount(){
    this.fade_In.stopAnimation();
  }

  fadeIn() {
    this.fade_In.setValue(0);
    Animated.timing(this.fade_In, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      this.fadeOut();
    });
  }

  fadeOut() {
    Animated.timing(this.fade_In, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      this.fadeIn();
    });
  }

  render() {
    return (
      <AnimmatableView animation={"bounceInUp"} style={styles.container}>
        <View style={styles.subContainer}>
          <Animated.View style={{ opacity: this.fade_In }}>
            <Image
              source={Images.logoArr}
              resizeMode={"contain"}
              style={styles.path}
            />
          </Animated.View>
        </View>
      </AnimmatableView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    width: wp("100%"),
    height: hp("100%"),
    justifyContent: "center",
    alignItems: "center",
    zIndex: 999,
  },
  subContainer: {
    width: wp("38%"),
    height: hp("18%"),
    borderRadius: wp("8%"),
    justifyContent: "center",
    alignItems: "center",
  },
  path: {
    width: wp("15%"),
    height: hp("8%"),
  },
});
