import React from 'react';
import {StyleSheet, View, Image} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {View as AnimatableView} from 'react-native-animatable';
import Images from '../../common/images';

export default function LogoAnimation(props) {
  return (
    <View style={styles.container}>
      <AnimatableView
        animation="slideInDown"
        duration={props.duration ? props.duration : 2000}
        style={styles.folloContainer}>
        <Image resizeMode={'center'} source={Images.follo} />
      </AnimatableView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginTop: hp('2%'),
    alignItems: 'center',
    justifyContent: 'center'
  },
  folloContainer: {
    width: wp('80%'),
    height: hp('12%'),
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  logoContainer: {
    width: wp('40%'),
    height: hp('12%'),
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  imageLogo: {
    marginLeft: wp('2%'),
  },
});