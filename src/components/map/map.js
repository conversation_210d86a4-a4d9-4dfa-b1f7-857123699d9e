import React, { Component } from "react";
import MapView, { <PERSON><PERSON>, PROVIDER_GOOGLE } from "react-native-maps";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
  KeyboardAvoidingView,
  SafeAreaView,
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import axios from "axios";
import { Fonts, Colors, Images, Strings } from "../../common";
import { GEOCODER_API } from "../../api/Constants";
import  AutoCompleteList  from "../autoComplete/AutoCompleteList";
const API_KEY = "AIzaSyB2EHXZ7yQZ3KItOpshW2F4DJ5ewmagOWU";
import {
  googleAutoCompleteAPI,
  geoCodeAdrressAPI,
} from "../../api/GoogleServices";
import { TextField } from "../textinput/Textinput";
let initialCoords = {
  latitude: 40.71331,
  longitude: -74.00753,
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};
import Geolocation from "react-native-geolocation-service";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { checkAndRequestPermission } from "../../utils/PermissionUtils";
import { PERMISSIONS } from "react-native-permissions";

const MapHeader = ({ onBackPress }) => {
  const { title } = Strings.map;
  return (
    <View style={[styles.header]}>
      <TouchableOpacity
        style={styles.backButtonContainer}
        onPress={onBackPress}
      >
        <View>
          <Image source={Images.backButton} />
        </View>
      </TouchableOpacity>

      <View style={styles.subContainer}>
        <Text style={styles.title}>{title}</Text>
      </View>
    </View>
  );
};

const BottomContainer = ({
  pressContinue,
  textFieldValue,
  onChangeText,
  autoComplete,
  predictionList,
  onSelectPlace,
}) => {
  const { pinLabel, buttonText } = Strings.map;

  return (
    <KeyboardAvoidingView>
      <View style={styles.bottomContainer}>
        <View style={{ width: wp("80%") }}>
          <Text  style={[styles.pinText]}>{pinLabel}</Text>
          <TextField
           // title={pinLabel}
            style={[styles.locationText]}
            value={textFieldValue}
            //underlineColorAndroid="transparent"
            updateMasterState={(key, value) => onChangeText(value)}
            editable
            multiline
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 8,
              //padding: Platform.OS == "ios" ? 15 : 0,
              height:'auto'
            }}
           container={{height: Platform.OS == "android" ?hp("11%"):hp("8%")}}
             //container={{backgroundColor:'pink',}}
          />
          {autoComplete && (
            <AutoCompleteList
              source={predictionList}
              onSelectPlace={(place) => onSelectPlace(place)}
            />
          )}
          <TouchableOpacity style={styles.submit} onPress={pressContinue}>
            <Text style={styles.submitText}>{buttonText}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

export default class Map extends Component {
  constructor(props) {
    super(props);
    this.state = {
      region: initialCoords,
      selectedLocation: "",
      showAuto: false,
    };
  }

  componentDidMount() {
    this.getInitialLocationValue();
  }
  getCurrentPos=()=>{
    Geolocation.getCurrentPosition(
      async (position) => {
        this.setState({
          selectedLocation: await this.geoCoderAPI(
            position.coords.latitude,
            position.coords.longitude
          ),
        });
        let coords = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        };
        this.setState({
          region: coords,
        });
        this._map.animateCamera({ center: coords, zoom: 14 });
        // this._map.animateToCoordinate(coords, 100);
      },
      (error) => {
        // See error code charts below.
      },
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
    );
  };
  preDefinedLocation=async()=>{
    const { initialLocation } = this.props;
    this.setState({
      selectedLocation: await this.geoCoderAPI(
        initialCoords.latitude,
        initialCoords.longitude
      ),
    });
    if (initialLocation.length > 0) {
      this.setState({ selectedLocation: initialLocation }, () => {
        console.log("INtial ", this.state.selectedLocation);
      });
    }
  }
  getInitialLocationValue = async () => {
    if (Platform.OS ==='android') {
      checkAndRequestPermission(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION).then(async res => {
      if (res) {
        this.getCurrentPos();
      } else {
       this.preDefinedLocation();
      }
    })
    } else {
      const status = await Geolocation.requestAuthorization('whenInUse');
      if(status==='granted'){
      this.getCurrentPos();
      }
      else if(status==='denied'){
        this.preDefinedLocation();
      }else if(status==='disabled'){

      }
    }
  };

  geoCoderAPI = async (lat, long) => {
    /** Get location address based on latitude and longitude  */
    let geoCoderUrl = GEOCODER_API(lat, long, API_KEY);
    let geoCoderResponse = await axios.get(geoCoderUrl);
    let formattedAddress = geoCoderResponse.data.results[0].formatted_address;
    return formattedAddress;
  };

  getLocationAddress = async () => {
    const { region } = this.state;
    /**
     * GeoCoderAPI() function will return a address in a formmatted string
     * example -> "123, Graham street, volk road, london"
     */
    this.setState({
      selectedLocation: await this.geoCoderAPI(
        region.latitude,
        region.longitude
      ),
    });
  };

  onChangeRegion = async (events) => {
    /**
     * when user clicks a place in map, marker will be placed on selected location
     * lat & long of selected place will be returned
     * geocoderAPI will return address of the selected place based on lat & long
     */
    const { coordinate } = events.nativeEvent;
    this.setState({
      region: coordinate,
      selectedLocation: await this.geoCoderAPI(
        coordinate.latitude,
        coordinate.longitude
      ),
    });
  };

  onChangeLocationText = (text) => {
    /**
     * Allows user to edit the textField that already has a selected place string
     */
    this.setState({ selectedLocation: text });
  };

  getPlacePrediction = async (keyword) => {
    let showPlaces = keyword.toString().length > 0 ? true : false;
    let placeURL = googleAutoCompleteAPI(API_KEY, keyword);
    let placeResponse = await axios.get(placeURL);
    this.setState({
      showAuto: showPlaces,
      predictionList: placeResponse.data.predictions,
    });
  };

  onSelectLocation = async (palce) => {
    this.setState({ showAuto: false, selectedLocation: palce.description });
    let URL = geoCodeAdrressAPI(palce.description, API_KEY);
    let addressAPI = await axios.get(URL);
    let addressCoordinate = {
      latitude: addressAPI.data.results[0].geometry.location.lat,
      longitude: addressAPI.data.results[0].geometry.location.lng,
    };
    this._map.animateToCoordinate(addressCoordinate, 3000);
    this.setState({ region: addressCoordinate });
  };
  render() {
    const { region, selectedLocation } = this.state;
    const { backPress, pressContinue } = this.props;
    const { map, container } = styles;

    return (
      <SafeAreaView>
        <KeyboardAwareScrollView>
          <View style={container}>
            <MapHeader onBackPress={backPress} />

            <MapView
              ref={(ref) => (this._map = ref)}
              style={map}
              initialRegion={region}
              //region={region}
              provider={PROVIDER_GOOGLE}
              mapType="satellite"
              onPress={(e) => this.onChangeRegion(e)}
            >
              <Marker
                draggable
                coordinate={region}
                image={require("../../assets/images/map_marker.png")}
              />
            </MapView>
            {/* <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            //style={styles.container}
            > */}
            <BottomContainer
              pressContinue={() => pressContinue(selectedLocation)}
              textFieldValue={selectedLocation}
              onChangeText={(text) => {
                this.onChangeLocationText(text);
                this.getPlacePrediction(text);
              }}
              autoComplete={this.state.showAuto}
              predictionList={this.state.predictionList}
              onSelectPlace={this.onSelectLocation}
            />
          </View>
        </KeyboardAwareScrollView>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    height: hp("100%"),
    backgroundColor: Colors.white,
  },
  title: {
    fontSize: 18,
    fontFamily: Fonts.montserratSemiBold,
    color: Colors.black,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    height: hp("7.5%"),
    backgroundColor: "white",
    elevation: 10,
    marginTop: Platform.OS == "ios" ? hp("3%") : 0,
  },
  backButtonContainer: {
    padding: 6,
    marginLeft: wp("5%"),
  },
  subContainer: {
    width: wp("80%"),
    justifyContent: "center",
    alignItems: "center",
  },
  map: {
    //maxHeight: hp("40%"),
    minHeight: hp("40%"),
  },
  bottomContainer: {
    height: hp("25%"),
    alignItems: "center",
    justifyContent: "center",
    textAlign: "left",
    //elevation: 10,
    bottom: 5,
    backgroundColor: Colors.white,
    marginTop: wp("25%"),
    //marginBottom: wp("20%"),
  },
  pinText: {
    color: Colors.themeColor,
    fontSize: 15,
    fontFamily: Fonts.montserratSemiBold,
    marginLeft:hp('-2.5%'),
    marginBottom:hp('-1%')
  },
  locationText: {
    fontFamily: Fonts.montserratRegular,
  },
  locationTextView: {
    marginVertical: 2,
    paddingVertical: 3,
    borderColor: Colors.shadowColor,
    borderBottomWidth: 1,
    // backgroundColor:"red"
  },
  submit: {
    width: wp("80%"),
    height: hp("5%"),
    backgroundColor: Colors.themeOpacity,
    // marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
});
