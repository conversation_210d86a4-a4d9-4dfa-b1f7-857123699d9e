import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  TextInput,
  Keyboard,
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Images from "../../common/images";
import Modal from "react-native-modal";
import Colors from "../../common/color";
import Fonts from "../../common/fonts";

class multiSelectdropDown extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataItems: [],
      showPlacholder: true,
      isAllChecked: false,
      filterStoredItem:[],
      showDisabledNotification: false, // For showing notification when disabled option is clicked
    };
  }

  componentDidMount() {
    console.log("dataItems", this.props.dataItems);
    
    // Initialize all items with proper visibility and disabled states
    if (this.props.dataItems && this.props.dataItems.length > 0) {
      const dataWithStates = this.props.dataItems.map(item => {
        if (item && typeof item.id !== 'undefined') {
          return {
            ...item,
            visible: item.visible !== undefined ? item.visible : true,
            disabled: item.disabled !== undefined ? item.disabled : false
          };
        }
        return item;
      });
      
      this.setState({ 
        dataItems: dataWithStates,
        isAllChecked: false 
      });
    } else {
      this.setState({ isAllChecked: false });
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    // Safety check for dataItems
    if (!nextProps.dataItems || !Array.isArray(nextProps.dataItems)) {
      return;
    }

    // Check if all non-"No Equipment Needed" items are selected (for Select All state)
    const nonNoEquipmentItems = nextProps.dataItems.filter(item => item && item.id !== 0);
    const allOtherSelected = nonNoEquipmentItems.length > 0 && 
      nonNoEquipmentItems.every(item => item && item.selected === true);

    // Ensure all properties are set for all items
    const dataWithAllStates = nextProps.dataItems.map(item => {
      if (item && typeof item === 'object') {
        return {
          ...item,
          visible: item.visible !== undefined ? item.visible : true,
          disabled: item.disabled !== undefined ? item.disabled : false
        };
      }
      return item;
    });

    this.setState({
      dataItems: dataWithAllStates,
      isAllChecked: allOtherSelected,
    });
  }

  _renderSeparator = () => (
    <View
      style={[
        {
          flex: 1,
          height: StyleSheet.hairlineWidth,
          alignSelf: "stretch",
          backgroundColor: "#dadada",
        },
      ]}
    />
  );

  check = (item, index) => {
    return (
      <TouchableOpacity
        style={styles.checked}
        onPress={() => this.onTapCheck(item, index)}
      >
        <Text style={{ color: "white", fontSize: 14 }}>✓</Text>
      </TouchableOpacity>
    );
  };
  
  onTapCheck = async (item, index) => {
    let data = this.state.dataItems;
    let selectedIndex = data.findIndex(ele => ele && ele.id === item.id);
    
    // Safety check - ensure item exists and has valid properties
    if (selectedIndex === -1 || !data[selectedIndex] || !item) {
      return;
    }
    
    // Handle disabled items - show notification
    if (data[selectedIndex].disabled) {
      
      // Show notification for disabled item click
      this.setState({ showDisabledNotification: true });
      
      // Auto-hide notification after 3 seconds
      setTimeout(() => {
        this.setState({ showDisabledNotification: false });
      }, 3000);
      
      return; // Prevent further interaction
    }
    
    // Toggle the selected state of the clicked item
    data[selectedIndex].selected = !data[selectedIndex].selected;
    
    // Call the parent callback to handle mutual exclusion logic
    if (this.props.selectedDataItem) {
      this.props.selectedDataItem(data);
    }
  };

  unCheck = (item, index) => {
    return (
      <TouchableOpacity
        style={styles.unchecked}
        onPress={() => this.onTapCheck(item, index)}
      ></TouchableOpacity>
    );
  };

  _renderItemFlatList = ({ item, index }) => {
    // Safety check for item
    if (!item || typeof item !== 'object') {
      console.warn('Invalid item in _renderItemFlatList');
      return null;
    }

    // Don't render if item is not visible
    if (item.visible === false) {
      return null;
    }

    // Determine if item is disabled
    const isDisabled = item.disabled === true;

    return (
      <View key={index} style={[
        styles.flatListContainer,
        isDisabled && styles.disabledContainer
      ]}>
        {item.selected === true ? (
          <View>{this.check(item, index)}</View>
        ) : (
          <View>{this.unCheck(item, index)}</View>
        )}
        <Text style={[
          { fontSize: 14, marginLeft: 10 },
          isDisabled ? styles.disabledText : { color: Colors.black }
        ]}>
          {item.name}
        </Text>
      </View>
    );
  };

  toggleAllCheck = () => {
    const { isAllChecked, dataItems } = this.state;
    let data = [...dataItems]; // Create a copy to avoid mutation

    if (isAllChecked) {
      // RULE 2: When "Select All" is unselected
      // - Show all equipment options including "No Equipment Needed"
      // - Maintain previous individual selections (don't auto-deselect)
      data = data.map((obj) => ({
        ...obj,
        selected: false, // Reset all selections
        disabled: false,
        visible: true
      }));
    } else {
      // RULE 1: When "Select All" is chosen
      // - Select all equipment EXCEPT "No Equipment Needed"
      // - This will trigger the parent callback which will handle hiding "No Equipment Needed"
      data = data.map((obj) => ({
        ...obj,
        selected: obj.id !== 0, // Select all except "No Equipment Needed"
        disabled: false
      }));
    }

    // Update local state first
    this.setState({ 
      isAllChecked: !isAllChecked, 
      dataItems: data 
    }, () => {
      // Then call parent callback to apply mutual exclusion logic
      if (this.props.selectedDataItem) {
        this.props.selectedDataItem(data);
      }
    });
  };

  // 'ALL' button render
  renderCheckAll = () => {
    const { isAllChecked, dataItems } = this.state;

    // Don't show "Select All" if there are no items
    if (!dataItems.length) return null;

    // Count selectable items (excluding "No Equipment Needed")
    const selectableItems = dataItems.filter(item => item && item.id !== 0 && item.visible !== false);
    
    // Don't show "Select All" if there are no selectable items or only one selectable item
    if (selectableItems.length <= 1) return null;

    return (
      <View style={styles.flatListContainer}>
        {isAllChecked === true ? (
          <TouchableOpacity
            style={styles.checked}
            onPress={this.toggleAllCheck}
          >
            <Text style={{ color: "white", fontSize: 14 }}>✓</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={styles.unchecked}
            onPress={this.toggleAllCheck}
          ></TouchableOpacity>
        )}
        <Text style={{ fontSize: 14, color: Colors.black, marginLeft: 10 }}>
          {isAllChecked ? "Unselect All" : "Select All"}
        </Text>
      </View>
    );
  };

  renderHeader = () => {
    if (this.state.dataItems.length !== 0) {
      return (
        <View key={"header"} style={styles.flatListContainer}>
          {this.state.selectAll == true ? (
            <TouchableOpacity
              style={{
                backgroundColor: Colors.themeColor,
                marginLeft: 10,
                borderWidth: 2,
                borderColor: Colors.themeColor,
                justifyContent: "center",
                alignItems: "center",
                width: 30,
                height: 30,
                borderRadius: 4,
              }}
            >
              <Text style={{ color: "white", fontSize: 14 }}>✓</Text>
            </TouchableOpacity>
          ) : (
            // <View>{this.check(item, index)}</View>
            <TouchableOpacity
              style={{
                backgroundColor: Colors.white,
                marginLeft: 10,
                borderWidth: 2,
                borderColor: Colors.themeColor,
                justifyContent: "center",
                alignItems: "center",
                width: 30,
                height: 30,
                borderRadius: 4,
              }}
            ></TouchableOpacity>
            // <View>{this.unCheck(item, index)}</View>
          )}
          <Text style={{ fontSize: 14, color: Colors.black, marginLeft: 10 }}>
            {"Select All"}
          </Text>
        </View>
      );
    } else {
      return null;
    }
  };

  onSubmit = () => {
    this.setState({
      showModal: false,
      isAllChecked: false,
    });
    Keyboard.dismiss()

    // Call the callback with the updated data
    if (this.props.selectedDataItem) {
      // Return full list to avoid accidentally dropping "No Equipment Needed"
      this.props.selectedDataItem(this.state.dataItems);
    }
  };

  renderChips = () => {
    return (
      <View style={styles.renderContainer}>
        {this.state.dataItems &&
          this.state.dataItems.map((item, index) => {
            if (item.selected == true) {
              return (
                <View style={[styles.chip, this.props.disabled && styles.disabledContainer]} key={index.toString()}>
                  <Text
                    numberOfLines={1}
                    style={[
                      {
                        fontFamily: Fonts.montserratRegular,
                        color: Colors.white,
                        marginLeft: 10,
                        marginRight: 10,
                      },
                      this.props.disabled && styles.disabledText
                    ]}
                  >
                    {item.name}
                  </Text>
                  {!this.props.disabled && (
                    <Text
                      style={{ fontSize: 16, color: Colors.white }}
                      onPress={() => {
                        let data = [...this.state.dataItems];
                        
                        // Find and deselect the item
                        const itemToRemove = data[index];
                        if (itemToRemove) {
                          data[index] = {
                            ...itemToRemove,
                            selected: false
                          };
                          
                          // Update local state and call parent callback
                          this.setState(
                            { dataItems: data },
                            () => {
                              if (this.props.selectedDataItem) {
                                this.props.selectedDataItem(data);
                              }
                            }
                          );
                        }
                      }}
                    >
                      ⨉
                    </Text>
                  )}
                </View>
              );
            } else {
              return null;
            }
          })}
      </View>
    );
  };

  handleSearchTextChange = (value) =>{
    this.setState({ searchTerm:value })
    if (value) {
      const searchedDetails = this.state.dataItems.filter(dataItems => {
          let userName = dataItems.name;
         if (!userName) return false;
          const userNameLowerCase = userName.toLowerCase();
          const searchTextLowerCase = value.toLowerCase();
  
          if (userNameLowerCase.indexOf(searchTextLowerCase) >= 0) {
              return true;
          }
          return false;
      });
      this.setState({ filterStoredItem: searchedDetails})
  } 
  }

  render() {
    const {filterStoredItem,dataItems,searchTerm } = this.state
    return (
      <View style={styles.mainContainer}>
        {this.renderChips(this.state.dataItems)}
        <TouchableOpacity
          style={[styles.dropDownButton, this.props.disabled && styles.disabledContainer]}
          onPress={() => {
            if (this.props.disabled) return;
            Keyboard.dismiss()
            this.setState({ showModal: true });
          }}
          disabled={this.props.disabled} >
          {
            this.state.dataItems?.filter(item => item.selected == true)?.length <= 0 && (
              <Text style={[
                { color: Colors.placeholder, marginStart:10},
                this.props.disabled && styles.disabledText
              ]}>
                  {this.props.disabled ? 'Disabled' : 'Select'}
                </Text>
              )
          }
          <Image
            source={Images.downArr}
            style={{ position: "absolute", right: 10, bottom: 10 }}
          />
        </TouchableOpacity>
        <Modal
          isVisible={this.state.showModal}
          backdropOpacity={0.0}
           transparent={true}
          backdropColor={Colors.black}
          style={styles.modalStyle}
          //DO FOR LATER
          // onBackdropPress={() => {
          //   this.setState({
          //     showModal: false,
          //   });
          // }}
          >
          <View
            style={[
              {
                overflow: "hidden",
                marginHorizontal: 18,
                marginVertical: 26,
                borderRadius: 6,
                alignSelf: "stretch",
                backgroundColor: "white",
                flex: 0.8,
              },
              styles.container,
            ]}
          >
            {this.renderCheckAll()}
            
            {/* Disabled Option Notification */}
            {this.state.showDisabledNotification && (
              <View style={styles.disabledNotificationContainer}>
                <View style={styles.disabledNotificationBox}>
                  <Text style={styles.disabledNotificationText}>
                    Please deselect "No Equipment Needed" first to choose other options
                  </Text>
                </View>
              </View>
            )}
            
            <View
              style={[
                { flexDirection: "row", paddingVertical: 5 },
                styles.searchBar,
              ]}
            >
              <View style={styles.center}>
                <Image source={Images.search} />
              </View>
              <TextInput
                value={this.state.searchTerm}
                // selectionColor={colors.searchSelectionColor}
                onChangeText={(searchTerm) => this.handleSearchTextChange(searchTerm)}
                placeholder={"search"}
                autoFocus={false}
                selectTextOnFocus
                placeholderTextColor={"black"}
                underlineColorAndroid="transparent"
                style={[
                  {
                    flex: 1,
                    fontSize: 17,
                    paddingVertical: 8,
                  },
                  styles.searchTextInput,
                ]}
              />
            </View>

            <FlatList
              keyboardShouldPersistTaps="always"
              removeClippedSubviews
              initialNumToRender={15}
              data={!searchTerm == '' ? filterStoredItem : dataItems}
              extraData={this.state}
              keyExtractor={(item, index) => `${index}`}
              ItemSeparatorComponent={this._renderSeparator}
              renderItem={this._renderItemFlatList}
            />
            <TouchableOpacity
              style={{
                width: "100%",
                backgroundColor: Colors.themeColor,
                height: 40,
                justifyContent: "center",
                alignItems: "center",
              }}
              onPress={() => {
                this.onSubmit();
              }}
            >
              <Text
                style={{
                  fontSize: 16,
                  color: "white",
                  fontFamily: Fonts.montserratBold,
                }}
              >
                Submit
              </Text>
            </TouchableOpacity>
          </View>
        </Modal>
      </View>
    );
  }
}

export default multiSelectdropDown;

const styles = StyleSheet.create({
  mainContainer: {
    width: wp("90%"),
    minHeight: hp("3%"),
    alignSelf: "center",
    borderBottomWidth: 1,
    borderBottomColor: Colors.placeholder,
    marginTop: 5
  },
  dropDownButton: {
    width: wp("90%"),
    minHeight: hp("3%"),
    alignSelf: "center",
    justifyContent: "center",
  },
  searchBar: {
    backgroundColor: Colors.white,
    flexDirection: "row",
  },
  center: {
    alignItems: "center",
    justifyContent: "center",
  },
  flatListContainer: {
    height: 35,
    width: wp("90%"),
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 4,
  },
  chip: {
    minWidth: 10,
    margin: 3,
    backgroundColor: Colors.themeColor,
    padding: 5,
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 4,
  },
  checked: {
    backgroundColor: Colors.themeColor,
    marginLeft: 10,
    borderWidth: 2,
    borderColor: Colors.themeColor,
    justifyContent: "center",
    alignItems: "center",
    width: 30,
    height: 30,
    borderRadius: 4,
  },
  unchecked: {
    backgroundColor: Colors.white,
    marginLeft: 10,
    borderWidth: 2,
    borderColor: Colors.themeColor,
    justifyContent: "center",
    alignItems: "center",
    width: 30,
    height: 30,
    borderRadius: 4,
  },
  renderContainer:{
    flexDirection: "row", 
    flexWrap: "wrap"
  },
  modalStyle:{
    paddingTop: 45,
    paddingBottom: 30,
    margin: 0,
    backgroundColor: Colors.multiSelectCard,
    justifyContent: "center",
    alignItems: "center",
  },
  disabledContainer: {
    opacity: 0.5,
  },
  disabledText: {
    color: Colors.placeholder,
    fontStyle: 'italic',
  },
  disabledNotificationContainer: {
    marginHorizontal: 10,
    marginVertical: 5,
  },
  disabledNotificationBox: {
    backgroundColor: '#FFE5E5', // Light red background
    borderRadius: 6,
    padding: 12,
    borderLeftWidth: 3,
    borderLeftColor: '#FF6B6B', // Red accent
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 3,
  },
  disabledNotificationText: {
    fontSize: 13,
    color: '#D63031', // Dark red text
    fontFamily: Fonts.montserratMedium,
    textAlign: 'center',
  }
});