import React from 'react';
import {StyleSheet, Text, TouchableOpacity} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Colors from '../../common/color';
import Strings from '../../common/string';
import Fonts from '../../common/fonts';

export default function NextButton(props) {
  return (
    <TouchableOpacity onPress={props.nextClick} style={[styles.container, props.containerStyles]}>
        <Text style={[styles.text, props.textStyles]}>
          {props.title ? props.title : Strings.step1.next}
        </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    width: wp('25%'),
    height: hp('5.2%'),
    borderRadius: hp('3%'),
    backgroundColor: Colors.themeColor,
    borderColor: Colors.themeColor,
    borderWidth: Platform.OS == 'ios' ? 1 : 0,
    shadowOffset: {width: 5, height: 7},
    shadowColor: Colors.themeOpacity,
    shadowOpacity: 1,
    elevation: 3,
    alignSelf: 'flex-end',
    marginRight: wp('5%'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    color: Colors.white,
    fontSize: 16,
    fontFamily: Fonts.montserratMedium,
  },
});
