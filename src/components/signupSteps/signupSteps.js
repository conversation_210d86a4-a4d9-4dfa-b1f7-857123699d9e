import React from 'react';
import {StyleSheet, View, Text} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Strings from '../../common/string';
import Colors from '../../common/color';
import Fonts from '../../common/fonts';

export default function Steps(props) {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>
        {`${Strings.step1.step}`}
        <Text style={{color: Colors.themeColor}}>{`${" "}${props.page}`}
        <Text style={styles.text}>{Strings.step1.final}</Text>
        </Text>
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: wp('40%'),
    flexDirection: 'row',
    marginBottom: hp('4%'),
    marginLeft: wp('4.5%')
  },
  text: {
      color: '#707070',
      fontSize: 12,
      fontFamily: Fonts.montserratMedium
  }
});
