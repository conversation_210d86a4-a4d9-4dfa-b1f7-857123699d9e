import React, { Component } from "react";
import { View, StyleSheet, Image } from "react-native";


class TabImage extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    return (
      <View>
        {/* {this.props.indexValue == 3 &&
          parseInt(this.props.notificationCount) > 0 && (
            <View
              style={{
                backgroundColor: Colors.themeColor,
                justifyContent: "center",
                alignItems: "center",
                width: 22,
                height: 22,
                borderRadius: 11,
              }}
            >
              <Text style={{ color: "#fff", fontSize: 10 }}>
                {parseInt(this.props.notificationCount) > 99
                  ? "99+"
                  : parseInt(this.props.notificationCount)}
              </Text>
            </View>
          )} */}
        <Image
          source={this.props.imageSource}
          style={ this.props.container}
        />
      </View>
    );
  }
}


export default TabImage;

const styles = StyleSheet.create({
  parentContainer: {
     flex: 1,
    backgroundColor: "rgba(245, 245, 245, 1)",
    overflow: "hidden",
    marginBottom:5,
  },
});
