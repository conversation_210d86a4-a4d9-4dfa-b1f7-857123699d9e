import React, {Component} from 'react';
import {View, Animated, StyleSheet, TextInput, Image, TouchableOpacity, Text, Platform} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Fonts from '../../common/fonts';
import {Bar} from 'react-native-progress';
import Colors from '../../common/color';

export class MobilenumberInput extends Component {
  constructor(props) {
    super(props);
    const {value} = this.props;
    this.position = new Animated.Value(value ? 1 : 0);
    this.state = {
      isFieldActive: false
    };
  }

  _handleFocus = () => {
    if (!this.state.isFieldActive) {
      this.setState({isFieldActive: true});
      Animated.timing(this.position, {
        toValue: 1,
        duration: 150,
      }).start();
    }
  };

  _handleBlur = () => {
    if (this.state.isFieldActive && !this.props.value) {
      this.setState({isFieldActive: false});
      Animated.timing(this.position, {
        toValue: 0,
        duration: 150,
      }).start();
    }
  };

  _onChangeText = (updatedValue) => {
    const {attrName, updateMasterState} = this.props;
    updateMasterState(attrName, updatedValue);
  };

  _returnAnimatedTitleStyles = () => {
    return {
      top: hp('4%'),
      fontSize: wp('3.5%'),
      color: Colors.themeColor,
      fontFamily: Fonts.montserratBold
    };
  };

  render() {
    return (
      <View style={[Styles.container,this.props.container]}>

        <Animated.Text
          style={[Styles.titleStyles, { top: hp('0%'),
          fontSize: wp('3.5%'),
          color: Colors.themeColor,
          fontFamily: Fonts.montserratBold}]}>
          {this.props.title}
        </Animated.Text>

        <View style={{width: wp('90%'), flex: 1, flexDirection: 'row'}}>

            <TouchableOpacity 
            onPress={this.props.onPresscountry}
            style={{width: wp('15%'), height: hp('5%'), marginTop: 10, flexDirection: 'row', justifyContent: 'center', alignItems:'flex-end'}}
            >
                <Text style={{color: 'black', fontSize: wp('4.5%'), fontFamily: Fonts.montserratRegular}}>{this.props.countryCode}</Text>
                <Image source={this.props.imageSource} resizeMode={'contain'} style={{marginLeft: wp('2%'), marginBottom: Platform.OS == 'ios' ? hp('0.7%') : hp('1%')}}/>
            </TouchableOpacity>

        <TextInput
          value={this.props.value}
          style={[Styles.textInput,{width: '70%', marginTop: Platform.OS == 'ios' ? 25 : 10, left: 3, alignSelf: 'center', paddingBottom: 0} , this.props.textInputStyles]}
          underlineColorAndroid={'#0000'}
          onFocus={this._handleFocus}
          onBlur={this._handleBlur}
          onChangeText={this._onChangeText}
          editable={this.props.showButton == true ? false : true}
          onSubmitEditing={this.props.onSubmitEditing}
          keyboardType={this.props.keyboardType}
          placeholderTextColor={Colors.placeholder}
          {...this.props.otherTextInputProps}
          {...this.props}
        />
        
        </View>

        <Bar
          progress={this.state.isFieldActive ? 1 : 0}
          style={{marginBottom: hp('1%')}}
          width={this.props.progressWidth ? this.props.progressWidth : wp('90%')}
          height={1}
          useNativeDriver={true}
          borderColor={Colors.white}
          unfilledColor={Colors.placeholder}
          color={Colors.themeColor}
        />

      </View>
    );
  }
}

const Styles = StyleSheet.create({
  container: {
    width: wp('90%'),
    borderRadius: 3,
    borderStyle: 'solid',
    height: hp('8%'),
    alignSelf: 'center',
    marginBottom: hp('1.5%'),
    marginTop: hp('1.5%'),
    justifyContent: 'flex-end',
  },
  textInput: {
    fontSize: wp('4.5%'),
    marginTop: 10,
    fontFamily: Fonts.montserratRegular,
    color: 'black',
    height: hp('5%'),
    textAlignVertical: 'bottom',
    paddingTop: 0, 
    paddingBottom:0,
    // justifyContent: "flex-end",
  },
  titleStyles: {
    position: 'absolute',
    fontFamily: Fonts.montserratRegular,
    left: 3
  },
  buttonContainer: {
    height: hp('7%'),
    width: wp('90%'),
    justifyContent: 'center',
    alignItems: 'flex-end'
  }
});
