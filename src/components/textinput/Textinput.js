import React, { Component } from "react";
import {
  View,
  Animated,
  StyleSheet,
  TextInput,
  Image,
  TouchableWithoutFeedback,
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Fonts from "../../common/fonts";
import { Bar } from "react-native-progress";
import Colors from "../../common/color";

export class TextField extends Component {
  constructor(props) {
    super(props);
    const { value } = this.props;
    this.position = new Animated.Value(value ? 1 : 0);
    this.state = {
      isFieldActive: false,
      textKey:1
    };
  }

  _handleFocus = () => {
    if (!this.state.isFieldActive) {
      this.setState({ isFieldActive: true });
      Animated.timing(this.position, {
        toValue: 1,
        duration: 150,
      }).start();
    }
  };

  _handleBlur = () => {
    if (this.state.isFieldActive && !this.props.value) {
      this.setState({ isFieldActive: false });
      Animated.timing(this.position, {
        toValue: 0,
        duration: 150,
      }).start();
    }
  };

  _onChangeText = (updatedValue) => {
    const { attrName, updateMasterState } = this.props;
    updateMasterState(attrName, updatedValue);
  };

  _returnAnimatedTitleStyles = () => {
    const { isFieldActive } = this.state;
    const { titleInActiveSize } = this.props;

    return {
      top: this.position.interpolate({
        inputRange: [0, 1],
        outputRange: [hp("4%"), 0],
      }),
      fontSize: isFieldActive ? wp("3.5%") : titleInActiveSize,
      color: isFieldActive ? Colors.themeColor : Colors.placeholder,
      fontFamily: isFieldActive
        ? Fonts.montserratBold
        : Fonts.montserratRegular,
    };
  };

  onPress = () => {
    this.setState({
      isFieldActive: true,
      placeholder: this.props.title,
    });
    Animated.timing(this.position, {
      toValue: 1,
      duration: 150,
    }).start();
    this.props.onPress();
  };

  onHideImagePress=()=>{
    this.setState({textKey:this.state.textKey+1})
    this.props.onPressHideImage()
  };
  render() {
    return (
      <View style={[Styles.container, this.props.container]}>
        <Animated.Text
          style={[
            Styles.titleStyles,
            this._returnAnimatedTitleStyles(),
            this.props.textTitleStyles,
          ]}
        >
          {this.props.title}
        </Animated.Text>

        <View>
          {this.props.showLeft == true && (
            <View
              style={[
                {
                  justifyContent: "flex-end",
                  alignItems: "flex-end",
                  marginTop: hp("2.1%"),
                  position: "absolute",
                },
                this.props.leftButton,
              ]}
            >
              {/* <TouchableWithoutFeedback onPress={this.props.onPressHideImage}> */}
              <Image
                source={this.props.leftImage}
                resizeMode={"contain"}
                style={{ width: wp("10%"), height: hp("3%") }}
              />
              {/* </TouchableWithoutFeedback> */}
            </View>
          )}
          <TextInput
            value={this.props.value}
            style={[
              Styles.textInput,
              { width: "90%", left: 3 },
              this.props.textInputStyles,
            ]}
            underlineColorAndroid={"#0000"}
            onFocus={this._handleFocus}
            onBlur={this._handleBlur}
            onChangeText={this._onChangeText}
            editable={this.props.showButton == true ? false : true}
            onSubmitEditing={this.props.onSubmitEditing}
            keyboardType={this.props.keyboardType}
            placeholderTextColor={Colors.placeholder}
            placeholder={this.state.placeholder}
            multiline={this.props.multiline}
            onContentSizeChange={() => {
              if(this.props.value!=undefined){
              if (this.props.value.length) this._handleFocus();
              }
            }}
            key={this.state.textKey}
            {...this.props.otherTextInputProps}
            {...this.props}
          />
          {this.props.hideShow == true && (
            <View
              style={{
                justifyContent: "flex-end",
                alignItems: "flex-end",
                marginTop: hp("2.1%"),
                alignSelf: "flex-end",
                position: "absolute",
              }}
            >
              <TouchableWithoutFeedback onPress={this.onHideImagePress}>
                <Image
                  source={this.props.hideImage}
                  resizeMode={"contain"}
                  style={{ width: wp("11%"), height: hp("3.5%") }}
                />
              </TouchableWithoutFeedback>
            </View>
          )}
           {this.props.showRight == true && (
            <View
              style={{position: 'absolute', right: 0, top: 20}}
            >
              <Image
                source={this.props.rightImage}
                resizeMode={"contain"}
                style={[{ width: wp("10%"),  }, this.props.rightImageStyle]}
              />
            </View>
          )}
          {this.props.showButton == true && (
            <View style={{ position: "absolute" }}>
              <TouchableWithoutFeedback
                onPress={() => {
                  this.onPress();
                }}
              >
                <View style={Styles.buttonContainer}>
                  <Image
                    source={this.props.imageSource}
                    style={{ marginRight: wp("3.5%") }}
                  />
                </View>
              </TouchableWithoutFeedback>
            </View>
          )}
        </View>
        <Bar
          progress={this.state.isFieldActive ? 1 : 0}
          style={{ marginBottom: hp("1%") }}
          width={
            this.props.progressWidth ? this.props.progressWidth : wp("90%")
          }
          height={1}
          useNativeDriver={true}
          borderColor={Colors.white}
          unfilledColor={Colors.placeholder}
          color={Colors.themeColor}
        />
      </View>
    );
  }
}

const Styles = StyleSheet.create({
  container: {
    width: wp("90%"),
    borderRadius: 3,
    borderStyle: "solid",
    height: hp("8%"),
    alignSelf: "center",
    marginBottom: hp("1.5%"),
    marginTop: hp("1.5%"),
    justifyContent: "flex-end",
  },
  textInput: {
    fontSize: wp("4.5%"),
    marginTop: 5,
    fontFamily: Fonts.montserratRegular,
    color: "black",
    height: hp("5%"),
    textAlignVertical: "bottom",
    paddingTop: 0,
    paddingBottom: 0,
    // justifyContent: "flex-end",
  },
  titleStyles: {
    position: "absolute",
    fontFamily: Fonts.montserratRegular,
    left: 3,
  },
  buttonContainer: {
    height: hp("7%"),
    width: wp("90%"),
    justifyContent: "center",
    alignItems: "flex-end",
  },
});
