import React, { Component } from "react";
import {
  View,
  Animated,
  StyleSheet,
  TextInput,
  Image,
  Text,
  TouchableWithoutFeedback,
} from "react-native";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Fonts from "../../common/fonts";
import { Bar } from "react-native-progress";
import Colors from "../../common/color";

export class TextField extends Component {
  constructor(props) {
    super(props);
    const { value } = this.props;
    this.position = new Animated.Value(value ? 1 : 0);
    this.state = {
      isFieldActive: false,
    };
  }

  _handleFocus = () => {
    if (!this.state.isFieldActive) {
      this.setState({ isFieldActive: true });
   
    }
  };

  _handleBlur = () => {
    if (this.state.isFieldActive && !this.props.value) {
      this.setState({ isFieldActive: false });

    }
  };

  _onChangeText = (updatedValue) => {
    const { attrName, updateMasterState } = this.props;
    updateMasterState(attrName, updatedValue);
  };

  _returnAnimatedTitleStyles = () => {
    return {
      top: 0,
      //   fontSize: isFieldActive  ? wp('3.5%') : titleInActiveSize,
      color: this.props.textTitleStyles? Colors.descriptionField:Colors.placeholder,
      fontFamily:this.props.textTitleStyles? Fonts.montserratBold: Fonts.montserratMedium,
    };
  };

  onPress = () => {
    this.setState({
      isFieldActive: true,
      placeholder: this.props.title,
    });
    Animated.timing(this.position, {
      toValue: 1,
      duration: 150,
    }).start();
    this.props.onPress();
  };

  render() {
    return (
      <View style={[Styles.container, this.props.container]}>
        <Text style={[Styles.titleStyles,this.props.textTitleStyles, this._returnAnimatedTitleStyles()]}>
          {this.props.title}
          <Text style={{ color: Colors.themeColor }}>
            {this.props.mandatory ? "*" : ""}
          </Text>
        </Text>

        <View>
          <TextInput
            value={this.props.value}
            style={[
              Styles.textInput,
              { width: "90%", left: 0 },
              this.props.textInputStyles,
            ]}
            underlineColorAndroid={"#0000"}
            onFocus={this._handleFocus}
            onBlur={this._handleBlur}
            onChangeText={this._onChangeText}
            editable={this.props.showButton == true ? false : true}
            onSubmitEditing={this.props.onSubmitEditing}
            keyboardType={this.props.keyboardType}
            placeholderTextColor={Colors.placeholder}
            placeholder={this.props.placeholder ? this.props.placeholder : "Enter here..."}
            {...this.props.otherTextInputProps}
            {...this.props}
          />
          {this.props.hideShow == true && (
            <View
              style={{
                justifyContent: "flex-end",
                alignItems: "flex-end",
                marginTop: hp("2.1%"),
                alignSelf: "flex-end",
                position: "absolute",
              }}
            >
              <TouchableWithoutFeedback onPress={this.props.onPressHideImage}>
                <Image
                  source={this.props.hideImage}
                  resizeMode={"contain"}
                  style={{ width: wp("12%"), height: hp("4%") }}
                />
              </TouchableWithoutFeedback>
            </View>
          )}
          {this.props.showButton == true && (
            <View style={{ position: "absolute" }}>
              <TouchableWithoutFeedback
                onPress={() => {
                  this.onPress();
                }}
              >
                <View
                  style={[Styles.buttonContainer, this.props.buttonContainer]}
                >
                  <Image
                    source={this.props.imageSource}
                    style={{ marginRight: wp("3.5%") }}
                  />
                </View>
              </TouchableWithoutFeedback>
            </View>
          )}
        </View>
        <Bar
          progress={this.state.isFieldActive ? 1 : 0}
          style={{ marginBottom: hp("1%") }}
          width={
            this.props.progressWidth ? this.props.progressWidth : wp("90%")
          }
          height={1}
          useNativeDriver={true}
          borderColor={Colors.white}
          unfilledColor={Colors.placeholder}
          color={Colors.themeColor}
        />
      </View>
    );
  }
}

const Styles = StyleSheet.create({
  container: {
    width: wp("90%"),
    borderRadius: 3,
    borderStyle: "solid",
    // height: hp("8%"),
    alignSelf: "center",
    marginBottom: hp("1.5%"),
    marginTop: hp("2%"),
    justifyContent: "flex-end",
  },
  textInput: {
    fontSize: wp("4.5%"),
    marginTop: 5,
    fontFamily: Fonts.montserratRegular,
    color: "black",
    // height: hp("5%"),
    minHeight: hp("5%"),
    textAlignVertical: "bottom",
    paddingTop: 10,
    paddingBottom: 0,
    paddingLeft: 10,
    // justifyContent: "flex-end",
  },
  titleStyles: {
    // position: "absolute",
    fontFamily: Fonts.montserratRegular,
    fontSize: wp("4%"),
    left: 3,
  },
  buttonContainer: {
    height: hp("7%"),
    width: wp("90%"),
    justifyContent: "center",
    alignItems: "flex-end",
  },
});
