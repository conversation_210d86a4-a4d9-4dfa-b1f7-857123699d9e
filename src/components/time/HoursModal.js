import { Text, View,  FlatList, TouchableOpacity, StyleSheet} from 'react-native'
import React from 'react'
import { CommonStyles, Dimensions } from '../../common'

const HoursModal = ({Clear,onSelected, data, isHours}) => {
    return (
      <View style={CommonStyles.hoursModalContainer}>
        <View style={CommonStyles.rowContainer}>
          <Text style={[styles.headerText,CommonStyles.mediumHeaderText]}>Select {isHours ? 'Hours' : 'Mins'}</Text>
          <TouchableOpacity onPress={()=>Clear()}>
            <Text  style={{alignSelf: 'flex-end', marginBottom: Dimensions.scaleWidth(3), marginRight: Dimensions.scaleWidth(2)}} >Clear</Text>
             {/* <Image style={{alignSelf: 'flex-end', marginBottom: Dimensions.scaleWidth(3), marginRight: Dimensions.scaleWidth(2)}} source={Images.closeBlack} />  */}
          </TouchableOpacity>
        </View>
        <FlatList 
          ref={(ref) => this.fListRef = ref}
          data={data }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{marginBottom: Dimensions.scaleHeight(5)}}
          renderItem={({item, index}) => {
            return(
            <TouchableOpacity onPress={() => onSelected(item)} style={[CommonStyles.textContainer,{height:50}]}>
              <Text style={[CommonStyles.inputText,{fontSize: Dimensions.scaleFont(2)}]}>{item}</Text>
            </TouchableOpacity>
          )}
          }
          keyExtractor={(item,index) => index.toString()}/>
          
         {/* <Text style={[drStyles.headerText,CommonStyles.mediumHeaderText]}>Done</Text> */}
      </View>
    )
}

const styles = StyleSheet.create({
  headerText: {
    // width: Dimensions.scaleWidth(10),
    flex: 1,
    height: 30,
    textAlign: 'center',
  },
})

export default HoursModal;