// @flow
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    Dimensions,
    StyleSheet,
    FlatList,
  } from "react-native";
  import PropTypes from "prop-types";
  import populateEvents from "./Packer";
  import React from "react";
  import moment from "moment";
  import _ from "lodash";
  import styleConstructor from "./style";
  import { widthPercentageToDP as wp, heightPercentageToDP as hp } from "react-native-responsive-screen";
import { Colors, Fonts, Strings } from "../../common";
  
  const LEFT_MARGIN = 60 - 1;
  const TEXT_LINE_HEIGHT = 10;
  
  function range(from, to) {
    return Array.from(Array(to), (_, i) => from + i);
  }
  
  let { width: dimensionWidth } = Dimensions.get("window");
  
  export default class TimelineCalendar extends React.PureComponent {
    static propTypes = {
      start: PropTypes.number,
      end: PropTypes.number,
      eventTapped: PropTypes.func,
      format24h: PropTypes.bool,
      events: PropTypes.arrayOf(
        PropTypes.shape({
          start: PropTypes.string.isRequired,
          end: PropTypes.string.isRequired,
          title: PropTypes.string.isRequired,
          summary: PropTypes.string.isRequired,
          color: PropTypes.string,
        })
      ).isRequired,
    };
  
    static defaultProps = {
      start: 0,
      end: 24,
      events: [],
      format24h: true,
    };
  
    constructor(props) {
      super(props);
      const { start, end } = this.props;
      this.calendarHeight = (end - start) * 100;
      this.styles = styleConstructor(props.styles, this.calendarHeight);
      const width = dimensionWidth - LEFT_MARGIN;
      const packedEvents = populateEvents(props.events, width, start);

      let initPosition =
        _.min(_.map(packedEvents, "top")) - this.calendarHeight / (end - start);
      const verifiedInitPosition = initPosition < 0 ? 0 : initPosition;
      this.state = {
        _scrollY: verifiedInitPosition,
        packedEvents:populateEvents(props.events, width, start),
        scrollHeight: 0,
        //allDay,
      };
    }
  
    componentDidUpdate(prevProps) {
      const width = dimensionWidth - LEFT_MARGIN;
      const { events: prevEvents, start: prevStart = 0 } = prevProps;
      const { events, start = 0 } = this.props;
      if (prevEvents !== events || prevStart !== start) {
        this.setState({
          packedEvents: populateEvents(events, width, start),
        });
      }
    }
  
    componentDidMount() {
      this.props.scrollToFirst && this.scrollToFirst();
    }
  
    scrollToFirst() {
      setTimeout(() => {
        if (this.state && this.state._scrollY && this._scrollView) {
          this._scrollView.scrollTo({
            x: 0,
            y: this.state._scrollY,
            animated: true,
          });
        }
      }, 1);
    }
  
    _renderLines() {
      const { format24h, start = 7, end = 24 } = this.props;
      const offset = this.calendarHeight / (end - start);
  
      const EVENT_DIFF = 20;
      return range(start, end + 1).map((i, index) => {
        let timeText;
  
        if (i === start) {
          timeText = "";
        } else if (i < 12) {
          timeText = !format24h ? `${i}:00 AM` : `${i}:00`;
        } else if (i === 12) {
          timeText = !format24h ? `12:00 PM` : `${i}:00`;
        } else if (i === 24) {
          timeText = !format24h ? `12:00 AM` : `23:59`;
        } else {
          timeText = !format24h ? `${i - 12}:00 PM` : `${i}:00`;
        }
        // alert(timeText)
        return [
          <Text
            key={`timeLabel${i}`}
            style={[this.styles.timeLabel, { top: offset * index - 6 }]}
          >
            {timeText}
          </Text>,
          i === start ? null : (
            <View
              key={`line${i}`}
              style={[
                this.styles.line,
                { top: offset * index, width: dimensionWidth - EVENT_DIFF,marginLeft:20 },
              ]}
            />
          ),
          <View
            key={`lineHalf${i}`}
            style={[
              this.styles.line,
              { top: offset * (index + 0.5), width: dimensionWidth - EVENT_DIFF,marginLeft:20 },
            ]}
          />,
        ];
      });
    }
  
    _onEventTapped(event) {
      if (this.props.eventTapped) {
        this.props.eventTapped(event);
      }
    }
  
    _renderEvents() {
      const { packedEvents } = this.state;
      let events = packedEvents.map((event, i) => {
        if(!event.isAllDay){
        const style = {
          left: event.left,
          height: event.height,
          width: event.width,
          top: event.top,
          backgroundColor: event.color ? event.color : "#B2F3BC",
        };
  
        // Fixing the number of lines for the event title makes this calculation easier.
        // However it would make sense to overflow the title to a new line if needed
        const numberOfLines = Math.floor(event.height / TEXT_LINE_HEIGHT);
        const formatTime = this.props.format24h ? "HH:mm" : "hh:mm A";
       
        return (
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => 
              // this._onEventTapped(this.props.events[event.index])
              this._onEventTapped(event)
            }
            key={i}
            style={[this.styles.event, style,{opacity:1}]}
          >
            {this.props.renderEvent ? (
              this.props.renderEvent(event)
            ) : (
              <View>
                <View style={{ flexDirection: "row" }}>
                  <Text
                    numberOfLines={1}
                    style={{
                      color: Colors.calendarSettingsText,
                      // fontWeight: '600',
                      minHeight: 15,
                      fontSize: event.height < 50 ? wp("3%") : wp("4%"),
                      fontFamily: "Montserrat-semibold",
                      marginHorizontal: event.height < 50 ? 1 : 5,
                      width: event.width - 60,
                    }}
                  >
                    {event.title || "Event"}
                  </Text>
                  {/* <Image
                    source={Images.calendar_edit}
                    style={{
                      width: event.height < 50 ? wp("3%") : wp("4%"),
                      height: event.height < 50 ? wp("3%") : wp("4%"),
                      marginTop: event.height < 50 ? 1 : 5,
                    }}
                  ></Image> */}
                </View>
                {numberOfLines > 2 ? (
                  <Text style={this.styles.eventTimes} numberOfLines={1}>
                    {moment(event.start).format(formatTime)} -{" "}
                    {moment(event.end).format(formatTime)}
                  </Text>
                ) : null}
                {numberOfLines > 1 ? (
                  <Text
                    numberOfLines={numberOfLines - 1}
                    style={[this.styles.eventSummary]}
                  >
                    {event.summary || " "}
                  </Text>
                ) : null}
              </View>
            )}
          </TouchableOpacity>
        );
      }
      });
  
      return (
        <View>
          <View style={{ marginLeft: LEFT_MARGIN }}>{events}</View>
        </View>
      );
    }
    renderAllDay=({item,index})=>{
        return(
            <View style={Styles.flatListContainer}>
                <TouchableOpacity style={Styles.viewContainer}  onPress={() => this._onEventTapped(item)}>
                <Text style={Styles.FlatListTitle}>{item.title}</Text>
                </TouchableOpacity>
            </View>
        )
    }
  
    render() {
      if (this.state.scrollHeight != 0) {
        let currentTiming = new Date().getHours();
        this._scrollView.scrollTo({
          y: (this.state.scrollHeight / 24) * currentTiming - 10,
          animated: false,
        });
      }
      return (
          <>
          <View style={Styles.allDayContainer}>
            <View style={Styles.allDayTextContainer}>
                <Text style={Styles.allDay}>{Strings.timeLine.allDay}</Text>
            </View>
            <FlatList
            data={this.state.packedEvents?.filter((x) => x.isAllDay)}
            renderItem={this.renderAllDay}
            extraData={this.state}
            />

          </View>
        <ScrollView
          ref={(ref) => (this._scrollView = ref)}
          contentContainerStyle={[
            this.styles.contentStyle,
            { width: dimensionWidth },
          ]}
          onContentSizeChange={(contentWidth, contentHeight) => {
            this.setState({ scrollHeight: contentHeight });
            let currentTiming = new Date().getHours();
            this._scrollView.scrollTo({
              y: (contentHeight / 24) * currentTiming - 10,
              animated: false,
            });
          }}
        >
          {this._renderLines()}
          {this._renderEvents()}
        </ScrollView>
        </>
      );
    }
  }
  const Styles=StyleSheet.create({
      allDayContainer:{
          flexDirection:'row',
          height:hp('7%'),
          borderBottomColor:Colors.lightGrey,
          borderBottomWidth:.2,
          backgroundColor:Colors.white,
          paddingLeft:8,
          paddingRight:10,
          alignItems:'center'
      },
      allDayTextContainer:{
          width:wp('15%'),
          //backgroundColor:'pink',
          alignItems:"center",
          justifyContent:"center",
      },
      flatListContainer:{
          //height:hp('1'),
          //backgroundColor:'green'
      },
      viewContainer:{
          height:hp('3'),
          // marginBottom:2,
          // marginTop:2,
          marginVertical: 2,
          backgroundColor:Colors.calendarSettingsColor,
          borderRadius:5,
          paddingLeft:20,
          //alignItems:'center',
        justifyContent:'center'

      },
      allDay:{
          fontSize:14,
         fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'Roboto',
          color:Colors.placeholder,
          fontWeight: '500',
          
      },
      FlatListTitle:{
        fontFamily: Fonts.montserratSemiBold,
        fontSize:14,
        color:Colors.calendarSettingsText,
      },
  })
  