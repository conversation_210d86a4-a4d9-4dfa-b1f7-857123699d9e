// @flow
import {Platform, StyleSheet} from 'react-native';
import {
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";

const leftMargin = 50 -1;

export default function styleConstructor(theme = {}, calendarHeight) {
  let style = {
    container: {
      flex: 1,
      backgroundColor: '#ffff',
      ...theme.container
    },
    contentStyle: {
      backgroundColor: '#ffff',
      height: calendarHeight + 10,
      ...theme.contentStyle
    },
    header: {
      paddingHorizontal: 30,
      height: 50,
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderColor: '#E6E8F0',
      backgroundColor: '#F5F5F6',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'stretch',
      ...theme.header
    },
    headerTextContainer: {
      justifyContent: 'center'
    },
    headerText: {
      fontSize: 16,
      ...theme.headerText
    },
    arrow: {
      width: 15,
      height: 15,
      resizeMode: 'contain'
    },
    arrowButton: {
      width: 50,
      alignItems: 'center',
      justifyContent: 'center',
      ...theme.arrowButton
    },
    event: {
      position: 'absolute',
      backgroundColor: '#F0F4FF',
      borderColor: '#DDE5FD',
      borderWidth: 1,
      paddingLeft: 4,
      minHeight: 25,
      flex: 1,
      opacity: 1,
      paddingTop: 2,
      paddingBottom: 0,
      flexDirection: 'column',
      alignItems: 'flex-start',
      overflow: 'hidden',
      borderRadius:5,
      ...theme.event
    },
    eventTitle: {
      color: '#25265E',
     // fontWeight: '600',
      minHeight: 15,
      fontSize:wp("4%"),
      fontFamily:'Montserrat-semibold',
     // margin:5,
      ...theme.eventTitle
    },
    eventSummary: {
      color: '#787993',
      fontSize: wp('3%'),
      flexWrap: 'wrap',
      fontFamily:'Montserrat-regular',
      margin:5,
      ...theme.eventSummary
    },
    eventTimes: {
      marginTop: 0,
      fontSize: wp('3%'),
      fontWeight: 'bold',
      fontFamily:'Montserrat-regular',
      color: '#787993',
      flexWrap: 'wrap',
      marginLeft:5,
      ...theme.eventTimes,
    },
    line: {
      height: 1,
      position: 'absolute',
      left: leftMargin,
      marginLeft:10,
      backgroundColor: 'rgb(216,216,216)',
      ...theme.line
    },
    lineNow: {
      height: 1,
      position: 'absolute',
      left: leftMargin,
      backgroundColor: 'red',
      ...theme.lineNow
    },
    timeLabel: {
      position: 'absolute',
      left: 10,
      color: '#BEBEBE',
      fontSize: wp('3%'),
      fontFamily: Platform.OS === 'ios' ? 'Helvetica Neue' : 'Roboto',
      fontWeight: '500',
      ...theme.timeLabel
    }
  };
  return StyleSheet.create(style);
}