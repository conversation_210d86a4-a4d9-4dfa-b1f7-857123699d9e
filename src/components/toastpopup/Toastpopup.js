import React from 'react';
import {
  Text,
  StyleSheet,
  Platform
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Colors from '../../common/color';
import Fonts from '../../common/fonts';
import {View as Anima} from 'react-native-animatable';

export default function Toastpopup(props) {
  return (
    <Anima
      animation={'slideInUp'}
      duration={500}
      style={[
        styles.toastContainer,
        props.type == 'error' ? styles.errorContainer : styles.successContainer,
        props.container
      ]}>
      <Text style={styles.message}>{props.toastMessage}</Text>
    </Anima>
  );
}

const styles = StyleSheet.create({
  toastContainer: {
    position: 'absolute',
    bottom: 20,
    // marginTop: hp('85%'),
    width: '80%',
    minHeight: '6%',
    alignSelf: 'center',
    marginBottom: 10,
    borderRadius: hp('1%'),
    zIndex: 999,
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == 'ios' ? 1 : 0.8,
    shadowOffset: {width: 5, height: 5},
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: 'rgba(0,0,0,0.14)',
    backgroundColor: Colors.black,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    backgroundColor: Colors.yearLight,
    borderColor: Colors.yearLight,
  },
  successContainer: {
    backgroundColor: Colors.green,
    borderColor: Colors.green,
  },
  message: {
    color: Colors.white,
    fontSize: wp('4%'),
    fontFamily: Fonts.montserratBold,
    margin: 5,
  },
});