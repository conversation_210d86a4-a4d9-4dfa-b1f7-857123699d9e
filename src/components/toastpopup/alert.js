import React, {Component} from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Text,
  TouchableOpacity,
} from 'react-native';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import Colors from '../../common/color';
import {View as AnimmatableView} from 'react-native-animatable';
import Fonts from '../../common/fonts';

export default class Alert extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      fadeIn: new Animated.Value(0),
    };
  }

  componentDidMount() {
    this.fadeIn();
  }

  fadeIn() {
    this.state.fadeIn.setValue(0);
    Animated.timing(this.state.fadeIn, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      this.fadeOut();
    });
  }

  fadeOut() {
    Animated.timing(this.state.fadeIn, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      this.fadeIn();
    });
  }

  render() {
    return (
      <AnimmatableView animation={'bounceInUp'} style={styles.container}>
        <View style={styles.subContainer}>
          <Text style={styles.title}>{this.props.title}</Text>
          <Text style={styles.desc}>{this.props.desc}</Text>
          <View
            style={{
              flex: 1,
              justifyContent: 'flex-end',
              alignItems: 'flex-end',
            }}>
            <TouchableOpacity
              style={styles.okButton}
              onPress={this.props.okTap}>
              <Text style={styles.okText}>OK</Text>
            </TouchableOpacity>
          </View>
        </View>
      </AnimmatableView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    width: wp('100%'),
    height: hp('100%'),
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  subContainer: {
    width: wp('80%'),
    minHeight: hp('30%'),
    borderBottomLeftRadius: wp('20%'),
    borderTopRightRadius: wp('20%'),
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == 'ios' ? 1 : 0,
    shadowOffset: {width: 10, height: 10},
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: 'rgba(0,0,0,0.14)',
    // backgroundColor: Colors.themeColor
  },
  path: {
    width: wp('15%'),
    height: hp('8%'),
  },
  title: {
    color: Colors.black,
    fontFamily: Fonts.montserratExtraBold,
    fontSize: wp('6%'),
    marginLeft: wp('5%'),
    marginTop: wp('4%'),
  },
  desc: {
    fontSize: wp('4.5%'),
    marginLeft: wp('5%'),
    marginTop: wp('4%'),
    fontFamily: Fonts.montserratMedium,
    color: Colors.yearLight
  },
  okButton: {
    backgroundColor: Colors.themeColor,
    margin: hp('5%'),
    width: wp('20%'),
    height: hp('6%'),
    borderRadius: wp('3%'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  okText: {
    backgroundColor: Colors.themeColor,
    color: Colors.white,
    fontFamily: Fonts.montserratBold,
    fontSize: wp('5%'),
  },
});