import React, { Component } from "react";
import {
  View,
  StyleSheet,
  Animated,
  Text,
  TouchableOpacity,
} from "react-native";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import { View as AnimmatableView } from "react-native-animatable";
import Fonts from "../../common/fonts";

export default class LogoutPop extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      fadeIn: new Animated.Value(0),
    };
  }

  componentDidMount() {
    this.fadeIn();
  }

  fadeIn() {
    this.state.fadeIn.setValue(0);
    Animated.timing(this.state.fadeIn, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      this.fadeOut();
    });
  }

  fadeOut() {
    Animated.timing(this.state.fadeIn, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      this.fadeIn();
    });
  }

  render() {
    return (
      <AnimmatableView
        animation={"slideInUp"}
        duration={1000}
        style={styles.container}
      >
        <View style={[styles.subContainer, this.props.container]}>
          <Text style={[styles.desc, this.props.descStyles]}>
            {this.props.desc}
          </Text>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-evenly",
              marginTop: hp("3%"),
            }}
          >
            <TouchableOpacity
              style={styles.okButton}
              onPress={this.props.declineTap}
            >
              <Text style={[styles.okText,this.props.declineTextStyle]}>
                {this.props.declineText ? this.props.declineText : "No"}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.yesButton}
              onPress={this.props.acceptTap}
            >
              <Text style={[styles.acceptText,this.props.acceptTextStyle]}>
                {this.props.acceptText ? this.props.acceptText : "Yes"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </AnimmatableView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    //margin:0,
    position: "absolute",
    width: wp("100%"),
    height: hp("100%"),
    justifyContent: "flex-end",
    alignItems: "center",
    zIndex: 999,
    shadowOffset: { width: 10, height: 10 },
    shadowOpacity: 1,
    elevation: 5,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  subContainer: {
    width: wp("100%"),
    minHeight: hp("25%"),
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 0.2 : 0.5,
    shadowOffset: { width: 10, height: 10 },
    shadowOpacity: 1,
    elevation: 3,
    bottom: hp("7%"),
    shadowColor: "rgba(0,0,0,0.14)",
    borderTopLeftRadius: wp("10%"),
    borderTopEndRadius: wp("10%"),
    alignSelf: "center",
    paddingBottom: Platform.OS == "ios" ? 60 : 80,
    // backgroundColor: Colors.themeColor
  },
  path: {
    width: wp("15%"),
    height: hp("8%"),
  },
  title: {
    color: Colors.black,
    fontFamily: Fonts.montserratExtraBold,
    fontSize: wp("6%"),
    marginLeft: wp("5%"),
    marginTop: wp("4%"),
  },
  desc: {
    fontSize: wp("5.5%"),
    marginLeft: wp("5%"),
    fontFamily: Fonts.montserratSemiBold,
    color: Colors.black,
    alignSelf: "center",
    marginTop: hp("5%"),
    textAlign: "center",
  },
  okButton: {
    backgroundColor: "rgba(117,117,117, 0.2)",
    width: wp("35%"),
    height: hp("6%"),
    borderRadius: hp("3%"),
    justifyContent: "center",
    alignItems: "center",
    
  },
  yesButton: {
    backgroundColor: Colors.themeOpacity,
    width: wp("35%"),
    height: hp("6%"),
    borderRadius: hp("3%"),
    justifyContent: "center",
    alignItems: "center",
  },
  okText: {
    color: "rgba(117,117,117, 1)",
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("5%"),
  },
  acceptText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("5%"),
  },
});
