import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import UnRegistered from "./RootNavigation";
import Registered from './registeredNavigation';

const Stack = createNativeStackNavigator();

const InitialRoute = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animationTypeForReplace: 'push',
      }}
    >
      <Stack.Screen name="UnRegistered" component={UnRegistered} />
      <Stack.Screen name="Registered" component={Registered} />
    </Stack.Navigator>
  );
};

export default InitialRoute;