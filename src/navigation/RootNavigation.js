import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Splash from '../views/splash/splash';
import Dashboard from '../views/dashboard/dashboard';
import Step1 from '../views/signup/step1';
import Step2 from '../views/signup/step2';
import Step3 from '../views/signup/step3';
import Step4 from '../views/signup/step4';
import CreditCard from '../views/creditCard/CreditCard';
import SubscriptionThanks from '../views/subscriptionThanks/SubscriptionThanks';
import Login from '../views/login/login';
import ForgotPassword from '../views/forgotPassword/ForgotPassword';
import ResetPassword from '../views/resetPassword/ResetPassword';
import RegisteredRoute from './registeredNavigation';

const Stack = createNativeStackNavigator();

const RootNavigation = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right',
      }}
      initialRouteName="Splash"
    >
      <Stack.Screen name="Splash" component={Splash} />
      <Stack.Screen name="Dashboard" component={Dashboard} />
      <Stack.Screen name="Step1" component={Step1} />
      <Stack.Screen name="Step2" component={Step2} />
      <Stack.Screen name="Step3" component={Step3} />
      <Stack.Screen name="Step4" component={Step4} />
      <Stack.Screen name="CreditCard" component={CreditCard} />
      <Stack.Screen name="SubscriptionThanks" component={SubscriptionThanks} />
      <Stack.Screen name="Login" component={Login} />
      <Stack.Screen name="ForgotPassword" component={ForgotPassword} />
      <Stack.Screen name="ResetPassword" component={ResetPassword} />
      <Stack.Screen name="RegisteredRoute" component={RegisteredRoute} />
    </Stack.Navigator>
  );
};

export default RootNavigation;