import React, { Component } from "react";
import {
  View,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  StyleSheet,
  Text,
  BackHandler,
} from "react-native";
import Colors from "../common/color";
import Images from "../common/images";
import Fonts from "../common/fonts";
import ImageComponent from "../components/tabImageComponent/tabImageComponent";
import {
  changeTab,
  showSideMenu,
  setPage,
  clickAdd,
  checkNotification,
  updateList,
  cameBack,
  setSelectedCalendarDate,
} from "../actions/postAction";
import { connect } from "react-redux";
import Strings from "../common/string";
import moment from "moment";
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen";
import { trackScreen } from "../Google Analytics/GoogleAnalytics";
import HomePlus from "../components/Navigation/HomePlus";
import withBackHandler from "../components/backhandler";
import { compose } from "redux";

const images = [
  Images.homeun,
  Images.calun,
  Images.plus,
  Images.notiun,
  Images.menuun,
];

const selectedImages = [
  Images.homesele,
  Images.calsele,
  Images.plus,
  Images.notisele,
  Images.menusele,
];

const menuLabels = [
  Strings.bottomMenuLabels.home,
  Strings.bottomMenuLabels.calendar,
  " ",
  Strings.bottomMenuLabels.Notification,
];

const { width } = Dimensions.get("window");

class TabBar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isModal:false,
      currentTab:0
    };
  }

  componentDidMount() {
    if (this.props.fromNotification == true) {
      this.props.navigation.navigate("Notifications");
    }
  }

  componentDidUpdate = (prevProps) => {
    if (
      (this.props.currentPage == Strings.menu.members ||
      this.props.currentPage == Strings.menu.company ||
      this.props.currentPage == Strings.menu.gates ||
      this.props.currentPage == Strings.menu.equip ||
      this.props.currentPage == Strings.menu.df ||
      this.props.currentPage == Strings.menu.dr||
      this.props.currentPage == Strings.menu.calendarSettings) &&
      prevProps.currentPage !== this.props.currentPage
    ) {
      this.props.navigation.navigate("Plus");
    }
  };

  onBackPress = () => {
    console.log('tab >>>> 1', this.state.currentTab)
    if(this.state.currentTab == 1){
      BackHandler.exitApp()
      return true
    } else if(this.state.currentTab == 2){
      this.props.navigation.navigate("Calender");
    } else if(this.state.currentTab == 3){
      this.props.navigation.navigate("Calender");
    } else if(this.state.currentTab == 4){
      this.props.navigation.navigate("Calender");
    }
    return true;
  };

  //ON PRESS TAB
  onPressTab = (name, focused,activeRouteIndex) => {
    const { currentPage, projectRoleId } = this.props;
    this.props.changeTab(name);
    if (name == "Menu") {
      trackScreen('Setting')
      this.props.showSideMenu(true);
      this.setState({ currentTab:4})
    }

    if (name == "Plus" && focused == true) {
      /* Pages = [Members,Companies,Delivery Requests ]*/
      if (projectRoleId === 2 || projectRoleId === 3) {
        /**
         * Only PA (2) & GC (3) can create new DR, Members and Companies through "+" button
         */
        this.props.clickAdd(true);
      }

      if (projectRoleId === 4) {
        /**
         * SC (4) cannot create new Members and Companies through "+" button
         * SC (4) can only create New DR through "+" button
         */
        if (currentPage == "Delivery Requests") {
          this.props.clickAdd(true);
        }
      }
    } else if (name == "Plus" && focused == false) {
      if(activeRouteIndex==1){
        if(this.props.isAddDeliveryCalendar==='Delivery'){
           this.props.setPage(Strings.menu.addNewDr);
        }else  if(this.props.isAddDeliveryCalendar==='Crane'){
           this.props.setPage(Strings.menu.addNewCrane);
        }
        else  if(this.props.isAddDeliveryCalendar==='Concrete'){
           this.props.setPage(Strings.menu.addNewConcrete);
        }
        else  if(this.props.isAddDeliveryCalendar==='Inspection'){
          this.props.setPage(Strings.menu.addNewIn);
        }

      }else if(activeRouteIndex==0){
        this.setState({isModal:true})
      }
      else{
      this.props.setPage(Strings.menu.addNewDr);
    }
    }

    if (name == "Tab1" || name == "Calender" || name == "Notifications") {
      this.props.setPage("");
      this.props.navigation.navigate(name);
      if(name == "Tab1"){
        this.setState({ currentTab:1})
        // Reset selected calendar date to current date when navigating to home
        console.log('🏠 TabBar: Navigating to Tab1 - resetting selectedCalendarDate to current date');
        this.props.setSelectedCalendarDate(moment().format('YYYY-MM-DD'));
      } else if(name == "Calender"){
        this.setState({ currentTab:2})
        // Don't reset selectedCalendarDate when navigating to Calendar
      } else if(name == "Notifications"){
        this.setState({ currentTab:3})
        // Reset selected calendar date to current date when navigating to notifications
        console.log('🔔 TabBar: Navigating to Notifications - resetting selectedCalendarDate to current date');
        this.props.setSelectedCalendarDate(moment().format('YYYY-MM-DD'));
      }
     
    }
    if(name == "Tab1" ){
      trackScreen('DashBoard')
    }else if( name == "Calender"){
      trackScreen('Delivery Calendar')

    }else if( name == "Notifications"){
      trackScreen('Notifications')
    }
  };
  dashboardPlus=(value)=>{
    if(value==4){
      this.props.setPage(Strings.menu.addNewDr);
    }else  if(value==3){
      this.props.setPage(Strings.menu.addNewCrane);
    }
    else  if(value==2){
      this.props.setPage(Strings.menu.addNewConcrete);
    }
    else  if(value==1){
      this.props.setPage(Strings.menu.addNewIn);
    }
  }
  imageSelection=(index,isactive)=>{

    if(index==3){
      if(isactive){
        if(this.props.badgecount>0){
          return Images.dotBell
        }
        else{
          return selectedImages[index]
        }
      }else{
        if(this.props.badgecount>0){
          return Images.dotBell
        }
        else{
          return images[index]
        }
      }
    }else{
      return  isactive ? selectedImages[index] : images[index]
    }                 
  }
  //RENDER
  render() {
    const { state, descriptors, navigation } = this.props;
    const { routes, index: activeRouteIndex } = state;
    
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.subContainer}>
          {routes &&
            routes.map((route, index) => {
              const isRouteActive = index === activeRouteIndex;
              const isNoneActive = activeRouteIndex == 2;

              return (
                <TouchableOpacity
                  key={route.key}
                  style={styles.tabButton}
                  onPress={() =>
                    this.onPressTab(route.name, isRouteActive, activeRouteIndex)
                  }
                >
                  <View
                    style={[
                      {
                        width: (width / (routes.length + 1) - 10) / 2,
                      },
                      styles.tabIconView,
                    ]}
                  >
                    <ImageComponent
                      style={
                        isRouteActive ? styles.activeTab : styles.inactiveTab
                      }
                      imageSource={this.imageSelection(index, isRouteActive)}
                      indexValue={index}
                      notificationCount={this.props.badgecount}
                      container={{
                        marginTop:
                          route.name == "Plus"
                            ? isNoneActive
                              ? -20
                              : -50
                            : 20,
                      }}
                    />
                  </View>
                  {isRouteActive && (
                    <Text style={styles.bottomTabLabel}>
                      {menuLabels[index]}
                    </Text>
                  )}
                </TouchableOpacity>
              );
            })}
        </View>
        <HomePlus 
          isModal={this.state.isModal} 
          close={() => {this.setState({isModal: false})}}
          onPress={(value) => {
            this.dashboardPlus(value);
            this.setState({isModal: false});
          }}
        />
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: Colors.white,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 6,
  },
  subContainer: {
    height: hp("8%"),
    backgroundColor: Colors.white,
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
  },
  activeTab: {
    height: 15,
    width: 15,
  },
  inactiveTab: {
    height: 20,
    width: 20,
  },
  bottomTabLabel: {
    flex: 1,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp(2.2),
    color: Colors.themeColor,
  },
  tabButton: {
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
  },
  tabIconView: {
    flex: 1,
    marginBottom: 10,
    justifyContent: "space-evenly",
    alignItems: "center",
  },
});

const mapStateToProps = (state) => {
  const {
    currentPage,
    addPress,
    badgecount,
    fromNotification,
    updatelist,
    projectRoleId,
    isAddDeliveryCalendar,
    isCurrentTab,
    selectedCalendarDate,
  } = state.LoginReducer;

  return {
    currentPage,
    addPress,
    badgecount,
    fromNotification,
    updatelist,
    projectRoleId,
    isAddDeliveryCalendar,
    isCurrentTab,
    selectedCalendarDate,
  };
};

export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    setPage,
    clickAdd,
    checkNotification,
    updateList,
    cameBack,
    setSelectedCalendarDate,
  }),
  withBackHandler
)(TabBar);