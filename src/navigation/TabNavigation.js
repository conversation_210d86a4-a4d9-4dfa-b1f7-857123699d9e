import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import TabBar from './TabBar';
import Calender from '../views/calendar/calendar';
import Tab1 from '../views/tab1/tab1';
import Tab3 from '../views/tab3/tab3';
import Notifications from '../views/notification/notification';

const Tab = createBottomTabNavigator();

const TabNavigation = () => {
  return (
    <Tab.Navigator
      tabBar={props => <TabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}
      initialRouteName="Tab1"
    >
      <Tab.Screen name="Tab1" component={Tab1} />
      <Tab.Screen name="Calender" component={Calender} />
      <Tab.Screen name="Plus" component={Tab3} />
      <Tab.Screen name="Notifications" component={Notifications} />
      <Tab.Screen name="Menu" component={Tab1} />
    </Tab.Navigator>
  );
};

export default TabNavigation;