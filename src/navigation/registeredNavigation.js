import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import TabNavigatorScreen from "./TabNavigation";
import Home from "../views/home/<USER>";
import Menu from "../views/menu/menu";
import AddMember from "../views/members/addMember";
import AddCompany from "../views/companies/addCompany";
import AddGates from "../views/gates/addGate";
import AddEquip from "../views/equipment/addEquip";
import AddDFOW from "../views/dfow/addDfow";
import AddProject from "../views/signup/step3";
import AddDR from "../views/dr/addDr";
import InviteMember from "../views/members/inviteMember";
import Settings from "../views/settings/setting";
import Profile from "../views/profile/Profile";
import Search from "../views/search/search";
import plusTab from "../views/tab3/tab3";
import UpgradeProject from "../views/signup/step4";
import Details from "../views/details/details";
import VoidList from "../views/void/voidlist";
import AddCrane from "../views/Crane/AddCrane";
import addinspection from "../views/Inspection/addinspection";
import AddConcrete from "../views/Concrete/AddConcrete";
import Delivery from "../views/dr/drList";
import DetailsConcrete from "../views/Concrete/DetailsConcrete";
import EquipmentList  from "../views/equipment/equipList";
import Member from "../views/members/membersList";
import Company from "../views/companies/companyList";
import CalendarSettings from"../views/CalendarSettings/CalendarSettings";
import AddNewEvent from "../views/CalendarSettings/AddNewEvent";
import EventDisplay from "../views/CalendarSettings/EventDisplay";
import timeSlot from '../views/timeSlot/timeSlot'
import timeSlotCrane from "../views/timeSlot/timeSlotCrane";
import timeSlotConcrete from "../views/timeSlot/timeSlotConcrete";
import timeSlotInspection from "../views/timeSlot/timeSlotInspection";

const Stack = createNativeStackNavigator();

const RegisteredNavigation = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'none',
      }}
      initialRouteName="Home"
    >
      <Stack.Screen 
        name="Home" 
        component={Home}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="Menu" 
        component={Menu}
        options={{
          gestureEnabled: true,
          headerShown: false,
          animation: 'none',
        }}
      />
      <Stack.Screen 
        name="AddMember" 
        component={AddMember}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="InviteMember" 
        component={InviteMember}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="AddCompany" 
        component={AddCompany}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="AddGates" 
        component={AddGates}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="AddEquip" 
        component={AddEquip}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="AddNewEvent" 
        component={AddNewEvent}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="AddDFOW" 
        component={AddDFOW}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="AddDR" 
        component={AddDR}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="addinspection" 
        component={addinspection}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="timeSlot" 
        component={timeSlot}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="timeSlotCrane" 
        component={timeSlotCrane}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="timeSlotConcrete" 
        component={timeSlotConcrete}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="timeSlotInspection" 
        component={timeSlotInspection}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="AddCrane" 
        component={AddCrane}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="AddConcrete" 
        component={AddConcrete}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="DetailsConcrete" 
        component={DetailsConcrete}
        options={{
          gestureEnabled: true,
          headerShown: false,
          animation: 'fade_from_bottom',
        }}
      />
      <Stack.Screen 
        name="AddProject" 
        component={AddProject}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="UpgradeProject" 
        component={UpgradeProject}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="TabNavigatorScreen" 
        component={TabNavigatorScreen}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="Settings" 
        component={Settings}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="Profile" 
        component={Profile}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="Delivery" 
        component={Delivery}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="CalendarSettings" 
        component={CalendarSettings}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="EquipmentList" 
        component={EquipmentList}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="Member" 
        component={Member}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="Company" 
        component={Company}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="Search" 
        component={Search}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="plusTab" 
        component={plusTab}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="Details" 
        component={Details}
        options={{
          gestureEnabled: true,
          headerShown: false,
          animation: 'fade_from_bottom',
        }}
      />
      <Stack.Screen 
        name="EventDisplay" 
        component={EventDisplay}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="VoidList" 
        component={VoidList}
        options={{
          gestureEnabled: true,
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default RegisteredNavigation;