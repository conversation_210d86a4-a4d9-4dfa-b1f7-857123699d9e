import {
  <PERSON>O<PERSON><PERSON>_SUCCESS,
  <PERSON>O<PERSON><PERSON>_FAILED,
  UPDATE_DATA,
  CHECK_NOTIFICATION,
  NOTIFICATION_DETAILS,
  STORE_ROLE_ID,
  GO_TO_VOID,
  STORE_ROLE,
  LOG_OUT,
  SEARCH,
  <PERSON>DIT_DATA,
  EDIT_INS,
  CAME_BACK,
  STORE_LAST_ID,
  C<PERSON>IC<PERSON>_ADD,
  COUNTRY_LIST,
  STATE_LIST,
  CITY_LIST,
  PROJECT_LIST,
  SELECTED_PROJECT,
  USER_DETAILS,
  SET_PAGE,
  <PERSON>ANGE_TAB,
  SHOW_MENU,
  DE<PERSON><PERSON><PERSON>_PAGE,
  DELIVERY_DETAILS_ID,
  INSPECTION_DETAILS_ID,
  <PERSON>D<PERSON>_COUNT,
  SIGNUP_DATA,
  UPDATE_LIST,
  REFRESH_PAGE,
  REFRESH_DASHBOARD,
  REFRESH_DELIVERY,
  SHOW_TOASTER,
  PROJECT_SWITCHED,
  UPDATE_DR_LIST,
  UPDATE_SETTING_SCREEN,
  UPDATE_INVITE_MEMBER,
  DR_RESPOSIBLE_PERSON,
  COMAPANY_LIST,
  SELECTED_COMPANY,
  ADD_ALL_REQUEST_TOGGLE,
  LAST_CRANE_ID,
  SHOW_CRANE_REQUEST_ID,
  EDIT_CRANE_REQUEST,
  ADD_CALENDAR_TOGGLE,
  EDIT_ASSOCIATED_WITH_CRANE,
  REFRESH_CALENDAR,
  REFRESH_CALENDAR_SETTINGS,
  EVENT_DISPLAY_PAGE,
  EVENT_DISPLAY_DATA,
  CONCRETE_DETAILS,
  CONCRETE_DETAILS_ID,
  LAST_CONCRETE_ID,
  EDIT_CONCRETE_REQUEST,
  SHOW_CONCRETE_REQUEST_ID,
  SELECTED_CONCRETE_LOCATIONS,
  SELECTED_CONCRETE_PLACEMENTS,
  SELECTED_CONCRETE_MIXDESIGNS,
  SELECTED_CONCRETE_PUMPSIZES,
  EDIT_EVENT_DATA,
  REFRESH_EVENT_DISPLAY,
  ENABLE_OPTION_EVENTS,
  CURRENT_TAB,
  DR_LIST_PAGE,
  SET_SELECTED_CALENDAR_DATE,
} from "../actions/types";
import { MEMBERS_LIST } from "../api/Constants";

const INITIAL_STATE = {
  loginSuccess: [],
  loginFailed: [],
  changeTab: 0,
  showMenu: false,
  currentPage: "",
  userDetails: [],
  projectDetails: [],
  projectlist: [],
  countrylist: [],
  statelist: [],
  citylist: [],
  addPress: false,
  lastid: 0,
  checkCameBack: false,
  editedData: [],
  editedEventData:[],
  logout: false,
  searchTap: "",
  roleData: [],
  detailsPage: "",
  navigateToVoid: false,
  projectRoleId: 0,
  updateData: false,
  deliveryDetailsId: 0,
  inspectionDetailsId: 0,
  badgecount: 0,
  notificationDetails: [],
  fromNotification: false,
  signupParams: {},
  updatelist: false,
  refresh: false,
  refresh_dashboard: false,
  refreshDelivery: false,
  //new redux state for api call
  showToaster: false, //boolean
  toastMessage: "", //string
  toastType: "", //string
  membersList: [],
  projectSwitched: new Date(),
  needToRefreshDrList: false,
  afterCreateProject: false,
  updateInviteMem: false,
  responsiblePersonData: [],
  company_List: [],
  selected_Company: [],
  isAddDelivery: "Delivery",
  isAddDeliveryCalendar:'Delivery',
  lastCraneRequestId: 0,
  craneRequestId: 0,
  editedCrane: [],
  isAssociatedCrane:false,
  isRefreshCalendar:false,
  evnetDisplay:"",
  eventData:[],
  concreteDetails:"",
  concreteDetailsRequestID:0,
  lastConcreteRequestId: 0,
  editedConcrete: [],
  concreteRequestId: 0,
  selectedConcreteLocations: [],
  selectedConcretePlacements: [],
  selectedConcreteMixDesigns: [],
  selectedConcretePumpSizes: [],
  isRefreshEventDisplay:false,
  isEnableEvents:false,
  isNetworkConnected: false,
  isCurrentTab:0,
  isCurrentDRPage: 0,
  selectedCalendarDate: new Date(),
};

export default (state = INITIAL_STATE, action) => {
  switch (action.type) {
    case REFRESH_PAGE:
      return {
        ...state,
        refresh: action.payload,
      };

    case REFRESH_DASHBOARD:
      return {
        ...state,
        refresh_dashboard: action.payload,
      };

    case UPDATE_LIST:
      return {
        ...state,
        updatelist: action.payload,
      };
    case CHECK_NOTIFICATION:
      return {
        ...state,
        fromNotification: action.payload,
      };
    case NOTIFICATION_DETAILS:
      return {
        ...state,
        notificationDetails: action.payload,
      };
    case STATE_LIST:
      return {
        ...state,
        statelist: action.payload,
      };
    case CITY_LIST:
      return {
        ...state,
        citylist: action.payload,
      };
    case UPDATE_DATA:
      return {
        ...state,
        updateData: action.payload,
      };
    case STORE_ROLE_ID:
      return {
        ...state,
        projectRoleId: action.payload,
      };
    case GO_TO_VOID:
      return {
        ...state,
        navigateToVoid: action.payload,
      };
    case STORE_ROLE:
      return {
        ...state,
        roleData: action.payload,
      };
    case SEARCH:
      return {
        ...state,
        searchTap: action.payload,
      };
    case COUNTRY_LIST:
      return {
        ...state,
        countrylist: action.payload,
      };
    case CAME_BACK:
      return {
        ...state,
        checkCameBack: action.payload,
      };
    case EDIT_DATA:
      return {
        ...state,
        editedData: action.payload,
      };
      case EDIT_INS:
      return {
        ...state,
        editedInspectionData: action.payload,
      };
    case USER_DETAILS:
      return {
        ...state,
        userDetails: action.payload,
      };
    case PROJECT_LIST:
      return {
        ...state,
        projectlist: action.payload,
      };
    case STORE_LAST_ID:
      return {
        ...state,
        lastid: action.payload,
      };
    case CLICK_ADD:
      return {
        ...state,
        addPress: action.payload,
      };
    case SELECTED_PROJECT:
      return {
        ...state,
        projectDetails: action.payload,
      };
    case CHANGE_TAB:
      return {
        ...state,
        changeTab: action.payload,
      };
    case SHOW_MENU:
      return {
        ...state,
        showMenu: action.payload,
      };
    case SET_PAGE:
      return {
        ...state,
        currentPage: action.payload,
      };
    case LOGIN_SUCCESS:
      return {
        ...state,
        loginSuccess: action.payload,
        loginFailed: [],
      };
    case LOGIN_FAILED:
      return {
        ...state,
        loginFailed: action.payload,
        loginSuccess: [],
      };
    case DETAILS_PAGE:
      return {
        ...state,
        detailsPage: action.payload,
      };
    case DELIVERY_DETAILS_ID:
      return {
        ...state,
        deliveryDetailsId: action.payload,
      };
      case INSPECTION_DETAILS_ID:
        return {
          ...state,
          inspectionDetailsId: action.payload,
        };
    case BADGE_COUNT:
      return {
        ...state,
        badgecount: action.payload,
      };
    case SIGNUP_DATA:
      return {
        ...state,
        signupParams: action.payload,
      };

    case REFRESH_DELIVERY:
      return {
        ...state,
        refreshDelivery: action.payload,
      };

    case SHOW_TOASTER:
      return {
        ...state,
        showToaster: action.payload.showToaster,
        toastMessage: action.payload.toastMessage,
        toastType: action.payload.toastType,
      };

    case MEMBERS_LIST:
      return {
        ...state,
        membersList: [...action.payload],
      };

    case LOG_OUT:
      return {
        ...state,
        loginSuccess: [],
        loginFailed: [],
        changeTab: 0,
        showMenu: false,
        currentPage: "",
        userDetails: [],
        projectDetails: [],
        projectlist: [],
        countrylist: [],
        addPress: false,
        lastid: 0,
        checkCameBack: false,
        editedData: [],
        logout: false,
      };
    case PROJECT_SWITCHED:
      return {
        ...state,
        projectSwitched: action.payload,
      };
    case UPDATE_DR_LIST:
      return {
        ...state,
        needToRefreshDrList: action.payload,
      };
    case UPDATE_SETTING_SCREEN:
      return {
        ...state,
        afterCreateProject: action.payload,
      };
    case UPDATE_INVITE_MEMBER:
      return {
        ...state,
        updateInviteMem: action.payload,
      };
    case DR_RESPOSIBLE_PERSON:
      return {
        ...state,
        responsiblePersonData: action.payload,
      };
    case COMAPANY_LIST:
      return {
        ...state,
        company_List: action.payload,
      };
    case SELECTED_COMPANY:
      return {
        ...state,
        selected_Company: action.payload,
      };
    case ADD_ALL_REQUEST_TOGGLE:
      return {
        ...state,
        isAddDelivery: action.payload,
      };
    case ADD_CALENDAR_TOGGLE:
      return {
        ...state,
        isAddDeliveryCalendar: action.payload,
      };
    case LAST_CRANE_ID:
      return {
        ...state,
        lastCraneRequestId: action.payload,
      };
    case SHOW_CRANE_REQUEST_ID:
      return {
        ...state,
        craneRequestId: action.payload,
      };
    case EDIT_CRANE_REQUEST:
      return {
        ...state,
        editedCrane: action.payload,
      };
    case EDIT_ASSOCIATED_WITH_CRANE:
      return {
        ...state,
        isAssociatedCrane: action.payload,
      };
    case REFRESH_CALENDAR:
      return {
        ...state,
        isRefreshCalendar: action.payload,
      };
    case REFRESH_CALENDAR_SETTINGS:
      return {
        ...state,
        isRefreshCalendarSettings: action.payload,
      };
    case EVENT_DISPLAY_PAGE:
      return {
        ...state,
        eventDisplay: action.payload,
      };
      case EVENT_DISPLAY_DATA:
        return {
          ...state,
          eventData: action.payload,
        };
    case CONCRETE_DETAILS:
      return{
        ...state,
        concreteDetails:action.payload,
      }
    case CONCRETE_DETAILS_ID:
      return{
        ...state,
        concreteDetailsRequestID:action.payload,
      }
    case LAST_CONCRETE_ID:
      return {
        ...state,
        lastConcreteRequestId: action.payload,
      };
    case EDIT_CONCRETE_REQUEST:
      return {
        ...state,
        editedConcrete: action.payload,
      };
    case SHOW_CONCRETE_REQUEST_ID:
      return {
        ...state,
        concreteRequestId: action.payload,
      };
    case SELECTED_CONCRETE_LOCATIONS:
      return {
        ...state,
        selectedConcreteLocations: action.payload,
      };
    case SELECTED_CONCRETE_PLACEMENTS:
      return {
        ...state,
        selectedConcretePlacements: action.payload,
      };
    case SELECTED_CONCRETE_MIXDESIGNS:
      return {
        ...state,
        selectedConcreteMixDesigns: action.payload,
      };
    case SELECTED_CONCRETE_PUMPSIZES:
      return {
        ...state,
        selectedConcretePumpSizes: action.payload,
      };
      case EDIT_EVENT_DATA:
        return {
          ...state,
          editedEventData: action.payload,
        };
      case REFRESH_EVENT_DISPLAY:
          return {
            ...state,
            isRefreshEventDisplay: action.payload,
          };
          case ENABLE_OPTION_EVENTS:
          return{
            ...state,
            isEnableEvents: action.payload,
          }
          case CURRENT_TAB:
          return{
            ...state,
            isCurrentTab: action.payload
          }
          case DR_LIST_PAGE:
            return{
              ...state,
              isCurrentDRPage: action.payload
            }

    case SET_SELECTED_CALENDAR_DATE:
      return {
        ...state,
        selectedCalendarDate: action.payload,
      };
       
    default:
      return state;
  }
};
