import {check, request, RESULTS} from 'react-native-permissions';

export async function checkAndRequestPermission(permission) {
  const permissionResult = await check(permission);
  switch (permissionResult) {
    case RESULTS.DENIED:
      if ((await request(permission)) === RESULTS.GRANTED) {
        return true;
      }
      return false;
    case RESULTS.LIMITED:
      return false;
    case RESULTS.GRANTED:
      return true;
    case RESULTS.BLOCKED:
      if ((await request(permission)) === RESULTS.GRANTED) {
        return false;
      }
  }
  return false;
}
