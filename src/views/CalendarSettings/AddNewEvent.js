import React, { Component } from "react";
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  FlatList,
  Platform,
  Switch,

} from "react-native";
import { CommonActions } from '@react-navigation/native';
import { Avatar } from "react-native-paper";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import {
  changeTab,
  showSideMenu,
  cameBack,
  updateList,
  refreshDashboard,
  refreshDeliveryList,
  showCraneRequestId,
  editCraneRequest,
  refreshCalendar,
  refreshCalendarSettings,
  editEventData, setPage,
  refreshEventDisplay,
  setSelectedCalendarDate,
} from "../../actions/postAction";
import Colors from "../../common/color";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { TextField } from "../../components/textinput/addMemberTextinput";
import { isEmpty } from "../../common/validators";
import Modal from "react-native-modal";
import DateTimePicker from "@react-native-community/datetimepicker";
import moment from "moment";
import DropDownPicker from "../../components/dropdown/DropDownPicker";
import { ADD_EVENT, EDIT_EVENT, GET_SINGLE_EVENT, GET_SINGLE_PROJECT, GET_TIMEZONE, GET_LOCATION_DETAILS, NO_EQUIPMENT_NEEDED, NO_EQUIPMENT_NEEDED_2 } from "../../api/Constants";
import { addEvent, getEvent, getTimeZone, putEditEvent, getLocationSettings, } from "../../api/Api";
import AppLoader from "../../components/apploader/AppLoader";
import { mixPanelTrackEvent } from "../../MixPanel/MixPanel";
import Dropdown from "../../components/dropdown/dropdown";
import NetInfo from '@react-native-community/netinfo';
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";
import NoInternet from "../../components/NoInternet/noInternet";
import MultiSelectDropDown from "../../components/multi-select-dropdown/multiSelectdropDown";

let selectedFromDate = new Date();
let selectedToDate = new Date();
let selectEndDate = new Date();
let selectedStartTime = "";
let selectedEndTime = "";
class AddNewEvent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      roleId: this.props.projectRoleId,
      description: "",
      selectedStartTime: "",
      selectedEndTime: "",
      fromDate: moment(new Date()).format("MM/DD/YYYY"),
      calFromDate: new Date(),
      toDate: moment(new Date()).format("MM/DD/YYYY"),
      calToDate: new Date(),
      showDateModal: false,
      showToDateModal: false,
      minimumFromDate: new Date(),
      minimumToDate: new Date(),
      calFromStartTime: new Date(),
      calToEndTime: new Date(),
      editEndTime: true,
      editStartTime: true,
      isAllDay: false,
      isDeilveryCalendar: false,
      isCraneCalendar: false,
      isConcreteCalendar: false,
      isInspectionCalendar: false,
      recurrence: Strings.calendarSettings.doseNotRepeat,
      listOfRecurrence: [
        { name: "Does Not Repeat" },
        { name: "Daily" },
        { name: "Weekly" },
        { name: "Monthly" },
        { name: "Yearly" },
      ],
      isRecurrence: false,
      times: 1,
      recur: [
        {
          id: 1,
          value: "Day",
          label: "Day",
          selected: false,
        },
        {
          id: 2,
          value: "Week",
          label: "Week",
          selected: false,
        },
        { id: 3, value: "Month", label: "Month", selected: false },
        {
          id: 4,
          value: "Year",
          label: "Year",
          selected: false,
        },
      ],
      selectedRecur: "",
      isLowerRecur: false,
      repeatEvery: false,
      daysList: [
        { key: 1, name: "Sunday", label: "S", selected: true },
        { key: 2, name: "Monday", label: "M", selected: true },
        { key: 3, name: "Tuesday", label: "T", selected: true },
        { key: 4, name: "Wednesday", label: "W", selected: true },
        { key: 5, name: "Thursday", label: "T", selected: true },
        { key: 6, name: "Friday", label: "F", selected: true },
        { key: 7, name: "Saturday", label: "S", selected: true },
      ],
      isDayWeek: false,
      isMonth: false,
      isYear: false,
      isMonthFirstCheck: true,
      isMonthSecondCheck: false,
      isMonthThirdCheck: false,
      isYearFirstCheck: true,
      isYearSecondCheck: false,
      isYearThirdCheck: false,
      monthlyDay: "",
      monthlyLastDay: "",
      editNewEvent: false,
      timeZoneList: [],
      selectedTimeZone: "",
      selectedTimeZoneId: 0,
      selectedDaysOccurs: 'Monday',
      isEdit: false,
      monthAndYearCheck: '',
      editId: 0,
      mixpanelParam: {
        ProjectName: this.props.projectDetails.projectName,
        CompanyName: this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName: this.props.userDetails.firstName,
      },
      timeZoneVisible: false,
      endDate: moment(new Date()).format("MM/DD/YYYY"),
      selectedEndDate: new Date(),
      isEndDate: false,
      isConnected: false,
      isNetworkCheck: false,
      showStartTimeModal: false,
      selectedLocationId: 0,
      locationDropdownList: [],
      selectedLocationNew: "",
      selectedLocationList: [], // Array to store multiple selected location IDs
      selectedLocationNames: [], // Array to store multiple selected location names
      gateList: [],
      selectedGate: [],
      selectedGateList: [],
      eqipModalVisible: false,
      gateModalVisible: false,
      equipTypeList: [],
      selectedEquipName: null,
      storeEquipmentList: [],
      isFutureEquipment: false,
      selectedEquipmentList: [], // Add this line
      eventStartDate: null,
      eventEndDate: null,
      timeSlotExplicitlyChanged: false, // Flag to track if user explicitly changed time via time slot picker
      isInformationOnlyEvent: false, // New toggle for Information Only Event
    };
  }

  // Method to manually close from date modal
  closeFromDateModal = () => {
    this.setState({ showDateModal: false });
  }

  // Method to manually close to date modal
  closeToDateModal = () => {
    this.setState({ showToDateModal: false });
  }

  // Method to manually close end date modal
  closeEndDateModal = () => {
    this.setState({ isEndDate: false });
  }

  // Method to open end date modal
  openEndDateModal = () => {
    Keyboard.dismiss();
    this.setState({ isEndDate: true });
  }

  componentDidMount() {
    if (Platform.OS === 'ios') {
      this.networkCheck();
    }
    else {
      this.initializeComponent();
    }
  }

  // New method to handle proper initialization sequence
  initializeComponent = async () => {
    this.checkAndUpdateSelectedDate();

    // Set initial times
    let initialStartTime = new Date();
    initialStartTime.setHours(new Date().getHours());
    initialStartTime.setMinutes(0);
    initialStartTime.setSeconds(0);

    let initialEndTime = new Date();
    initialEndTime.setHours(initialStartTime.getHours());
    initialEndTime.setMinutes(30);
    initialEndTime.setSeconds(0);

    this.setState({
      calFromStartTime: initialStartTime,
      calToEndTime: initialEndTime,
      selectedStartTime: moment(initialStartTime).format("hh:mm A"),
      selectedEndTime: moment(initialEndTime).format("hh:mm A"),
    });
    selectedStartTime = initialStartTime;
    selectedEndTime = initialEndTime;

    // IMPORTANT: Wait for timezone and location data to load BEFORE checkEdit
    await Promise.all([
      this.getInitialTimeZoneAsync(),
      this.getLocationDetailAsync()
    ]);

    // Now call checkEdit after data is loaded
    this.onMonthChanged();
    this.checkEdit();
  }

  onSelectPlace = async (place) => {
    Keyboard.dismiss();
    this.setState({
      showAutoComplete: false,
      textFieldValue: place.description,
      isModalVisible: false, // Close the modal on selection
      selectedAddress: place.description, // Update selected address to display outside modal
    });

    const URL = geoCodeAdrressAPI(place.description, API_KEY);
    const addressAPI = await axios.get(URL);
    const addressCoordinate = {
      latitude: addressAPI.data.results[0].geometry.location.lat,
      longitude: addressAPI.data.results[0].geometry.location.lng,
    };
  };

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  checkAndUpdateSelectedDate = () => {
    let dateStr = null;

    // Priority 1: Use Redux selectedCalendarDate if available
    if (this.props.selectedCalendarDate) {
      dateStr = this.props.selectedCalendarDate;
    }
    // Priority 2: Use navigation params if Redux date not available
    else if (this.props.route.params?.selectedDate) {
      dateStr = this.props.route.params.selectedDate;
    }

    if (dateStr) {
      let parsedMoment = moment(dateStr, "MM/DD/YYYY", true);
      if (!parsedMoment.isValid()) {
        parsedMoment = moment(dateStr);
      }

      if (parsedMoment.isValid()) {
        const parsedDate = parsedMoment.toDate();
        selectedFromDate = parsedDate;
        selectedToDate = parsedDate;

        this.setState({
          fromDate: parsedMoment.format("MM/DD/YYYY"),
          toDate: parsedMoment.format("MM/DD/YYYY"),
          calFromDate: parsedDate,
          calToDate: parsedDate,
        });
      }
    }
  }

  networkCheck = () => {
    NetInfo.addEventListener(state => {
      if (!state.isConnected && !state.isInternetReachable) {
        this.setState({ isNetworkCheck: true })
      } else {
        this.setState({ isNetworkCheck: false })
        // Use the same initialization method for consistency
        this.initializeComponent();
      }
    })
  }

  /**
   * Helper function to parse date without timezone conversion
   * Extracts just the date part (YYYY-MM-DD) to avoid UTC conversion issues
   * Example: "2025-10-31T00:00:00.000Z" -> Date object for Oct 31 in local timezone
   */
  parseLocalDate = (dateString) => {
    if (!dateString) return new Date();
    const datePart = dateString.split('T')[0]; // Get "2025-10-31" from "2025-10-31T00:00:00.000Z"
    const [year, month, day] = datePart.split('-').map(Number);
    return new Date(year, month - 1, day); // Create date in local timezone (month is 0-indexed)
  }

  /**
   * Get single Event is API call function to get particular event
   */
  getSingleEvent = () => {
    let url = `${GET_SINGLE_EVENT}/${this.props.editedEventData.id}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`
    getEvent(url, {}, () => null, (response) => {
      if (response.status) {
        if (response.status == 200) {
          const eventData = response.data.event;

          // Update location data
          if (eventData.LocationId) {
            let locationIds = eventData.LocationId;
            // Parse LocationId if it's a stringified array
            if (typeof locationIds === 'string') {
              try {
                const locationIdStr = locationIds.replace(/[\[\]]/g, '');
                locationIds = locationIdStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
              } catch (error) {
                console.error('Error parsing LocationId:', error);
                locationIds = [];
              }
            } else if (!Array.isArray(locationIds)) {
              locationIds = [locationIds];
            }
            
            // Update location dropdown list with selected status
            const updatedLocationList = this.state.locationDropdownList.map(loc => ({
              ...loc,
              selected: locationIds.includes(loc.id)
            }));
            
            const selectedLocationNames = updatedLocationList
              .filter(loc => loc.selected)
              .map(loc => loc.value);
            
            this.setState({
              locationDropdownList: updatedLocationList,
              selectedLocationList: locationIds,
              selectedLocationNames: selectedLocationNames,
              selectedLocationId: locationIds.length > 0 ? locationIds[0] : null, // Keep for backward compatibility
              selectedLocationNew: selectedLocationNames.length > 0 ? selectedLocationNames[0] : null, // Keep for backward compatibility
            });
          }

          // Update gate data
          if (eventData.GateId) {
            let gateIds = eventData.GateId;
            if (typeof gateIds === 'string') {
              try {
                const gateIdStr = gateIds.replace(/[\[\]]/g, '');
                gateIds = gateIdStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
              } catch (error) {
                console.error('Error parsing GateId:', error);
                gateIds = [];
              }
            } else if (!Array.isArray(gateIds)) {
              gateIds = [gateIds];
            }

            this.setState({
              selectedGateList: gateIds,
              selectedGateId: gateIds.length > 0 ? gateIds.join(',') : null
            });
          }

          // Update equipment data
          if (eventData.EquipmentId) {
            let equipmentIds = [];
            try {
              // Remove the square brackets and split by comma
              const equipmentIdStr = eventData.EquipmentId.replace(/[\[\]]/g, '');
              equipmentIds = equipmentIdStr.split(',').map(id => parseInt(id.trim()));
            } catch (error) {
              console.error('Error parsing EquipmentId:', error);
            }

            // Update the equipment list with selection status
            const updatedEquipList = this.state.equipTypeList.map(equip => {
              let isSelected = equipmentIds.includes(equip.id);

              // Special handling for "No Equipment Needed" case
              // Check if equipment ID 0 or -1 is in the list and current item is the no-equipment option
              if ((equipmentIds.includes(0) && equip.id === 0) || (equipmentIds.includes(-1) && equip.id === -1)) {
                isSelected = true;
              }

              return {
                ...equip,
                selected: isSelected
              };
            });

            this.setState({
              equipTypeList: updatedEquipList,
              selectedEquipmentList: equipmentIds
            }, () => {
              // Force update the MultiSelectDropDown component
              if (this.multiSelectRef) {
                this.multiSelectRef.setState({
                  dataItems: updatedEquipList,
                  isAllChecked: updatedEquipList.every(item => item.selected)
                });
              }
            });
          }

          if ((eventData.recurrence === Strings.calendarSettings.daily) || (eventData.recurrence === Strings.calendarSettings.doseNotRepeat)) {
            this.setState({
              selectEndDate: eventData.toDate != null ? this.parseLocalDate(eventData.toDate) : new Date(),
              endDate: eventData.toDate != null ? moment(this.parseLocalDate(eventData.toDate)).format('MM/DD/YYYY') : moment(new Date()).format('MM/DD/YYYY')
            });
          } else {
            this.setState({
              selectEndDate: eventData.endDate != null ? this.parseLocalDate(eventData.endDate) : new Date(),
              endDate: eventData.endDate != null ? moment(this.parseLocalDate(eventData.endDate)).format('MM/DD/YYYY') : moment(new Date()).format('MM/DD/YYYY')
            });
          }
        }
      }
    })
  }
  checkEdit = () => {
    if (this.props.editedEventData.id) {
      this.getSingleEvent();
      this.setState({ isEdit: true, editId: this.props.editedEventData.id, showLoader: false })
      let data = this.props.editedEventData;
      const item = this.state.timeZoneList.filter(element => {
        return element.id === data.TimeZone.id;
      });

      // Set location data first, then parse gate/equipment IDs, then filter
      if (data.LocationId) {
        let locationIds = data.LocationId;

        if (typeof locationIds === 'string') {
          try {
            const locationIdStr = locationIds.replace(/[\[\]]/g, '');
            locationIds = locationIdStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
          } catch (error) {
            console.error('Error parsing LocationId:', error);
            locationIds = [];
          }
        } else if (!Array.isArray(locationIds)) {
          locationIds = [locationIds];
        }
        
        // Update location dropdown list with selected status
        const updatedLocationList = this.state.locationDropdownList.map(loc => ({
          ...loc,
          selected: locationIds.includes(loc.id)
        }));
        
        const selectedLocationNames = updatedLocationList
          .filter(loc => loc.selected)
          .map(loc => loc.value);
        
        let gateIds = [];
        if (data.GateId) {
          let gateId = data.GateId;
          if (typeof gateId === 'string') {
            try {
              const gateIdStr = gateId.replace(/[\[\]]/g, '');
              gateIds = gateIdStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            } catch (error) {
              console.error('Error parsing GateId:', error);
            }
          } else if (!Array.isArray(gateId)) {
            gateIds = [gateId];
          } else {
            gateIds = gateId;
          }
        }

        // Set location IDs and gate IDs BEFORE filtering
        this.setState({
          locationDropdownList: updatedLocationList,
          selectedLocationList: locationIds,
          selectedLocationNames: selectedLocationNames,
          selectedLocationId: locationIds.length > 0 ? locationIds[0] : null, // Keep for backward compatibility
          selectedLocationNew: selectedLocationNames.length > 0 ? selectedLocationNames[0] : null, // Keep for backward compatibility
          selectedGateList: gateIds,
          selectedGateId: gateIds.length > 0 ? gateIds.join(',') : null
        }, () => {
          this.filterEquipmentAndGatesByLocation(false);
          setTimeout(() => {
            this.setEditEquipmentAndGate(data);
          }, 100);
        });
      } else {
        setTimeout(() => {
          this.setEditEquipmentAndGate(data);
        }, 100);
      }

      if (data.recurrence == 'Daily') {
        this.setState({
          isDayWeek: true,
          isLowerRecur: true,
          repeatEvery: true,
          selectedRecur: 'Day',
          times: data.repeatEveryCount != null ? data.repeatEveryCount : 1
        })
      } else if (data.recurrence == 'Weekly') {
        let days = '';
        let arr = this.state.daysList
        for (let index = 0; index < arr.length; index++) {
          for (let sec = 0; sec < data.days.length; sec++) {
            if (arr[index].name === data.days[sec]) {
              arr[index].selected = true;
              break;
            } else {
              arr[index].selected = false;
            }
          }
        }

        for (let [index, e] of data.days.entries()) {
          if (index == data.days.length - 1) {
            days += e;
          } else {
            days += e + ", ";
          }
        }

        this.setState({
          isDayWeek: true, isLowerRecur: true, repeatEvery: true,
          selectedRecur: 'Week',
          times: data.repeatEveryCount != null ? data.repeatEveryCount : 1,
          selectedDaysOccurs: days,
          daysList: arr
        })
      } else if (data.recurrence == 'Monthly') {
        let bool = false;
        let secondOption = '';
        if (data.chosenDateOfMonth) {
          bool = true;
        } else {
          secondOption = data.monthlyRepeatType != null ? data.monthlyRepeatType : '';
        }

        this.setState({
          isDayWeek: false,
          isLowerRecur: true,
          repeatEvery: true,
          selectedRecur: 'Month',
          isMonth: true,
          isMonthFirstCheck: bool,
          monthAndYearCheck: secondOption,
          times: data.repeatEveryCount != null ? data.repeatEveryCount : 1
        })
      }
      else if (data.recurrence == 'Yearly') {
        let bool = false;
        let secondOption = '';
        if (data.chosenDateOfMonth) {
          bool = true;
        } else {
          secondOption = data.monthlyRepeatType != null ? data.monthlyRepeatType : '';
        }

        this.setState({
          isDayWeek: false,
          isMonth: false,
          isYear: true,
          isLowerRecur: true,
          repeatEvery: false,
          selectedRecur: 'Yearly',
          isYearFirstCheck: bool,
          monthAndYearCheck: secondOption,
          //times:data.repeatEveryCount!=null?data.repeatEveryCount:1
        })
      }

      // Use moment.parseZone to preserve timezone information
      const startTimeMoment = moment.parseZone(data.startTime);
      const endTimeMoment = moment.parseZone(data.endTime);
      const fromDateMoment = moment.parseZone(data.fromDate);
      const toDateMoment = moment.parseZone(data.toDate);

      const fullYearFrom = fromDateMoment.year();
      const fullMonthFrom = fromDateMoment.month();
      const startDateFrom = fromDateMoment.date();
      const delStartTime = new Date(
        fullYearFrom,
        fullMonthFrom,
        startDateFrom,
        startTimeMoment.hour(),
        startTimeMoment.minute()
      );
      const fullYearTo = toDateMoment.year();
      const fullMonthTo = toDateMoment.month();
      const endDateTo = toDateMoment.date();
      const delEndTime = new Date(
        fullYearTo,
        fullMonthTo,
        endDateTo,
        endTimeMoment.hour(),
        endTimeMoment.minute()
      );

      selectedStartTime = delStartTime;
      selectedEndTime = delEndTime;

      // Parse dates without timezone conversion to avoid date shifting
      selectedFromDate = data.fromDate != null ? this.parseLocalDate(data.fromDate) : new Date();
      selectedToDate = data.toDate != null ? this.parseLocalDate(data.toDate) : new Date();

      this.setState({
        description: data.description != null ? data.description : '',
        selectedTimeZone: item != null ? item[0].value : '',
        selectedTimeZoneId: item != null ? item[0].id : '',
        eventStartDate: data.startTime,
        eventEndDate: data.endTime,
        isAllDay: data.isAllDay != null ? data.isAllDay : false,
        isDeilveryCalendar: data.isApplicableToDelivery != null ? data.isApplicableToDelivery : false,
        isCraneCalendar: data.isApplicableToCrane != null ? data.isApplicableToCrane : false,
        isConcreteCalendar: data.isApplicableToConcrete != null ? data.isApplicableToConcrete : false,
        isInspectionCalendar: data.isApplicableToInspection != null ? data.isApplicableToInspection : false,
        recurrence: data.recurrence != null ? data.recurrence : 'Does Not Repeat',
        fromDate: data.fromDate != null ? moment(this.parseLocalDate(data.fromDate)).format('MM/DD/YYYY') : moment(new Date()).format('MM/DD/YYYY'),
        calFromDate: data.fromDate != null ? this.parseLocalDate(data.fromDate) : new Date(),
        toDate: data.toDate != null ? moment(this.parseLocalDate(data.toDate)).format('MM/DD/YYYY') : moment(new Date()).format('MM/DD/YYYY'),
        calToDate: data.toDate != null ? this.parseLocalDate(data.toDate) : new Date(),
        selectedStartTime: data.startTime != null ? moment(data.startTime).format('hh:mm A') : moment(new Date()).format('hh:mm A'),
        selectedEndTime: data.endTime != null ? moment(data.endTime).format('hh:mm A') : moment(new Date()).format('hh:mm A'),
        // Need for future purpose
        // calFromStartTime:moment(data.startTime).format('hh:mm A'),
        // calToEndTime:data.endTime!=null?new Date(data.endTime):new Date(),
        editStartTime: data.isAllDay != null ? !data.isAllDay : false,
        editEndTime: data.isAllDay != null ? !data.isAllDay : false,
        showLoader: false,
      });

      // Call onMonthChanged separately to avoid blocking state update
      if (this.onMonthChanged) {
        setTimeout(() => this.onMonthChanged(), 0);
      }
    }
    this.props.editEventData({})

  }

  // New method to set equipment and gate after lists are populated
  setEditEquipmentAndGate = (data) => {
    // Set gate data and update gate list selections
    if (data.GateId) {
      let gateIds = data.GateId;
      // Parse GateId if it's a string or array
      if (typeof gateIds === 'string') {
        try {
          // Remove brackets and split by comma
          const gateIdStr = gateIds.replace(/[\[\]]/g, '');
          gateIds = gateIdStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
        } catch (error) {
          console.error('Error parsing GateId:', error);
          gateIds = [];
        }
      } else if (!Array.isArray(gateIds)) {
        gateIds = [gateIds];
      }

      // Update gate list with selected status
      const updatedGateList = this.state.gateList.map(gate => ({
        ...gate,
        selected: gateIds.includes(gate.id)
      }));

      this.setState({
        gateList: updatedGateList,
        selectedGateList: gateIds,
        selectedGateId: gateIds.length > 0 ? gateIds.join(',') : null
      }, () => {
        // Force update the MultiSelectDropDown component for gates
        if (this.gateMultiSelectRef) {
          this.gateMultiSelectRef.setState({
            dataItems: updatedGateList,
            isAllChecked: updatedGateList.length > 0 && updatedGateList.every(item => item.selected)
          });
        }
      });
    }

    // Set equipment data
    if (data.EquipmentId) {
      let equipmentIds = [];
      try {
        const equipmentIdStr = data.EquipmentId.replace(/[\[\]]/g, '');
        equipmentIds = equipmentIdStr.split(',').map(id => parseInt(id.trim()));
      } catch (error) {
        console.error('Error parsing EquipmentId:', error);
      }

      const updatedEquipList = this.state.equipTypeList.map(equip => {
        let isSelected = equipmentIds.includes(equip.id);
        if ((equipmentIds.includes(0) && equip.id === 0) || (equipmentIds.includes(-1) && equip.id === -1)) {
          isSelected = true;
        }
        return {
          ...equip,
          selected: isSelected
        };
      });

      this.setState({
        equipTypeList: updatedEquipList,
        selectedEquipmentList: equipmentIds
      }, () => {
        if (this.multiSelectRef) {
          this.multiSelectRef.setState({
            dataItems: updatedEquipList,
            isAllChecked: updatedEquipList.every(item => item.selected)
          });
        }
      });
    }
  }

  processDate = (date) => {

    let startHours = new Date(date).getHours();
    let startMinutes = new Date(date).getMinutes();

    const ampm = startHours >= 12 ? "PM" : "AM";

    startHours %= 12;
    startHours = startHours || 12;
    startMinutes =
      startMinutes < 10 ? `0${startMinutes}` : startMinutes;
    const strTime = `${startHours}:${startMinutes} ${ampm}`;

    return strTime;
  }
  getInitialTimeZone = () => {
    getTimeZone(`${GET_SINGLE_PROJECT}/${this.props.projectDetails.id}`, {}, () => null, (response) => {
      if (response.status) {
        if (response.status == 200) {
          let temp = response.data.data != null ? response.data.data.TimeZoneId : '';
          this.timeZone(temp);
          // Force re-render after timezone is loaded
          setTimeout(() => this.forceUpdate(), 100);
        }
      }

    }
    )
  }

  // Async version for proper sequencing
  getInitialTimeZoneAsync = () => {
    return new Promise((resolve) => {
      getTimeZone(`${GET_SINGLE_PROJECT}/${this.props.projectDetails.id}`, {}, () => null, (response) => {
        if (response.status && response.status == 200) {
          let temp = response.data.data != null ? response.data.data.TimeZoneId : '';
          this.timeZoneAsync(temp).then(resolve);
        } else {
          resolve();
        }
      });
    });
  }

  // Helper function to extract timezone offset from selectedTimeZone string
  getTimezoneOffset = () => {
    const timeZoneString = this.state.selectedTimeZone;
    if (!timeZoneString) return '+00:00';

    // Extract offset from strings like "(UTC-10:00) Hawaii" or "(UTC+05:30) India"
    const offsetMatch = timeZoneString.match(/\(UTC([+-]\d{1,2}:\d{2})\)/);
    if (offsetMatch) {
      return offsetMatch[1];
    }
    return '+00:00';
  };

  // Helper function to format date with timezone offset
  formatDateWithTimezone = (date) => {
    if (!date) return null;

    const offset = this.getTimezoneOffset();

    // Use local date components to avoid timezone conversion issues
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${offset}`;
  };

  getLocationDetail = () => {
    let URL = `${GET_LOCATION_DETAILS}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`;
    getLocationSettings(
      URL,
      {},
      () => null,
      (response) => {

        // Process location data
        let defaultLocation = {};
        let locationDropdownList = [];
        let fullLocationData = []; // Store full location data including equipment and gates

        response.data.data.forEach((item) => {
          // Build location dropdown
          locationDropdownList.push({
            id: item.id,
            label: item.locationPath,
            value: item.locationPath,
            name: item.locationPath,
            selected: false, // Add selected property for multi-select
          });
          fullLocationData.push(item);

          // Set default location
          if (item.isDefault) {
            defaultLocation = {
              id: item.id,
              label: item.locationPath,
              value: item.locationPath,
              name: item.locationPath,
              selected: true, // Mark default as selected
            };
          }
        });

        // Update state with all data
        this.setState({
          locationDropdownList,
          selectedLocationNew: defaultLocation?.value || null,
          selectedLocationId: defaultLocation?.id || null,
          selectedLocationList: defaultLocation?.id ? [defaultLocation.id] : [],
          selectedLocationNames: defaultLocation?.value ? [defaultLocation.value] : [],
          fullLocationData, // Store full data for equipment and gate filtering
        }, () => {
          // Now filter equipment and gates based on the selected location ID
          this.filterEquipmentAndGatesByLocation(false);
        });
      }
    );
  };

  // Async version for proper sequencing
  getLocationDetailAsync = () => {
    return new Promise((resolve) => {
      let URL = `${GET_LOCATION_DETAILS}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`;
      getLocationSettings(URL, {}, () => null, (response) => {
        let defaultLocation = {};
        let locationDropdownList = [];
        let fullLocationData = [];

        response.data.data.forEach((item) => {
          locationDropdownList.push({
            id: item.id,
            label: item.locationPath,
            value: item.locationPath,
            name: item.locationPath,
            selected: false, // Add selected property for multi-select
          });
          fullLocationData.push(item);

          if (item.isDefault) {
            defaultLocation = {
              id: item.id,
              label: item.locationPath,
              value: item.locationPath,
              name: item.locationPath,
              selected: true, // Mark default as selected
            };
          }
        });

        this.setState({
          locationDropdownList,
          selectedLocationNew: defaultLocation?.value || null,
          selectedLocationId: defaultLocation?.id || null,
          selectedLocationList: defaultLocation?.id ? [defaultLocation.id] : [],
          selectedLocationNames: defaultLocation?.value ? [defaultLocation.value] : [],
          fullLocationData,
        }, () => {
          this.filterEquipmentAndGatesByLocation(false);
          resolve();
        });
      });
    });
  };

  filterEquipmentAndGatesByLocation = (allLocationsSelected = false) => {
    const { selectedLocationList, fullLocationData, editIN, selectedEquipTypeId, selectedGateId, selectedGateList } = this.state;
    console.log('filterEquipmentAndGatesByLocation called with allLocationsSelected:', allLocationsSelected);

    if (!selectedLocationList || selectedLocationList.length === 0 || !fullLocationData || !fullLocationData.length) {
      return;
    }

    let equipTypeList = [];
    let storeEquipmentList = [];
    let gateList = [];

    // Find all selected locations from stored data
    const selectedLocations = fullLocationData.filter(item => selectedLocationList.includes(item.id));

    // Filter equipment based on all selected locations
    const equipmentMap = new Map(); // Use Map to avoid duplicates
    
    // Add "No Equipment Needed" options first to the map
    const NoEquipmentOption1 = {
      ...NO_EQUIPMENT_NEEDED,
      visible: true, // Initially visible
      disabled: false // Initially enabled
    };
    equipmentMap.set(NoEquipmentOption1.id, NoEquipmentOption1);

    
    selectedLocations.forEach(selectedLocation => {
      if (selectedLocation && selectedLocation.EquipmentId && Array.isArray(selectedLocation.EquipmentId)) {
        selectedLocation.EquipmentId.forEach((equip) => {
          // Check if this equipment was previously selected (for edit mode) or if all locations are selected
          let isSelected = false;
          if (editIN && selectedEquipTypeId) {
            const selectedIds = typeof selectedEquipTypeId === 'string' ?
              selectedEquipTypeId.split(',').map(id => id.trim()) :
              [String(selectedEquipTypeId)];
            isSelected = selectedIds.some(id => String(id) === String(equip.id));
          } else if (allLocationsSelected) {
            // Auto-select all equipment when all locations are selected
            isSelected = true;
          }

          // Only add if not already in map (avoid duplicates)
          if (!equipmentMap.has(equip.id)) {
            equipmentMap.set(equip.id, {
              id: equip.id,
              value: equip.equipmentName,
              name: equip.equipmentName,
              label: equip.equipmentName,
              selected: isSelected,
              visible: true, // Initially visible
              disabled: false, // Initially enabled
              isCrane: equip.PresetEquipmentType?.isCraneType || false,
            });
            storeEquipmentList.push(equip);
          }
        });
      }
    });
    
    // Convert map to array
    equipTypeList = Array.from(equipmentMap.values());

    // Filter gates based on all selected locations
    const gateMap = new Map(); // Use Map to avoid duplicates
    
    selectedLocations.forEach(selectedLocation => {
      if (selectedLocation && selectedLocation.gateDetails && Array.isArray(selectedLocation.gateDetails)) {
        selectedLocation.gateDetails.forEach(gate => {
          // Check if this gate was previously selected (for edit mode) or if all locations are selected
          let isSelected = false;
          if (editIN) {
            // Check against selectedGateList array first
            if (selectedGateList && selectedGateList.length > 0) {
              isSelected = selectedGateList.some(id => String(id) === String(gate.id));
            } else if (selectedGateId) {
              // Fallback to selectedGateId string parsing
              const selectedIds = typeof selectedGateId === 'string' ?
                selectedGateId.split(',').map(id => id.trim()) :
                [String(selectedGateId)];
              isSelected = selectedIds.some(id => String(id) === String(gate.id));
            }
          } else if (allLocationsSelected) {
            // Auto-select all gates when all locations are selected
            isSelected = true;
          }

          // Only add if not already in map (avoid duplicates)
          if (!gateMap.has(gate.id)) {
            gateMap.set(gate.id, {
              id: gate.id,
              name: gate.gateName,
              value: gate.gateName,
              label: gate.gateName,
              selected: isSelected,
            });
          }
        });
      }
    });
    
    // Convert map to array
    gateList = Array.from(gateMap.values());

    // Update equipment and gate lists
    const deduplicatedGateList = [...new Map(gateList.map(item => [item.id, item])).values()];

    // Prepare state updates
    const stateUpdates = {
      equipTypeList: [...new Map(equipTypeList.map(item => [item.id, item])).values()],
      storeEquipmentList,
      gateList: deduplicatedGateList,
    };

    // If all locations are selected, auto-select all equipment and gates
    if (allLocationsSelected) {
      const selectedEquipmentIds = equipTypeList.filter(item => item.selected).map(item => item.id);
      const selectedGateIds = deduplicatedGateList.filter(item => item.selected).map(item => item.id);
      
      stateUpdates.selectedEquipmentList = selectedEquipmentIds;
      stateUpdates.selectedGateList = selectedGateIds;
      
      // Set the first selected equipment and gate for backward compatibility
      if (selectedEquipmentIds.length > 0) {
        const firstSelectedEquipment = equipTypeList.find(item => item.id === selectedEquipmentIds[0]);
        stateUpdates.selectedEquipName = firstSelectedEquipment?.name || null;
        stateUpdates.selectedEquipTypeId = selectedEquipmentIds[0];
      }
      
      if (selectedGateIds.length > 0) {
        stateUpdates.selectedGateId = selectedGateIds[0];
      }
    }

    this.setState(stateUpdates, () => {
      // Update the MultiSelectDropDown components based on location selection
      if (allLocationsSelected) {
        // Update equipment MultiSelectDropDown to show "Select All" as checked
        if (this.multiSelectRef) {
          this.multiSelectRef.setState({
            dataItems: stateUpdates.equipTypeList,
            isAllChecked: true
          });
        }
        
        // Update gate MultiSelectDropDown to show "Select All" as checked
        if (this.gateMultiSelectRef) {
          this.gateMultiSelectRef.setState({
            dataItems: stateUpdates.gateList,
            isAllChecked: true
          });
        }
      } else {
        // When not all locations are selected, reset "Select All" state
        if (this.multiSelectRef) {
          this.multiSelectRef.setState({
            dataItems: stateUpdates.equipTypeList,
            isAllChecked: false
          });
        }
        
        if (this.gateMultiSelectRef) {
          this.gateMultiSelectRef.setState({
            dataItems: stateUpdates.gateList,
            isAllChecked: false
          });
        }
      }
      
      if (this.state.editIN) {
        if (equipTypeList.length === 0) {
          this.setState({
            selectedEquipName: null,
            selectedEquipTypeId: null,
            selectedEquipmentList: [],
          });
        }
        if (deduplicatedGateList.length === 0) {
          this.setState({
            selectedGate: [],
            selectedGateList: [],
            selectedGateId: null
          });
        } else {
          // Update selected gate list from gate list selections
          const selectedGates = deduplicatedGateList.filter(gate => gate.selected);
          const selectedGateIds = selectedGates.map(gate => gate.id);
          this.setState({
            selectedGateList: selectedGateIds,
            selectedGateId: selectedGateIds.length > 0 ? selectedGateIds.join(',') : null
          }, () => {
            // Force update the MultiSelectDropDown component for gates
            if (this.gateMultiSelectRef) {
              this.gateMultiSelectRef.setState({
                dataItems: this.state.gateList,
                isAllChecked: this.state.gateList.length > 0 && this.state.gateList.every(item => item.selected)
              });
            }
          });
        }
      }
    });
  };

  autoSelectAllLocationsAndGates = () => {
    // Auto-select all locations
    const updatedLocationList = this.state.locationDropdownList.map(location => ({
      ...location,
      selected: true
    }));

    const allLocationIds = updatedLocationList.map(location => location.id);
    const allLocationNames = updatedLocationList.map(location => location.value);

    // Update location state
    this.setState({
      locationDropdownList: updatedLocationList,
      selectedLocationList: allLocationIds,
      selectedLocationNames: allLocationNames,
    }, () => {
      // Force a re-render of the location MultiSelectDropDown component
      if (this.locationMultiSelectRef) {
        this.locationMultiSelectRef.setState({
          dataItems: updatedLocationList,
          isAllChecked: true
        });
      }
      
      // Now filter equipment and gates with all locations selected
      this.filterEquipmentAndGatesByLocation(true);
    });
  }

  resetLocationsToFirstSelected = () => {
    // Reset locations to only the first selected location (if any)
    const firstSelectedLocation = this.state.locationDropdownList.find(location => location.selected);
    
    if (firstSelectedLocation) {
      const updatedLocationList = this.state.locationDropdownList.map(location => ({
        ...location,
        selected: location.id === firstSelectedLocation.id
      }));

      this.setState({
        locationDropdownList: updatedLocationList,
        selectedLocationList: [firstSelectedLocation.id],
        selectedLocationNames: [firstSelectedLocation.value],
      }, () => {
        // Force a re-render of the location MultiSelectDropDown component
        if (this.locationMultiSelectRef) {
          this.locationMultiSelectRef.setState({
            dataItems: updatedLocationList,
            isAllChecked: false
          });
        }
        
        // Filter equipment and gates based on the single selected location
        this.filterEquipmentAndGatesByLocation(false);
      });
    }
  }

  getSelectedGateList = (data) => {
    if (data && data.length > 0) {
      // Create a new array with the updated selection state
      let updatedList = data.map(item => {
        if (item && typeof item.selected !== 'undefined') {
          return {
            ...item,
            selected: item.selected,
          };
        }
        return item;
      });

      // Get selected gates
      const selectedGates = updatedList.filter(item => item && item.selected === true);
      const selectedGateIds = selectedGates.map(item => item.id);

      // Update the state with the new data
      this.setState({
        gateList: updatedList,
        selectedGateList: selectedGateIds,
        selectedGateId: selectedGateIds.length > 0 ? selectedGateIds.join(',') : null
      }, () => {
        // Force a re-render of the MultiSelectDropDown component
        if (this.gateMultiSelectRef) {
          this.gateMultiSelectRef.setState({
            dataItems: updatedList,
            isAllChecked: updatedList.length > 0 && updatedList.every(item => item.selected === true)
          });
        }
      });
    } else {
      // Handle case where no data is provided
      this.setState({
        gateList: [],
        selectedGateList: [],
        selectedGateId: null
      });
    }
  };

  getSelectedLocationList = (data) => {
    if (data && data.length > 0) {
      // Create a new array with the updated selection state
      let updatedList = data.map(item => {
        if (item && typeof item.selected !== 'undefined') {
          return {
            ...item,
            selected: item.selected,
          };
        }
        return item;
      });

      // Get selected locations
      const selectedLocations = updatedList.filter(item => item && item.selected === true);
      const selectedLocationIds = selectedLocations.map(item => item.id);
      const selectedLocationNames = selectedLocations.map(item => item.value);

      // Check if all locations are selected
      const allLocationsSelected = selectedLocations.length === data.length && data.length > 0;
      console.log('Location selection - allLocationsSelected:', allLocationsSelected, 'selectedLocations.length:', selectedLocations.length, 'data.length:', data.length);

      // Update the state with the new data
      this.setState({
        locationDropdownList: updatedList,
        selectedLocationList: selectedLocationIds,
        selectedLocationNames: selectedLocationNames,
        // Reset equipment and gate selections when location changes (unless all locations are selected)
        selectedGate: allLocationsSelected ? this.state.selectedGate : [],
        selectedGateList: allLocationsSelected ? this.state.selectedGateList : [],
        selectedGateId: allLocationsSelected ? this.state.selectedGateId : null,
        selectedEquipName: allLocationsSelected ? this.state.selectedEquipName : null,
        selectedEquipTypeId: allLocationsSelected ? this.state.selectedEquipTypeId : null,
        selectedEquipmentList: allLocationsSelected ? this.state.selectedEquipmentList : [],
        equipTypeList: [],
        gateList: [],
        storeEquipmentList: [],
      }, () => {
        // Filter equipment and gates based on the newly selected locations
        this.filterEquipmentAndGatesByLocation(allLocationsSelected);
      });
    } else {
      // Handle case where no data is provided
      this.setState({
        locationDropdownList: [],
        selectedLocationList: [],
        selectedLocationNames: [],
        selectedGate: [],
        selectedGateList: [],
        selectedGateId: null,
        selectedEquipName: null,
        selectedEquipTypeId: null,
        selectedEquipmentList: [],
        equipTypeList: [],
        gateList: [],
        storeEquipmentList: [],
      });
    }
  }

  getSelectedEquipmentList = (data) => {
    if (data && data.length > 0) {

      // Create a new array with the updated selection state
      let updatedList = data.map(item => {
        if (item && typeof item.selected !== 'undefined') {
          return {
            ...item,
            selected: item.selected,
            disabled: false // Initialize disabled state
          };
        }
        return item;
      });

      // Check if "No Equipment Needed" is selected (either option)
      const noEquipmentNeeded = updatedList.find(item => item && (item.id === 0 || item.id === -1)); // NO_EQUIPMENT_NEEDED has id: 0 or -1
      const otherEquipmentSelected = updatedList.some(item => item && item.id !== 0 && item.id !== -1 && item.selected === true);

      // Check if "Select All" scenario (all non-"No Equipment" items selected)
      const nonNoEquipmentItems = updatedList.filter(item => item && item.id !== 0 && item.id !== -1);
      const allOtherEquipmentSelected = nonNoEquipmentItems.length > 0 &&
        nonNoEquipmentItems.every(item => item && item.selected === true);

      // Apply mutual exclusion logic
      if (noEquipmentNeeded && noEquipmentNeeded.selected === true) {
        // RULE 3: When "No Equipment Needed" is selected
        // - Deselect all other equipment options
        // - Disable all other equipment options
        updatedList = updatedList.map(item => {
          if (item && typeof item.id !== 'undefined') {
            if (item.id === 0 || item.id === -1) {
              return {
                ...item,
                selected: true,
                disabled: false,
                visible: true
              };
            } else {
              return {
                ...item,
                selected: false, // Automatically deselect
                disabled: true,  // Disable other options when "No Equipment" is selected
                visible: true
              };
            }
          }
          return item;
        });
      } else if (allOtherEquipmentSelected && !noEquipmentNeeded?.selected) {
        // RULE 1: When "Select All" is chosen
        // - Select all equipment EXCEPT "No Equipment Needed"
        // - Hide "No Equipment Needed" from visible options
        // - Auto-select all locations and gates
        updatedList = updatedList.map(item => {
          if (item && typeof item.id !== 'undefined') {
            if (item.id === 0 || item.id === -1) {
              return {
                ...item,
                selected: false,
                disabled: false,
                visible: false // Hide from visible options
              };
            } else {
              return {
                ...item,
                selected: true,
                disabled: false,
                visible: true
              };
            }
          }
          return item;
        });

        // Auto-select all locations when equipment "Select All" is chosen
        this.autoSelectAllLocationsAndGates();
      } else if (otherEquipmentSelected) {
        // RULE 4: When any individual equipment is selected
        // - Automatically unselect "No Equipment Needed"
        // - Keep "No Equipment Needed" visible but unselected
        // - Allow multiple selection of other equipment
        updatedList = updatedList.map(item => {
          if (item && typeof item.id !== 'undefined') {
            if (item.id === 0 || item.id === -1) {
              return {
                ...item,
                selected: false, // Automatically unselect
                disabled: false,
                visible: true    // Keep visible but unselected
              };
            } else {
              return {
                ...item,
                disabled: false,
                visible: true
              };
            }
          }
          return item;
        });
      } else {
        // RULE 2: When "Select All" is unselected or no selections
        // - Show all equipment options including "No Equipment Needed"
        // - Enable all options
        // - Reset locations to first selected location (if any)
        updatedList = updatedList.map(item => {
          if (item && typeof item.id !== 'undefined') {
            return {
              ...item,
              disabled: false,
              visible: true
            };
          }
          return item;
        });

        // Reset locations when equipment "Select All" is unselected
        this.resetLocationsToFirstSelected();
      }

      const selectedEquipment = updatedList.filter(item => item && item.selected === true);

      // Get selected equipment IDs
      const selectedIds = selectedEquipment.map(item => item.id);

      // Update the state with the new data
      this.setState({
        equipTypeList: updatedList,
        selectedEquipmentList: selectedIds,
        isAssociatedWithCraneRequest: selectedEquipment.some(item => item && item.isCrane === true && item.selected === true) ? true : false
      }, () => {
        // Force a re-render of the MultiSelectDropDown component
        if (this.multiSelectRef) {
          this.multiSelectRef.setState({
            dataItems: updatedList,
            isAllChecked: allOtherEquipmentSelected && !noEquipmentNeeded?.selected
          });
        }
      });
    } else {
      // Handle case where no data is provided
      this.setState({
        equipTypeList: [],
        selectedEquipmentList: [],
        isAssociatedWithCraneRequest: false
      });
    }
  }

  // Helper method to update timezone state immediately
  updateTimezoneState = (timeZoneValue, timeZoneId) => {
    this.setState({
      selectedTimeZone: timeZoneValue,
      selectedTimeZoneId: timeZoneId,
    }, () => {
      // Force immediate re-render
      this.forceUpdate();
    });
  };

  timeZone = (timeZone) => {
    getTimeZone(GET_TIMEZONE, {}, () => null, (response) => {
      if (response.status) {
        if (response.status == 200) {
          let list = [];
          response.data.data.forEach(e => {
            let temp = {};
            temp = {
              id: e.id,
              value: e.location,
              label: e.location,
              selected: false,
              name: e.location,
            }
            list.push(temp)
          });
          let item = null;
          if (timeZone != null) {
            item = list.filter(element => {
              return element.id === timeZone;
            });
          }
          // Update state immediately without callback to ensure fast rendering
          this.setState({
            showLoader: false,
            timeZoneList: list,
            selectedTimeZone: item != null ? item[0].value : null,
            selectedTimeZoneId: item != null ? item[0].id : '',
          });

          // Call checkEdit separately to avoid blocking state update
          if (this.checkEdit) {
            setTimeout(() => this.checkEdit(), 0);
          }

          // Force immediate re-render for timezone display
          setTimeout(() => this.forceUpdate(), 50);
        }
      }
    })
  }

  // Async version for proper sequencing
  timeZoneAsync = (timeZone) => {
    return new Promise((resolve) => {
      getTimeZone(GET_TIMEZONE, {}, () => null, (response) => {
        if (response.status && response.status == 200) {
          let list = [];
          response.data.data.forEach(e => {
            list.push({
              id: e.id,
              value: e.location,
              label: e.location,
              selected: false,
              name: e.location,
            });
          });

          let item = null;
          if (timeZone != null) {
            item = list.filter(element => element.id === timeZone);
          }

          this.setState({
            showLoader: false,
            timeZoneList: list,
            selectedTimeZone: item != null ? item[0].value : null,
            selectedTimeZoneId: item != null ? item[0].id : '',
          }, resolve);
        } else {
          resolve();
        }
      });
    });
  }

  responseEdit = (response) => {
    mixPanelTrackEvent(Strings.mixpanel.editCalendarEvent, this.state.mixpanelParam)
    this.showToaster("success", response.data.message)
    this.props.refreshCalendarSettings(true);
    this.props.refreshEventDisplay(true);
    this.props.navigation.pop(2);

  }
  submit = () => {
    Keyboard.dismiss();
    if (
      isEmpty(this.state.description) ||
      this.state.description.trim() == ""
    ) {
      this.showToaster("error", Strings.errors.emptyDescription);
    } else if (this.state.description.length < 3) {
      this.showToaster("error", "Description " + Strings.errors.lengthError);
    } else if (!this.state.isDeilveryCalendar && !this.state.isCraneCalendar && !this.state.isConcreteCalendar && !this.state.isInspectionCalendar) {
      this.showToaster("error", Strings.errors.selectCalendar);
    } else if (this.state.recurrence === 'Select Recurrence') {
      this.showToaster("error", Strings.errors.recurrence);
    } else if (this.state.calFromDate === new Date()) {
      this.showToaster("error", Strings.errors.futureDate)
    } else if (!this.state.isInformationOnlyEvent && (!this.state.selectedLocationList || this.state.selectedLocationList.length === 0)) {
      this.showToaster("error", "Please select at least one location");
    }
    //  else if (!this.state.isInformationOnlyEvent && (!this.state.selectedGateList || this.state.selectedGateList.length === 0)) {
    //   this.showToaster("error", "Please select at least one gate");
    // } else if (!this.state.isInformationOnlyEvent && (!this.state.selectedEquipmentList || this.state.selectedEquipmentList.length === 0)) {
    //   this.showToaster("error", "Please select at least one equipment");
    // } 
  else {
      let startTime = new Date(
        selectedFromDate.getFullYear(),
        selectedFromDate.getMonth(),
        selectedFromDate.getDate(),
        new Date(selectedStartTime).getHours(),
        new Date(selectedStartTime).getMinutes(),
        0
      );
      if (startTime < new Date()) {
        this.showToaster("error", Strings.errors.futureDate);
      } else if (this.state.calToDate < this.state.calFromDate) {
        this.showToaster("error", Strings.errors.errorEnddate);
      }
      else if (this.state.calToEndTime < this.state.calFromStartTime) {
        this.showToaster("error", Strings.errors.errorEndTime);
      }
      else if (this.state.recurrence !== 'Does Not Repeat' && this.state.recurrence !== 'Daily' && this.state.selectedEndDate < this.state.calFromDate) {
        this.showToaster("error", Strings.errors.errorEnddate);
      }
      else {
        this.setState({
          showLoader: true,
          disableSubmit: true,
        });

        // Use direct Date methods to avoid timezone conversion issues
        // This ensures 12 AM (midnight) is formatted as "00:00" not "12:00"
        const formatTimeToHHMM = (date) => {
          if (!date) return "00:00";
          const hours = date.getHours();
          const minutes = date.getMinutes();
          return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        };

        let param = {};
        let days = [];
        this.state.daysList.forEach(e => {
          if (e.selected) {
            days.push(e.name)
          }
        });
        if (this.state.recurrence === 'Does Not Repeat') {
          param = {
            chosenDateOfMonth: false,
            dateOfMonth: null,
            description: this.state.description,
            endTime: formatTimeToHHMM(this.state.calToEndTime),
            fromDate: moment(this.state.calFromDate).format('YYYY MM DD 00:00:00'),
            isAllDay: this.state.isAllDay,
            isApplicableToCrane: this.state.isCraneCalendar,
            isApplicableToDelivery: this.state.isDeilveryCalendar,
            isApplicableToConcrete: this.state.isConcreteCalendar,
            isApplicableToInspection: this.state.isInspectionCalendar,
            isInformationOnly: this.state.isInformationOnlyEvent,
            monthlyRepeatType: "",
            recurrence: "Does Not Repeat",
            repeatEveryCount: null,
            repeatEveryType: null,
            startTime: formatTimeToHHMM(this.state.calFromStartTime),
            toDate: moment(this.state.calToDate).format('YYYY MM DD 00:00:00'),
            TimeZoneId: this.state.selectedTimeZoneId,
            createdBy: this.state.roleId,
            endDate: moment(this.state.calToDate).format('YYYY MM DD 00:00:00'),
            LocationId: this.state.isInformationOnlyEvent ? [] : (this.state.selectedLocationList || []),
            EquipmentId: this.state.isInformationOnlyEvent ? [] : this.state.selectedEquipmentList,
            GateId: this.state.isInformationOnlyEvent ? [] : (this.state.selectedGateList || []),
          }
        } else if (this.state.recurrence === 'Monthly') {
          let repeat = '';
          if (this.state.isMonthFirstCheck || this.state.isMonthSecondCheck) {
            repeat = this.state.monthlyDay;
          } else if (this.state.isMonthThirdCheck) {
            repeat = `${this.state.monthlyLastDay} ${moment(this.state.fromDate).format("dddd")}`;
          }
          param = {
            description: this.state.description,
            fromDate: moment(this.state.calFromDate).format('YYYY MM DD 00:00:00'),
            toDate: moment(this.state.calToDate).format('YYYY MM DD 00:00:00'),
            startTime: formatTimeToHHMM(this.state.calFromStartTime),
            endTime: formatTimeToHHMM(this.state.calToEndTime),
            isAllDay: this.state.isAllDay,
            isApplicableToDelivery: this.state.isDeilveryCalendar,
            isApplicableToCrane: this.state.isCraneCalendar,
            isApplicableToConcrete: this.state.isConcreteCalendar,
            isApplicableToInspection: this.state.isInspectionCalendar,
            recurrence: "Monthly",
            chosenDateOfMonth: this.state.isMonthFirstCheck,
            dateOfMonth: moment(this.state.calFromDate).format("DD"),
            monthlyRepeatType: repeat,
            repeatEveryType: this.state.times > 1 ? "Months" : "Month",
            repeatEveryCount: this.state.times.toString(),
            TimeZoneId: this.state.selectedTimeZoneId,
            createdBy: this.state.roleId,
            endDate: moment(this.state.selectedEndDate).format('YYYY MM DD 00:00:00'),
            LocationId: this.state.isInformationOnlyEvent ? [] : (this.state.selectedLocationList || []),
            EquipmentId: this.state.isInformationOnlyEvent ? [] : this.state.selectedEquipmentList,
            GateId: this.state.isInformationOnlyEvent ? [] : (this.state.selectedGateList || []),
          }
        } else if (this.state.recurrence === 'Yearly') {
          let repeat = '';
          if (this.state.isYearFirstCheck || this.state.isYearSecondCheck) {
            repeat = this.state.monthlyDay;
          } else if (this.state.isYearThirdCheck) {
            repeat = `${this.state.monthlyLastDay} ${moment(this.state.fromDate).format("dddd")}`;
          }

          param = {
            description: this.state.description,
            fromDate: moment(this.state.calFromDate).format('YYYY MM DD 00:00:00'),
            toDate: moment(this.state.calToDate).format('YYYY MM DD 00:00:00'),
            startTime: formatTimeToHHMM(this.state.calFromStartTime),
            endTime: formatTimeToHHMM(this.state.calToEndTime),
            isAllDay: this.state.isAllDay,
            isApplicableToDelivery: this.state.isDeilveryCalendar,
            isApplicableToCrane: this.state.isCraneCalendar,
            isApplicableToConcrete: this.state.isConcreteCalendar,
            isApplicableToInspection: this.state.isInspectionCalendar,
            recurrence: "Yearly",
            chosenDateOfMonth: this.state.isYearFirstCheck,
            dateOfMonth: moment(this.state.calFromDate).format("DD"),
            monthlyRepeatType: repeat,
            repeatEveryType: this.state.times > 1 ? "Years" : "Year",
            repeatEveryCount: this.state.times.toString(),
            TimeZoneId: this.state.selectedTimeZoneId,
            createdBy: this.state.roleId,
            endDate: moment(this.state.selectedEndDate).format('YYYY MM DD 00:00:00'),
            LocationId: this.state.isInformationOnlyEvent ? [] : (this.state.selectedLocationList || []),
            EquipmentId: this.state.isInformationOnlyEvent ? [] : this.state.selectedEquipmentList,
            GateId: this.state.isInformationOnlyEvent ? [] : (this.state.selectedGateList || []),
          }
        } else if (this.state.recurrence === 'Daily') {
          param = {
            description: this.state.description,
            fromDate: moment(this.state.calFromDate).format('YYYY MM DD 00:00:00'),
            toDate: moment(this.state.calToDate).format('YYYY MM DD 00:00:00'),
            startTime: formatTimeToHHMM(this.state.calFromStartTime),
            endTime: formatTimeToHHMM(this.state.calToEndTime),
            isAllDay: this.state.isAllDay,
            isApplicableToDelivery: this.state.isDeilveryCalendar,
            isApplicableToCrane: this.state.isCraneCalendar,
            isApplicableToConcrete: this.state.isConcreteCalendar,
            isApplicableToInspection: this.state.isInspectionCalendar,
            recurrence: "Daily",
            chosenDateOfMonth: false,
            dateOfMonth: null,
            monthlyRepeatType: "",
            repeatEveryType: this.state.times > 1 ? "Days" : "Day",
            repeatEveryCount: this.state.times.toString(),
            days: days,
            TimeZoneId: this.state.selectedTimeZoneId,
            createdBy: this.state.roleId,
            endDate: moment(this.state.calToDate).format('YYYY MM DD 00:00:00'),
            LocationId: this.state.isInformationOnlyEvent ? [] : (this.state.selectedLocationList || []),
            EquipmentId: this.state.isInformationOnlyEvent ? [] : this.state.selectedEquipmentList,
            GateId: this.state.isInformationOnlyEvent ? [] : (this.state.selectedGateList || []),
          }
        } else if (this.state.recurrence === 'Weekly') {
          param = {
            description: this.state.description,
            fromDate: moment(this.state.calFromDate).format('YYYY MM DD 00:00:00'),
            toDate: moment(this.state.calToDate).format('YYYY MM DD 00:00:00'),
            startTime: formatTimeToHHMM(this.state.calFromStartTime),
            endTime: formatTimeToHHMM(this.state.calToEndTime),
            isAllDay: this.state.isAllDay,
            isApplicableToDelivery: this.state.isDeilveryCalendar,
            isApplicableToCrane: this.state.isCraneCalendar,
            isApplicableToConcrete: this.state.isConcreteCalendar,
            isApplicableToInspection: this.state.isInspectionCalendar,
            createdBy: this.state.roleId,
            recurrence: "Weekly",
            chosenDateOfMonth: false,
            dateOfMonth: null,
            monthlyRepeatType: "",
            repeatEveryType: this.state.times > 1 ? "Weeks" : "Week",
            repeatEveryCount: this.state.times.toString(),
            days: days,
            TimeZoneId: this.state.selectedTimeZoneId,
            endDate: moment(this.state.selectedEndDate).format('YYYY MM DD 00:00:00'),
            LocationId: this.state.isInformationOnlyEvent ? [] : (this.state.selectedLocationList || []),
            EquipmentId: this.state.isInformationOnlyEvent ? [] : this.state.selectedEquipmentList,
            GateId: this.state.isInformationOnlyEvent ? [] : (this.state.selectedGateList || []),
          }
        }
        let url = '';
        if (this.state.isEdit) {
          url = `${EDIT_EVENT}/${this.state.editId}?ParentCompanyId=${this.props.projectDetails.ParentCompany.id}&ProjectId=${this.props.projectDetails.id}`;
          putEditEvent(url, param, () => null, (response) => {
            this.setState({
              showLoader: false,
            });

            if (response.status) {
              if (response.status == 201 || response.status == 200) {
                this.responseEdit(response);
              } else if (response.status == 400) {
                this.showToaster("error", response.data.message.message)
              }
              else {
                this.showToaster("error", response.data.message)
              }
            } else {
              this.showToaster("error", "Something Went Wrong")
            }
          })
        }
        else {
          url = `${ADD_EVENT}?ParentCompanyId=${this.props.projectDetails.ParentCompany.id}&ProjectId=${this.props.projectDetails.id}`;
          addEvent(url, param, () => null, (response) => {
            this.setState({
              showLoader: false,
            });
            if (response.status) {
              if (response.status == 201) {
                mixPanelTrackEvent(Strings.mixpanel.addCalendarEvent, this.state.mixpanelParam)
                this.showToaster("success", response.data.message)
                this.props.refreshCalendarSettings(true);
                setTimeout(() => {
                  this.props.navigation.goBack();
                }, 2000)
              } else if (response.status == 400) {
                this.showToaster("error", response.data.message.message)
              }
              else {
                this.showToaster("error", response.data.message)
              }
            } else {
              this.showToaster("error", "Something Went Wrong")
            }
          })
        }
      }

    }
  }

  showToaster = (type, message) => {
    Keyboard.dismiss();
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
        disableSubmit: false,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showToaster: false,
          });
        }, 2000);
      }
    );
  };
  onPressFromDateTF = () => {
    this.setState({
      showDateModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.fromDate) {
        this.onchangeFromDate("", this.state.calFromDate);
      }
    }
  };
  onchangeFromDate = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showDateModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      const fullYear = selectedValue.getFullYear();
      const fullMonth = selectedValue.getMonth();
      const date = selectedValue.getDate();
      let sampleDate = `${fullMonth + 1}/${date}/${fullYear}`;
      this.setState(
        {
          fromDate: moment(sampleDate).format("MM/DD/YYYY"),
          calFromDate: selectedValue,
        }, () => this.onMonthChanged()
      );

      selectedFromDate = selectedValue;

      // Dispatch selected date to Redux so calendar shows the selected date
      this.props.setSelectedCalendarDate(selectedValue);

      // For iOS, no auto-close - let users close manually for better month navigation
    }
  };
  onPressToDateTF = () => {
    this.setState({
      showToDateModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.toDate) {
        this.onchangeToDate("", this.state.calToDate);
      }
    }
  };
  onchangeToDate = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showToDateModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      const fullYear = selectedValue.getFullYear();
      const fullMonth = selectedValue.getMonth();
      const date = selectedValue.getDate();
      let sampleDate = `${fullMonth + 1}/${date}/${fullYear}`;

      this.setState({
        toDate: moment(sampleDate).format("MM/DD/YYYY"),
        calToDate: selectedValue,
      });

      selectedToDate = selectedValue;

      // Dispatch selected date to Redux so calendar shows the selected date
      this.props.setSelectedCalendarDate(selectedValue);

      // For iOS, no auto-close - let users close manually for better month navigation
    }
  };
  onchangeEndDate = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == Strings.datePicker.set || tevent.type == Strings.datePicker.dismissed) {
        this.setState({
          isEndDate: false,
        });
      }
    }

    if (Platform.OS == Strings.platforms.ios || tevent.type == Strings.datePicker.set) {
      const fullYear = selectedValue.getFullYear();
      const fullMonth = selectedValue.getMonth();
      const date = selectedValue.getDate();
      let sampleDate = `${fullMonth + 1}/${date}/${fullYear}`;

      this.setState({
        endDate: moment(sampleDate).format("MM/DD/YYYY"),
        selectedEndDate: selectedValue,
      },);

      selectEndDate = selectedValue;

      // Dispatch selected date to Redux so calendar shows the selected date
      this.props.setSelectedCalendarDate(selectedValue);

      // For iOS, no auto-close - let users close manually for better month navigation
    }
  };
  onPressStartDateTF = () => {
    this.setState({
      showStartTimeModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedStartTime) {
        this.onChangeStart("", this.state.calFromStartTime);
      }
    }
  };

  onChangeStart = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showStartTimeModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      let deliveryDate = selectedFromDate;
      const fullYear = deliveryDate.getFullYear();
      const fullMonth = deliveryDate.getMonth();
      const date = deliveryDate.getDate();
      let hours = selectedValue.getHours();
      let minutes = selectedValue.getMinutes();

      const delStartTime = new Date(fullYear, fullMonth, date, hours, minutes);
      const ampm = hours >= 12 ? "PM" : "AM";

      hours %= 12;
      hours = hours || 12;
      minutes = minutes < 10 ? `0${minutes}` : minutes;

      const strTime = `${hours}:${minutes} ${ampm}`;
      this.setState({
        selectedStartTime: strTime,
        calFromStartTime: selectedValue,
      });

      selectedStartTime = delStartTime;
    }
  };
  onMonthChanged = () => {
    const startDate = moment(this.state.calFromDate).format("YYYY-MM");
    const chosenDay = moment(this.state.calFromDate).format("dddd");
    const day = moment(startDate, "YYYY-MM").startOf("month").day(chosenDay);
    const getAllDays = [];
    if (day.date() > 7) day.add(7, "d");
    const month = day.month();
    while (month === day.month()) {
      getAllDays.push(day.toString());
      day.add(7, "d");
    }
    let week;
    let extraOption;
    getAllDays.forEach((element, i) => {
      if (
        moment(this.state.calFromDate).format("YYYY-MM-DD") ===
        moment(element).format("YYYY-MM-DD")
      ) {
        const number = i + 1;
        if (number === 1) {
          week = "First";
        }
        if (number === 2) {
          week = "Second";
        }
        if (number === 3) {
          week = "Third";
        }
        if (number === 4) {
          extraOption = "Last";
          week = "Fourth";
        }
        if (number === 5) {
          week = "Last";
        }
        if (number === 6) {
          week = "Last";
        }
      }
    });
    let monthlyDayOfWeek = `${week} ${chosenDay}`;
    this.setState({
      monthlyDay: monthlyDayOfWeek,
      monthlyLastDay: extraOption,
    });
    if (this.state.monthAndYearCheck != '') {
      if (this.state.monthAndYearCheck == monthlyDayOfWeek) {
        if (this.state.selectedRecur === 'Month') {
          this.setState({
            isMonthSecondCheck: true,
            isMonthFirstCheck: false,
            isMonthThirdCheck: false,
          })
        } else {
          this.setState({
            isYearFirstCheck: false,
            isYearSecondCheck: true,
            isYearThirdCheck: false,
          })
        }
      } else {
        if (this.state.selectedRecur === 'Month') {
          this.setState({
            isMonthSecondCheck: false,
            isMonthFirstCheck: false,
            isMonthThirdCheck: true,
          })
        } else {
          this.setState({
            isYearFirstCheck: false,
            isYearSecondCheck: false,
            isYearThirdCheck: true,
          })
        }
      }
    }

  };
  onPressEndDateTF = () => {
    this.setState({
      showEndTimeModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedEndTime) {
        this.onChangeEndTime("", this.state.calToEndTime);
      }
    }
  };
  onChangeEndTime = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showEndTimeModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      let deliveryDate = selectedToDate;
      const fullYear = deliveryDate.getFullYear();
      const fullMonth = deliveryDate.getMonth();
      const date = deliveryDate.getDate();
      let hours = selectedValue.getHours();
      let minutes = selectedValue.getMinutes();

      const delEndTime = new Date(fullYear, fullMonth, date, hours, minutes);

      const ampm = hours >= 12 ? "PM" : "AM";

      hours %= 12;
      hours = hours || 12;
      minutes = minutes < 10 ? `0${minutes}` : minutes;

      const endTime = `${hours}:${minutes} ${ampm}`;

      this.setState({
        selectedEndTime: endTime,
        calToEndTime: selectedValue,
      });

      selectedEndTime = delEndTime;
    }
  };

  updateMasterState = (key, value) => {
    if (key == Strings.placeholders.description) {
      this.setState({
        description: value,
      });
    } else if (key == "Number") {
      this.setState({
        times: value,
      });
    }
  };
  setDay = (item, index) => {
    let countTrue = 0;
    let countFalse = 0;
    let updatedList = this.state.daysList;

    for (let lists of updatedList) {
      if (item.name === lists.name) {
        updatedList[index].selected = !lists.selected;
      }
    }
    for (let data of updatedList) {
      if (data.selected) {
        countTrue++;
      } else if (!data.selected) {
        countFalse++;
      }
    }
    if (countTrue == 7) {
      this.setState({
        selectedRecur: 'Day',
        recurrence: "Daily"
      })
    } else {
      this.setState({
        selectedRecur: 'Week',
        recurrence: "Weekly"
      })
    }
    if (countFalse <= 6) {
      this.setState({ daysList: updatedList });
    }
    else {
      for (let lists of updatedList) {
        if (item.name === lists.name) {
          updatedList[index].selected = !lists.selected;
        }
      }
      this.setState({ daysList: updatedList });
    }
    let day = [];
    for (let items of updatedList) {
      if (items.selected === true) {
        day.push({ name: items.name })
      }
    }
    let selectedDays = '';
    for (let [index, days] of day.entries()) {
      if (index === day.length - 1) {
        selectedDays += days.name;
      }
      else {
        selectedDays += days.name + ", ";
      }
    }

    this.setState({ selectedDaysOccurs: selectedDays });
  };

  updateWeek = () => {
    let updateList = this.state.daysList;
    for (let [index, lists] of updateList.entries()) {
      if (index == 1) {
        updateList[1].selected = true;
      } else {
        updateList[index].selected = false;
      }
    }
    this.setState({ daysList: updateList });
  };
  updateDay = () => {
    let updateList = this.state.daysList;
    for (let [index, lists] of updateList.entries()) {
      updateList[index].selected = true;
    }
    this.setState({ daysList: updateList });
  };


  onPressEqipType = (item) => {
    if (item.isCrane) {
      this.getLastCraneId();
    }
    this.setState({
      selectedEquipName: item.value,
      selectedEquipTypeId: item.id,
      eqipModalVisible: false,
      isAssociatedWithCraneRequest: item.isCrane,
    });
    Keyboard.dismiss();
  };

  // onPressGateType - No longer needed with MultiSelectDropDown
  // Function removed as gates now use multi-select dropdown

  renderDay = ({ item, index }) => {
    return (
      <TouchableOpacity onPress={() => this.setDay(item, index)}>
        {/* <Avatar.Text
          size={Platform.OS=='android'?35:40}
          label={item.label}
          color={item.selected ? Colors.white : Colors.lightGrey}
          style={{
            backgroundColor: item.selected
              ? Colors.themeColor
              : Colors.lightOrange,
            marginLeft: 10,
          }}
        /> */}
      </TouchableOpacity>
    );
  };
  trackColors = {
    false: Colors.placeholder,
    true: Colors.switchColor,
  };
  renderHeader = () => {
    return (
      <View style={Styles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={Styles.title}>
          {this.state.isEdit
            ? Strings.addNewEvent.edit
            : Strings.addNewEvent.title}
        </Text>
      </View>
    );
  };
  onPressCancel = () => {
    this.props.navigation.goBack();
  }
  bottomContainer = () => {
    return (
      <View style={Styles.bottomContainer}>
        <TouchableOpacity onPress={this.onPressCancel}>
          <View style={Styles.cancel}>
            <Text style={Styles.cancelText}>{Strings.addMember.cancel}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            this.submit();
          }}
        >
          <View style={Styles.submit}>
            <Text style={Styles.submitText}>
              {this.state.editNewEvent == true
                ? Strings.addMember.update
                : Strings.addMember.submit}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  render() {
    return (
      <>
        {this.state.isNetworkCheck ?
          <NoInternet
            Refresh={() => this.networkCheck()} /> :

          <SafeAreaView style={Styles.safeArea}>
            <View style={Styles.parentContainer}>
              {this.renderHeader()}

              <KeyboardAwareScrollView
                keyboardShouldPersistTaps={false}
                enableResetScrollToCoords={false}
                scrollsToTop={false}
                extraScrollHeight={60}
              >
                <TextField
                  attrName={Strings.placeholders.description}
                  title={Strings.placeholders.description}
                  value={this.state.description}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  maxLength={150}
                  onSubmitEditing={() => Keyboard.dismiss()}
                  textInputStyles={{
                    // here you can add additional TextInput drStyles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                />
                <View
                  style={{
                    width: wp("90%"),
                    alignSelf: "center",
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                  <TextField
                    attrName={Strings.addNewEvent.fromDate}
                    title={Strings.addNewEvent.fromDate}
                    value={this.state.fromDate.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={true}
                    textInputStyles={{
                      // here you can add additional TextInput drStyles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    showButton={true}
                    onPress={() => {
                      Keyboard.dismiss()
                      this.onPressFromDateTF();
                    }}
                    container={{
                      width: wp("42%"),
                      alignSelf: "flex-start",
                    }}
                    imageSource={Images.calGray}
                    placeholder={"Select"}
                    progressWidth={wp("42%")}
                    buttonContainer={{
                      width: wp("42%"),
                    }}
                  />
                  {/* Start Time  */}
                  <TextField
                    attrName={Strings.placeholders.startTime}
                    title={Strings.placeholders.startTime}
                    value={this.state.selectedStartTime.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={true}
                    textInputStyles={{
                      // here you can add additional TextInput drStyles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    editable={this.state.editStartTime}
                    showButton={this.state.editStartTime}
                    onPress={() => {
                      Keyboard.dismiss()
                      this.onPressStartDateTF();
                    }}
                    container={{
                      width: wp("42%"),
                      alignSelf: "flex-start",
                    }}
                    imageSource={Images.clock}
                    placeholder={"Select"}
                    progressWidth={wp("42%")}
                    buttonContainer={{
                      width: wp("42%"),
                    }}
                  />
                </View>
                <View
                  style={{
                    width: wp("90%"),
                    alignSelf: "center",
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                  <TextField
                    attrName={Strings.addNewEvent.toDate}
                    title={Strings.addNewEvent.toDate}
                    value={this.state.toDate.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={true}
                    textInputStyles={{
                      // here you can add additional TextInput drStyles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    showButton={true}
                    onPress={() => {
                      Keyboard.dismiss()
                      this.onPressToDateTF();
                    }}
                    container={{
                      width: wp("42%"),
                      alignSelf: "flex-start",
                    }}
                    imageSource={Images.calGray}
                    placeholder={"Select"}
                    progressWidth={wp("42%")}
                    buttonContainer={{
                      width: wp("42%"),
                    }}
                  />
                  {/* End Time  */}
                  <TextField
                    attrName={Strings.placeholders.endTime}
                    title={Strings.placeholders.endTime}
                    value={this.state.selectedEndTime.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={true}
                    editable={this.state.editEndTime}
                    textInputStyles={{
                      // here you can add additional TextInput drStyles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    showButton={this.state.editEndTime}
                    onPress={() => {
                      Keyboard.dismiss()
                      this.onPressEndDateTF();
                    }}
                    container={{
                      width: wp("42%"),
                      alignSelf: "flex-start",
                    }}
                    imageSource={Images.clock}
                    placeholder={"Select"}
                    progressWidth={wp("42%")}
                    buttonContainer={{
                      width: wp("42%"),
                    }}
                  />
                </View>
                {/* <Text style={Styles.timeZoneText}>
                  {Strings.placeholders.timeZone}
                  <Text style={Styles.mandatory}>{Strings.addNewEvent.mandatory}</Text>
                </Text> */}
                {/* <Text style={Styles.timeZoneValue} key={this.state.selectedTimeZone}>
                  {this.state.selectedTimeZone || ''}
                </Text> */}

                {/* dropdown picker for time zone not required */}
                {/* <DropDownPicker
            searchable={true}
            searchablePlaceholder={Strings.addNewEvent.timeZoneSearch}
            items={this.state.timeZoneList}
            defaultValue={this.state.selectedTimeZone}
            placeholder={Strings.placeholders.chooseTimezone}
            placeholderStyle={Styles.dropDownPlaceHolderStyle}
            containerStyle={Styles.dropDownContainer}
            style={Styles.dropDownPickerStyle}
            itemStyle={Styles.dropDownItemStyle}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={[Styles.arrowDownStyle,{ width: size,height: size }]}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={[Styles.arrowDownStyle,{ width: size,height: size }]}
              />
            )}
            dropDownStyle={Styles.dropDownListStyle}
            onChangeItem={(item) => {
              // Use helper method for immediate state update
              this.updateTimezoneState(item.value, item.id);
            }}
            selectedLabelStyle={Styles.selectedDropDown}
            zIndex={10000}
          /> */}

                <Text
                  style={[
                    Styles.timeZoneText,
                    { fontFamily: Fonts.montserratMedium, fontSize: wp("4%") },
                  ]}
                >
                  {Strings.placeholders.location}
                  {/* <Text style={Styles.mandatory}>
                    {Strings.addNewEvent.mandatory}
                  </Text> */}
                </Text>
                <MultiSelectDropDown
                  ref={ref => this.locationMultiSelectRef = ref}
                  dataItems={this.state.locationDropdownList}
                  title={Strings.placeholders.location}
                  selectedDataItem={this.getSelectedLocationList}
                  disabled={this.state.isInformationOnlyEvent}
                />

                <Text
                  style={[
                    drStyles.idTitle,
                    {
                      fontSize: wp("4%"),
                      marginLeft: 10,
                      marginTop: hp("3%"),
                      alignSelf: "center",
                      width: wp("90%"),
                    },
                  ]}
                >
                  {Strings.placeholders.equip}{" "}
                  {/* <Text style={{ color: Colors.red }}>*</Text> */}
                </Text>
                <MultiSelectDropDown
                  ref={ref => this.multiSelectRef = ref}
                  dataItems={this.state.equipTypeList}
                  title={Strings.placeholders.equip}
                  selectedDataItem={this.getSelectedEquipmentList}
                  disabled={this.state.isInformationOnlyEvent}
                />

                <Text
                  style={[
                    drStyles.idTitle,
                    {
                      fontSize: wp("4%"),
                      marginLeft: 10,
                      marginTop: hp("3%"),
                      alignSelf: "center",
                      width: wp("90%"),
                    },
                  ]}
                >
                  {Strings.placeholders.gate}{" "}
                  {/* <Text style={{ color: Colors.red }}>*</Text> */}
                </Text>
                <MultiSelectDropDown
                  ref={ref => this.gateMultiSelectRef = ref}
                  dataItems={this.state.gateList}
                  title={Strings.placeholders.gate}
                  selectedDataItem={this.getSelectedGateList}
                  disabled={this.state.isInformationOnlyEvent}
                />

                <Dropdown
                  data={this.state.equipTypeList}
                  title={Strings.addEquipment.type}
                  value={this.state.selectedEquipName}
                  closeBtn={() => this.setState({ eqipModalVisible: false })}
                  onPress={(item) => this.onPressEqipType(item)}
                  visible={this.state.eqipModalVisible}
                  onbackPress={() => this.setState({ eqipModalVisible: false })}
                  container={drStyles.equipmentContainer}
                  customMainContainer={drStyles.renderEquipStyle}
                  equipTextContainer={drStyles.equipTextStyle}
                  disabled={this.state.isInformationOnlyEvent}
                />

                <View style={[Styles.escortContainer, { marginTop: 0 }]}>
                  <Text style={Styles.escortText}>
                    {Strings.addNewEvent.allDay}{" "}
                  </Text>

                  <Switch
                    style={{ transform: [{ scaleX: 0.9 }, { scaleY: 0.8 }] }}
                    trackColor={{ false: Colors.placeholder, true: "#11FF00" }}
                    thumbColor={"#fff"}
                    ios_backgroundColor="#3e3e3e"
                    onValueChange={() => {
                      Keyboard.dismiss()
                      let startTime = new Date();
                      let endTime = new Date();
                      if (!this.state.isAllDay) {
                        startTime.setHours(0);
                        startTime.setMinutes(0);
                        startTime.setSeconds(0);
                        endTime.setHours(0);
                        endTime.setMinutes(0);
                        endTime.setSeconds(0);
                      } else {
                        startTime.setHours(new Date().getHours());
                        startTime.setMinutes(0);
                        startTime.setSeconds(0);
                        endTime.setHours(startTime.getHours());
                        endTime.setMinutes(30);
                        endTime.setSeconds(0);
                      }

                      this.setState({
                        isAllDay: !this.state.isAllDay,
                        selectedStartTime: moment(startTime).format("hh:mm A"),
                        selectedEndTime: moment(endTime).format("hh:mm A"),
                        calFromStartTime: startTime,
                        calToEndTime: endTime,
                        editStartTime: !this.state.editStartTime,
                        editEndTime: !this.state.editEndTime,
                      });
                    }}
                    value={this.state.isAllDay}
                  />
                </View>
               
                
                
                
                <View style={{ flexDirection: "row" }}>
                  <Text
                    style={[Styles.escortText, { marginLeft: 20, marginTop: 10 }]}
                  >
                    {Strings.addNewEvent.eventApplicableto}
                  </Text>
                  <Text style={[Styles.escortText, { marginLeft: 0, marginTop: 10, color: Colors.themeColor }]}>*</Text>
                </View>
                <View style={[Styles.escortContainer, { marginTop: 20 }]}>
                  <Text style={Styles.escortText}>
                    {Strings.addNewEvent.deliveryCalendar}{" "}
                  </Text>

                  <Switch
                    style={{ transform: [{ scaleX: 0.9 }, { scaleY: 0.8 }] }}
                    trackColor={{ false: Colors.placeholder, true: "#11FF00" }}
                    thumbColor={"#fff"}
                    ios_backgroundColor="#3e3e3e"
                    onValueChange={() => {
                      Keyboard.dismiss()
                      this.setState({
                        isDeilveryCalendar: !this.state.isDeilveryCalendar,
                      });
                    }}
                    value={this.state.isDeilveryCalendar}
                  />
                </View>
                <View style={[Styles.escortContainer, { marginTop: 10 }]}>
                  <Text style={Styles.escortText}>
                    {Strings.addNewEvent.craneCalendar}{" "}
                  </Text>

                  <Switch
                    style={{ transform: [{ scaleX: 0.9 }, { scaleY: 0.8 }] }}
                    trackColor={{ false: Colors.placeholder, true: "#11FF00" }}
                    thumbColor={"#fff"}
                    ios_backgroundColor="#3e3e3e"
                    onValueChange={() => {
                      Keyboard.dismiss()
                      this.setState({
                        isCraneCalendar: !this.state.isCraneCalendar,
                      });
                    }}
                    value={this.state.isCraneCalendar}
                  />
                </View>
                <View style={[Styles.escortContainer, Styles.escortMargin]}>
                  <Text style={Styles.escortText}>
                    {Strings.addNewEvent.concreteCalendar}{" "}
                  </Text>
                  <Switch
                    style={Styles.swtichStyle}
                    trackColor={this.trackColors}
                    thumbColor={Colors.white}
                    ios_backgroundColor={Colors.iosSwitch}
                    onValueChange={() => {
                      Keyboard.dismiss()
                      this.setState({
                        isConcreteCalendar: !this.state.isConcreteCalendar,
                      });
                    }}
                    value={this.state.isConcreteCalendar}
                  />
                </View>
                <View style={[Styles.escortContainer, Styles.escortMargin]}>
                  <Text style={Styles.escortText}>
                    {Strings.addNewEvent.inspectionEvent}{" "}
                  </Text>
                  <Switch
                    style={Styles.swtichStyle}
                    trackColor={this.trackColors}
                    thumbColor={Colors.white}
                    ios_backgroundColor={Colors.iosSwitch}
                    onValueChange={() => {
                      Keyboard.dismiss()
                      this.setState({
                        isInspectionCalendar: !this.state.isInspectionCalendar,
                      });
                    }}
                    value={this.state.isInspectionCalendar}
                  />
                </View>
                <TextField
                  attrName={Strings.addNewEvent.recurrence}
                  title={Strings.addNewEvent.recurrence}
                  value={this.state.recurrence}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  textInputStyles={{
                    // here you can add additional TextInput styles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                  showButton={true}
                  onPress={() => {
                    Keyboard.dismiss()
                    this.setState({ isRecurrence: true });
                  }}
                  imageSource={Images.downArr}
                //   placeholder={'Select'}
                />

                {this.state.isLowerRecur && (
                  <View>
                    {this.state.repeatEvery && (
                      <>
                        <View>
                          <Text style={[Styles.escortText, { marginLeft: 20 }]}>
                            {Strings.addNewEvent.repeat}
                          </Text>

                          <View style={{ flexDirection: "row" }}>
                            <TextField
                              attrName="Number"
                              title=""
                              value={this.state.times}
                              updateMasterState={(key, value) => {
                                this.updateMasterState(key, value);
                              }}
                              defaultValue="1"
                              keyboardType="numeric"
                              // mandatory={true}
                              textInputStyles={{
                                // here you can add additional TextInput styles
                                color: Colors.black,
                                fontSize: 14,
                              }}
                              // showButton={true}
                              // onPress={() => {
                              // this.setState({isRecurrence:true})
                              // }}
                              container={{
                                width: wp("20%"),
                                alignSelf: "flex-start",
                                marginLeft: 20,
                                marginTop: -5,
                              }}
                              progressWidth={wp("20%")}
                              buttonContainer={{
                                width: wp("20%"),
                              }}
                            //imageSource={Images.downArr}
                            //   placeholder={'Select'}
                            />

                            <DropDownPicker
                              items={this.state.recur}
                              defaultValue={this.state.selectedRecur}
                              //placeholder={'select'}
                              placeholderStyle={{
                                color: Colors.placeholder,
                                fontSize: 14,
                                marginTop: 10,
                              }}
                              containerStyle={{
                                height: hp("6%"), marginTop: 3,

                              }}
                              style={{
                                backgroundColor: Colors.white,
                                width: wp("30%"),
                                borderColor: "#0000",
                                borderBottomColor: Colors.placeholder,
                                alignSelf: "center",
                                height: hp("5%"),
                                marginLeft: 20,
                              }}
                              itemStyle={{
                                justifyContent: "flex-start",
                              }}
                              globalTextStyle={{ marginTop: 0 }}
                              customArrowUp={(size) => (
                                <Image
                                  source={Images.downArr}
                                  style={{
                                    width: size,
                                    height: size,
                                    alignSelf: "flex-end",
                                  }}
                                />
                              )}
                              customArrowDown={(size) => (
                                <Image
                                  source={Images.downArr}
                                  style={{
                                    width: size,
                                    height: size,
                                    alignSelf: "flex-end",
                                  }}
                                />
                              )}
                              dropDownStyle={{
                                backgroundColor: Colors.white,
                                width: "85%",
                                justifyContent: "center",
                                alignItems: "center",
                                height:
                                  Platform.OS == "android" ? hp("10%") : "120%",
                                marginLeft: 20,

                              }}
                              onChangeItem={(item) => {
                                let dropdownValue;
                                if (item.value == "Day") {
                                  this.updateDay();
                                  this.setState({
                                    isDayWeek: true,
                                    isYear: false,
                                    isMonth: false,
                                    repeatEvery: true,
                                  });
                                  dropdownValue = "Daily";
                                } else if (item.value == "Month") {
                                  dropdownValue = "Monthly";
                                  this.setState({
                                    isDayWeek: false,
                                    isYear: false,
                                    isMonth: true,
                                    repeatEvery: true,
                                    isMonthFirstCheck: true,
                                  });
                                } else if (item.value == "Week") {
                                  this.updateWeek();
                                  dropdownValue = "Weekly";
                                  this.setState({
                                    isDayWeek: true,
                                    isMonth: false,
                                    isYear: false,
                                    repeatEvery: true,
                                    selectedDaysOccurs: 'Monday',
                                  });
                                } else if (item.value == "Year") {
                                  dropdownValue = "Yearly";
                                  this.setState({
                                    isDayWeek: false,
                                    isMonth: false,
                                    isYear: true,
                                    repeatEvery: false,
                                    isYearFirstCheck: true,
                                  });
                                }
                                this.setState({
                                  selectedRecur: item.value,
                                  recurrence: dropdownValue,
                                });
                              }}
                              selectedLabelStyle={{ color: Colors.black }}
                              zIndex={5000}

                            />
                          </View>
                        </View>
                      </>
                    )}
                    {this.state.isDayWeek && (
                      <>
                        <View
                          style={Styles.weeklyContainer}
                        >
                          <FlatList
                            data={this.state.daysList}
                            renderItem={this.renderDay}
                            horizontal={true}
                            scrollEnabled={false}
                            extraData={this.state}
                          />
                        </View>
                        {this.state.recurrence == 'Daily' && <View style={{ flexDirection: 'row', width: wp('90'), marginLeft: 20 }}>
                          <Text style={[Styles.escortText, { marginTop: 20, color: Colors.themeColor }]}>* </Text>
                          <Text style={Styles.occursText}>Occurs every day until {moment(this.state.toDate).format('MMMM DD, YYYY')}</Text>
                        </View>}
                      </>
                    )}
                    {this.state.isMonth && (
                      <>
                        <View style={{ flexDirection: "row", marginLeft: 20, marginTop: 30, }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (!this.state.isMonthFirstCheck) {
                                this.setState({
                                  isMonthFirstCheck: !this.state.isMonthFirstCheck,
                                  isMonthSecondCheck: false,
                                  isMonthThirdCheck: false,
                                })
                              }
                            }}
                            style={{
                              height: 15,
                              width: 15,
                              backgroundColor: this.state.isMonthFirstCheck
                                ? Colors.themeColor
                                : Colors.white,
                              borderRadius: 30,
                              borderColor: Colors.lightGrey,
                              borderWidth: 1,
                              marginRight: 10,
                            }}
                          ></TouchableOpacity>
                          <Text style={Styles.escortText}>
                            {" "}
                            On day {moment(this.state.fromDate).format("DD")}
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: "row",
                            marginLeft: 20,
                            marginTop: 10,
                          }}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              if (!this.state.isMonthSecondCheck) {
                                this.setState({
                                  isMonthSecondCheck: !this.state.isMonthSecondCheck,
                                  isMonthFirstCheck: false,
                                  isMonthThirdCheck: false,
                                })
                              }
                            }
                            }
                            style={{
                              height: 15,
                              width: 15,
                              backgroundColor: this.state.isMonthSecondCheck
                                ? Colors.themeColor
                                : Colors.white,
                              borderRadius: 30,
                              borderColor: Colors.lightGrey,
                              borderWidth: 1,
                              marginRight: 10,
                            }}
                          ></TouchableOpacity>
                          <Text style={Styles.escortText}>
                            {" "}
                            On the {this.state.monthlyDay}
                          </Text>
                        </View>
                        {this.state.monthlyLastDay != "" &&
                          this.state.monthlyLastDay != undefined && (
                            <View
                              style={{
                                flexDirection: "row",
                                marginLeft: 20,
                                marginTop: 10,
                              }}
                            >
                              <TouchableOpacity
                                onPress={() => {
                                  if (!this.state.isMonthThirdCheck) {
                                    this.setState({
                                      isMonthThirdCheck:
                                        !this.state.isMonthThirdCheck,
                                      isMonthFirstCheck: false,
                                      isMonthSecondCheck: false,
                                    })
                                  }
                                }}
                                style={{
                                  height: 15,
                                  width: 15,
                                  backgroundColor: this.state.isMonthThirdCheck
                                    ? Colors.themeColor
                                    : Colors.white,
                                  borderRadius: 30,
                                  borderColor: Colors.lightGrey,
                                  borderWidth: 1,
                                  marginRight: 10,
                                }}
                              ></TouchableOpacity>
                              <Text style={Styles.escortText}>
                                {" "}
                                On the {this.state.monthlyLastDay}{" "}
                                {moment(this.state.fromDate).format("dddd")}
                              </Text>
                            </View>
                          )}
                      </>
                    )}
                    {this.state.isYear && (
                      <>
                        <View style={{ flexDirection: "row", marginLeft: 20 }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (!this.state.isYearFirstCheck) {
                                this.setState({
                                  isYearFirstCheck: !this.state.isYearFirstCheck,
                                  isYearSecondCheck: false,
                                  isYearThirdCheck: false,
                                })
                              }
                              //   else{
                              //   this.setState({
                              //     isYearFirstCheck: !this.state.isYearFirstCheck,
                              //     isYearSecondCheck: !this.state.isYearSecondCheck,
                              //     isYearThirdCheck: !this.state.isYearThirdCheck,
                              //   })
                              // }
                            }
                            }
                            style={{
                              height: 15,
                              width: 15,
                              backgroundColor: this.state.isYearFirstCheck
                                ? Colors.themeColor
                                : Colors.white,
                              borderRadius: 30,
                              borderColor: Colors.lightGrey,
                              borderWidth: 1,
                              marginRight: 10,
                            }}
                          ></TouchableOpacity>
                          <Text style={Styles.escortText}>
                            {" "}
                            On day {moment(this.state.fromDate).format("DD")}
                          </Text>
                        </View>
                        <View
                          style={{
                            flexDirection: "row",
                            marginLeft: 20,
                            marginTop: 10,
                          }}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              if (!this.state.isYearSecondCheck) {
                                this.setState({
                                  isYearFirstCheck: false,
                                  isYearSecondCheck: !this.state.isYearSecondCheck,
                                  isYearThirdCheck: false,
                                })
                              }
                            }}
                            style={{
                              height: 15,
                              width: 15,
                              backgroundColor: this.state.isYearSecondCheck
                                ? Colors.themeColor
                                : Colors.white,
                              borderRadius: 30,
                              borderColor: Colors.lightGrey,
                              borderWidth: 1,
                              marginRight: 10,
                            }}
                          ></TouchableOpacity>
                          <Text style={Styles.escortText}>
                            {" "}
                            On the {this.state.monthlyDay}
                          </Text>
                        </View>
                        {this.state.monthlyLastDay != "" &&
                          this.state.monthlyLastDay != undefined && (
                            <View
                              style={{
                                flexDirection: "row",
                                marginLeft: 20,
                                marginTop: 10,
                              }}
                            >
                              <TouchableOpacity
                                onPress={() => {
                                  if (!this.state.isYearThirdCheck) {
                                    this.setState({
                                      isYearThirdCheck: !this.state.isYearThirdCheck,
                                      isYearFirstCheck: false,
                                      isYearSecondCheck: false,
                                    })
                                  }
                                }
                                }
                                style={{
                                  height: 15,
                                  width: 15,
                                  backgroundColor: this.state.isYearThirdCheck
                                    ? Colors.themeColor
                                    : Colors.white,
                                  borderRadius: 30,
                                  borderColor: Colors.lightGrey,
                                  borderWidth: 1,
                                  marginRight: 10,
                                }}
                              ></TouchableOpacity>
                              <Text style={Styles.escortText}>
                                {" "}
                                On the {this.state.monthlyLastDay}{" "}
                                {moment(this.state.fromDate).format("dddd")}
                              </Text>
                            </View>
                          )}
                      </>
                    )}

                    {((this.state.recurrence == Strings.calendarSettings.weekly) || (this.state.recurrence == Strings.calendarSettings.monthly) || (this.state.recurrence == Strings.calendarSettings.yearly)) &&
                      <>
                        <TextField
                          attrName={Strings.addNewEvent.endDate}
                          title={Strings.addNewEvent.endDate}
                          value={this.state.endDate.toString()}
                          updateMasterState={(key, value) => {
                            this.updateMasterState(key, value);
                          }}
                          mandatory={true}
                          textInputStyles={{
                            // here you can add additional TextInput drStyles
                            color: Colors.black,
                            fontSize: 14,
                          }}
                          showButton={true}
                          onPress={this.openEndDateModal}
                          container={{

                          }}
                          imageSource={Images.calGray}
                        />
                        <View style={Styles.occursContainer}>
                          <Text style={[Styles.escortText, { marginTop: 20, color: Colors.themeColor }]}>* </Text>
                          {this.state.recurrence == Strings.calendarSettings.weekly &&
                            <Text style={Styles.occursText}>{Strings.addNewEvent.occursEvery} {this.state.selectedDaysOccurs} {Strings.addNewEvent.until} {moment(this.state.endDate).format('MMMM DD, YYYY')}</Text>
                          }
                          {this.state.recurrence == Strings.calendarSettings.monthly && <>

                            {this.state.isMonthFirstCheck && <><Text style={Styles.occursText}>{Strings.addNewEvent.occursDay} {moment(this.state.fromDate).format("DD")} {Strings.addNewEvent.until} {moment(this.state.endDate).format('MMMM DD, YYYY')}</Text></>}
                            {this.state.isMonthSecondCheck && <><Text style={Styles.occursText}>{Strings.addNewEvent.occurs}{this.state.monthlyDay} {Strings.addNewEvent.until} {moment(this.state.endDate).format('MMMM DD, YYYY')}</Text></>}
                            {this.state.isMonthThirdCheck && <><Text style={Styles.occursText}>{Strings.addNewEvent.occurs} {this.state.monthlyLastDay}{" "}
                              {moment(this.state.fromDate).format("dddd")} {Strings.addNewEvent.until} {moment(this.state.endDate).format('MMMM DD, YYYY')}</Text></>}
                          </>}
                          {this.state.recurrence == Strings.calendarSettings.yearly && <>

                            {this.state.isYearFirstCheck && <><Text style={Styles.occursText}>{Strings.addNewEvent.occursDay} {moment(this.state.fromDate).format("DD")} {Strings.addNewEvent.until} {moment(this.state.endDate).format('MMMM DD, YYYY')}</Text></>}
                            {this.state.isYearSecondCheck && <><Text style={Styles.occursText}>{Strings.addNewEvent.occurs} {this.state.monthlyDay} {Strings.addNewEvent.until} {moment(this.state.endDate).format('MMMM DD, YYYY')}</Text></>}
                            {this.state.isYearThirdCheck && <><Text style={Styles.occursText}>{Strings.addNewEvent.occurs} {this.state.monthlyLastDay}{" "}
                              {moment(this.state.fromDate).format("dddd")} {Strings.addNewEvent.until} {moment(this.state.endDate).format('MMMM DD, YYYY')}</Text></>}
                          </>}
                        </View>
                      </>
                    }
                  </View>
                )}
                {this.bottomContainer()}

                {this.state.showToaster && (
                  <Toastpopup
                    backPress={() => this.setState({ showToaster: false })}
                    toastMessage={this.state.toastMessage}
                    type={this.state.toastType}
                    container={{ marginBottom: hp("12%") }}
                  />
                )}

                {/* From Calender iOS */}
                {Platform.OS == "ios" && (
                  <Modal
                    isVisible={this.state.showDateModal}
                    onBackdropPress={this.closeFromDateModal}
                    animationInTiming={500}
                    backdropColor="rgba(0,0,0,0.5)"
                    backdropOpacity={1}
                    style={{
                      paddingTop: 45,
                      margin: 0,
                      justifyContent: "flex-end",
                    }}
                  >
                    <View style={{ backgroundColor: Colors.white }}>
                      <DateTimePicker
                        testID="datePicker"
                        mode="date"
                        minimumDate={this.state.minimumFromDate}
                        value={this.state.calFromDate}
                        style={{
                          backgroundColor: Colors.white,
                          width: '100%',
                        }}
                        display={"inline"}
                        themeVariant='light'
                        accentColor={Colors.themeColor}
                        onChange={this.onchangeFromDate}
                      />
                    </View>
                  </Modal>
                )}

                {/* From Calender Android */}

                {Platform.OS == "android" && this.state.showDateModal && (
                  <DateTimePicker
                    testID="datePicker"
                    mode="date"
                    minimumDate={this.state.minimumFromDate}
                    value={this.state.calFromDate}
                    style={{
                      backgroundColor: Colors.white,
                      width: '100%',
                    }}
                    onChange={this.onchangeFromDate}
                  />
                )}
                {/* To Calender iOS */}

                {Platform.OS == "ios" && (
                  <Modal
                    isVisible={this.state.showToDateModal}
                    onBackdropPress={this.closeToDateModal}
                    animationInTiming={500}
                    backdropColor="rgba(0,0,0,0.5)"
                    backdropOpacity={1}
                    style={{
                      paddingTop: 45,
                      margin: 0,
                      justifyContent: "flex-end",
                    }}
                  >
                    <View style={{ backgroundColor: Colors.white }}>
                      <DateTimePicker
                        testID="datePicker"
                        mode="date"
                        minimumDate={this.state.minimumToDate}
                        value={this.state.calToDate}
                        style={{
                          backgroundColor: Colors.white,
                          width: '100%',
                        }}
                        display={"inline"}
                        themeVariant='light'
                        accentColor={Colors.themeColor}
                        onChange={this.onchangeToDate}
                      />
                    </View>
                  </Modal>
                )}

                {/* To Calender Android */}

                {Platform.OS == "android" && this.state.showToDateModal && (
                  <DateTimePicker
                    testID="datePicker"
                    mode="date"
                    minimumDate={this.state.minimumToDate}
                    value={this.state.calToDate}
                    style={{
                      backgroundColor: Colors.white,
                      width: '100%',
                    }}
                    onChange={this.onchangeToDate}
                  />
                )}
                {/* Time picker iOS - start time */}

                {Platform.OS == "ios" && (
                  <Modal
                    isVisible={this.state.showStartTimeModal}
                    onBackdropPress={() => {
                      this.setState({
                        showStartTimeModal: false,
                      });
                    }}
                    backdropColor="rgba(0,0,0,0.5)"
                    backdropOpacity={1}
                    style={{
                      paddingTop: 45,
                      margin: 0,
                      justifyContent: "flex-end",
                    }}
                  >
                    <View style={{ backgroundColor: Colors.white }}>
                      <DateTimePicker
                        testID="datePicker"
                        mode={"time"}
                        // timeZoneOffsetInMinutes={0}
                        // minuteInterval={interval}
                        value={this.state.calFromStartTime}
                        textColor="black"
                        style={{
                          backgroundColor: Colors.white,
                          width: '100%',
                        }}
                        //mode={mode}
                        display={"spinner"}
                        onChange={(time, date) => {
                          this.onChangeStart(time, date);
                        }}
                      />

                      <View>
                        <TouchableOpacity
                          activeOpacity={0.5}
                          style={Styles.datePickerOkContainer}
                          onPress={() => {
                            this.setState({
                              showStartTimeModal: false,
                            });
                          }}
                        >
                          <Text style={Styles.datePickerOkLabel}>Done</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </Modal>
                )}

                {/* Timepicker android - start Time */}

                {Platform.OS == "android" && this.state.showStartTimeModal && (
                  <DateTimePicker
                    testID="datePicker"
                    mode={"time"}
                    // timeZoneOffsetInMinutes={0}
                    // minuteInterval={interval}
                    value={this.state.calFromStartTime}
                    minimumDate={this.state.minimumFromDate}
                    style={{
                      backgroundColor: Colors.white,
                      width: '100%',
                    }}
                    // mode={mode}
                    onChange={(time, date) => {
                      this.onChangeStart(time, date);
                    }}
                  />
                )}
                {/* Timepicker - ios End Time */}

                {Platform.OS == "ios" && (
                  <Modal
                    isVisible={this.state.showEndTimeModal}
                    onBackdropPress={() => {
                      this.setState({
                        showEndTimeModal: false,
                      });
                    }}
                    backdropColor="rgba(0,0,0,0.5)"
                    backdropOpacity={1}
                    style={{
                      paddingTop: 45,
                      margin: 0,
                      justifyContent: "flex-end",
                    }}
                  >
                    <View style={{ backgroundColor: Colors.white }}>
                      <DateTimePicker
                        testID="datePicker"
                        mode={"time"}
                        // timeZoneOffsetInMinutes={0}
                        // minuteInterval={interval}
                        minimumDate={this.state.minimumDate}
                        value={this.state.calToEndTime}
                        textColor="black"
                        style={{
                          backgroundColor: Colors.white,
                          width: '100%',
                        }}
                        display={"spinner"}
                        // mode={mode}
                        onChange={(time, date) => {
                          this.onChangeEndTime(time, date);
                        }}
                      />
                      <View>
                        <TouchableOpacity
                          activeOpacity={0.5}
                          style={Styles.datePickerOkContainer}
                          onPress={() => {
                            this.setState({
                              showEndTimeModal: false,
                            });
                          }}
                        >
                          <Text style={Styles.datePickerOkLabel}>Done</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </Modal>
                )}

                {/* Timepicker android - End Time */}

                {Platform.OS == "android" && this.state.showEndTimeModal && (
                  <DateTimePicker
                    testID="datePicker"
                    mode={"time"}
                    // timeZoneOffsetInMinutes={0}
                    // minuteInterval={interval}
                    minimumDate={this.state.minimumDate}
                    value={this.state.calToEndTime}
                    style={{
                      backgroundColor: Colors.white,
                      width: '100%',
                    }}
                    // mode={mode}
                    onChange={(time, date) => {
                      this.onChangeEndTime(time, date);
                    }}
                  />
                )}

                {/* From Calender iOS */}
                {Platform.OS == Strings.platforms.ios && (
                  <Modal
                    isVisible={this.state.isEndDate}
                    onBackdropPress={this.closeEndDateModal}
                    animationInTiming={500}
                    backdropColor="rgba(0,0,0,0.5)"
                    backdropOpacity={1}
                    style={Styles.datePickerContainer}
                  >
                    <View style={{ backgroundColor: Colors.white }}>
                      <DateTimePicker
                        mode="date"
                        minimumDate={this.state.minimumFromDate}
                        value={this.state.selectedEndDate}
                        style={{
                          backgroundColor: Colors.white,
                          width: '100%',
                        }}
                        display={Strings.datePicker.inline}
                        themeVariant={Strings.datePicker.light}
                        accentColor={Colors.themeColor}
                        onChange={this.onchangeEndDate}
                      />
                    </View>
                  </Modal>
                )}

                {/* From Calender Android */}

                {Platform.OS == Strings.platforms.android && this.state.isEndDate && (
                  <DateTimePicker
                    mode="date"
                    minimumDate={this.state.minimumFromDate}
                    value={this.state.selectedEndDate}
                    style={{
                      backgroundColor: Colors.white,
                      width: '100%',
                    }}
                    onChange={this.onchangeEndDate}
                  />
                )}
              </KeyboardAwareScrollView>

              {this.state.isRecurrence && (
                <Dropdown
                  data={this.state.listOfRecurrence}
                  title={Strings.addNewEvent.recurrence}
                  value={""}
                  closeBtn={() => this.setState({ isRecurrence: false })}
                  onPress={(item) => {
                    if (item.name == "Does Not Repeat") {
                      this.setState({ isLowerRecur: false, isRecurrence: false, recurrence: item.name, });
                    } else {
                      this.setState({ isLowerRecur: true, recurrence: item.name, });
                    }
                    let recurrenceSelect;
                    if (item.name !== "Does Not Repeat") {

                      if (item.name == "Daily") {
                        this.updateDay();
                        recurrenceSelect = "Day";
                        this.setState({
                          isDayWeek: true,
                          isMonth: false,
                          isYear: false,
                          repeatEvery: true,
                        });
                      } else if (item.name == "Monthly") {
                        recurrenceSelect = "Month";
                        this.setState({
                          isDayWeek: false,
                          isMonth: true,
                          isYear: false,
                          repeatEvery: true,
                        });
                      } else if (item.name == "Weekly") {
                        this.updateWeek();
                        recurrenceSelect = "Week";
                        this.setState({
                          isDayWeek: true,
                          isMonth: false,
                          isYear: false,
                          repeatEvery: true,
                          selectedDaysOccurs: 'Monday',
                        });
                      } else if (item.name == "Yearly") {
                        recurrenceSelect = "Year";
                        this.setState({
                          isDayWeek: false,
                          isMonth: false,
                          isYear: true,
                          repeatEvery: false,
                        });
                      }
                      this.setState({
                        recurrence: item.name,
                        selectedRecur: recurrenceSelect,
                        isRecurrence: false,
                      });
                    }

                  }}
                  visible={this.state.isRecurrence}
                  onbackPress={() => this.setState({ isRecurrence: false })}
                  container={{ alignItems: "center" }}
                  textContainer={{
                    textAlign: "center",
                    marginRight: 20,
                    fontSize: 14
                  }}
                />
              )}
              {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

            </View>
          </SafeAreaView>

        }
      </>
    );
  }
}
const drStyles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("78%"),
    fontSize: wp("5.6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratSemiBold,
  },
  memberContainer: {
    width: wp("90%"),
    height: hp("10%"),
    alignSelf: "center",
    marginTop: hp("1%"),
    justifyContent: "center",
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  idText: {
    width: wp("90%"),
    height: hp("5%"),
    backgroundColor: "#EFEFEF",
    marginTop: wp("1%"),
    padding: hp("1%"),
    paddingLeft: 15,
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("4%"),
  },
  escortContainer: {
    width: "90%",
    alignSelf: "center",
    marginTop: hp("4%"),
    justifyContent: "space-between",
    flexDirection: "row",
  },
  escortText: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("3%"),
  },
  bottomContainer: {
    width: wp("90%"),
    flexDirection: "row",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: hp("4%"),
    marginTop: hp("8%"),
  },
  cancel: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: "#757575",
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  root: {
    justifyContent: "center",
    borderRadius: 50,
    backgroundColor: "#e0e0e0",
    paddingHorizontal: 10,
    paddingVertical: 4,
    height: 28,
    marginBottom: 4,
    marginRight: 4,
  },
  equipmentContainer: {
    height: hp("4%"),
    paddingBottom: 5,
  },
  renderEquipStyle: {
    marginBottom: 10,
  },
  equipTextStyle: {
    width: "100%",
    fontSize: 16,
    paddingTop: 2,
  },
  submitButton: {
    flex: 1,
    backgroundColor: Colors.pendingEventColor,
    padding: 10,
    borderRadius: 25,
    // marginLeft: 10,
    alignItems: 'center',
  },
  submitButtonText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: 16,
    paddingLeft: 70,
    paddingRight: 70,

  }
});

const Styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  timeZoneText: {
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    marginLeft: 20,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("78%"),
    fontSize: wp("5.6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratSemiBold,
  },
  memberContainer: {
    width: wp("90%"),
    height: hp("10%"),
    alignSelf: "center",
    marginTop: hp("1%"),
    justifyContent: "center",
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  idText: {
    width: wp("90%"),
    height: hp("5%"),
    backgroundColor: "#EFEFEF",
    marginTop: wp("1%"),
    padding: hp("1%"),
    paddingLeft: 15,
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("4%"),
  },
  escortContainer: {
    width: "90%",
    alignSelf: "center",
    marginTop: hp("4%"),
    justifyContent: "space-between",
    flexDirection: "row",
  },
  escortMargin: {
    marginTop: 10
  },
  escortText: {
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
  },
  timeZoneText: {
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    marginLeft: 20,
  },
  occursText: {
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    marginTop: 20,
  },
  bottomContainer: {
    width: wp("90%"),
    flexDirection: "row",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: hp("4%"),
    marginTop: hp("8%"),
  },
  cancel: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: "#757575",
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  root: {
    justifyContent: "center",
    borderRadius: 50,
    backgroundColor: "#e0e0e0",
    paddingHorizontal: 10,
    paddingVertical: 4,
    height: 28,
    marginBottom: 4,
    marginRight: 4,
  },
  datePickerOkContainer: {
    width: "100%",
    alignItems: "center",
    backgroundColor: Colors.white,
    paddingBottom: hp("2%"),
  },
  datePickerOkLabel: {
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 6,
    paddingBottom: 6,
    fontFamily: Fonts.montserratBold,
  },
  weeklyContainer: {
    marginTop: 5,
    alignItems: "center",
    width: '95%',
    marginRight: 10,
  },
  swtichStyle: {
    transform: [{ scaleX: 0.9 }, { scaleY: 0.8 }]
  },
  occursContainer: {
    flexDirection: 'row',
    width: wp('90'),
    marginLeft: 20
  },
  datePickerContainer: {
    paddingTop: 45,
    margin: 0,
    justifyContent: "flex-end",
  },
  datePickerStyle: {
    backgroundColor: Colors.white,
    width: '100%',
  },
  dropDownPlaceHolderStyle: {
    color: Colors.placeholder,
    fontSize: 14,
    marginLeft: -5
  },
  dropDownPickerStyle: {
    backgroundColor: Colors.white,
    width: wp("90%"),
    borderColor: Colors.borderTransparent,
    borderBottomColor: Colors.placeholder,
    alignSelf: "center",
    height: hp("5%"),
  },
  dropDownContainer: {
    height: hp("6%"),
    marginTop: 6,
    marginBottom: 20,
  },
  dropDownItemStyle: {
    justifyContent: "flex-start",
  },
  arrowDownStyle: {
    alignSelf: "flex-end"
  },
  dropDownListStyle: {
    backgroundColor: Colors.white,
    width: "90%",
    alignSelf: "center",
  },
  selectedDropDown: {
    color: Colors.black,
    marginLeft: -5,
  },
  mandatory: {
    color: Colors.themeColor
  },
  timeZoneValue: {
    color: Colors.black,
    fontSize: 14,
    marginLeft: wp('7'),
    paddingTop: wp(3),
    paddingBottom: wp(2),
  }
});

const mapStateToProps = (state) => {
  const {
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    deliveryDetailsId,
    inspectionDetailsId,
    updateList,
    responsiblePersonData,
    lastCraneRequestId,
    craneRequestId,
    editedCrane,
    editedEventData,
    projectRoleId,
    isNetworkConnected,
    selectedCalendarDate
  } = state.LoginReducer;

  return {
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    deliveryDetailsId,
    inspectionDetailsId,
    updateList,
    responsiblePersonData,
    lastCraneRequestId,
    craneRequestId,
    editedCrane,
    editedEventData,
    projectRoleId,
    isNetworkConnected,
    selectedCalendarDate
  };
};

export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    cameBack,
    updateList,
    refreshDashboard,
    refreshDeliveryList,
    showCraneRequestId,
    editCraneRequest,
    refreshCalendar,
    refreshCalendarSettings,
    editEventData,
    setPage,
    refreshEventDisplay,
    setSelectedCalendarDate,
  }),
  withBackHandler
)(AddNewEvent);