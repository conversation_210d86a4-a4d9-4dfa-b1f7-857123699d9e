import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Image,
  ActivityIndicator,
} from "react-native";

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import moment from "moment";

import { ExpandableCalendar, CalendarProvider } from "react-native-calendars";
import { Images, Strings, Fonts, Colors } from "../../common";

import {

  GET_ACCOUNT_PROJECT,
  GET_PROJECT_ROLE,
  GET_CALERNDAR_SETTINGS,
  GET_CALERNDAR_SETTINGS_MONTH,
} from "../../api/Constants";
import {
  getProjectList,
  getprojectRole,
  getCalendarSettings,
  getCalendarSettingsMonth,
} from "../../api/Api";

import { AppView, AppLoader, TextField } from "../../components";
import { FlatList } from "react-native-gesture-handler";
import { connect } from "react-redux";
import {
  showDeliverdetailsid,
  // showInspectiondetailsid,
  onTapDetail,
  cameBack,
  storeLastid,
  storeProjectRole,
  toggleAddCalendar,
  lastCraneId,
  refreshDashboard,
  refreshCalendarSettings,
  eventDisplayPage,
  eventDisplayData,
  enableEditEvents,
  setSelectedCalendarDate,
} from "../../actions/postAction";
import TimelineCalendar from "../../components/timeline/TimelineCalendar";
import * as RNLocalize from "react-native-localize";
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";

const testIDs = {
  CONTAINER: "agenda",
  ITEM: "item",
};

let colorobj = {
  normal: Colors.calendarSettingsText
};
let eventcolorobj = {
  normal: Colors.calendarSettingsText
};
let Orderobj = {
  normal: Colors.calendarSettingsText
};

class CalendarSettings extends Component {
  constructor(props) {
    super(props);
    this.state = {
      listClick: false,
      searchbarShow: false,
      items: {},
      currentDate: moment(new Date()).format("YYYY-MM-DD"),
      showLoader: false,
      markedDates: {},
      monthStart: moment(new Date())
        .startOf("month")
        .toISOString(),
      monthEnd: moment(new Date()).endOf("month").toISOString(),
      DeliveryRequestId: "",
      InspectionRequestId: "",
      event: [],
      showFilter: false,
      filter: false,
      statusList: [
        {
          label: "Approved",
          value: "Approved",
          id: "1",
        },
        {
          label: "Declined",
          value: "Declined",
          id: "2",
        },
        {
          label: "Delivered",
          value: "Delivered",
          id: "3",
        },
        {
          label: "Pending",
          value: "Pending",
          id: "4",
        },
      ],
      selectedStatusName: "",
      selectedResponsibleNameId: 0,
      selectedGateNameId: 0,
      selectedEquipNameId: 0,
      selectedStatusId: 0,
      descriptionFilter: "",
      companyFilterList: [],
      selectedCompanyName: null,
      responiblePersonList: [],
      selectedResponsibleName: null,
      gateList: [],
      selectedGateName: null,
      equipmentList: [],
      selectedEquipmentName: null,
      selectedCompanyId: 0,
      refreshing: false,
      searchText: "",
      showNoData: false,
      parentcompanyid: 0,
      refresh: false,
      currentMonth: moment(new Date()).format("MMM YYYY").toString(),
      eventAllDay: [],
      timeZoneParam:RNLocalize.getTimeZone(),
      daylight:moment(new Date()).isDST(),
      isNetworkCheck: false,
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      },
    };
  }

  componentDidMount() {
    // Set initial selected date in Redux
    const formattedCurrentDate = moment(this.state.currentDate).format("YYYY-MM-DD");
    this.props.setSelectedCalendarDate(formattedCurrentDate);
    
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.renderInitial();
    }
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.isRefreshCalendarSettings) {
      this.renderInitial();
      this.props.refreshCalendarSettings(false);
    }
    if (this.props.projectSwitched != nextProps.projectSwitched) {
      this.renderInitial();
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.renderInitial();
    }
    })
  }
  renderInitial = () => {
    this.setState({ event: [], markedDates: {} });
    // Store current date in Redux for AddNewEvent component
    const formattedCurrentDate = moment(this.state.currentDate).format("YYYY-MM-DD");
    this.props.setSelectedCalendarDate(formattedCurrentDate);
    if (this.state.listClick === true) {
      this.getCalendarSettingsMonthAPI();
    } else {
      this.getCalendarSettingsAPI();
    }
  }

  getCompanyProject = () => {
    getProjectList(
      GET_ACCOUNT_PROJECT + `${this.state.parentcompanyid}`,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.data) {
            let data = [];
            if (this.props.projectDetails.id) {
              data = this.props.projectDetails;
            } else {
              data = response.data.data[0];
            }
            this.getRole(data);
          }
        }
      }
    );
  };

  getRole = (data) => {
    let url = GET_PROJECT_ROLE + data.id + "/" + data.ParentCompany.id;

    getprojectRole(
      url,
      {},
      () => null,
      (response) => {
        if (response.data) {
          this.props.storeProjectRole(response.data.data.RoleId);
        }
      }
    );
  };
  showErrorMessage = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showToaster: false,
          });
        }, 2000);
      }
    );
  };
  getCalendarSettingsMonthAPI = () => {
    this.setState({
      showLoader: true,
    });
    let url = `${GET_CALERNDAR_SETTINGS_MONTH}?start=${moment(this.state.monthStart).format('YYYY-MM-DD')}&end=${moment(this.state.monthEnd).format('YYYY-MM-DD')}&ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}&currentViewMonth=${moment(this.state.currentDate).format('MMMM YYYY')}&search=${this.state.searchText}&timezone=${this.state.timeZoneParam}&isDST=${this.state.daylight}`;

    getCalendarSettingsMonth(
      url,
      {},
      () => null,
      (response) => {
        this.setState({
          showLoader: false,
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          // showNoData: false,
        });
        if (response?.status) {
          if (response?.status == 200) {
            if (response?.data?.events?.length <= 0) {
              this.setState({ showNoData: true, event: [] });
            } else {
              let data = response.data.events;
              let events = [];
              data.forEach((e) => {
                events.push({
                  start: moment(e?.fromDate).format("YYYY-MM-DD HH:mm:ss"),
                  end: moment(e?.toDate).format("YYYY-MM-DD HH:mm:ss"),
                  title: e.description,
                  summary: "",
                  color:Colors.calendarSettingsColor,
                  day: moment(e.fromDate).format("YYYY-MM-DD"),
                  id: e.id,
                  isAllDay: e.isAllDay,
                  repeatEveryType:e.repeatEveryType,
                  repeatEveryCount:e.repeatEveryCount,
                  days:(e.repeatEveryType=='Weeks'||e.repeatEveryType=='Week')?e.days:[],
                  chosenDateOfMonth:e.chosenDateOfMonth,
                  dateOfMonth:e.dateOfMonth,
                  monthlyRepeatType:e.monthlyRepeatType,
                  occurTime:e.endTime,
                });
                //   }
              });

              events.sort((a, b) => moment(a.start) - moment(b.start));
              let days = [];
              events.forEach((e) => {
                if (days.includes(e.day)) {
                  delete e.day;
                } else {
                  days.push(e.day);
                }
              });

              this.setState({
                // markedDates: markeddatess,
                event: events,
              });
            }
          } else if (response.status == 400) {
            this.setState({ showNoData: true, event: [] });
            this.showErrorMessage("error", response.data.message)
          } else {
            this.setState({ showNoData: true, event: [] });
            this.showErrorMessage("error", response.data.message)
          }
        } else {
          this.setState({ showNoData: true, event: [] });
          this.showErrorMessage("error", 'Something went Wrong')
        }
      }
    );
  };


  getCalendarSettingsAPI = () => {
    this.setState({
      showLoader: true,
    });
   let url = `${GET_CALERNDAR_SETTINGS}?start=${moment(this.state.monthStart).format('YYYY-MM-DD')}&end=${moment(this.state.monthEnd).format('YYYY-MM-DD')}&ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}&search=${this.state.searchText}&calendarView=Month&timezone=${this.state.timeZoneParam}&isDST=${this.state.daylight}`;


    getCalendarSettings(
      url,
      {},
      () => null,
      (response) => {
        this.setState({
          showLoader: false,
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          showNoData: false,
        });
        if (response.status) {
          if (response.status == 200) {
            data = response.data.events;
            let markeddatess = {};
            let events = [];
            let eventsAllDay = [];
            data.forEach((e) => {
              let markedstartdate = moment(e.fromDate).format(
                "YYYY-MM-DD"
              );
              if (markeddatess[markedstartdate]) {
                if (markeddatess[markedstartdate].order > Orderobj["normal"]) {
                  markeddatess[markedstartdate] = {
                    marked: true,
                    dotColor: colorobj["normal"],
                    order: Orderobj["normal"],
                  };
                }
              } else {
                markeddatess[markedstartdate] = {
                  marked: true,
                  dotColor: colorobj["normal"],
                  order: Orderobj[ "normal"],
                };
              }
              events.push({
                start: moment(e.fromDate).format("YYYY-MM-DD HH:mm:ss"),
                end: moment(e.toDate).format("YYYY-MM-DD HH:mm:ss"),
                title: e.description,
                summary: "",
                color: Colors.calendarSettingsColor,
                day: moment(e.fromDate).format("YYYY-MM-DD"),
                id: e.id,
                isAllDay: e.isAllDay,
                repeatEveryType:e.repeatEveryType,
                repeatEveryCount:e.repeatEveryCount,
                days:(e.repeatEveryType=='Weeks'||e.repeatEveryType=='Week')?e.days:[],
                chosenDateOfMonth:e.chosenDateOfMonth,
                dateOfMonth:e.dateOfMonth,
                monthlyRepeatType:e.monthlyRepeatType,
                occurTime:e.endTime,
                endDate:e.endDate,
              });

            });
            this.setState({
              markedDates: markeddatess,
              event: events,
              eventAllDay: eventsAllDay,
            });
          } else if (response.status == 400) {
            this.setState({ showNoData: true, event: [] });
            this.showErrorMessage(Strings.toast.error, response.data.message)
          } else {
            this.setState({ showNoData: true, event: [] });
            this.showErrorMessage(Strings.toast.error, response.data.message)
          }
        } else {
          this.setState({ showNoData: true, event: [] });
          this.showErrorMessage(Strings.toast.error, Strings.errors.something)
        }
      }
    );
  };

  onDateChanged = (date) => {
    // fetch and set data for date + week ahead
    console.log("date",date)
    // Ensure the date is in YYYY-MM-DD format
    const formattedDate = moment(date).format("YYYY-MM-DD");
    this.setState({ currentDate: formattedDate });
    // Store selected date in Redux for AddNewEvent component
    this.props.setSelectedCalendarDate(formattedDate);
  };
  onMonthChange = (month) /* month, updateSource */ => {
    let afterMonthChange = moment(month.dateString)
      .format("MMM YYYY")
      .toString();
    const startOfMonth = moment(month.dateString)
      .startOf("month")
      .toISOString();
    const endOfMonth = moment(month.dateString)
      .endOf("month")
      .toISOString();
    
    // Update currentDate to first day of the new month if no specific date is selected
    const newCurrentDate = moment(month.dateString).format("YYYY-MM-DD");
    
    this.setState(
      {
        monthStart: startOfMonth,
        monthEnd: endOfMonth,
        currentMonth: afterMonthChange,
        currentDate: newCurrentDate,
      },
      () => {
        // Update Redux with the new current date
        this.props.setSelectedCalendarDate(newCurrentDate);
        this.getCalendarSettingsAPI();
      }
    );
  };
  renderNoDeliveryRequest = () => {
    return (
      this.state.showNoData &&
      this.state.listClick && (
        <View style={styles.noDRView}>
          <Text style={styles.noDRText}>No Events Found</Text>
        </View>
      )
    );
  };

  eventClick = (item) => {
    this.props.eventDisplayData(item);
    this.props.enableEditEvents(true);
    this.props.eventDisplayPage('EventDisplay');
    mixPanelTrackEvent('Calendar Event Details',this.state.mixpanelParam)

  }
  searchList = () => {
    this.setState({ showIndicator: true, clearSearch: false }, () => {
      this.renderInitial();
    });
  };
  clearSearch = () => {
    this.setState({
      clearSearch: false,
      searchText: "",
      showIndicator: true,
    });
    this.searchList();
  };

  updateMasterState = (key, value) => {
    this.setState(
      {
        searchText: value,
      },
      () => {
        this.searchList();
      }
    );
  };
  searchBar = () => {
    return (
      <View style={searchStyles.searchHeader}>
        <View style={searchStyles.mainContainer}>
          <TouchableOpacity
            style={searchStyles.closeBtn}
            onPress={() => {
              this.setState(
                {
                  searchbarShow: false,
                  searchText: "",
                },
                this.clearSearch()
              );
            }}
          >
            <Image
              resizeMode={"contain"}
              source={Images.closeBlack}
              style={searchStyles.closeImg}
            />
          </TouchableOpacity>

          <View style={searchStyles.searchPageTitle}>
            <Text style={searchStyles.titleText}>{Strings.search.title}</Text>
          </View>

          <TouchableOpacity
            style={searchStyles.closeBtn}
            onPress={() => {
              if (this.state.showright) {
                this.setState({ showAllDelete: true });
              }
            }}
          >
            {this.state.showright == true && (
              <Image
                resizeMode={"contain"}
                source={Images.delete1}
                style={searchStyles.closeImg}
              />
            )}
          </TouchableOpacity>
        </View>

        <View style={{ flexDirection: "row", justifyContent: "center" }}>
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.SearchHere}
            title={Strings.placeholders.SearchHere}
            value={this.state.searchText}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={searchStyles.txtInputStyles}
            textTitleStyles={searchStyles.txtTitleStyles}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />

          <View style={searchStyles.clearSearchView}>
            {this.state.showIndicator == true && (
              <ActivityIndicator style={{ marginBottom: 5 }} />
            )}
            {this.state.clearSearch == true && (
              <TouchableOpacity onPress={() => this.clearSearch()}>
                <Image source={Images.closeBlack} style={{ marginBottom: 5 }} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    );
  };
  renderHeader = () => {
    return (
      <View style={styles.headerContainer}>
        <Text style={styles.title}>{Strings.menu.calendarSettings}</Text>
        <View style={styles.headerRowContainer}>
          <TouchableOpacity
            style={[
              styles.image,
              {
                marginTop: wp("1%"),
                marginBottom:
                  this.state.listClick === true ? hp("1%") : hp("2%"),
              },
            ]}
            onPress={() => {
              this.setState(
                { listClick: !this.state.listClick, event: [] },
                () => {

                  if (this.state.listClick === true) {
                    this.getCalendarSettingsMonthAPI();
                  } else {
                    this.getCalendarSettingsAPI();
                  }
                }
              );
            }}
          >
            <Image
              source={
                this.state.listClick === true ? Images.ham : Images.list_unclick
              }
              style={{
                height: this.state.listClick ? 28.1 : 18,
                width: this.state.listClick ? 28 : 18,
              }}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.image}
            onPress={() => {
              this.setState({
                searchbarShow: true,
                event: [],
                markedDates: {},
              });
            }}
          >
            <Image source={Images.Search1} style={{ height: 21, width: 21 }} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  renderSearchBar = () => {
    if (this.state.searchbarShow == true) {
      return this.searchBar();
    } else {
      return this.renderHeader();
    }
  };
  renderFlatListItem = ({ item, index }) => {
    return (
      <View style={styles.flatlistContainer}>
        <TouchableOpacity
          onPress={() => {
            this.props.eventDisplayData(item);
            this.props.enableEditEvents(true);
            this.props.eventDisplayPage('EventDisplay');
          }}
        >
          <View style={{ flexDirection: "column" }}>
            {item.day && (
              <Text
                style={{
                  marginTop: hp("1%"),
                  marginBottom: hp("1%"),
                  color: "#292529",
                  fontSize: wp("4.5%"),
                  fontFamily: "Montserrat-bold",
                }}
              >
                {moment(item.day).format("ddd, MMM DD")}
              </Text>
            )}
            <View style={{ flexDirection: "row", margin: 10 }}>
              <View style={{ flexDirection: "column", marginRight: 10 }}>
                <Text
                  style={{
                    fontSize: wp("3.5%"),
                    fontFamily: "Montserrat-regular",
                    color: "#BEBEBE",
                    marginBottom: hp("3%"),
                    marginTop: hp("1%"),
                  }}
                >
                  {moment(item.start).format("hh:mm A")}
                </Text>
                <Text
                  style={{
                    fontSize: wp("3.5%"),
                    fontFamily: "Montserrat-regular",
                    color: "#BEBEBE",
                  }}
                >
                  {moment(item.end).format("hh:mm A")}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  width: wp("80%"),
                  backgroundColor: item.color,
                  borderRadius: 5,
                  marginTop: hp("1%"),
                }}
              >
                {item.isAllDay&&
                <Image source={Images.allday} style={{   marginLeft: 20,marginRight:-10,
                    marginTop: hp("1%"),width:16,height:16}}/>
                    }
                <Text
                  style={{
                    marginTop: hp("1%"),
                    marginLeft:20,
                    marginBottom: hp("1%"),
                    color: Colors.calendarSettingsText,
                    fontSize: wp("4%"),
                    fontFamily: "Montserrat-semibold",
                  }}
                  numberOfLines={1}
                >
                  {item.title}
                </Text>
                {/* <Text style={{
                  marginLeft: 20,
                  marginBottom: hp("1%"),
                  color: Colors.black,
                  fontSize: wp("3%"),
                  fontFamily: "Montserrat-semibold",
                }} >{item.isAllDay}</Text> */}
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  };
  itemSeparator = () => (
    <View
      style={{
        backgroundColor: "rgb(216,216,216)",
        height: 0.5,
      }}
    />
  );
  getTheme = () => {
    const themeColor = "#2E2E2E";
    const disabledColor = "#a6acb1";
    const white = "#ffffff";

    return {
      // arrows
      arrowColor: "#CFD5DA",
      arrowStyle: { paddingLeft: 15, paddingRight: 15 },
      // month
      monthTextColor: "#292529",
      textMonthFontSize: wp("4.5%"),
      textMonthFontFamily: Fonts.montserratBold,
      textMonthFontWeight: "bold",
      // day names
      textSectionTitleColor: "#2C3593",
      textDayHeaderFontSize: wp("3%"),
      textDayHeaderFontFamily: Fonts.montserratSemiBold,
      // textDayHeaderFontWeight: 'medium',
      // today
      //todayBackgroundColor: lightThemeColor,
      //todayTextColor: themeColor,
      // dates
      dayTextColor: themeColor,
      textDayFontSize: wp("4%"),
      textDayFontFamily: Fonts.montserratRegular,
      // textDayFontWeight: '500',
      textDayStyle: { marginTop: Platform.OS === "android" ? 2 : 4 },
      // selected date
      selectedDayBackgroundColor: Colors.themeColor,
      selectedDayTextColor: "white",

      // disabled datet
      textDisabledColor: disabledColor,
      // dot (marked date)
      dotColor: themeColor,
      selectedDotColor: white,
      disabledDotColor: disabledColor,
      dotStyle: { marginTop: -2, height: 8, width: 8, borderRadius: 4 },
    };
  };
  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <AppView>
        <View style={styles.parentContainer}>
          {this.renderSearchBar()}
          {!this.state.listClick && (
            <CalendarProvider
              date={this.state.currentDate}
              onDateChanged={this.onDateChanged}
              onMonthChange={this.onMonthChange}
              theme={{ todayButtonTextColor: "#2E2E2E" }}
              showTodayButton={false}
              disabledOpacity={0.6}
            >
              <ExpandableCalendar
                ref={(reff) => (this.calendarRef = reff)}
                firstDay={0}
                renderHeader={(date) => {
                  return (
                    <Text style={{fontSize: 18, fontWeight: "bold", color: Colors.black}}>{this.state.currentMonth}</Text>
                  );
                }}
                markedDates={this.state.markedDates}
                theme={this.getTheme()}
                leftArrowImageSource={Images.arrow_left}
                rightArrowImageSource={Images.arrow_right}
              />
              <TimelineCalendar
                format24h={false}
                eventTapped={(e) => this.eventClick(e)}
                events={this.state.event.filter((event) =>
                  moment(event.day).isSame(this.state.currentDate, "day")
                )}
              // allDayEvents={this.state.eventAllDay.filter((events) =>
              //   moment(events.day).isSame(this.state.currentDate, "day")
              //  )}
              //  scrollToFirst={true}
              //start={7}
              // end={24}
              />
            </CalendarProvider>
          )}

          {this.state.listClick && this.state.event.length > 0 && (
            <FlatList
              data={this.state.event}
              renderItem={this.renderFlatListItem}
              ItemSeparatorComponent={this.itemSeparator}
              keyExtractor={(item, index) => index.toString()}
              onEndReachedThreshold={0}
              onRefresh={() => this._onReset()}
              refreshing={this.state.refreshing}
            />
          )}
          {this.renderNoDeliveryRequest()}
        </View>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}
      </AppView>
       }
       </>
    );
  }
}
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
    // height: hp("50%"),
  },
  parentContainer: {
    flex: 1,
    backgroundColor: "#FCFBFC",
  },
  headerContainer: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderBottomWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  image: {
    marginRight: wp("6%"),
    marginBottom: hp("2%"),
  },
  headerRowContainer: {
    flex: 1,
    height: hp("15%"),
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  title: {
    color: Colors.black,
    fontSize: wp("5%"),
    fontFamily: Fonts.montserratBold,
    marginBottom: hp("2%"),
    marginLeft: wp("4%"),
    width: wp("50%"),
  },
  calendar: {
    paddingLeft: 0,
  },
  section: {
    backgroundColor: "#f0f4f7",
    color: "#79838a",
  },
  item: {
    padding: 20,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#e8ecf0",
    flexDirection: "row",
  },
  itemHourText: {
    color: "black",
  },
  itemTitleText: {
    color: "black",
    marginLeft: 16,
    fontWeight: "bold",
    fontSize: wp("4.5%"),
    fontFamily: "Montserrat-semibold",
  },
  itemButtonContainer: {
    flex: 1,
    alignItems: "flex-end",
  },
  emptyItem: {
    paddingLeft: 20,
    height: 52,
    justifyContent: "center",
    borderBottomWidth: 1,
    borderBottomColor: "#e8ecf0",
  },
  emptyItemText: {
    color: "#79838a",
    fontSize: 14,
  },
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 3,
    alignSelf: "center",
    flexDirection: "row",
    flex: 1,
  },
  noDRView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  noDRText: {
    alignSelf: "center",
    position: "absolute",
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("45%"),
  },
  filterCountView: {
    position: "absolute",
    marginTop: -10,
    right: -10,
    backgroundColor: Colors.themeColor,
    width: 16,
    justifyContent: "center",
    alignItems: "center",
    height: 16,
    borderRadius: 8,
  },
});
const modalStyles = StyleSheet.create({
  container: {
    flex: 1,
    width: wp("100%"),
    height: hp("95%"),
  },
  topContainer: {
    flexDirection: "row",
    margin: 20,
    height: 40,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 20,
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  buttonContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-end",
    marginBottom: hp("15%"),
    justifyContent: "space-around",
  },
  cancelButton: {
    backgroundColor: `rgba(117,117,117,0.2)`,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonBackground,
    fontSize: wp("4.5%"),
    fontFamily: Fonts.montserratBold,
  },
  applyButton: {
    backgroundColor: Colors.themeOpacity,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  applyText: {
    color: Colors.themeColor,
    fontSize: wp("4.5%"),
    fontFamily: Fonts.montserratBold,
  },
  filtermodal: {
    paddingTop: 45,
    paddingBottom: 30,
    margin: 0,
    backgroundColor: Colors.white,
  },
});
const searchStyles = StyleSheet.create({
  searchHeader: {
    marginTop: hp("2%"),
    height: hp("18%"),
    width: wp("95%"),
    alignSelf: "center",
  },
  mainContainer: {
    width: "100%",
    height: hp("6%"),
    flexDirection: "row",
    alignItems: "center",
  },
  closeBtn: {
    width: wp("15%"),
    height: hp("8%"),
    marginLeft: wp("2%"),
    justifyContent: "center",
    alignItems: "center",
  },
  closeImg: {
    width: wp("5%"),
    height: hp("5%"),
  },
  titleText: {
    color: Colors.black,
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratSemiBold,
  },
  searchPageTitle: { flex: 1, justifyContent: "center", alignItems: "center" },
  txtInputStyles: {
    color: Colors.black,
    fontSize: 14,
    width: "75%",
    marginLeft: wp("10%"),
    fontFamily: Fonts.montserratMedium,
    paddingTop: 10,
  },
  txtTitleStyles: {
    marginLeft: wp("10%"),
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  clearSearchView: {
    position: "absolute",
    right: wp("5%"),
    width: wp("10%"),
    height: hp("5%"),
    marginTop: hp("3%"),
    justifyContent: "flex-end",
    alignItems: "center",
  },
});
const mapStateToProps = (state) => {
  const {
    projectDetails,
    checkCameBack,
    projectSwitched,
    refresh_dashboard,
    isRefreshCalendarSettings,
    userDetails,
  } = state.LoginReducer;
  return {
    projectDetails,
    checkCameBack,
    projectSwitched,
    refresh_dashboard,
    userDetails,
    isRefreshCalendarSettings,
  };
};

export default connect(mapStateToProps, {
  showDeliverdetailsid,
  // showInspectiondetailsid,
  onTapDetail,
  cameBack,
  storeLastid,
  storeProjectRole,
  toggleAddCalendar,
  lastCraneId,
  refreshDashboard,
  refreshCalendarSettings,
  eventDisplayPage,
  eventDisplayData,
  enableEditEvents,
  setSelectedCalendarDate,

})(CalendarSettings);
