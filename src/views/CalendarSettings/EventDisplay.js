import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  SafeAreaView,
  TouchableWithoutFeedback,
  Platform,
} from "react-native";import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { AppLoader, } from "../../components";
import Colors from "../../common/color";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import {getSingleEvent,putDeleteEvent} from "../../api/Api";
import {  GET_SINGLE_EVENT,PUT_DELETE_EVENT} from "../../api/Constants";
import {  eventDisplayData,refreshCalendarSettings,editEventData,refreshEventDisplay} from "../../actions/postAction";
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import moment from "moment";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
class EventDisplay extends Component {
    constructor(props){
        super(props);
        this.state={
            description:"",
            day:"",
            isAllDay:"",
            location:"",
            recurrence:"",
            date:"",
            allDay:"",
            id:0,
            showToaster: false,
            timezone:'',
            occurs:"",
            showLoader:false,
            wholeData:[],
            isNetworkCheck:false
        };
    }
  componentWillReceiveProps(nextProps) {
      if (nextProps.isRefreshEventDisplay) {
        this.getEvent();
        this.props.refreshEventDisplay(false);
      }
    }
  componentDidMount(){
    if( Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      let data=this.props.eventData
      let day= moment(data.day).format("ddd");
      let date= moment(data.start).format("MM/DD/YYYY");
      let startTime=moment(data.start).format("hh:mm A");
      let endTime=moment(data.end).format("hh:mm A");
      let fullDate=`${day} ${date} ${startTime}-${endTime}`;
      let allDay=`${day} ${date} - All Day`;
      //TODO FOR LATER
     // this.onOccurs(data.start)
      this.setState({
          description:data.title,
          day:data.day,
          isAllDay:data.isAllDay,
          fulldate:fullDate,
          date:fullDate,
          allDay:allDay,
          id:data.id,
          //TODO FOR LATER
          // location:data.TimeZone.location,
          // recurrence:data.recurrence,
  
      })
      this.getEvent();
      this.occurMessage(data)
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      let data=this.props.eventData
      let day= moment(data.day).format("ddd");
      let date= moment(data.start).format("MM/DD/YYYY");
      let startTime=moment(data.start).format("hh:mm A");
      let endTime=moment(data.end).format("hh:mm A");
      let fullDate=`${day} ${date} ${startTime}-${endTime}`;
      let allDay=`${day} ${date} - All Day`;
      //TODO FOR LATER
     // this.onOccurs(data.start)
      this.setState({
          description:data.title,
          day:data.day,
          isAllDay:data.isAllDay,
          fulldate:fullDate,
          date:fullDate,
          allDay:allDay,
          id:data.id,
          //TODO FOR LATER
          // location:data.TimeZone.location,
          // recurrence:data.recurrence,
  
      })
      this.getEvent();
      this.occurMessage(data)
    }
    })
  }

  occurMessage=(data)=>{
    this.message = 'Occurs every day';
    if (data.repeatEveryType === 'Day') {
      this.message = '';
      this.message = 'Occurs every day';
    }
    if (data.repeatEveryType === 'Days') {
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} days`;
      }
    }
    if (data.repeatEveryType === 'Week') {
      this.message = '';
      let weekDays = '';
      data.days.forEach((day1) => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (data.repeatEveryType === 'Weeks') {
      let weekDays = '';
      data.days.forEach((day1)=> {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} weeks on ${weekDays}`;
      }
    }
    if (data.repeatEveryType === 'Month' || data.repeatEveryType === 'Months' || data.repeatEveryType === 'Year' || data.repeatEveryType === 'Years') {
      if (data.chosenDateOfMonth) {
        this.message = `Occurs on day ${data.dateOfMonth}`;
      } else {
        this.message = `Occurs on the ${data.monthlyRepeatType}`;
      }
    }
    this.message += ` until ${moment(data.occurTime).format('MM-DD-YYYY')}`;
    this.setState({occurs:this.message})
  }
  getEvent=()=>{
    let timezoneoffset=`${moment().utcOffset()}`;
    let timehours=`${moment(moment().utcOffset()).format('hh:mm')}`;
    this.setState({showLoader:true})
    let url=`${GET_SINGLE_EVENT}/${this.props.eventData.id}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`
    getSingleEvent(url,{},timezoneoffset,timehours,()=>{},(response)=>{
      this.setState({showLoader:false})
      if(response.status){
        if(response.status==200){
          let timezones=response.data.event.TimeZone!=null?response.data.event.TimeZone.location:'';
          this.setState({timezone:timezones,wholeData:response.data.event})
        }else if(response.status==400){
          this.showToaster('error',response.data.message)
        }else{
          this.showToaster('error',"Something Went Wrong")
        }
    }else{
      this.showToaster('error',"Something Went Wrong")
    }
    })
  }
  deleteEvent=()=>{
      let url=`${PUT_DELETE_EVENT}/${this.state.id}?ParentCompanyId=${this.props.projectDetails.ParentCompany.id}&ProjectId=${this.props.projectDetails.id}`
      putDeleteEvent(url,{},()=>null,(response)=>{
          if(response.status){
              if(response.status==200){                 
                mixPanelTrackEvent('Deleted Calendar Event',{})
                this.props.refreshCalendarSettings(true);
                this.showToaster('success',response.data.message)
                setTimeout(()=>this.props.navigation.goBack(),2000)

              }else if(response.status==400){
                this.showToaster('error',response.data.message)
              }else{
                this.showToaster('error',"Something Went Wrong")
              }
          }else{
            this.showToaster('error',"Something Went Wrong")
          }
      })
  }

showToaster = (type, message) => {
  this.setState(
    {
      showToaster: true,
      toastType: type,
      toastMessage: message,
      disableSubmit: false,
    },
    () => {
      setTimeout(() => {
        this.setState({
          showToaster: false,
        });
      }, 2000);
    }
  );
};

    renderHeader = () => {
        return (
          <View style={drStyles.header}>
            <TouchableWithoutFeedback
              onPress={() => {
               // this.props.cameBack(false);
                this.props.navigation.goBack();
              }}
            >
              <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
            </TouchableWithoutFeedback>
            <Text style={drStyles.title}>{Strings.dsiplayEvent.title}</Text>
           {this.props.isEnableEvents&&<>           
            <TouchableOpacity
              style={{
                width: 25,
                height: 25,
                justifyContent: "center",
                alignItems: "center",
                borderColor: Colors.black,
              }}
              onPress={() => {
                this.deleteEvent()
              }}
            >
              <Image source={Images.deleteBin} style={{height:17,width:17}}/>
          
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                width: 25,
                height: 25,
                //borderRadius: 25 / 2,
                marginRight: 20,
                marginLeft:10,
                justifyContent: "center",
                alignItems: "center",
                //borderWidth: 2,
                borderColor: Colors.black,
              }}
              onPress={() => {
                this.props.editEventData(this.state.wholeData)
                this.props.navigation.navigate("AddNewEvent");
              }}
            >
              <Image source={Images.editBlack} style={{height:17,width:17}}/>
          
            </TouchableOpacity></>}
          </View>
        );
      };
     
  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={drStyles.safeArea}>
        <View style={drStyles.parentContainer}>
          {this.renderHeader()}

        <Text style={drStyles.des}> {this.state.description}</Text>

        <View style={drStyles.timeView}>
        <Image source={Images.clockGrey} style={{height:15,width:15}}/>
        <Text style={drStyles.date}>{this.state.isAllDay?this.state.allDay:this.state.date}</Text>
        </View>

        <Text style={drStyles.timezone}>{this.state.timezone}</Text>
        <Text style={drStyles.timezone}>{this.state.occurs}</Text>

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

{this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}
        </View>
      </SafeAreaView>
       }
       </>
    );
  }
}


const drStyles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: Colors.white,
    },
    parentContainer: {
      flex: 1,
      backgroundColor: Colors.white,
    },
    header: {
      width: wp("100%"),
      height: hp("8%"),
      flexDirection: "row",
      alignItems: "flex-end",
      paddingBottom: hp("3%"),
      paddingLeft: wp("4%"),
    },
    title: {
      width: wp("70%"),
      fontSize: wp("5.6%"),
      textAlign: "center",
      fontFamily: Fonts.montserratSemiBold,
      //backgroundColor:'pink'
    },
    timeView:{
        flexDirection:'row',
        marginLeft:20,
        marginRight:20,
        marginTop:20,
    },
    des:{
        fontSize:16,
        fontFamily:Fonts.montserratBlack,
        marginLeft:20,
        marginRight:20,
        fontWeight:"700",
        textAlign:"left",
    },
    date:{
        fontSize:14,
        fontFamily:Fonts.montserratRegular,
        marginLeft:10,
    },
    timezone:{
      fontSize:14,
      fontFamily:Fonts.montserratRegular,
      marginLeft:40,
      marginTop:10,
      marginRight:30,
    },
    memberContainer: {
      width: wp("90%"),
      height: hp("10%"),
      alignSelf: "center",
      marginTop: hp("1%"),
      justifyContent: "center",
    },
    idTitle: {
      color: Colors.placeholder,
      fontSize: wp("4%"),
      fontFamily: Fonts.montserratMedium,
    },
    idText: {
      width: wp("90%"),
      height: hp("5%"),
      backgroundColor: "#EFEFEF",
      marginTop: wp("1%"),
      padding: hp("1%"),
      paddingLeft: 15,
      color: Colors.themeColor,
      fontFamily: Fonts.montserratMedium,
      fontSize: wp("4%"),
    },
    escortContainer: {
      width: "90%",
      alignSelf: "center",
      marginTop: hp("4%"),
      justifyContent: "space-between",
      flexDirection: "row",
    },
    escortText: {
      color: Colors.placeholder,
      fontSize: wp("4%"),
      fontFamily: Fonts.montserratRegular,
    },
    bottomContainer: {
      width: wp("90%"),
      flexDirection: "row",
      justifyContent: "center",
      alignSelf: "center",
      marginBottom: hp("4%"),
      marginTop: hp("8%"),
    },
    cancel: {
      width: wp("35%"),
      height: hp("7%"),
      backgroundColor: Colors.shadowColor,
      marginRight: wp("3%"),
      borderRadius: hp("3.5%"),
      justifyContent: "center",
      alignItems: "center",
    },
    submit: {
      width: wp("35%"),
      height: hp("7%"),
      backgroundColor: Colors.themeOpacity,
      marginLeft: wp("3%"),
      borderRadius: hp("3.5%"),
      justifyContent: "center",
      alignItems: "center",
    },
    cancelText: {
      color: "#757575",
      fontFamily: Fonts.montserratSemiBold,
      fontSize: wp("4%"),
    },
    submitText: {
      color: Colors.themeColor,
      fontFamily: Fonts.montserratSemiBold,
      fontSize: wp("4%"),
    },
    root: {
      justifyContent: "center",
      borderRadius: 50,
      backgroundColor: "#e0e0e0",
      paddingHorizontal: 10,
      paddingVertical: 4,
      height: 28,
      marginBottom: 4,
      marginRight: 4,
    },
  });
  const mapStateToProps = (state) => {
    const {
      projectDetails,
      eventData,
      isRefreshEventDisplay,
      isEnableEvents,
    } = state.LoginReducer;
    return {
      projectDetails,
      eventData,
      isRefreshEventDisplay,
      isEnableEvents,
    };
  };
  
  export default connect(mapStateToProps, {
    eventDisplayData,
    refreshCalendarSettings,
    editEventData,
    refreshEventDisplay,
  })(EventDisplay);