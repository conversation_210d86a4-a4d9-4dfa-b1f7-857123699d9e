import React, { Component } from "react";
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
  Switch,
  FlatList,
  ScrollView,
  TextInput,
  Button,
  Alert,
} from "react-native";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { TextField } from "../../components/textinput/addMemberTextinput";
import { isEmpty } from "../../common/validators";
import {
  GET_EQUIP_LIST,
  GET_GATE_LIST,
  SEARCH_MEMBER,
  EDIT_CONCRETE,
  ADD_CONCRETE,
  CONCRETE_DROPDOWN_DETAILS,
  GET_SINGLE_CONCRETE,
  GET_PROJECT_ROLE,
  LIST_ALL_MEMBER,
  GET_SINGLE_PROJECT,
  GET_TIMEZONE,
  GET_LOCATION_DETAILS,
  NO_EQUIPMENT_NEEDED,
} from "../../api/Constants";
import Modal from "react-native-modal";
import { Selectize, Chip } from "react-native-material-selectize";
import DateTimePicker from "@react-native-community/datetimepicker";
import moment from "moment";
import MultiSelectDropDown from "../../components/multi-select-dropdown/multiSelectdropDown";
import DeletePop from "../../components/toastpopup/logoutPop";
import {
  CommonStyles,
  Fonts,
  Strings,
  Colors,
  Images,
  Dimensions,
} from "../../common";
import {
  changeTab,
  showSideMenu,
  cameBack,
  updateList,
  refreshDashboard,
  refreshDeliveryList,
  refreshCalendar,
  editConcreteRequest,
  selectedConcreteLocationsData,
  selectedConcreteMixDesignsData,
  selectedConcretePlacementsData,
  selectedConcretePumpSizesData,
} from "../../actions/postAction";
import {
  searchMember,
  getGateList,
  getDeliveryDetails,
  addConcreteRequest,
  getConcreteDropDownDetails,
  _getData,
  getAllMemberList,
  getTimeZone,
  getEquipList,
  getLocationSettings,
} from "../../api/Api";
import AppLoader from "../../components/apploader/AppLoader";
import HoursModal from "../../components/time/HoursModal";
import {
  trackScreen,
  trackEvent,
} from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent } from "../../MixPanel/MixPanel";
import DropDownPicker from "../../components/dropdown/DropDownPicker";
import Dropdown from "../../components/dropdown/dropdown";
import RecurrenceComponent from "../../components/Recurrence/RecurrenceComponent";
import NetInfo from "@react-native-community/netinfo";
import NoInternet from "../../components/NoInternet/noInternet";
import * as RNLocalize from "react-native-localize";
const API_KEY = "AIzaSyB2EHXZ7yQZ3KItOpshW2F4DJ5ewmagOWU";
import axios from "axios";
// import { AutoCompleteList } from "..";
import {
  googleAutoCompleteAPI,
  geoCodeAdrressAPI,
} from "../../api/GoogleServices";
import AsyncStorage from "@react-native-async-storage/async-storage";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";
let selectedDate = new Date();
let selectedPumpDate = new Date();
let concreteConfirmedDate = new Date();
let pumpConfirmedDate = new Date();
let selectedStartDate = "";
let selectedEndDate = "";
let selectedPumpStartDate = "";
let selectedPumpEndDate = "";

const range = (start, end) => {
  return Array(end - start + 1)
    .fill()
    .map((_, idx) => start + idx);
};

let hoursData = range(0, 12);
let minutesData = range(0, 60);

let backupLocationData = [];
let backupPlacementData = [];
let backupMixDesignData = [];

let backupPumpsizeData = [];
let backupConcreteSuppliers = [];

//Default calender date will be 7th day from current date
let initialCalDate = new Date(
  moment(new Date()).add(7, "days").format("YYYY-MM-DD"),
);

const vehicle_Type = [
  { type: "Medium and Heavy Duty Truck" },
  { type: "Passenger Car" },
  { type: "Light Duty Truck" },
];

const vehicleTypeOptions = vehicle_Type.map((item) => ({
  label: item.type,
  value: item.type,
  name: item.type,
}));

class AddConcrete extends Component {
  handleNavigation = () => {
    const {
      selectedLocationNew,
      selectedLocationId, // Add this to your state if not already there
      selectedEquipmentList,
      equipTypeList,
      selectedGate,
      selectedGateId, // Add this to your state if not already there
      selectedTimeZone, // Add this to your state if not already there
      selectedTimeZoneId, // Add this to your state if not already there
      selectedDate,
      selectedStartTime,
      selectedEndTime,
      projectId,
    } = this.state;

    let errorMessage = "";

    if (!selectedLocationNew) {
      errorMessage = "Please select a location";
    }
    // } else if (!selectedGate) {
    //   errorMessage = "Please select a gate";
    // } else if (
    //   (!selectedEquipmentList || selectedEquipmentList.length === 0) &&
    //   (!equipTypeList || equipTypeList.length === 0)
    // ) {
    //   errorMessage = "Please select at least one equipment";
    // }

    if (errorMessage) {
      Alert.alert("Validation Error", errorMessage);
      return;
    }
    const equipmentSource =
      selectedEquipmentList && selectedEquipmentList.length > 0
        ? selectedEquipmentList
        : equipTypeList;

    const equipmentToSend = Array.isArray(equipmentSource)
      ? equipmentSource.filter((item) => item && item.selected === true)
      : [];

    // if (equipmentToSend.length === 0) {
    //   Alert.alert("Validation Error", "Please select at least one equipment");
    //   return;
    // }

    const navParams = {
      location: selectedLocationNew,
      locationId: selectedLocationId, // Add location ID
      equipment: equipmentToSend,
      gate: selectedGate,
      gateId: selectedGateId, // Add gate ID
      timeZone: selectedTimeZone, // Add time zone
      timeZoneId: selectedTimeZoneId, // Add time zone ID
      bookingId: this.state.bookingId || null,
      projectId: projectId,
      ...(selectedDate && { date: selectedDate.toString() }),
      // Only include times if they exist
      ...(selectedStartTime && { startTime: selectedStartTime.toString() }),
      ...(selectedEndTime && { endTime: selectedEndTime.toString() }),
      prevParams: this.props.route.params,
    };
    this.props.navigation.navigate("timeSlotConcrete", navParams);
  };
  constructor(props) {
    super(props);
    this.state = {
      selectedLocation: this.props.selectedConcreteLocations,
      locationsData: [],
      selectedLocationsList: [],
      selectedLocationId: 0,
      description: "",
      placement: "",
      selectedPlacement: this.props.selectedConcretePlacements,
      placementsData: [],
      selectedPlacementsList: [],
      concreteId: 1,
      vehicleType: null,
      pumpVehicleType: null,
      vehicleTypeOptions: vehicleTypeOptions,
      vehicleModelVisible: false,
      pumpVehicleModelVisible: false,
      responisblePerson: "",
      selectedDate: "",
      showDateModal: false,
      showMultipleSec: false,
      selectedStartTime: "",
      selectedEndTime: "",
      selectedPumpStartTime: "",
      selectedPumpEndTime: "",
      truckSpacingHrs: "",
      truckSpacingMins: "0",
      completePlacementHrs: "1",
      completePlacementMins: "0",
      calSelectedDate: new Date(), //moment(new Date).add(7, 'day').format('YYYY-MM-DD'),
      calSelectedStartTime: new Date(),
      calSelectedEndTime: new Date(), //new Date(dt.setHours(dt.getHours() + 1)),
      calSelectedPumpStartTime: new Date(),
      calSelectedPumpEndTime: new Date(),
      minimumDate: new Date(),
      minimumStartTime: new Date(),
      minimumEndTime: new Date(),
      showStartTimeModal: false,
      showEndTimeModal: false,
      showTruckSpacingHrsModal: false,
      showTruckSpacingMinsModal: false,
      showCompletePlacementHrsModal: false,
      showCompletePlacementMinsModal: false,
      orderNo: "",
      slump: "",
      quantity: "",
      primer: "",
      primerList: [
        {
          id: 1,
          name: "Yes",
          value: "Yes",
          label: "Yes",
          selected: false,
        },
        {
          id: 2,
          name: "No",
          value: "No",
          label: "No",
          selected: false,
        },
      ],
      isConcreteConfirmed: false,
      isPumpRequired: false,
      pumpOrderedDate: "",
      pumpLocation: "",
      pumpShowUp: "",
      isPumpConfirmed: false,
      pumpSelectedDate: new Date(),
      showPumpDateModal: false,
      responsiblePersonData: [],
      selectedItem: [
        {
          id: `${this.props.responsiblePersonData.name}`,
          email: this.props.userDetails.email,
          userId: this.props.responsiblePersonData.id,
        },
      ],
      selectedItemIndex: 0,
      selectedItemList: [],
      equipTypeList: [],
      gateList: [],
      selectedGate: null,
      selectedEquipName: null,
      selectedResponsibleCompany: null,
      projectId: 0,
      disableSubmit: false,
      showLoader: false,
      showToaster: false,
      toastType: "",
      toastMessage: "",
      showCancel: false,
      mixDesign: "",
      selectedMixDesign: this.props.selectedConcreteMixDesigns,
      mixDesignsData: [],
      selectedMixDesignsList: [],
      selectedPumpSize: this.props.selectedConcretePumpSizes,
      pumpSizeData: [],
      selectedPumpSizeList: [],
      hoursModal: false,
      minsModal: false,

      scrollPosition: 0,
      showInfo: false,
      editConcrete: false,
      selectedPumpLocation: [],
      pumpLocationsData: [],
      selectedPumpLocationsList: [],
      cubicYardsTotal: "",
      concreteSupplier: [],
      editedData: null,
      editedConcreteSupplierList: [],
      editConcreteId: 0,
      notes: "",
      status: "Tentative",
      mixpanelParam: {
        ProjectName: this.props.projectDetails.projectName,
        CompanyName:
          this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName: this.props.userDetails.firstName,
      },
      isEditDate: true,
      controlledByList: [],
      deactiveDateChecker: new Date(),
      // Recurrence Component Props
      recurrence: Strings.calendarSettings.doseNotRepeat,
      isRecurrence: false,
      times: "1",
      isMonthFirstCheck: true,
      isMonthSecondCheck: false,
      isMonthThirdCheck: false,
      isYearFirstCheck: true,
      isYearSecondCheck: false,
      isYearThirdCheck: false,
      monthlyDay: "",
      monthlyLastDay: "",
      endDateRecurrence: moment(new Date()).format("MM/DD/YYYY"),
      selectedDaysOccurs: "",
      selectedDayArray: [],
      listSetName: "",
      yearListSetName: "",
      selectedEndDateYear: new Date(),
      timeZoneList: [],
      locationDropdownList: [],
      selectedLocationNew: "",
      additionalLocationText: "",
      additionalLocationTextId: 0,
      selectedTimeZone: "",
      selectedTimeZoneId: 0,
      editedZoneID: "",
      daysList: [
        { key: 1, name: "Sunday", label: "S", selected: true },
        { key: 2, name: "Monday", label: "M", selected: true },
        { key: 3, name: "Tuesday", label: "T", selected: true },
        { key: 4, name: "Wednesday", label: "W", selected: true },
        { key: 5, name: "Thursday", label: "T", selected: true },
        { key: 6, name: "Friday", label: "F", selected: true },
        { key: 7, name: "Saturday", label: "S", selected: true },
      ],
      isFromDate: false,
      isNetworkCheck: false,
      timeZoneParam: RNLocalize.getTimeZone(),
      editRequestID: "",
      recurrenceSeriesID: "",
      recurrenceEndDateSeries: "",
      recurrenceDeliverStartDate: "",
      recurrenceDeliverEndDate: "",
      recurrenceType: Strings.calendarSettings.doseNotRepeat,
      recurrenceEndDateRes: "",
      deliveryStatus: "",
      textFieldValue: "",
      predictionList: [],
      showAutoComplete: false,
      isModalVisible: false,
      selectedAddress: "",
      pumpTextFieldValue: "",
      pumpPredictionList: [],
      pumpShowAutoComplete: false,
      isPumpModalVisible: false,
      selectedPumpAddress: "",
      selectedDay: [],
      gateModalVisible: false,
      storeEquipmentList: [],
      isFutureEquipment: false,
      editIN: false,
      fullLocationData: [],
      selectedEquipmentList: [],
      concreteStartDate: null,
      concreteEndDate: null,
      timeSlotExplicitlyChanged: false, // Flag to track if user explicitly changed time via time slot picker
    };
    // this.searchMember = this.searchMember.bind(this);
    this.onPressVehicleType = this.onPressVehicleType.bind(this);
    this.onPressPumpVehicleType = this.onPressPumpVehicleType.bind(this);
  }

  componentDidUpdate(prevProps) {
    // Removed automatic loadData call to prevent unnecessary checks on every state update
    // Data loading is now handled by the focus listener
  }

  componentWillUnmount() {
    // Remove the focus listener when component unmounts
    if (this.focusListener) {
      this.focusListener();
    }
  }

  loadData = async () => {
    const back = await AsyncStorage.getItem("Isback");
    try {
      if (back === "true") {
        const savedData = await AsyncStorage.getItem("DRDateTime");

        if (savedData) {
          const { date, fromTime, toTime } = JSON.parse(savedData);

          // Convert loaded strings to Date objects
          const loadedDate = moment(date, "MM/DD/YYYY").toDate();
          const loadedStartTime = this.parseTimeString(fromTime, loadedDate);
          const loadedEndTime = this.parseTimeString(toTime, loadedDate);

          this.setState(
            {
              selectedDate: date,
              calSelectedDate: loadedDate,
              selectedStartTime: fromTime,
              calSelectedStartTime: loadedStartTime,
              selectedEndTime: toTime,
              calSelectedEndTime: loadedEndTime,
            },
            () => { },
          );

          // Update global variables
          //selectedDeliveryDate = loadedDate; no where used
          selectedStartDate = loadedStartTime;
          selectedEndDate = loadedEndTime;

          // Set flag to indicate user explicitly changed time via time slot picker
          this.setState({ timeSlotExplicitlyChanged: true });
        }
        await AsyncStorage.setItem("Isback", "false");
      }
    } catch (error) {
      console.error("[LOAD] Error:", error);
    }
  };
  parseTimeString = (timeString, baseDate) => {
    if (!timeString) return new Date(baseDate);

    const timeParts = timeString.split(/:| /);
    let hours = parseInt(timeParts[0]);
    const minutes = parseInt(timeParts[1]);
    const period = timeParts[2].toLowerCase();

    // Convert to 24-hour format
    if (period === "pm" && hours < 12) hours += 12;
    if (period === "am" && hours === 12) hours = 0;

    return new Date(
      baseDate.getFullYear(),
      baseDate.getMonth(),
      baseDate.getDate(),
      hours,
      minutes,
    );
  };

  // Helper function to extract timezone offset from selectedTimeZone string
  getTimezoneOffset = () => {
    const timeZoneString = this.state.selectedTimeZone;
    if (!timeZoneString) return "+00:00";

    // Extract offset from strings like "(UTC-10:00) Hawaii" or "(UTC+05:30) India"
    const offsetMatch = timeZoneString.match(/\(UTC([+-]\d{1,2}:\d{2})\)/);
    if (offsetMatch) {
      return offsetMatch[1];
    }
    return "+00:00";
  };

  // Helper function to format date with timezone offset
  formatDateWithTimezone = (date) => {
    if (!date) return null;

    const offset = this.getTimezoneOffset();

    // Use local date components to avoid timezone conversion issues
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${offset}`;
  };

  onPressVehicleType(item) {
    this.setState({
      vehicleType: item.value,
      vehicleModelVisible: false,
    });
  }

  onPressPumpVehicleType(item) {
    this.setState({
      pumpVehicleType: item.value,
      pumpVehicleModelVisible: false,
    });
  }

  getPlacePrediction = async (keyword) => {
    const showPlaces = keyword.toString().length > 0;
    const placeURL = googleAutoCompleteAPI(API_KEY, keyword);
    const placeResponse = await axios.get(placeURL);
    this.setState({
      showAutoComplete: showPlaces,
      predictionList: placeResponse.data.predictions,
    });
  };

  // Method to handle place selection
  onSelectPlace = async (place) => {
    Keyboard.dismiss();
    this.setState({
      showAutoComplete: false,
      textFieldValue: place.description,
      isModalVisible: false, // Close the modal on selection
      selectedAddress: place.description, // Update selected address to display outside modal
    });

    const URL = geoCodeAdrressAPI(place.description, API_KEY);
    const addressAPI = await axios.get(URL);
    const addressCoordinate = {
      latitude: addressAPI.data.results[0].geometry.location.lat,
      longitude: addressAPI.data.results[0].geometry.location.lng,
    };
  };

  // Toggle modal visibility
  toggleModal = () => {
    this.setState({ isModalVisible: !this.state.isModalVisible });
  };

  getPlacePredictionPump = async (keyword) => {
    const showPlaces = keyword.toString().length > 0;
    const placeURL = googleAutoCompleteAPI(API_KEY, keyword);
    const placeResponse = await axios.get(placeURL);
    this.setState({
      pumpShowAutoComplete: showPlaces,
      pumpPredictionList: placeResponse.data.predictions,
    });
  };

  onSelectPlacePump = async (place) => {
    Keyboard.dismiss();
    this.setState({
      pumpShowAutoComplete: false,
      pumpTextFieldValue: place.description,
      isPumpModalVisible: false,
      selectedPumpAddress: place.description,
    });

    const URL = geoCodeAdrressAPI(place.description, API_KEY);
    const addressAPI = await axios.get(URL);
    const addressCoordinate = {
      latitude: addressAPI.data.results[0].geometry.location.lat,
      longitude: addressAPI.data.results[0].geometry.location.lng,
    };
  };

  togglePumpModal = () => {
    this.setState({ isPumpModalVisible: !this.state.isPumpModalVisible });
  };

  initialDateTimeSetup = () => {
    let date = new Date();
    // old code == > date.setDate(date.getDate() + 1);
    date.setDate(date.getDate());
    date.setHours(7);
    date.setMinutes(0);
    date.setSeconds(0);
    selectedDate = date;

    //initial calselectedStartDate should be 7 am
    let initialStartTime = this.state.calSelectedStartTime;
    initialStartTime.setHours(7);
    initialStartTime.setMinutes(0);
    initialStartTime.setSeconds(0);

    //initially end time would be 1 hr ahead then the startTime
    let initialEndTime = this.state.calSelectedEndTime;
    initialEndTime.setHours(initialStartTime.getHours() + 1);
    initialEndTime.setMinutes(0);
    initialEndTime.setSeconds(0);

    this.setState(
      {
        selectedDate: "",
        // minimumDate: date,
        pumpOrderedDate: moment(new Date()).format("MM/DD/YYYY"),
        calSelectedStartTime: "",
        calSelectedEndTime: "",
        calSelectedPumpStartTime: initialStartTime,
        calSelectedPumpEndTime: initialEndTime,
        selectedStartTime: "",
        selectedEndTime: "",
        selectedPumpStartTime: moment(initialStartTime).format("h:mm a"),
        selectedPumpEndTime: moment(initialEndTime).format("h:mm a"),
      },
      () => {
        this.checkAndUpdateSelectedDate();
      },
    );
    selectedStartDate = initialStartTime;
    selectedEndDate = initialEndTime;
    selectedPumpStartDate = initialStartTime;
    selectedPumpEndDate = initialEndTime;
  };

  // getDropdownDetails = () => {
  //   let url = `${CONCRETE_DROPDOWN_DETAILS}?ProjectId=${this.state.projectId}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`;
  //   try {
  //     getConcreteDropDownDetails(
  //       url,
  //       {},
  //       () => null,
  //       (response) => {
  //         this.setState({
  //           showLoader: false,
  //         });

  //         if (response.toString() == Strings.errors.timeout) {
  //           this.setState(
  //             {
  //               showToaster: true,
  //               toastMessage: Strings.errors.checkInternet,
  //               type: "error",
  //             },
  //             () => {
  //               setTimeout(() => {
  //                 this.setState({
  //                   showToaster: false,
  //                   disableSubmit: false,
  //                 });
  //               }, 2000);
  //             }
  //           );
  //         } else if (response.status) {
  //           if (response.status == 200 || response.status == 201) {
  //             const {data} = response.data
  //             if (data && data != null && data != undefined) {
  //               if (!this.state.editConcrete) {
  //                 this.setState({concreteId: data.ConcreteRequestId})
  //               }
  //               const {locationsData, placementsData, mixDesignsData, pumpSizeData} = this.state;
  //               let defaultLocation={}
  //               data.locationDropdown.map((item) => {
  //                 if (item.isDefault) defaultLocation = {id: item.id, label: item.locationPath, value:item.locationPath }
  //                 this.state.locationDropdownList.push({id: item.id, label: item.locationPath, value:item.locationPath })
  //                 this.state.locationsData.push({id: item.location, uniqueId: item.id, chosenFromDropdown: true})
  //               })
  //               this.setState({selectedLocationNew: defaultLocation != null ? defaultLocation.value : null,
  //                 selectedLocationId:
  //                 defaultLocation != null ? defaultLocation?.id : null,})
  //               // data.placementDropdown.map((item) => {
  //               //   this.state.placementsData.push({id: item.placement, uniqueId: item.id, chosenFromDropdown: true})
  //               // })
  //               data.mixDesignDropdown.map((item) => {
  //                 this.state.mixDesignsData.push({id: item.mixDesign, uniqueId: item.id, chosenFromDropdown: true})
  //               })
  //               data.pumpSizeDropdown.map((item) => {
  //                 this.state.pumpSizeData.push({id: item.pumpSize, uniqueId: item.id, chosenFromDropdown: true})
  //               })
  //               if (response.data.data.concreteSupplierDropdown.length !== 0) {
  //                 let responsibleCompany = [];

  //                 for (let item of response.data.data.concreteSupplierDropdown) {
  //                   responsibleCompany.push({
  //                     id: item.id,
  //                     name: item.companyName,
  //                     label: item.companyName,
  //                     selected: false,
  //                   });
  //                 }

  //                 this.setState({
  //                   concreteSupplier: responsibleCompany,
  //                 });
  //               }
  //               this.setState({locationsData, placementsData, mixDesignsData, pumpSizeData})
  //               backupLocationData = this.state.locationsData
  //               // backupPlacementData = this.state.placementsData
  //               backupMixDesignData = this.state.mixDesignsData
  //               backupPumpsizeData = this.state.pumpSizeData

  //             }
  //           } else if (response.data.message.message) {
  //             this.showToaster("error", response.data.message.message);
  //           } else {
  //             this.showToaster("error", response.data.message);
  //           }
  //         } else {
  //           this.showToaster("error", response.toString());
  //         }
  //       }
  //     );
  //   } catch (e) {
  //   }
  // }

  getLocationDetail = () => {
    let URL = `${GET_LOCATION_DETAILS}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`;
    getLocationSettings(
      URL,
      {},
      () => null,
      (response) => {
        // Process location data
        let defaultLocation = {};
        let locationDropdownList = [];
        let locationsData = [];
        let fullLocationData = []; // Store full location data including equipment and gates

        response.data.data.forEach((item) => {
          // Build location dropdown
          const locationItem = {
            id: item.id,
            label: item.locationPath,
            value: item.locationPath,
          };
          locationDropdownList.push(locationItem);
          locationsData.push({
            id: item.locationPath,
            uniqueId: item.id,
            chosenFromDropdown: true,
          });
          fullLocationData.push(item);

          // Set default location
          if (item.isDefault) {
            defaultLocation = locationItem;
          }
        });

        // Update state with all data
        this.setState(
          {
            locationDropdownList,
            locationsData,
            selectedLocationNew: defaultLocation?.value || null,
            selectedLocationId: defaultLocation?.id || null,
            fullLocationData, // Store full data for equipment and gate filtering
          },
          () => {
            // Now filter equipment and gates based on the selected location ID
            this.filterEquipmentAndGatesByLocation();
          },
        );

        // Set backup data
        backupLocationData = locationsData;
      },
    );
  };

  filterEquipmentAndGatesByLocation = () => {
    const {
      selectedLocationId,
      fullLocationData,
      editIN,
      selectedEquipTypeId,
      selectedGateId,
    } = this.state;

    if (!selectedLocationId || !fullLocationData || !fullLocationData.length) {
      return;
    }

    let equipTypeList = [];
    let storeEquipmentList = [];
    let gateList = [];

    // Find the selected location from stored data
    const selectedLocation = fullLocationData.find(
      (item) => item.id === selectedLocationId,
    );

    // Check if "No Equipment Needed" was previously selected
    let isNoEquipmentSelected = false;
    if (
      editIN &&
      selectedEquipTypeId !== undefined &&
      selectedEquipTypeId !== null
    ) {
      const selectedIds =
        typeof selectedEquipTypeId === "string"
          ? selectedEquipTypeId.split(",").map((id) => id.trim())
          : [String(selectedEquipTypeId)];
      // Check if ID 0 (No Equipment Needed) is in the selected IDs
      isNoEquipmentSelected = selectedIds.some(
        (id) => String(id) === "0" || Number(id) === 0,
      );
    }

    // Add "No Equipment Needed" option first
    const NoEquipmentOption = {
      ...NO_EQUIPMENT_NEEDED,
      visible: true,
      disabled: false,
      selected: isNoEquipmentSelected,
    };
    equipTypeList.push(NoEquipmentOption);

    // Filter equipment based on selected location
    if (
      selectedLocation &&
      selectedLocation.EquipmentId &&
      Array.isArray(selectedLocation.EquipmentId)
    ) {
      selectedLocation.EquipmentId.forEach((equip) => {
        // Check if this equipment was previously selected (for edit mode)
        let isSelected = false;
        if (
          editIN &&
          selectedEquipTypeId !== undefined &&
          selectedEquipTypeId !== null
        ) {
          const selectedIds =
            typeof selectedEquipTypeId === "string"
              ? selectedEquipTypeId.split(",").map((id) => id.trim())
              : [String(selectedEquipTypeId)];
          isSelected = selectedIds.some(
            (id) =>
              String(id) === String(equip.id) ||
              Number(id) === Number(equip.id),
          );
        }

        equipTypeList.push({
          id: equip.id,
          value: equip.equipmentName,
          name: equip.equipmentName,
          label: equip.equipmentName,
          selected: isSelected,
          visible: true,
          disabled: false,
          isCrane: equip.PresetEquipmentType?.isCraneType || false,
        });
        storeEquipmentList.push(equip);
      });
    }

    // Filter gates based on selected location
    if (
      selectedLocation &&
      selectedLocation.gateDetails &&
      Array.isArray(selectedLocation.gateDetails)
    ) {
      selectedLocation.gateDetails.forEach((gate) => {
        // Check if this gate was previously selected (for edit mode)
        let isSelected = false;
        if (editIN && selectedGateId) {
          const selectedIds =
            typeof selectedGateId === "string"
              ? selectedGateId.split(",").map((id) => id.trim())
              : [String(selectedGateId)];
          isSelected = selectedIds.some((id) => String(id) === String(gate.id));
        }

        gateList.push({
          id: gate.id,
          name: gate.gateName,
          value: gate.gateName,
          label: gate.gateName,
          selected: isSelected,
        });
      });
    }

    // Extract selected equipment items for edit mode
    const selectedEquipmentItems = equipTypeList.filter(
      (item) => item.selected === true,
    );
    // Check if any crane equipment is selected
    const isCraneSelected = selectedEquipmentItems.some(
      (item) => item.isCrane === true,
    );

    // Extract selected gate items for edit mode
    const selectedGateItems = gateList.filter((item) => item.selected === true);
    const selectedGateNames = selectedGateItems
      .map((item) => item.name)
      .join(",");
    const selectedGateIds = selectedGateItems.map((item) => item.id).join(",");

    // Update equipment and gate lists
    this.setState(
      {
        equipTypeList: [
          ...new Map(equipTypeList.map((item) => [item.id, item])).values(),
        ],
        storeEquipmentList,
        gateList: [
          ...new Map(gateList.map((item) => [item.id, item])).values(),
        ],
        selectedEquipmentList: selectedEquipmentItems, // Initialize selected equipment list
        isAssociatedWithCraneRequest: isCraneSelected, // Set crane association
      },
      () => {
        if (this.state.editIN) {
          if (equipTypeList.length === 0) {
            this.setState({
              selectedEquipName: null,
              selectedEquipTypeId: null,
              selectedEquipmentList: [],
            });
          }
          if (gateList.length === 0) {
            this.setState({
              selectedGate: null,
              selectedGateId: null,
            });
          } else if (selectedGateItems.length > 0) {
            // Update the selected gate display string based on filtered gates
            this.setState({
              selectedGate: selectedGateNames,
              selectedGateId: selectedGateIds,
            });
          }
          // Trigger the callback to update the selected equipment display
          if (selectedEquipmentItems.length > 0) {
            this.getSelectedEquipmentList(this.state.equipTypeList);
          }
        }
      },
    );
  };

  getDropdownDetails = () => {
    let url = `${CONCRETE_DROPDOWN_DETAILS}?ProjectId=${this.state.projectId}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`;
    try {
      getConcreteDropDownDetails(
        url,
        {},
        () => null,
        (response) => {
          this.setState({
            showLoader: false,
          });

          if (response.toString() == Strings.errors.timeout) {
            this.setState(
              {
                showToaster: true,
                toastMessage: Strings.errors.checkInternet,
                type: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                    disableSubmit: false,
                  });
                }, 2000);
              },
            );
          } else if (response.status) {
            if (response.status == 200 || response.status == 201) {
              const { data } = response.data;
              if (data && data != null && data != undefined) {
                if (!this.state.editConcrete) {
                  this.setState({ concreteId: data.ConcreteRequestId });
                }
                const { placementsData, mixDesignsData, pumpSizeData } =
                  this.state;

                // Location data is now handled by getLocationDetail

                // data.placementDropdown.map((item) => {
                //   this.state.placementsData.push({id: item.placement, uniqueId: item.id, chosenFromDropdown: true})
                // })
                data.mixDesignDropdown.map((item) => {
                  this.state.mixDesignsData.push({
                    id: item.mixDesign,
                    uniqueId: item.id,
                    chosenFromDropdown: true,
                  });
                });
                data.pumpSizeDropdown.map((item) => {
                  this.state.pumpSizeData.push({
                    id: item.pumpSize,
                    uniqueId: item.id,
                    chosenFromDropdown: true,
                  });
                });
                if (response.data.data.concreteSupplierDropdown.length !== 0) {
                  let responsibleCompany = [];

                  for (let item of response.data.data
                    .concreteSupplierDropdown) {
                    responsibleCompany.push({
                      id: item.id,
                      name: item.companyName,
                      label: item.companyName,
                      selected: false,
                    });
                  }

                  this.setState({
                    concreteSupplier: responsibleCompany,
                  });
                }
                this.setState({ placementsData, mixDesignsData, pumpSizeData });
                // backupPlacementData = this.state.placementsData
                backupMixDesignData = this.state.mixDesignsData;
                backupPumpsizeData = this.state.pumpSizeData;
              }
            } else if (response.data.message.message) {
              this.showToaster("error", response.data.message.message);
            } else {
              this.showToaster("error", response.data.message);
            }
          } else {
            this.showToaster("error", response.toString());
          }
        },
      );
    } catch (e) { }
  };

  componentWillUnmount() {
    this.props.editConcreteRequest([]);
    this.props.selectedConcreteLocationsData([]);
    this.props.selectedConcretePlacementsData([]);
    this.props.selectedConcreteMixDesignsData([]);
    this.props.selectedConcretePumpSizesData([]);
    this.setState({ editedConcreteSupplierList: [] });
  }

  async componentDidMount() {
    // Add focus listener to handle data refresh when navigating back
    this.focusListener = this.props.navigation.addListener("focus", () => {
      this.loadData();
    });

    if (Platform.OS === "ios") {
      this.networkCheck();
    } else {
      this.setState({
        editRequestID: this.props.route.params
          ? this.props.route.params != undefined &&
          this.props.route.params.showEditRequestID
          : 1,
      });

      try {
        this.getInitialTimeZone();
        if (
          this.props.route.params?.notificationDetails &&
          this.props.route.params?.notificationDetails.Project
        ) {
          this.setState(
            {
              projectId:
                this.props.route.params?.notificationDetails.Project.id,
            },
            () => {
              this.loadInitial();
            },
          );
        } else {
          this.setState(
            {
              projectId: this.props.projectDetails.id,
            },
            () => {
              this.loadInitial();
            },
          );
        }
      } catch (e) {
        console.log("error", e);
      }
    }
  }

  checkAndUpdateSelectedDate = () => {
    if (this.props.route.params?.selectedDate) {
      const dateStr = this.props.route.params.selectedDate;
      let parsedMoment = moment(dateStr, "MM/DD/YYYY", true);
      if (!parsedMoment.isValid()) {
        parsedMoment = moment(dateStr);
      }

      if (parsedMoment.isValid()) {
        const parsedDate = parsedMoment.toDate();
        let selectedDeliveryDate = parsedDate;
        this.setState({
          selectedDate: parsedMoment.format("MM/DD/YYYY"),
          calSelectedDate: parsedDate,
        });
      }
    }
  };

  networkCheck = () => {
    NetInfo.addEventListener((state) => {
      if (!state.isConnected && !state.isInternetReachable) {
        this.setState({ isNetworkCheck: true });
      } else {
        this.setState({ isNetworkCheck: false });
        this.setState({
          editRequestID: this.props.route.params
            ? this.props.route.params != undefined &&
            this.props.route.params.showEditRequestID
            : 1,
        });

        try {
          this.getInitialTimeZone();
          if (
            this.props.route.params?.notificationDetails &&
            this.props.route.params?.notificationDetails.Project
          ) {
            this.setState(
              {
                projectId:
                  this.props.route.params?.notificationDetails.Project.id,
              },
              () => {
                this.loadInitial();
              },
            );
          } else {
            this.setState(
              {
                projectId: this.props.projectDetails.id,
              },
              () => {
                this.loadInitial();
              },
            );
          }
        } catch (e) {
          console.log("error", e);
        }
      }
    });
  };

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  loadInitial = () => {
    // this.getEquiptypes();
    // this.getGateList();
    this.getLocationDetail();
    this.getDropdownDetails();
    this.getControlledByList();
    this.searchMember(
      true,
      this.props.userDetails.firstName,
      Strings.placeholders.location,
    );
    this.searchMember(
      true,
      this.props.userDetails.firstName,
      Strings.addConcrete.mixDesign,
    );
    this.searchMember(
      true,
      this.props.userDetails.firstName,
      Strings.addConcrete.pumpSize,
    );
    {
      !this.state.editConcrete && this.initialDateTimeSetup();
    }
    // setTimeout(() => {
    //   this.checkEdit();
    // }, 1000);
  };

  getInitialTimeZone = () => {
    this.setState({ showLoader: true });
    getTimeZone(
      `${GET_SINGLE_PROJECT}/${this.props.projectDetails.id}`,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.status == 200) {
            let temp =
              response.data.data != null ? response.data.data.TimeZoneId : "";
            this.timeZone(temp);
          }
        }
      },
    );
  };

  timeZone = (timeZone) => {
    getTimeZone(
      GET_TIMEZONE,
      {},
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.status == 200) {
            let list = [];
            response.data.data.forEach((e) => {
              let temp = {};
              temp = {
                id: e.id,
                value: e.location,
                label: e.location,
                selected: false,
                name: e.location,
              };
              list.push(temp);
            });
            let item = null;
            if (timeZone != null) {
              item = list.filter((element) => {
                return element.id === timeZone;
              });
            }
            this.setState(
              {
                showLoader: false,
                timeZoneList: list,
                selectedTimeZone: item != null ? item[0].value : null,
                selectedTimeZoneId: item != null ? item[0].id : "",
              },
              () => this.checkEdit(),
            );
          }
        }
      },
    );
  };

  //  getGateList = () => {
  //     let param = { isFilter: true, showActivatedAlone: true };
  //     getGateList(
  //       GET_GATE_LIST +
  //         this.state.projectId +
  //         "/0/0/" +
  //         this.props.projectDetails.ParentCompany.id,
  //       param,
  //       () => null,
  //       (response) => {
  //         if (response.status) {
  //           if (response.data.data.length !== 0) {
  //             let gateList = [];

  //             for (let item of response.data.data) {
  //               gateList.push({
  //                 id: item.id,
  //                 name: item.gateName,
  //                 value: item.gateName,
  //                 label: item.gateName,
  //                 selected: false,
  //               });
  //             }

  //             this.setState({
  //               gateList: gateList,
  //             });
  //           }
  //         }
  //       }
  //     );
  //   };

  //    getEquiptypes = () => {
  //       let param = { isFilter: true, showActivatedAlone: true };
  //       let url = `${GET_EQUIP_LIST}${this.state.projectId}/0/0/${this.props.projectDetails.ParentCompany.id}`;
  //       getEquipList(
  //         url,
  //         param,
  //         () => null,
  //         (response) => {
  //           if (response.status) {
  //             if (response.data.data.length !== 0) {
  //               let equipTypelist = [];
  //               for (let item of response.data.data) {
  //                 equipTypelist.push({
  //                   id: item.id,
  //                   value: item.equipmentName,
  //                   name: item.equipmentName,
  //                   label: item.equipmentName,
  //                   selected: false,
  //                   isCrane: item.PresetEquipmentType.isCraneType,
  //                 });
  //               }

  //               this.setState({
  //                 equipTypeList: equipTypelist,
  //                 storeEquipmentList: response.data.data,
  //               });
  //             }
  //           }
  //         }
  //       );
  //     };

  getControlledByList = () => {
    getAllMemberList(
      LIST_ALL_MEMBER +
      this.props.projectDetails.id +
      "/" +
      this.props.projectDetails.ParentCompany.id,
      {},
      () => { },
      (response) => {
        if (response.status) {
          if (response.data.message == Strings.popup.getMemEqui) {
            this.storeContactPerson(response.data.data);
          } else {
            this.setState({
              memberlist: [],
            });
          }
        } else {
          this.showError("error", Strings.errors.failed);
        }
      },
    );
  };

  storeContactPerson = (data) => {
    let memberList = [];

    for (let item of data) {
      if (item.User.firstName != null) {
        memberList.push({
          label:
            item.User.firstName +
            " " +
            item.User.lastName +
            " (" +
            item.User.email +
            ")",
          value: item.User.email,
          id: item.id,
          name: item.User.email,
        });
      } else {
        memberList.push({
          label: item.User.email,
          value: item.User.email,
          id: item.id,
        });
      }
    }
    this.setState({ controlledByList: memberList });
  };

  checkEdit = () => {
    if (this.props.editedConcrete.item) {
      let data = this.props.editedConcrete.item;

      if (data && data != undefined && data != null) {
        this.setState(
          {
            projectId: data.ProjectId,
            concreteId: data.ConcreteRequestId,
            editConcrete: true,
            selectedLocation: [],
            editedData: data,
          },
          () => {
            this.getRole();
            this.getConcreteDetails();
          },
        );
        trackScreen("Edit Concrete Request");
        this.props.editConcreteRequest([]);
      }
    } else {
      trackScreen("New Concrete Request");
    }
  };
  getRole = async () => {
    let url =
      GET_PROJECT_ROLE +
      this.state.projectId +
      "/" +
      this.props.projectDetails.ParentCompany.id;
    this.setState({ showLoader: true });
    let response = await _getData(url);
    this.setState({ showLoader: false });
    if (response.data) {
      this.setState({
        roleId: response.data.data.RoleId,
        editedZoneID: response.data.data.TimeZoneId,
      });
    }
  };
  getConcreteDetails = () => {
    let url = `${GET_SINGLE_CONCRETE}/${this.state.concreteId}/${this.state.projectId}/${this.props.projectDetails.ParentCompany.id}`;
    getDeliveryDetails(
      url,
      {},
      () => null,
      (resp) => {
        if (resp.status) {
          if (resp.status == 200) {
            this.loadEditDetails(resp.data.data);
          }
          //TODO FOR LATER
          // else if(resp.status==400){
          //   this.setState({
          //     showLoader: false,
          //   });
          //   console.log('data 1>>>>',resp.data.message)
          //   this.showErrorMessage("error", resp.data.message);
          // }else{
          //   this.setState({
          //     showLoader: false,
          //   });
          //   console.log('data 2>>>>', JSON.stringify(resp))
          //   this.showErrorMessage("error", resp.toString());
          //   }
        } else {
          this.setState({
            showLoader: false,
          });
        }
      },
    );
  };

  getSelectedDateFormat = (date) => {
    const formattedDate = date.replace(" ", "T");
    const fullYear = new Date(formattedDate).getFullYear();
    const fullMonth = new Date(formattedDate).getMonth();
    const startDate = new Date(formattedDate).getDate();
    return `${fullMonth + 1}/${startDate}/${fullYear}`;
  };

  loadEditDetails = (data) => {
    const item = this.state.timeZoneList.filter((element) => {
      return element.id === this.state.editedZoneID;
    });

    if (data && data != null && data != undefined) {
      // Use moment.parseZone to preserve timezone information
      const startMoment = moment.parseZone(data.concretePlacementStart);
      const endMoment = moment.parseZone(data.concretePlacementEnd);
      const pumpStartMoment = moment.parseZone(data.pumpWorkStart);
      const pumpEndMoment = moment.parseZone(data.pumpWorkEnd);

      // Extract hours and minutes using moment to avoid timezone conversion
      const startHours24 = startMoment.hour();
      const startMinutesNum = startMoment.minute();
      const endHours24 = endMoment.hour();
      const endMinutesNum = endMoment.minute();
      const pumpStartHours24 = pumpStartMoment.hour();
      const pumpStartMinutesNum = pumpStartMoment.minute();
      const pumpEndHours24 = pumpEndMoment.hour();
      const pumpEndMinutesNum = pumpEndMoment.minute();

      const fullYear = startMoment.year();
      const fullMonth = startMoment.month();
      const startDate = startMoment.date();
      const endDate = endMoment.date();

      const pumpfullYear = pumpStartMoment.year();
      const pumpfullMonth = pumpStartMoment.month();
      const pumpstartDate = pumpStartMoment.date();
      const pumpendDate = pumpEndMoment.date();

      const ampm = startHours24 >= 12 ? "pm" : "am";

      // Convert to 12-hour format for display
      let startHours = startHours24 % 12 || 12;
      startHours = startHours < 10 ? `0${startHours}` : startHours;
      let startMinutes =
        startMinutesNum < 10 ? `0${startMinutesNum}` : startMinutesNum;

      const pumpampm = pumpStartHours24 >= 12 ? "pm" : "am";

      let pumpstartHours = pumpStartHours24 % 12 || 12;
      pumpstartHours =
        pumpstartHours < 10 ? `0${pumpstartHours}` : pumpstartHours;
      let pumpstartMinutes =
        pumpStartMinutesNum < 10
          ? `0${pumpStartMinutesNum}`
          : pumpStartMinutesNum;

      const endampm = endHours24 >= 12 ? "pm" : "am";

      let endHours = endHours24 % 12 || 12;
      endHours = endHours < 10 ? `0${endHours}` : endHours;
      let endMinutes = endMinutesNum < 10 ? `0${endMinutesNum}` : endMinutesNum;

      const pumpendampm = pumpEndHours24 >= 12 ? "pm" : "am";

      let pumpendHours = pumpEndHours24 % 12 || 12;
      pumpendHours = pumpendHours < 10 ? `0${pumpendHours}` : pumpendHours;
      let pumpendMinutes =
        pumpEndMinutesNum < 10 ? `0${pumpEndMinutesNum}` : pumpEndMinutesNum;

      const strTime = `${startHours}:${startMinutes} ${ampm}`;
      const endTime = `${endHours}:${endMinutes} ${endampm}`;
      const pumpstrTime = `${pumpstartHours}:${pumpstartMinutes} ${pumpampm}`;
      const pumpendTime = `${pumpendHours}:${pumpendMinutes} ${pumpendampm}`;

      // Use the extracted 24-hour values to create Date objects without timezone conversion
      const delStartTime = new Date(
        fullYear,
        fullMonth,
        startDate,
        startHours24,
        startMinutesNum,
      );
      const pumpdelStartTime = new Date(
        pumpfullYear,
        pumpfullMonth,
        pumpstartDate,
        pumpStartHours24,
        pumpStartMinutesNum,
      );
      const delEndTime = new Date(
        fullYear,
        fullMonth,
        endDate,
        endHours24,
        endMinutesNum,
      );
      const pumpdelEndTime = new Date(
        pumpfullYear,
        pumpfullMonth,
        pumpendDate,
        pumpEndHours24,
        pumpEndMinutesNum,
      );

      selectedStartDate = data.concretePlacementStart;
      selectedEndDate = data.concretePlacementEnd;
      selectedPumpStartDate = data.pumpWorkStart;
      selectedPumpEndDate = data.pumpWorkEnd;
      selectedPumpStartDate = data.isPumpRequired
        ? pumpdelStartTime
        : delStartTime;
      selectedPumpEndDate = data.isPumpRequired ? pumpdelEndTime : delEndTime;
      selectedEndDate = delEndTime;
      selectedStartDate = delStartTime;
      selectedDate = new Date(data.concretePlacementStart);
      selectedPumpDate =
        data.pumpOrderedDate != null
          ? new Date(data.pumpOrderedDate)
          : new Date(data.concretePlacementStart);
      let selectedLocationsData = [];
      //   let selectedGate = data.gateDetails
      //   .map((e) => e.Gate.gateName)
      //   .join(",")
      //   .toString();
      // let selectedEquipName = data.equipmentDetails
      //   .map((e) => e.Equipment.equipmentName)
      //   .join(",")
      //   .toString();

      this.setState({ selectedLocation: [] });
      // Process location details
      if (data && data != null && data != undefined && data.locationDetails) {
        data.locationDetails.forEach((element) => {
          if (
            element &&
            element.ConcreteLocation &&
            element.ConcreteLocation != null &&
            element.ConcreteLocation != undefined
          ) {
            let item = element.ConcreteLocation;
            if (item && item != undefined) {
              selectedLocationsData.push({
                id: item.location,
                uniqueId: item.id,
                chosenFromDropdown: true,
              });
            }
          }
        });
      }

      let selectedMixDesignsData = [];
      if (data && data != null && data != undefined && data.mixDesignDetails) {
        data.mixDesignDetails.forEach((element) => {
          if (
            element &&
            element.ConcreteMixDesign &&
            element.ConcreteMixDesign != null &&
            element.ConcreteMixDesign != undefined
          ) {
            let item = element.ConcreteMixDesign;
            if (item && item != undefined) {
              selectedMixDesignsData.push({
                id: item.mixDesign,
                uniqueId: item.id,
                chosenFromDropdown: true,
              });
            }
          }
        });
      }

      let selectedPumpSizesData = [];
      if (data && data != null && data != undefined && data.pumpSizeDetails) {
        data.pumpSizeDetails.forEach((element) => {
          if (
            element &&
            element.ConcretePumpSize &&
            element.ConcretePumpSize != null &&
            element.ConcretePumpSize != undefined
          ) {
            let item = element.ConcretePumpSize;
            if (item && item != undefined) {
              selectedPumpSizesData.push({
                id: item.pumpSize,
                uniqueId: item.id,
                chosenFromDropdown: true,
              });
            }
          }
        });
      }
      let selectedConcreteSupplier = [];
      for (let item of this.state.concreteSupplier) {
        if (
          data.concreteSupplierDetails.some(
            (person) => person.Company.companyName == item.name,
          )
        ) {
          selectedConcreteSupplier.push({
            id: item.id,
            label: item.name,
            name: item.name,
            selected: true,
          });
        } else {
          selectedConcreteSupplier.push({
            id: item.id,
            label: item.name,
            name: item.name,
            selected: false,
          });
        }
      }

      let selectedPersons = [];
      for (let item of data.memberDetails) {
        if (item.Member.User.firstName != null) {
          selectedPersons.push({
            id: `${item.Member.User.firstName} ${item.Member.User.lastName}(${item.Member.User.email})`,
            email: item.Member.User.email,
            userId: item.Member.id,
          });
        } else {
          selectedPersons.push({
            id: item.Member.User.email,
            email: item.Member.User.email,
            userId: item.Member.id,
          });
        }
      }

      this.setState({
        recurrenceType:
          data.recurrence != null
            ? data.recurrence.recurrence
            : Strings.calendarSettings.doseNotRepeat,
        deliveryStatus: data.status != null ? data.status : null,
      });

      concreteConfirmedDate = new Date(data.concreteConfirmedOn);
      pumpConfirmedDate = new Date(data.pumpConfirmedOn);

      selectedStartDate = delStartTime;
      selectedEndDate = delEndTime;
      if (data.status == "Delivered" || data.status == "Completed") {
        if (this.state.roleId == 3 || this.state.roleId == 4) {
          if (this.state.editConcrete) {
            this.setState({ isEditDate: false });
          }
        }
      }
      let selectedDateRequest = `${fullMonth + 1}/${startDate}/${fullYear}`;

      try {
        this.setState(
          {
            selectedDayArray: data.recurrence ? data.recurrence.days : [],
            selectedDay: data.recurrence ? data.recurrence.days : [],
            editConcreteId: data.id != null ? data.id : "0",
            bookingId: data.id,
            description: data.description != null ? data.description : "",
            textFieldValue: data.OriginationAddress,
            vehicleType: data.vehicleType,
            pumpTextFieldValue: data.OriginationAddressPump,
            pumpVehicleType: data.vehicleTypePump,
            concreteId:
              data.ConcreteRequestId != null ? data.ConcreteRequestId : "",
            slump: data.slump != null ? data.slump : "",
            quantity:
              data.concreteQuantityOrdered != null
                ? data.concreteQuantityOrdered
                : "",
            primer: data.primerForPump != null ? data.primerForPump : "",
            orderNo:
              data.concreteOrderNumber != null ? data.concreteOrderNumber : "",
            isConcreteConfirmed:
              data.isConcreteConfirmed != null
                ? data.isConcreteConfirmed
                : false,
            isPumpConfirmed:
              data.isPumpConfirmed != null ? data.isPumpConfirmed : false,
            isPumpRequired: data.isPumpRequired,
            cubicYardsTotal:
              data.cubicYardsTotal != null ? data.cubicYardsTotal : "",
            pumpShowUp: data.pumpShowupTime,
            truckSpacingHrs: data.truckSpacingHours,
            truckSpacingMins: data.truckSpacingMinutes,
            completePlacementHrs:
              data.hoursToCompletePlacement != null
                ? data.hoursToCompletePlacement
                : "",
            completePlacementMins:
              data.minutesToCompletePlacement != null
                ? data.minutesToCompletePlacement
                : "",
            selectedLocation: selectedLocationsData,
            // selectedPlacement: selectedPlacementsData,
            selectedMixDesign: selectedMixDesignsData,
            selectedPumpSize: selectedPumpSizesData,
            selectedEndTime: endTime,
            calSelectedEndTime: new Date(data.concretePlacementEnd),
            calSelectedStartTime: new Date(data.concretePlacementStart),
            selectedStartTime: strTime,
            concreteStartDate: data.concretePlacementStart,
            concreteEndDate: data.concretePlacementEnd,
            selectedPumpStartTime: data.isPumpRequired ? pumpstrTime : strTime,
            selectedPumpEndTime: data.isPumpRequired ? pumpendTime : endTime,
            calSelectedPumpStartTime: data.isPumpRequired
              ? new Date(data.pumpWorkStart)
              : new Date(data.concretePlacementStart),
            calSelectedPumpEndTime: data.isPumpRequired
              ? new Date(data.pumpWorkEnd)
              : new Date(data.concretePlacementEnd),
            concreteSupplier:
              selectedConcreteSupplier != null ? selectedConcreteSupplier : [],
            pumpLocation: data.pumpLocation != null ? data.pumpLocation : "",
            pumpOrderedDate:
              data.pumpOrderedDate == null
                ? this.getSelectedDateFormat(data.concretePlacementStart)
                : this.getSelectedDateFormat(data.pumpOrderedDate),
            pumpSelectedDate: data.isPumpRequired
              ? new Date(data.pumpOrderedDate)
              : new Date(data.concretePlacementStart),
            editedConcreteSupplierList: selectedConcreteSupplier,
            calSelectedDate: new Date(data.concretePlacementStart),
            selectedDate: moment(selectedDateRequest).format("MM/DD/YYYY"),
            notes: data.notes != null ? data.notes : "",
            status: data.status != null ? data.status : "",
            selectedItem: selectedPersons,
            showMultipleSec: true,
            deactiveDateChecker: new Date(data.concretePlacementStart),
            selectedTimeZoneId: item && item.length > 0 ? item[0].id : "",
            recurrenceSeriesID:
              data.recurrence != null ? data.recurrence.id : null,
            recurrenceEndDateSeries:
              data.recurrence != null
                ? data.recurrence.recurrenceEndDate
                : null,
            recurrenceDeliverStartDate:
              data.concretePlacementStart != null
                ? data.concretePlacementStart
                : null,
            recurrenceDeliverEndDate:
              data.concretePlacementEnd != null
                ? data.concretePlacementEnd
                : null,
            recurrenceEndDateRes:
              data.recurrence != null ? data.recurrence : null,
            endDateRecurrence:
              data.recurrence != null
                ? moment(data.recurrence.recurrenceEndDate).format("MM/DD/YYYY")
                : null,
            selectedEndDateYear:
              this.state.editConcrete == false
                ? data.recurrence != null
                  ? data.recurrence.recurrenceEndDate
                  : null
                : new Date(),
            recurrence:
              data.recurrence != null
                ? data.recurrence.recurrence
                : this.state.recurrence,
            selectedGate:
              data.gateDetails.length > 0
                ? data.gateDetails
                  .map((e) => e.Gate.gateName)
                  .join(",")
                  .toString()
                : null,
            selectedEquipName:
              data.equipmentDetails.length > 0
                ? data.equipmentDetails
                  .map((e) => e.Equipment.equipmentName)
                  .join(",")
                  .toString()
                : null,
            // Location and edit mode settings
            selectedLocationNew:
              data.location != null ? data.location?.locationPath : "",
            selectedLocationId: data.location != null ? data.location.id : "",
            additionalLocationText:
              data.locationDetails && data.locationDetails.length > 0
                ? data.locationDetails[0].ConcreteLocation.location
                : "",
            additionalLocationTextId:
              data.locationDetails && data.locationDetails.length > 0
                ? data.locationDetails[0].ConcreteLocation.id
                : 0,
            editIN: true,
            selectedEquipTypeId:
              data.equipmentDetails.length > 0
                ? data.equipmentDetails.map((e) => e.Equipment.id).join(",")
                : 0,
            selectedGateId:
              data.gateDetails.length > 0
                ? data.gateDetails.map((e) => e.Gate.id).join(",")
                : 0,
          },
          () => {
            this.filterEquipmentAndGatesByLocation();
            this.props.selectedConcreteLocationsData([]);
            this.props.selectedConcretePlacementsData([]);
            this.props.selectedConcreteMixDesignsData([]);
            this.props.selectedConcretePumpSizesData([]);
          },
        );
      } catch (error) {
        console.error("Error in setState:", error);
      }
    }
  };

  getSelectedCompanyList = (data) => {
    this.setState({
      concreteSupplier: data,
    });
  };

  getSelectedEquipmentList = (data) => {
    if (data && data.length > 0) {
      // Create a new array with the updated selection state
      let updatedList = data.map((item) => {
        if (item && typeof item.selected !== "undefined") {
          return {
            ...item,
            selected: item.selected,
            disabled: false, // Initialize disabled state
          };
        }
        return item;
      });

      // Check if "No Equipment Needed" is selected
      const noEquipmentNeeded = updatedList.find(
        (item) => item && item.id === 0,
      ); // NO_EQUIPMENT_NEEDED has id: 0
      const otherEquipmentSelected = updatedList.some(
        (item) => item && item.id !== 0 && item.selected === true,
      );

      // Check if "Select All" scenario (all non-"No Equipment" items selected)
      const nonNoEquipmentItems = updatedList.filter(
        (item) => item && item.id !== 0,
      );
      const allOtherEquipmentSelected =
        nonNoEquipmentItems.length > 0 &&
        nonNoEquipmentItems.every((item) => item && item.selected === true);

      // Apply mutual exclusion logic
      if (noEquipmentNeeded && noEquipmentNeeded.selected === true) {
        // RULE 3: When "No Equipment Needed" is selected
        // - Deselect all other equipment options
        // - Disable all other equipment options
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            if (item.id === 0) {
              return {
                ...item,
                selected: true,
                disabled: false,
                visible: true,
              };
            } else {
              return {
                ...item,
                selected: false, // Automatically deselect
                disabled: true, // Disable other options when "No Equipment" is selected
                visible: true,
              };
            }
          }
          return item;
        });
      } else if (allOtherEquipmentSelected && !noEquipmentNeeded?.selected) {
        // RULE 1: When "Select All" is chosen
        // - Select all equipment EXCEPT "No Equipment Needed"
        // - Hide "No Equipment Needed" from visible options
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            if (item.id === 0) {
              return {
                ...item,
                selected: false,
                disabled: false,
                visible: false, // Hide from visible options
              };
            } else {
              return {
                ...item,
                selected: true,
                disabled: false,
                visible: true,
              };
            }
          }
          return item;
        });
      } else if (otherEquipmentSelected) {
        // RULE 4: When any individual equipment is selected
        // - Automatically unselect "No Equipment Needed"
        // - Keep "No Equipment Needed" visible but unselected
        // - Allow multiple selection of other equipment
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            if (item.id === 0) {
              return {
                ...item,
                selected: false, // Automatically unselect
                disabled: false,
                visible: true, // Keep visible but unselected
              };
            } else {
              return {
                ...item,
                disabled: false,
                visible: true,
              };
            }
          }
          return item;
        });
      } else {
        // RULE 2: When "Select All" is unselected or no selections
        // - Show all equipment options including "No Equipment Needed"
        // - Enable all options
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            return {
              ...item,
              disabled: false,
              visible: true,
            };
          }
          return item;
        });
      }

      const selectedEquipment = updatedList.filter(
        (item) => item && item.selected === true,
      );

      // Update the state with the new data and clear time slots
      this.setState(
        {
          equipTypeList: updatedList,
          selectedEquipmentList: selectedEquipment,
          isAssociatedWithCraneRequest: selectedEquipment.some(
            (item) => item && item.isCrane === true && item.selected === true,
          )
            ? true
            : false,
          // Clear existing time slots when equipment selection changes
          selectedStartTime: '',
          selectedEndTime: '',
        },
        () => {
          // Force a re-render of the MultiSelectDropDown component
          if (this.multiSelectRef) {
            this.multiSelectRef.setState({
              dataItems: updatedList,
              isAllChecked:
                allOtherEquipmentSelected && !noEquipmentNeeded?.selected,
            });
          }
        },
      );
    } else {
      // Handle case where no data is provided
      this.setState({
        equipTypeList: [],
        selectedEquipmentList: [],
        isAssociatedWithCraneRequest: false,
      });
    }
  };

  updateMasterState = (key, value) => {
    if (key == Strings.placeholders.description) {
      this.setState({
        description: value,
      });
    } else if (key == Strings.addConcrete.placement) {
      this.setState({
        placement: value,
      });
    } else if (key == Strings.addConcrete.responisblePerson) {
      this.setState({
        responisblePerson: value,
      });
    } else if (key == Strings.addConcrete.date) {
      this.setState({
        selectedDate: value,
      });
    } else if (key == Strings.addConcrete.fromTime) {
      this.setState({
        selectedStartTime: value,
      });
    } else if (key == Strings.addConcrete.toTime) {
      this.setState({
        selectedEndTime: value,
      });
    } else if (key == Strings.addConcrete.orderNo) {
      this.setState({
        orderNo: value,
      });
    } else if (key == Strings.addConcrete.slump) {
      this.setState({
        slump: value,
      });
    } else if (key == Strings.addConcrete.quantity) {
      this.setState({
        quantity: value,
      });
    } else if (key == Strings.addConcrete.primerForPump) {
      this.setState({
        primer: value,
      });
    } else if (key == Strings.addConcrete.pumpOrdered) {
      this.setState({
        pumpOrderedDate: value,
      });
    } else if (key == Strings.addConcrete.pumpLocation) {
      this.setState({
        pumpLocation: value,
      });
    } else if (key == Strings.addConcrete.pumpShowUp) {
      this.setState({
        pumpShowUp: value,
      });
    } else if (key == Strings.addConcrete.cubicYardsTotal) {
      this.setState({
        cubicYardsTotal: value,
      });
    } else if (key == Strings.addConcrete.completePlacementHrs) {
      this.setState({
        completePlacementHrs: value,
      });
    } else if (key == Strings.addCraneRequest.completePlacementMins) {
      this.setState({
        completePlacementMins: value,
      });
    } else if (key == Strings.addConcrete.truckSpacing) {
      this.setState({
        truckSpacingHrs: value,
      });
    } else if (key == Strings.placeholders.notes) {
      this.setState({
        notes: value,
      });
    } else if (key == Strings.placeholders.additional_location) {
      this.setState({
        additionalLocationText: value,
      });
    }
  };

  onSelectPicker = (item) => {
    if (this.state.showTruckSpacingHrsModal) {
      this.setState({ truckSpacingHrs: item });
    } else if (this.state.showTruckSpacingMinsModal) {
      this.setState({ truckSpacingMins: item });
    } else if (this.state.showCompletePlacementHrsModal) {
      this.setState({ completePlacementHrs: item });
    } else if (this.state.showCompletePlacementMinsModal) {
      this.setState({ completePlacementMins: item });
    } else {
      this.onCloseModals();
    }
    this.onCloseModals();
  };

  onCloseModals = () => {
    this.setState({
      hoursModal: false,
      minsModal: false,
      showTruckSpacingHrsModal: false,
      showTruckSpacingMinsModal: false,
      showCompletePlacementHrsModal: false,
      showCompletePlacementMinsModal: false,
    });
  };

  onChangeperson = (text, type) => {
    // alert("hello")
    if (text != "") {
      this.setState({
        locationsData: backupLocationData,
        placementsData: backupPlacementData,
        mixDesignsData: backupMixDesignData,
        pumpSizeData: backupPumpsizeData,
      });
      this.searchMember(true, text, type);
    } else {
      this.setState({ responsiblePersonData: [] });
    }
  };

  searchMember = (initial, text, type) => {
    if (type == Strings.placeholders.responisblePerson) {
      searchMember(
        SEARCH_MEMBER +
        this.state.projectId +
        `/${text}` +
        `/${this.props.projectDetails.ParentCompany.id}`,
        {},
        () => null,
        (response) => {
          if (response.status && response.data.length !== 0) {
            this.updateResponsiblePersonSearch(initial, response.data, type);
          }
        },
      );
    } else {
      if (type == Strings.placeholders.location) {
        const updatedSearchList = backupLocationData.filter(function (item) {
          const itemData = item.id ? item.id.toUpperCase() : "".toUpperCase();
          const textData = text.toUpperCase();
          return itemData.indexOf(textData) > -1;
        });
        this.state.selectedLocation.push(updatedSearchList[0]);

        this.setState({
          locationsData: updatedSearchList,
          selectedLocation: this.state.selectedLocation,
        });
      } else if (type == Strings.addConcrete.mixDesign) {
        const updatedSearchList = backupMixDesignData.filter(function (item) {
          const itemData = item.id ? item.id.toUpperCase() : "".toUpperCase();
          const textData = text.toUpperCase();
          return itemData.indexOf(textData) > -1;
        });
        this.state.selectedMixDesign.push(updatedSearchList[0]);

        this.setState({
          mixDesignsData: updatedSearchList,
          selectedMixDesign: this.state.selectedMixDesign,
        });
      } else if (type == Strings.addConcrete.pumpSizeData) {
        const updatedSearchList = backupPumpsizeData.filter(function (item) {
          const itemData = item.id ? item.id.toUpperCase() : "".toUpperCase();
          const textData = text.toUpperCase();
          return itemData.indexOf(textData) > -1;
        });
        this.state.selectedPumpSize.push(updatedSearchList[0]);

        this.setState({
          pumpSizeData: updatedSearchList,
          selectedPumpSize: this.state.selectedPumpSize,
        });
      }
    }
  };

  updateResponsiblePersonSearch = (initial, response, type) => {
    let searchList = [];
    let selectedItem = [];
    let userId = -1;

    for (let item of response) {
      if (
        this.state.selectedItem.filter((data) => data.email == item.emails) ==
        true ||
        (item.emails == this.props.userDetails.email) == true
      ) {
      } else {
        searchList.push({
          id: `${item.email}`,
          email: item.email,
          userId: item.id,
        });
      }

      if (
        (item.emails == this.props.userDetails.email) == true &&
        initial == true
      ) {
        selectedItem.push({
          id: item.email,
          email: item.email,
          userId: item.id,
        });
        userId = item.id;
      } else {
      }
    }

    if (userId == -1) {
      this.setState({
        responsiblePersonData: searchList,
        selectedItem: selectedItem,
      });
    } else {
      this.setState({
        responsiblePersonData: searchList,
        selectedItem: selectedItem,
        selectedItemIndex: userId,
      });
    }
  };

  showToaster = (type, message) => {
    Keyboard.dismiss();
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
        disableSubmit: false,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showToaster: false,
          });
        }, 2000);
      },
    );
  };

  onPressEqipType = (item) => {
    if (item.isCrane) {
      this.getLastCraneId();
    }
    this.setState({
      selectedEquipName: item.value,
      selectedEquipTypeId: item.id,
      eqipModalVisible: false,
      isAssociatedWithCraneRequest: item.isCrane,
    });
    Keyboard.dismiss();
  };

  onPressGateType = (item) => {
    this.setState({
      selectedGate: item.value,
      selectedGateId: item.id,
      gateModalVisible: false,
    });
  };

  submit = () => {
    const {
      recurrence,
      times,
      isMonthFirstCheck,
      isMonthSecondCheck,
      isYearFirstCheck,
      isYearSecondCheck,
      selectedEndDateYear,
      selectedDayArray,
      listSetName,
      yearListSetName,
      editConcrete,
      endDateRecurrence,
    } = this.state;

    let responsibleData = [];
    let responsibleValue = true;

    let isEquipType = false;
    let isGateType = false;
    if (
      this.state.calSelectedDate > new Date() &&
      this.state.calSelectedDate > this.state.deactiveDateChecker
    ) {
      let equipTypeData = this.state.equipTypeList;
      isEquipType = equipTypeData.find((item) => {
        if (item.id == this.state.selectedEquipTypeId) {
          return true;
        }
      });

      let gateTypeData = this.state.gateList;
      isGateType = gateTypeData.find((item) => {
        if (item.id == this.state.selectedGateId) {
          return true;
        }
      });
    }

    let selectedArrayItem = [];
    selectedArrayItem.push({
      id: `${this.props.responsiblePersonData.name}`,
      email: this.props.userDetails.email,
      userId: this.props.responsiblePersonData.id,
    });

    if (this.state.selectedItemList.length == 0) {
      for (let item of this.state.selectedItem) {
        responsibleData.push({ userId: item.userId });
      }
    } else if (
      this.state.selectedItemList != undefined ||
      this.state.selectedItemList != null
    ) {
      let entities = this.state.selectedItemList.entities;
      let results = this.state.selectedItemList.result;
      for (let item of results) {
        if (item == this.props.userDetails.email) {
          responsibleData.push({ userId: this.state.selectedItemIndex });
        } else {
          if (entities.item[item].userId == undefined) {
            responsibleValue = false;
          } else {
            responsibleData.push({
              userId: entities.item[item].userId,
            });
          }
        }
      }
    }

    let isResponseValue = false;

    if (
      this.state.calSelectedDate > new Date() &&
      this.state.calSelectedDate > this.state.deactiveDateChecker
    ) {
      const arr1 = this.state.controlledByList;
      const arr2 = responsibleData;
      const result = arr1.filter((o) =>
        arr2.some(({ userId }) => o.id == userId),
      );

      if (responsibleData.length !== result.length) {
        isResponseValue = true;
      }
    }

    let selectedMixDesignsData = [];
    if (this.state.selectedMixDesignsList.length == 0) {
      for (let data of this.state.selectedMixDesign) {
        if (data) {
          selectedMixDesignsData.push({
            chosenFromDropdown: true,
            id: data.uniqueId,
            mixDesign: data.id,
          });
        }
      }
    } else if (
      this.state.selectedMixDesignsList != undefined ||
      this.state.selectedMixDesignsList != null
    ) {
      let entities = this.state.selectedMixDesignsList.entities;
      let results = this.state.selectedMixDesignsList.result;
      if (results) {
        for (let item of results) {
          if (
            item &&
            entities.item[item].id == item &&
            item != null &&
            item != undefined
          ) {
            let data = entities.item[item];
            if (data.uniqueId) {
              selectedMixDesignsData.push({
                chosenFromDropdown: true,
                id: data.uniqueId,
                mixDesign: data.id,
              });
            } else {
              selectedMixDesignsData.push({
                chosenFromDropdown: false,
                id: null,
                mixDesign: data.id,
              });
            }
          }
        }
      }
    }

    let selectedPumpSizeData = [];
    if (this.state.selectedPumpSizeList.length == 0) {
      for (let data of this.state.selectedPumpSize) {
        if (data) {
          selectedPumpSizeData.push({
            chosenFromDropdown: true,
            id: data.uniqueId,
            pumpSize: data.id,
          });
        }
      }
    } else if (
      this.state.selectedPumpSizeList != undefined ||
      this.state.selectedPumpSizeList != null
    ) {
      if (
        this.state.selectedPumpSizeList.entities &&
        this.state.selectedPumpSizeList.result
      ) {
        let entities = this.state.selectedPumpSizeList.entities;
        let results = this.state.selectedPumpSizeList.result;
        if (results) {
          for (let item of results) {
            if (
              item &&
              entities.item[item].id == item &&
              item != null &&
              item != undefined
            ) {
              let data = entities.item[item];
              if (data.uniqueId) {
                selectedPumpSizeData.push({
                  chosenFromDropdown: true,
                  id: data.uniqueId,
                  pumpSize: data.id,
                });
              } else {
                selectedPumpSizeData.push({
                  chosenFromDropdown: false,
                  id: null,
                  pumpSize: data.id,
                });
              }
            }
          }
        }
      }
    }
    Keyboard.dismiss();
    let selectedSuppliersList = [];
    let selectedEquipmentList = [];
    for (let item of this.state.equipTypeList) {
      if (item.selected == true) {
        selectedEquipmentList.push(item.id);
      }
    }
    for (let item of this.state.concreteSupplier) {
      if (item.selected == true) {
        selectedSuppliersList.push(item.id);
      }
    }
    if (
      isEmpty(this.state.description) ||
      this.state.description.trim() == ""
    ) {
      this.showToaster("error", Strings.errors.emptyDescription);
    } else if (this.state.description.length < 3) {
      this.showToaster("error", "Description " + Strings.errors.lengthError);
    }
    // else if (selectedPlacementsData.length == 0) {
    //   this.showToaster("error", Strings.errors.emptyPlacement);
    // }
    else if (
      this.state.selectedItem.length == 0 &&
      this.state.selectedItemList.length == 0
    ) {
      this.showToaster("error", Strings.errors.emptyResponsiblePerson);
    } else if (!responsibleValue) {
      this.showToaster("error", Strings.errors.validResponsiblePerson);
    } else if (isEmpty(this.state.selectedDate)) {
      this.showToaster(
        "error",
        "Please select date and time slot from the time slot screen",
      );
    } else if (isEmpty(this.state.calSelectedStartTime)) {
      this.showToaster("error", Strings.errors.emptyFromTime);
    } else if (isEmpty(this.state.calSelectedEndTime)) {
      this.showToaster("error", Strings.errors.emptyToTime);
    } else if (
      !this.state.editConcrete &&
      this.state.calSelectedDate < new Date().setHours(0, 0, 0, 0)
    ) {
      // Check if selected date is in the past (only for new entries, not for edits)
      this.showToaster("error", Strings.errors.futureDate);
    } else {
      // Check if selected time is in the past when date is today (only for new entries)
      if (
        !this.state.editConcrete &&
        moment(this.state.calSelectedDate).format("MM/DD/YYYY") ===
        moment(new Date()).format("MM/DD/YYYY")
      ) {
        // Create a date object with selected date and time
        const selectedDateTime = new Date(
          this.state.calSelectedDate.getFullYear(),
          this.state.calSelectedDate.getMonth(),
          this.state.calSelectedDate.getDate(),
          this.state.calSelectedStartTime.getHours(),
          this.state.calSelectedStartTime.getMinutes(),
          0,
          0
        );

        // Get current time with same precision
        const currentDateTime = new Date();
        currentDateTime.setSeconds(0, 0); // Reset seconds and milliseconds for fair comparison

        // Check if selected time is in the past
        if (selectedDateTime < currentDateTime) {
          this.showToaster("error", Strings.errors.futureTime);
          return;
        }
      }

      if (isEmpty(selectedSuppliersList)) {
        this.showToaster("error", Strings.errors.emptySupplier);
      // } else if (isEmpty(selectedEquipmentList)) {
      //   this.showToaster("error", Strings.errors.emptyEquip);
      // } else if (isEmpty(this.state.selectedGate)) {
      //   this.showToaster("error", Strings.errors.emptyGate);
      // } 
      }else if (
        this.state.isPumpRequired &&
        selectedPumpSizeData.length == 0
      ) {
        this.showToaster("error", Strings.errors.emptyPumpSize);
      } else if (
        this.state.isPumpRequired &&
        isEmpty(this.state.pumpSelectedDate.toString())
      ) {
        this.showToaster("error", Strings.errors.emptyPumpOrderedDate);
      } else if (
        this.state.isPumpRequired &&
        isEmpty(this.state.pumpLocation)
      ) {
        this.showToaster("error", Strings.errors.emptyPumpLocation);
      } else if (
        this.state.status == "Completed" &&
        this.state.completePlacementHrs == 0 &&
        this.state.completePlacementMins == 0
      ) {
        this.showToaster("error", "Please provide Valid Completion Time");
      } else {
        let values = [];
        if (this.state.selectedItemList.length == 0) {
          for (let item of this.state.selectedItem) {
            values.push(item.userId);
          }
        } else if (
          this.state.selectedItemList != undefined ||
          this.state.selectedItemList != null
        ) {
          let entities = this.state.selectedItemList.entities;
          let results = this.state.selectedItemList.result;
          for (let item of results) {
            if (item == this.props.userDetails.email) {
              values.push(this.state.selectedItemIndex);
            } else {
              if (entities.item[item].userId != undefined) {
                values.push(entities.item[item].userId);
              }
            }
          }
        }

        // For edit mode, preserve original concrete dates if no time changes were made
        let startTime, endTime;

        if (
          this.state.editConcrete &&
          this.state.concreteStartDate &&
          this.state.concreteEndDate
        ) {
          // FOOLPROOF APPROACH: Check if user explicitly changed times via time slot picker
          // If no explicit time change flag is set, always preserve original dates in edit mode

          const userExplicitlyChangedTime =
            this.state.timeSlotExplicitlyChanged === true;

          // Multiple comparison methods to ensure we catch when no changes were made
          const originalStartTime = moment(this.state.concreteStartDate)
            .local()
            .format("HH:mm");
          const originalEndTime = moment(this.state.concreteEndDate)
            .local()
            .format("HH:mm");
          const currentStartTime = moment(
            this.state.calSelectedStartTime,
          ).format("HH:mm");
          const currentEndTime = moment(this.state.calSelectedEndTime).format(
            "HH:mm",
          );

          // Compare dates with multiple approaches
          const originalDateLocal = moment(this.state.concreteStartDate)
            .local()
            .format("YYYY-MM-DD");
          const originalDateUTC = moment(this.state.concreteStartDate)
            .utc()
            .format("YYYY-MM-DD");
          const originalDateRaw = moment(this.state.concreteStartDate).format(
            "YYYY-MM-DD",
          );
          const currentDate = moment(this.state.calSelectedDate).format(
            "YYYY-MM-DD",
          );

          // Check if user came back from time slot selection without changes
          const cameFromTimeSlot =
            this.state.selectedDate &&
            this.state.selectedStartTime &&
            this.state.selectedEndTime;
          const timeSlotMatches =
            cameFromTimeSlot &&
            this.state.selectedStartTime === originalStartTime &&
            this.state.selectedEndTime === originalEndTime;

          // AGGRESSIVE PRESERVATION: If user didn't explicitly change time, always preserve
          let shouldPreserve;
          if (!userExplicitlyChangedTime) {
            shouldPreserve = true;
          } else {
            // Only use comparison logic if user explicitly changed time
            const timesMatch =
              originalStartTime === currentStartTime &&
              originalEndTime === currentEndTime;
            const datesMatch =
              originalDateLocal === currentDate ||
              originalDateUTC === currentDate ||
              originalDateRaw === currentDate;
            shouldPreserve = timesMatch && (datesMatch || timeSlotMatches);
          }

          if (shouldPreserve) {
            startTime = new Date(this.state.concreteStartDate);
            endTime = new Date(this.state.concreteEndDate);
          } else {
            // Times or date changed, reconstruct with new values
            startTime = new Date(
              this.state.calSelectedDate.getFullYear(),
              this.state.calSelectedDate.getMonth(),
              this.state.calSelectedDate.getDate(),
              this.state.calSelectedStartTime.getHours(),
              this.state.calSelectedStartTime.getMinutes(),
              0,
            );
            endTime = new Date(
              this.state.calSelectedDate.getFullYear(),
              this.state.calSelectedDate.getMonth(),
              this.state.calSelectedDate.getDate(),
              this.state.calSelectedEndTime.getHours(),
              this.state.calSelectedEndTime.getMinutes(),
              0,
            );
          }
        } else {
          // New concrete request, construct times normally
          startTime = new Date(
            this.state.calSelectedDate.getFullYear(),
            this.state.calSelectedDate.getMonth(),
            this.state.calSelectedDate.getDate(),
            this.state.calSelectedStartTime.getHours(),
            this.state.calSelectedStartTime.getMinutes(),
            0,
          );
          endTime = new Date(
            this.state.calSelectedDate.getFullYear(),
            this.state.calSelectedDate.getMonth(),
            this.state.calSelectedDate.getDate(),
            this.state.calSelectedEndTime.getHours(),
            this.state.calSelectedEndTime.getMinutes(),
            0,
          );
        }

        let endRecurrenceTime = new Date(
          selectedEndDateYear.getFullYear(),
          selectedEndDateYear.getMonth(),
          selectedEndDateYear.getDate(),
          new Date(selectedEndDate).getHours(),
          new Date(selectedEndDate).getMinutes(),
          0,
        );

        let startTimeValue = new Date(
          this.state.calSelectedDate.getFullYear(),
          this.state.calSelectedDate.getMonth(),
          this.state.calSelectedDate.getDate(),
        );

        let endTimeValue = new Date(
          this.state.calSelectedDate.getFullYear(),
          this.state.calSelectedDate.getMonth(),
          this.state.calSelectedDate.getDate(),
        );

        let endRecurrenceTimeValue = new Date(
          selectedEndDateYear.getFullYear(),
          selectedEndDateYear.getMonth(),
          selectedEndDateYear.getDate(),
        );

        let pumpStartTime = new Date(
          this.state.pumpSelectedDate.getFullYear(),
          this.state.pumpSelectedDate.getMonth(),
          this.state.pumpSelectedDate.getDate(),
          new Date(selectedPumpStartDate).getHours(),
          new Date(selectedPumpStartDate).getMinutes(),
          0,
        );
        let pumpEndTime = new Date(
          this.state.pumpSelectedDate.getFullYear(),
          this.state.pumpSelectedDate.getMonth(),
          this.state.pumpSelectedDate.getDate(),
          new Date(selectedPumpEndDate).getHours(),
          new Date(selectedPumpEndDate).getMinutes(),
          0,
        );

        // Use direct Date methods to avoid timezone conversion issues
        // This ensures 12 AM (midnight) is formatted as "00:00" not "12:00"
        const formatTimeToHHMM = (date) => {
          if (!date) return "00:00";
          const hours = date.getHours();
          const minutes = date.getMinutes();
          return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
        };

        let startPickerValue = formatTimeToHHMM(selectedStartDate);
        let endPickerValue = formatTimeToHHMM(selectedEndDate);

        let pumpStartValueTime = formatTimeToHHMM(selectedPumpStartDate);
        let pumpEndValueTime = formatTimeToHHMM(selectedPumpEndDate);

        const deliveryStartTime = formatTimeToHHMM(
          this.state.calSelectedStartTime,
        );
        const deliveryEndTime = formatTimeToHHMM(this.state.calSelectedEndTime);

        // Need later
        // let timeDifference = endTime.valueOf() - startTime.valueOf();
        let pumpTimeDifference =
          pumpEndTime.valueOf() - pumpStartTime.valueOf();
        if (
          this.state.status != "Completed" &&
          endTime.valueOf() < startTime.valueOf()
        ) {
          this.showToaster(Strings.toast.error, Strings.errors.notValidTime);
        } else if (
          this.state.status != "Completed" &&
          endTime.valueOf() == startTime.valueOf()
        ) {
          this.showToaster(
            Strings.toast.error,
            Strings.errors.notValidSameTime,
          );
        } else if (
          editConcrete == false &&
          recurrence != Strings.calendarSettings.doseNotRepeat &&
          endRecurrenceTimeValue.valueOf() < startTimeValue.valueOf()
        ) {
          this.showToaster(Strings.toast.error, Strings.errors.errorEnddate);
        }

        //DO FOR LATER
        // else if (timeDifference < 3600000) {
        //   this.showToaster("error", Strings.errors.startTimeAndEnd);
        // }
        else if (pumpEndTime.valueOf() < pumpStartTime.valueOf()) {
          this.showToaster(
            Strings.toast.error,
            Strings.errors.pumpNotValidTime,
          );
        } else if (pumpEndTime.valueOf() == pumpStartTime.valueOf()) {
          this.showToaster(Strings.toast.error, Strings.errors.pumpSameTime);
        } else if (pumpTimeDifference < 3600000) {
          this.showToaster(
            Strings.toast.error,
            Strings.errors.PumpStartTimeAndEnd,
          );
        } else {
          const { isPumpRequired, isPumpConfirmed, isConcreteConfirmed } =
            this.state;
          let param = {};
          let data = {};

          if (this.state.editConcrete) {
            data = {
              id: this.state.editConcreteId,
              location: this.state.additionalLocationText,
              LocationId: this.state.selectedLocationId,
              LocationDetailId: this.state.additionalLocationTextId,
              description: this.state.description,
              ProjectId: this.state.projectId,
              concreteSupplier: selectedSuppliersList,
              EquipmentId: selectedEquipmentList,
              GateId: this.state.selectedGateId,
              concretePlacementStart: this.formatDateWithTimezone(startTime),
              concretePlacementEnd: this.formatDateWithTimezone(endTime),
              isPumpConfirmed: this.state.isPumpRequired
                ? this.state.isPumpConfirmed
                : false,
              isPumpRequired: this.state.isPumpRequired,
              isConcreteConfirmed: this.state.isConcreteConfirmed,
              ParentCompanyId: this.props.projectDetails.ParentCompany.id,
              responsiblePersons: values,
              concreteOrderNumber: this.state.orderNo,
              truckSpacingHours: this.state.truckSpacingHrs.toString(),
              mixDesign: selectedMixDesignsData,
              slump: this.state.slump,
              concreteQuantityOrdered: this.state.quantity,
              concreteConfirmedOn: concreteConfirmedDate,
              pumpSize: this.state.isPumpRequired ? selectedPumpSizeData : [],
              pumpLocation: this.state.isPumpRequired
                ? this.state.pumpLocation
                : null,
              pumpOrderedDate: this.state.isPumpRequired
                ? this.state.pumpSelectedDate
                : null,
              // pumpShowupTime: this.state.pumpShowUp,
              pumpConfirmedOn:
                isPumpRequired && isPumpConfirmed ? pumpConfirmedDate : null,
              pumpWorkEnd: this.state.isPumpRequired ? pumpEndTime : null,
              pumpWorkStart: this.state.isPumpRequired ? pumpStartTime : null,
              requestType: "concreteRequest",
              primerForPump: this.state.primer,
              ConcreteRequestId: this.state.concreteId,
              notes: this.state.notes,
              hoursToCompletePlacement:
                this.state.status == "Completed"
                  ? this.state.completePlacementHrs.toString()
                  : null,
              minutesToCompletePlacement:
                this.state.status == "Completed"
                  ? this.state.completePlacementMins.toString()
                  : null,
              cubicYardsTotal:
                this.state.status == "Completed"
                  ? this.state.cubicYardsTotal
                  : null,
              originationAddress: this.state.textFieldValue,
              vehicleType: this.state.vehicleType,
              originationAddressPump: this.state.pumpTextFieldValue,
              vehicleTypePump: this.state.pumpVehicleType,
            };
          } else {
            data = {
              location: this.state.additionalLocationText,
              LocationId: this.state.selectedLocationId,
              description: this.state.description,
              ProjectId: this.state.projectId,
              EquipmentId: selectedEquipmentList,
              GateId: this.state.selectedGateId,
              concreteSupplier: selectedSuppliersList,
              concretePlacementStart: this.formatDateWithTimezone(startTime),
              concretePlacementEnd: this.formatDateWithTimezone(endTime),
              isPumpConfirmed: this.state.isPumpRequired
                ? this.state.isPumpConfirmed
                : false,
              isPumpRequired: this.state.isPumpRequired,
              isConcreteConfirmed: this.state.isConcreteConfirmed,
              ParentCompanyId: this.props.projectDetails.ParentCompany.id,
              responsiblePersons: values,
              concreteOrderNumber: this.state.orderNo,
              truckSpacingHours: this.state.truckSpacingHrs.toString(),
              mixDesign: selectedMixDesignsData,
              slump: this.state.slump,
              concreteQuantityOrdered: this.state.quantity,
              concreteConfirmedOn: concreteConfirmedDate,
              pumpSize: this.state.isPumpRequired ? selectedPumpSizeData : [],
              pumpLocation: this.state.isPumpRequired
                ? this.state.pumpLocation
                : null,
              pumpOrderedDate: this.state.isPumpRequired
                ? moment(this.state.pumpSelectedDate).format("MM/DD/YYYY")
                : null,
              // pumpShowupTime: this.state.pumpShowUp,
              pumpConfirmedOn:
                isPumpRequired && isPumpConfirmed ? pumpConfirmedDate : null,
              pumpWorkEnd: this.state.isPumpRequired ? pumpEndValueTime : null,
              pumpWorkStart: this.state.isPumpRequired
                ? pumpStartValueTime
                : null,
              requestType: "concreteRequest",
              primerForPump: this.state.primer,
              ConcreteRequestId: this.state.concreteId,
              notes: this.state.notes,
              originationAddress: this.state.textFieldValue,
              vehicleType: this.state.vehicleType,
              originationAddressPump: this.state.pumpTextFieldValue,
              vehicleTypePump: this.state.pumpVehicleType,
            };
          }

          if (
            editConcrete == false &&
            recurrence === Strings.calendarSettings.doseNotRepeat
          ) {
            param = {
              ...data,
              recurrence: "Does Not Repeat",
              repeatEveryCount: null,
              repeatEveryType: null,
              chosenDateOfMonth: false,
              dateOfMonth: null,
              monthlyRepeatType: "",
              concretePlacementStart: moment(startTime).format(
                "YYYY MM DD 00:00:00",
              ),
              concretePlacementEnd: moment(startTime).format(
                "YYYY MM DD 00:00:00",
              ),
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
            };
          } else if (
            editConcrete == false &&
            recurrence === Strings.calendarSettings.daily
          ) {
            let dailyDays = [];
            this.state.daysList.forEach((e) => {
              if (e.selected) {
                dailyDays.push(e.name);
              }
            });
            param = {
              ...data,
              recurrence: "Daily",
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Days" : "Day",
              chosenDateOfMonth: false,
              dateOfMonth: null,
              monthlyRepeatType: "",
              concretePlacementStart: moment(startTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              concretePlacementEnd: moment(endRecurrenceTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
              days: dailyDays,
            };
          } else if (
            editConcrete == false &&
            recurrence === Strings.calendarSettings.weekly
          ) {
            param = {
              ...data,
              recurrence: "Weekly",
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Weeks" : "Week",
              chosenDateOfMonth: false,
              dateOfMonth: null,
              monthlyRepeatType: "",
              concretePlacementStart: moment(startTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              concretePlacementEnd: moment(endRecurrenceTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
              days: selectedDayArray,
            };
          } else if (
            editConcrete == false &&
            recurrence === Strings.calendarSettings.monthly
          ) {
            let repeat = null;
            let choseDate = false;
            if (isMonthFirstCheck == true) {
              repeat = null;
              choseDate = true;
            } else if (isMonthSecondCheck == true) {
              repeat = listSetName;
              choseDate = false;
            } else {
              repeat = listSetName;
              choseDate = false;
            }

            param = {
              ...data,
              recurrence: "Monthly",
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Months" : "Month",
              chosenDateOfMonth: choseDate,
              dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
              monthlyRepeatType: repeat,
              concretePlacementStart: moment(startTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              concretePlacementEnd: moment(endRecurrenceTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
            };
          } else if (
            editConcrete == false &&
            recurrence === Strings.calendarSettings.yearly
          ) {
            let repeat = null;
            let choseDate = false;
            if (isYearFirstCheck == true) {
              repeat = null;
              choseDate = true;
            } else if (isYearSecondCheck == true) {
              repeat = yearListSetName;
              choseDate = false;
            } else {
              repeat = yearListSetName;
              choseDate = false;
            }
            param = {
              ...data,
              recurrence: "Yearly",
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Years" : "Year",
              chosenDateOfMonth: choseDate,
              dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
              monthlyRepeatType: repeat,
              concretePlacementStart: moment(startTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              concretePlacementEnd: moment(endRecurrenceTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
            };
          } else if (editConcrete === true) {
            if (recurrence === Strings.calendarSettings.doseNotRepeat) {
              // Handle "Does Not Repeat" case first
              data = {
                ...data,
                // ConcreteRequestId: this.state.concreteId,
                chosenDateOfMonthValue: null,
                // craneDropOffLocation: null,
                // cranePickUpLocation: null,
                chosenDateOfMonth: true,
                chosenDateOfMonthValue: 1,
                recurrence: "Does Not Repeat",
                recurrenceEdited: true,
                dateOfMonth: "",
                days: [],
                // define: [],
                monthlyRepeatType: "",
                repeatEveryCount: null,
                repeatEveryType: null,
                // deliveryStart: moment(startTime).format("YYYY MM DD 00:00:00"),
                // deliveryEnd: moment(endRecurrenceTime).format("YYYY MM DD 00:00:00"),
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.daily) {
              // Handle "Daily" recurrence
              let dailyDays = [];
              this.state.daysList.forEach((e) => {
                if (e.selected) {
                  dailyDays.push(e.name);
                }
              });
              data = {
                ...data,
                concretePlacementStart: this.state.calSelectedStartTime,
                concretePlacementEnd: this.state.calSelectedEndTime,
                recurrence: "Daily",
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Days" : "Day",
                chosenDateOfMonth: true,
                recurrenceEdited: true,
                dateOfMonth: "",
                monthlyRepeatType: "",
                // ConcreteRequestId: this.state.concreteId,
                // CraneRequestId: null,
                chosenDateOfMonthValue: 1,
                // craneDropOffLocation: null,
                // cranePickUpLocation: null,
                days: dailyDays,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.weekly) {
              // Handle "Weekly" recurrence
              data = {
                ...data,
                concretePlacementStart: moment(startTime).format(
                  "YYYY MM DD 00:00:00",
                ),
                concretePlacementEnd: moment(endRecurrenceTime).format(
                  "YYYY MM DD 00:00:00",
                ),
                recurrence: "Weekly",
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Weeks" : "Week",
                chosenDateOfMonth: true,
                // DeliveryId: this.state.deliveryId,
                dateOfMonth: "",
                monthlyRepeatType: "",
                days: selectedDayArray,
                recurrenceEdited: true,
                // CraneRequestId: null,
                chosenDateOfMonthValue: 1,
                // craneDropOffLocation: null,
                // cranePickUpLocation: null,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.monthly) {
              // Handle "Monthly" recurrence
              let repeat = null;
              let choseDate = false;
              if (isMonthFirstCheck === true) {
                repeat = null;
                choseDate = true;
              } else if (isMonthSecondCheck === true) {
                repeat = listSetName;
                choseDate = false;
              }
              data = {
                ...data,
                recurrence: "Monthly",
                concretePlacementStart: moment(startTime).format(
                  "YYYY MM DD 00:00:00",
                ),
                concretePlacementEnd: moment(endRecurrenceTime).format(
                  "YYYY MM DD 00:00:00",
                ),
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Months" : "Month",
                chosenDateOfMonth: choseDate,
                // DeliveryId: this.state.deliveryId,
                recurrenceEdited: true,
                // CraneRequestId: null,
                chosenDateOfMonthValue: null,
                // craneDropOffLocation: null,
                // cranePickUpLocation: null,
                dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
                monthlyRepeatType: repeat,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.yearly) {
              // Handle "Yearly" recurrence
              let repeat = "";
              let choseDate = false;
              if (isYearFirstCheck === true) {
                repeat = null;
                choseDate = true;
              } else if (isYearSecondCheck === true) {
                repeat = yearListSetName;
                choseDate = false;
              }
              data = {
                ...data,
                recurrence: "Yearly",
                concretePlacementStart: moment(startTime).format(
                  "YYYY MM DD 00:00:00",
                ),
                concretePlacementEnd: moment(endRecurrenceTime).format(
                  "YYYY MM DD 00:00:00",
                ),
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Years" : "Year",
                chosenDateOfMonth: choseDate,
                // DeliveryId: this.state.deliveryId,
                recurrenceEdited: true,
                // CraneRequestId: null,
                chosenDateOfMonthValue: null,
                // craneDropOffLocation: null,
                // cranePickUpLocation: null,
                dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
                monthlyRepeatType: repeat,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            }
          }

          this.setState({
            showLoader: true,
            disableSubmit: true,
          });
          try {
            addConcreteRequest(
              this.state.editConcrete == true ? EDIT_CONCRETE : ADD_CONCRETE,
              this.state.editConcrete == true ? data : param,
              () => null,
              (addConcreteResp) => {
                this.setState({
                  showLoader: false,
                });

                if (addConcreteResp.toString() == Strings.errors.timeout) {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: Strings.errors.checkInternet,
                      type: "error",
                    },
                    () => {
                      setTimeout(() => {
                        this.setState({
                          showToaster: false,
                          disableSubmit: false,
                        });
                      }, 2000);
                    },
                  );
                } else if (addConcreteResp.status) {
                  if (
                    addConcreteResp.status == 200 ||
                    addConcreteResp.status == 201
                  ) {
                    this.setState({
                      showToaster: true,
                      toastMessage:
                        this.state.editConcrete == true
                          ? "Concrete Request Updated Successfully."
                          : "Concrete Request Created Successfully.",
                      toastType: "success",
                    });
                    setTimeout(() => {
                      if (
                        this.props.route.params?.from == "search" ||
                        this.props.route.params?.from == "DetailsConcrete"
                      ) {
                        this.props.route.params.updateData("data");
                      }
                      this.props.refreshDashboard(true, "Add Dr Submit");
                      this.props.updateList(false);
                      this.props.refreshCalendar(true);
                      this.props.navigation.goBack();
                      this.setState({ showToaster: false });
                    }, 2000);
                    if (this.state.editConcrete) {
                      trackEvent("Edited_Concrete_Request");
                      mixPanelTrackEvent(
                        "Edited Concrete Request",
                        this.state.mixpanelParam,
                      );
                    } else {
                      trackEvent("Created_New_Concrete_Request");
                      mixPanelTrackEvent(
                        "Created New Concrete Request",
                        this.state.mixpanelParam,
                      );
                    }
                  } else if (addConcreteResp.data.message.message) {
                    this.showToaster(
                      "error",
                      addConcreteResp.data.message.message,
                    );
                  } else {
                    this.showToaster("error", addConcreteResp.data.message);
                  }
                } else {
                  this.showToaster("error", addConcreteResp.toString());
                }
              },
            );
          } catch (e) {
            console.log("error---->", e);
          }
        }
      }
    }
  };

  bottomContainer = () => {
    return (
      <View style={drStyles.bottomContainer}>
        <TouchableOpacity onPress={() => this.setState({ showCancel: true })}>
          <View style={drStyles.cancel}>
            <Text style={drStyles.cancelText}>{Strings.addMember.cancel}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            if (this.state.disableSubmit == false) {
              this.submit();
            }
          }}
        >
          <View style={drStyles.submit}>
            <Text style={drStyles.submitText}>
              {this.state.editConcrete
                ? Strings.addMember.update
                : Strings.addMember.submit}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  onPressDelDateTF = (type) => {
    if (type == "concrete") {
      this.setState({
        showDateModal: true,
        showPumpDateModal: false,
      });
    } else if (type == "pump") {
      this.setState({
        showDateModal: false,
        showPumpDateModal: true,
      });
    }

    if (Platform.OS == "ios") {
      if (type == "concrete") {
        if (!this.state.selectedDate) {
          this.onchangeDate("", this.state.calSelectedDate);
        }
      } else if (type == "pump") {
        if (!this.state.pumpOrderedDate) {
          this.onchangePumpDate("", this.state.pumpSelectedDate);
        }
      }
    }
  };

  onchangeDate = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showDateModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      const fullYear = selectedValue.getFullYear();
      const fullMonth = selectedValue.getMonth();
      const date = selectedValue.getDate();

      let sampleSelectDate = `${fullMonth + 1}/${date}/${fullYear}`;
      this.setState(
        {
          selectedDate: moment(sampleSelectDate).format("MM/DD/YYYY"),
          calSelectedDate: selectedValue,
          isFromDate: true,
          // pumpOrderedDate: `${fullMonth + 1}/${date}/${fullYear}`,
          // pumpSelectedDate: selectedValue,
          // selectedStartTime: "",
          // selectedEndTime: "",
        },
        () => {
          this.setState({
            //  selectedDate: `${fullMonth + 1}/${date}/${fullYear}`,
            //calSelectedDate: selectedValue,
            pumpOrderedDate: moment(sampleSelectDate).format("MM/DD/YYYY"),
            pumpSelectedDate: selectedValue,
            // selectedStartTime: "",
          });
        },
      );
      selectedDate = selectedValue;
      selectedPumpDate = selectedValue;
    }
  };

  onchangePumpDate = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({ showPumpDateModal: false });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      const fullYear = selectedValue.getFullYear();
      const fullMonth = selectedValue.getMonth();
      const date = selectedValue.getDate();
      let sampleSelectDate = `${fullMonth + 1}/${date}/${fullYear}`;
      this.setState({
        pumpOrderedDate: moment(sampleSelectDate).format("MM/DD/YYYY"),
        pumpSelectedDate: selectedValue,
      });
      selectedPumpDate = selectedValue;
    }
  };
  onPressPumpStartDateTF = () => {
    this.setState({
      showPumpStartTimeModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedPumpStartTime) {
        this.onChangePumpStart("", this.state.calSelectedPumpStartTime);
      }
    }
  };
  onChangePumpStart = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showPumpStartTimeModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      let deliveryDate = selectedPumpDate;
      const fullYear = deliveryDate.getFullYear();
      const fullMonth = deliveryDate.getMonth();
      const date = deliveryDate.getDate();
      let hours = selectedValue.getHours();
      let minutes = selectedValue.getMinutes();

      const delStartTime = new Date(fullYear, fullMonth, date, hours, minutes);
      const ampm = hours >= 12 ? "pm" : "am";

      // Fix: Ensure 12-hour format displays correctly
      hours = hours % 12 || 12; // Converts 0 to 12 and keeps 12 as 12
      minutes = minutes < 10 ? `0${minutes}` : minutes;

      const strTime = `${hours}:${minutes} ${ampm}`;

      // Update end time to be 1 hour ahead
      const delEndTime = moment(delStartTime, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .toDate();
      let updatedEndTimes = moment(selectedValue, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .toDate();
      let updatedEndTimeStrTime = moment(selectedValue, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .format("hh:mm a");

      this.setState({
        selectedPumpStartTime: strTime,
        calSelectedPumpStartTime: selectedValue,
        selectedPumpEndTime: updatedEndTimeStrTime,
        calSelectedPumpEndTime: updatedEndTimes,
      });

      selectedPumpStartDate = delStartTime;
      selectedPumpEndDate = delEndTime;
    }
  };

  onPressPumpEndDateTF = () => {
    this.setState({
      showPumpEndTimeModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedPumpEndTime) {
        this.onChangePumpEndTime("", this.state.calSelectedPumpEndTime);
      }
    }
  };

  onChangePumpEndTime = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showPumpEndTimeModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      let deliveryDate = selectedPumpDate;
      //let deliveryDate = selectedValue;
      const fullYear = deliveryDate.getFullYear();
      const fullMonth = deliveryDate.getMonth();
      const date = deliveryDate.getDate();
      let hours = selectedValue.getHours();
      let minutes = selectedValue.getMinutes();

      const delEndTime = new Date(fullYear, fullMonth, date, hours, minutes);

      const ampm = hours >= 12 ? "pm" : "am";

      hours %= 12;
      hours = (hours < 10 ? `0${hours}` : hours) || 12;
      minutes = minutes < 10 ? `0${minutes}` : minutes;

      const endTime = `${hours}:${minutes} ${ampm}`;

      this.setState({
        selectedPumpEndTime: endTime,
        calSelectedPumpEndTime: selectedValue,
      });

      selectedPumpEndDate = delEndTime;
    }
  };

  onPressStartDateTF = () => {
    this.setState({
      showStartTimeModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedStartTime) {
        this.onChangeStart("", this.state.calSelectedStartTime);
      }
    }
  };

  onChangeStart = (tevent, selectedValue) => {
    if (Platform.OS === "android") {
      if (tevent.type === "set" || tevent.type === "dismissed") {
        this.setState({
          showStartTimeModal: false,
        });
      }
    }

    if (Platform.OS === "ios" || tevent.type === "set") {
      let deliveryDate = this.state.calSelectedDate;
      const fullYear = deliveryDate.getFullYear();
      const fullMonth = deliveryDate.getMonth();
      const date = deliveryDate.getDate();
      let hours = selectedValue.getHours();
      let minutes = selectedValue.getMinutes();

      const delStartTime = new Date(fullYear, fullMonth, date, hours, minutes);
      const ampm = hours >= 12 ? "pm" : "am";

      // ✅ Fix applied: Ensure 12 PM remains 12 and doesn't convert to 00
      hours = hours % 12 === 0 ? 12 : hours % 12;

      minutes = minutes < 10 ? `0${minutes}` : minutes;

      const strTime = `${hours}:${minutes} ${ampm}`;

      // Update end time (1 hour ahead)
      const delEndTime = moment(delStartTime, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .toDate();
      let updatedEndTimes = moment(selectedValue, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .toDate();
      let updatedEndTimeStrTime = moment(selectedValue, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .format("hh:mm a");

      this.setState({
        selectedStartTime: strTime,
        calSelectedStartTime: selectedValue,
        selectedEndTime: updatedEndTimeStrTime,
        calSelectedEndTime: updatedEndTimes,
      });

      selectedStartDate = delStartTime;
      selectedEndDate = delEndTime;
    }
  };

  onPressEndDateTF = () => {
    this.setState({
      showEndTimeModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedEndTime) {
        this.onChangeEndTime("", this.state.calSelectedEndTime);
      }
    }
  };

  onChangeEndTime = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showEndTimeModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      let deliveryDate = this.state.calSelectedDate;
      const fullYear = deliveryDate.getFullYear();
      const fullMonth = deliveryDate.getMonth();
      const date = deliveryDate.getDate();
      let hours = selectedValue.getHours();
      let minutes = selectedValue.getMinutes();

      const delEndTime = new Date(fullYear, fullMonth, date, hours, minutes);

      const ampm = hours >= 12 ? "pm" : "am";

      hours %= 12;
      hours = (hours < 10 ? `0${hours}` : hours) || 12;
      minutes = minutes < 10 ? `0${minutes}` : minutes;

      const endTime = `${hours}:${minutes} ${ampm}`;

      this.setState({
        selectedEndTime: endTime,
        calSelectedEndTime: selectedValue,
      });

      selectedEndDate = delEndTime;
    }
  };

  renderHeader = () => {
    return (
      <View style={drStyles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.props.cameBack(false);
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={CommonStyles.header}>
          {this.state.editConcrete
            ? Strings.addConcrete.edit
            : Strings.addConcrete.tittle}
        </Text>
        <TouchableOpacity
          style={{
            width: 25,
            height: 25,
            borderRadius: 25 / 2,
            marginRight: 15,
            justifyContent: "center",
            alignItems: "center",
            borderWidth: 2,
            borderColor: Colors.black,
          }}
          onPress={() => {
            this.setState({ showInfo: true });
          }}
        >
          <Text style={{ color: Colors.black, fontSize: wp("5%") }}>i</Text>
        </TouchableOpacity>
      </View>
    );
  };

  renderSeleRow = (id, onPress, item, style) => {
    return (
      <TouchableOpacity
        activeOpacity={0.6}
        key={id}
        onPress={onPress}
        style={{
          width: wp("90%"),
          //height: 30,
          alignSelf: "center",
          justifyContent: "center",
          // backgroundColor: 'red'
        }}
      >
        <Text
          style={{
            color: "rgba(0, 0, 0, 0.87)",
            width: wp("90%"),
            marginLeft: 4,
            marginTop: 2,
            padding: 5,
          }}
        >
          {id}
        </Text>
      </TouchableOpacity>
    );
  };

  renderChip = (id, onClose, item, style, iconStyle) => (
    <Chip
      key={id}
      iconStyle={iconStyle}
      onClose={() => {
        handleChipClose(onClose);
      }}
      text={id}
      style={style}
    />
  );

  // getSelectedCompanyList = (data) => {
  //   this.setState({
  //     responisbleCompanyList: data,
  //   });
  // };

  //on press done in date picker
  onDatePickerDonePressed() {
    //let event = {type: 'set'}
    // console.log('Done',selectedDate)
    // if (selectedDate === this.state.calSelectedDate) {
    //   console.log('Done')
    //   this.onchangeDate(event, selectedDate);
    // } else if (selectedPumpDate == this.state.pumpSelectedDate) {
    //   console.log('samole')
    //   this.onchangePumpDate(event, selectedPumpDate);
    // }
    this.setState({ showDateModal: false, showPumpDateModal: false });
    // this.setState({ showDateModal: false });
  }

  renderConcreteDetails = () => {
    const { orderNo, slump, quantity, primer, isConcreteConfirmed } =
      this.state;
    return (
      <View>
        <View
          style={[
            CommonStyles.rowContainer,
            { marginTop: hp("3%"), width: "92%" },
          ]}
        >
          <Text
            style={[
              drStyles.idTitle,
              CommonStyles.mediumHeaderText,
              {
                fontSize: wp("4%"),
              },
            ]}
          >
            {Strings.concrete.concreteDetails}
          </Text>
          <View style={CommonStyles.lineStyle} />
        </View>
        {/* concrete supplier */}
        <View
          style={{
            width: "90%",
            alignSelf: "center",
            marginTop: hp("3%"),
          }}
        >
          <Text
            style={[
              drStyles.idTitle,
              {
                fontSize: wp("4%"),
                marginLeft: 4,
                alignSelf: "center",
                width: wp("90%"),
              },
            ]}
          >
            {Strings.addConcrete.concreteSupplier}{" "}
            <Text style={{ color: Colors.red }}>*</Text>
          </Text>
          <MultiSelectDropDown
            dataItems={this.state.concreteSupplier}
            title={"Select"}
            selectedDataItem={this.getSelectedCompanyList}
          />
        </View>
        {/* mix design */}
        <View
          style={{
            width: "90%",
            alignSelf: "center",
            marginTop: hp("3%"),
          }}
        >
          <Text
            style={[
              drStyles.idTitle,
              {
                fontSize: wp("4%"),
                marginLeft: 4,
                alignSelf: "center",
                width: wp("90%"),
              },
            ]}
          >
            {Strings.addConcrete.mixDesign}{" "}
          </Text>
          <Selectize
            tintColor={Colors.themeColor}
            items={this.state.mixDesignsData}
            selectedItems={this.state.selectedMixDesign}
            containerStyle={{
              zIndex: 1,
            }}
            renderRow={(id, onPress, item, style) =>
              this.renderSeleRow(id, onPress, item, style)
            }
            renderChip={(id, onClose, item, style, iconStyle) => {
              return (
                <View style={[drStyles.root, style]}>
                  <View style={drStyles.container}>
                    <Text style={drStyles.text} numberOfLines={1}>
                      {id}
                    </Text>
                    <TouchableOpacity
                      style={[drStyles.iconWrapper, iconStyle]}
                      onPress={onClose}
                    >
                      <Text
                        style={[
                          drStyles.icon,
                          this.isIOS ? drStyles.iconIOS : drStyles.iconAndroid,
                        ]}
                      >
                        x
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              );
            }}
            onChangeSelectedItems={(text) => {
              this.setState({
                selectedMixDesignsList: text,
                mixDesignsData: [],
              });
            }}
            textInputProps={{
              onChangeText: (text) =>
                this.onChangeperson(text, Strings.addConcrete.mixDesign),
              // placeholder: 'Enter ' + Strings.addConcrete.mixDesign,
              style: CommonStyles.selctizeInput,
            }}
          />
        </View>
        {/* order number */}
        <TextField
          attrName={Strings.addConcrete.orderNo}
          title={Strings.addConcrete.orderNo}
          value={orderNo.toString()}
          updateMasterState={(key, value) => {
            this.setState({ orderNo: value });
          }}
          mandatory={false}
          textInputStyles={CommonStyles.inputText}
        />
        {/* slump */}
        <TextField
          attrName={Strings.addConcrete.slump}
          title={Strings.addConcrete.slump}
          value={slump.toString()}
          updateMasterState={(key, value) => {
            this.setState({ slump: value });
          }}
          mandatory={false}
          textInputStyles={CommonStyles.inputText}
        // placeholder={"Enter " + Strings.addConcrete.slump}
        />

        {/* <View style={CommonStyles.rowContainer}> */}
        {/* Track spacing Hrs */}
        <TextField
          attrName={Strings.addConcrete.truckSpacing}
          title={Strings.addConcrete.truckSpacing}
          value={this.state.truckSpacingHrs}
          updateMasterState={(key, value) => {
            this.updateMasterState(key, value);
          }}
          mandatory={false}
          textInputStyles={CommonStyles.inputText}
          //showButton={true}
          onPress={() => {
            Keyboard.dismiss();
            this.setState({ hoursModal: true, showTruckSpacingHrsModal: true });
          }}
          container={
            {
              // width: Dimensions.scaleWidth(42),
              // alignSelf: "flex-start",
            }
          }
          imageSource={Images.clock}
        //placeholder={Strings.addConcrete.selectHrs}
        />

        {/* To Time  */}
        {/* <TextField
          attrName={Strings.addConcrete.truckSpacingMins}
          value={this.state.truckSpacingMins ? `${this.state.truckSpacingMins.toString()}` + (this.state.truckSpacingMins == '1' ? ' Min' : ' Mins') : (this.state.truckSpacingMins == '0' ? `${'0' + ' Min'}` : '')}
          updateMasterState={(key, value) => {
            this.updateMasterState(key, value);
          }}
          mandatory={false}
          textInputStyles={CommonStyles.inputText}
          showButton={true}
          onPress={() => {
            Keyboard.dismiss();
            this.setState({minsModal: true,showTruckSpacingMinsModal: true})
          }}
          container={{
         //   width: Dimensions.scaleWidth(42),
            alignSelf: "flex-start",
          }}
          imageSource={Images.clock}
          // placeholder={Strings.addConcrete.selectMins}
          // progressWidth={Dimensions.scaleWidth(42)}
          // buttonContainer={{
          //   width: Dimensions.scaleWidth(42),
          // }}
        /> */}
        {/* </View> */}
        {/* quantity */}
        <TextField
          attrName={Strings.addConcrete.quantityOrdered}
          title={Strings.addConcrete.quantityOrdered}
          value={quantity.toString()}
          updateMasterState={(key, value) => {
            this.setState({ quantity: value });
          }}
          mandatory={false}
          textInputStyles={CommonStyles.inputText}
        // placeholder={"Enter " + Strings.addConcrete.quantity}
        // keyboardType={'number-pad'}
        />
        {/* primer For pump*/}

        <TextField
          attrName={Strings.addConcrete.primerForPump}
          title={Strings.addConcrete.primerForPump}
          value={primer.toString()}
          updateMasterState={(key, value) => {
            this.setState({ primer: value });
          }}
          mandatory={false}
          textInputStyles={CommonStyles.inputText}
        // placeholder={"Enter " + Strings.addConcrete.primerForPump}
        />
        {/* <DropDownPicker
              items={this.state.primerList}
              defaultValue={this.state.primer}
              placeholder={"Select"}
              placeholderStyle={{
                color: Colors.placeholder,
                fontSize: 14,
              }}
              customArrowUp={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              customArrowDown={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              //     containerStyle={{ height: hp("5%") }}
              style={{
                backgroundColor: Colors.white,
                width: wp("90%"),
                borderColor: "#0000",
                borderBottomColor: Colors.placeholder,
                alignSelf: "center",
                marginBottom:20,
                //    height: hp("3%"),
              }}
              itemStyle={{
                justifyContent: "flex-start",
                fontSize: 14,
              }}
              dropDownStyle={{
                backgroundColor: Colors.white,
                width: "90%",
                alignSelf: "center",
              }}
              onChangeItem={(item)=>{ this.setState({primer: item.value});}}
              labelStyle={{
                fontSize: 14,
                fontFamily: Fonts.montserratRegular,
              }}
              arrowStyle={{
                height: hp("2%"),
              }}
              selectedLabelStyle={{ color: Colors.black }}
              zIndex={3000}
            /> */}
        {/* concrete confirmed */}

        <Text style={{ color: Colors.placeholder, fontSize: 16, margin: 10 }}>
          Carbon Tracking
        </Text>

        <View
          style={{
            borderWidth: 1,
            marginStart: 10,
            marginEnd: 10,
            paddingLeft: 10,
            borderLeftWidth: 10,
            borderLeftColor: "#00BBA1",
            borderRightColor: Colors.placeholder,
            borderTopColor: Colors.placeholder,
            borderBottomColor: Colors.placeholder,
          }}
        >
          <View style={{ marginTop: 10 }}>
            <Text style={{ fontSize: 15, color: Colors.expiredColorObject }}>
              {Strings.addDR.origin}
            </Text>

            <TouchableOpacity onPress={this.toggleModal}>
              <View
                style={{
                  borderBottomWidth: 1,
                  borderColor: "#dcdcdc",
                  paddingTop: 20,
                  paddingBottom: 20,
                }}
              >
                <Text
                  style={{
                    paddingLeft: 10,
                    fontSize: this.state.textFieldValue ? 14 : 15,
                    color: this.state.textFieldValue
                      ? Colors.black
                      : Colors.expiredColorObject,
                  }}
                >
                  {this.state.textFieldValue || "Origination Address"}
                </Text>
              </View>
            </TouchableOpacity>

            <Modal
              animationType="slide"
              transparent={true}
              visible={this.state.isModalVisible}
              onRequestClose={this.toggleModal}
              style={{
                paddingTop: 45,
                paddingBottom: 30,
                margin: 0,
                backgroundColor: "rgba(0, 0, 0, 0.3)",
                alignItems: "center",
              }}
            >
              <View
                style={{
                  width: "90%",
                  height: "80%",
                  backgroundColor: "#fff",
                  borderRadius: 10,
                  padding: 20,
                }}
              >
                <View style={{ flexDirection: "row" }}>
                  <Text
                    style={{ fontSize: 18, color: Colors.expiredColorObject }}
                  >
                    {Strings.addDR.origin}
                  </Text>
                  <View style={{}}>
                    <TouchableOpacity onPress={this.toggleModal}>
                      <Image
                        source={Images.close_icon}
                        style={{
                          width: 30,
                          height: 30,

                          marginLeft: "60%",
                        }}
                      />
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={{ marginTop: 10 }}>
                  <TextInput
                    value={this.state.textFieldValue}
                    placeholder="Enter Address"
                    style={{
                      fontSize: 14,

                      height: 60,

                      borderBottomWidth: 1,
                      borderBottomColor: Colors.expiredColorObject,
                    }}
                    multiline={true}
                    scrollEnabled={true}
                    numberOfLines={4}
                    onChangeText={(text) => {
                      this.setState({ textFieldValue: text });
                      this.getPlacePrediction(text);
                    }}
                  />
                </View>

                {this.state.showAutoComplete && (
                  <ScrollView
                    bounces={true}
                    onScroll={({ nativeEvent }) => {
                      const isCloseToBottom =
                        nativeEvent.layoutMeasurement.height +
                        nativeEvent.contentOffset.y >=
                        nativeEvent.contentSize.height;
                      if (isCloseToBottom) {
                        Keyboard.dismiss();
                      }
                    }}
                    scrollEventThrottle={400}
                  >
                    {this.state.predictionList.map((item) => (
                      <View key={item.place_id}>
                        <TouchableOpacity
                          onPress={() => this.onSelectPlace(item)}
                        >
                          <View
                            style={{
                              paddingTop: 5,
                              paddingBottom: 5,
                              marginStart: 20,
                              marginEnd: 20,
                            }}
                          >
                            <Text style={{ fontSize: 14 }}>
                              {item.description}
                            </Text>
                          </View>
                        </TouchableOpacity>
                        <View
                          style={{
                            height: 1,
                            backgroundColor: "#dcdcdc",
                            marginVertical: 10,
                            marginStart: 20,
                            marginEnd: 20,
                          }}
                        />
                      </View>
                    ))}
                  </ScrollView>
                )}
              </View>
            </Modal>
          </View>

          <TextField
            attrName={Strings.addDR.vehicleType}
            title={Strings.addDR.vehicleType}
            value={this.state.vehicleType}
            updateMasterState={this.updateMasterState}
            showButton={true}
            onPress={() => {
              Keyboard.dismiss();
              this.setState({ vehicleModelVisible: true });
            }}
            imageSource={Images.downArr}
            placeholder={"Choose vehicle type"}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
            }}
          />
        </View>

        <View style={[CommonStyles.rowContainer]}>
          <Text style={CommonStyles.questionHeader}>
            {Strings.addConcrete.concreteConfirmed}
            {isConcreteConfirmed && (
              <Text
                style={{
                  color: Colors.blackShade,
                  marginTop: Dimensions.scaleHeight(1),
                }}
              >
                {`\n\n`}Confirmed on{" "}
                {moment(concreteConfirmedDate).format(
                  "MMMM DD, YYYY, hh:mm:ss A",
                )}
              </Text>
            )}
          </Text>
          <Switch
            style={{ transform: [{ scaleX: 0.9 }, { scaleY: 0.8 }] }}
            trackColor={{ false: Colors.placeholder, true: "#11FF00" }}
            thumbColor={"#fff"}
            ios_backgroundColor="#3e3e3e"
            onValueChange={() => {
              Keyboard.dismiss();
              this.setState({
                isConcreteConfirmed: !this.state.isConcreteConfirmed,
              });
              concreteConfirmedDate = new Date();
            }}
            value={isConcreteConfirmed}
          />
        </View>
      </View>
    );
  };

  renderPumpDetails = () => {
    const {
      isPumpRequired,
      pumpLocation,
      pumpShowUp,
      isPumpConfirmed,
      editConcrete,
      recurrenceType,
      deliveryStatus,
    } = this.state;
    return (
      <View>
        <View
          style={[
            CommonStyles.rowContainer,
            { marginTop: hp("3%"), width: "92%" },
          ]}
        >
          <Text
            style={[
              drStyles.idTitle,
              CommonStyles.mediumHeaderText,
              {
                fontSize: wp("4%"),
              },
            ]}
          >
            {Strings.addConcrete.pumpDetails}
          </Text>
          <View style={CommonStyles.lineStyle} />
        </View>
        {/* pump required */}
        <View
          style={[
            {
              width: "90%",
              alignSelf: "center",
              marginTop: hp("3%"),
            },
            CommonStyles.rowContainer,
          ]}
        >
          <Text
            style={[
              drStyles.idTitle,
              {
                marginLeft: 4,
                fontSize: wp("4%"),
              },
            ]}
          >
            {Strings.addConcrete.pumpRequired}
          </Text>
          <Switch
            style={{ transform: [{ scaleX: 0.9 }, { scaleY: 0.8 }] }}
            trackColor={{ false: Colors.placeholder, true: "#11FF00" }}
            thumbColor={"#fff"}
            ios_backgroundColor="#3e3e3e"
            onValueChange={() => {
              Keyboard.dismiss();
              this.setState({ isPumpRequired: !this.state.isPumpRequired });
              // this.setState({isPumpConfirmed: false});
              // pumpConfirmedDate = new Date();
            }}
            value={isPumpRequired}
          />
        </View>
        {/* pump size */}
        {isPumpRequired && (
          <>
            <View
              style={{
                width: wp("90%"),
                alignSelf: "center",
                marginTop: hp("3%"),
              }}
            >
              <Text
                style={[
                  drStyles.idTitle,
                  {
                    marginLeft: 4,
                    fontSize: wp("4%"),
                  },
                ]}
              >
                {Strings.addConcrete.pumpSize}{" "}
                {isPumpRequired && <Text style={{ color: Colors.red }}>*</Text>}
              </Text>
              <Selectize
                tintColor={Colors.themeColor}
                items={this.state.pumpSizeData}
                selectedItems={this.state.selectedPumpSize}
                containerStyle={{
                  zIndex: 1,
                }}
                renderRow={(id, onPress, item, style) =>
                  this.renderSeleRow(id, onPress, item, style)
                }
                renderChip={(id, onClose, item, style, iconStyle) => (
                  <View style={[drStyles.root, style]}>
                    <View style={drStyles.container}>
                      <Text style={drStyles.text} numberOfLines={1}>
                        {id}
                      </Text>
                      <TouchableOpacity
                        style={[drStyles.iconWrapper, iconStyle]}
                        onPress={onClose}
                      >
                        <Text
                          style={[
                            drStyles.icon,
                            this.isIOS
                              ? drStyles.iconIOS
                              : drStyles.iconAndroid,
                          ]}
                        >
                          x
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
                onChangeSelectedItems={(text) => {
                  this.setState({
                    selectedPumpSizeList: text,
                    pumpSizeData: [],
                  });
                }}
                textInputProps={{
                  onChangeText: (text) =>
                    this.onChangeperson(text, Strings.addConcrete.pumpSize),
                  // placeholder: 'Enter ' + Strings.addConcrete.pumpSize,
                  style: CommonStyles.selctizeInput,
                }}
              />
            </View>

            <View
              style={{
                width: "90%",
                alignSelf: "center",
              }}
            >
              <TextField
                attrName={Strings.addConcrete.pumpOrdered}
                title={Strings.addConcrete.pumpOrdered}
                value={this.state.pumpOrderedDate.toString()}
                updateMasterState={(key, value) => {
                  this.updateMasterState(key, value);
                }}
                mandatory={true}
                textInputStyles={
                  editConcrete == true &&
                    deliveryStatus == Strings.deliveryStatusName.expired
                    ? styles.textStyle
                    : editConcrete == true
                      ? styles.textStyleEdited
                      : styles.textStyle
                }
                showButton={this.state.isEditDate}
                editable={this.state.isEditDate}
                onPress={() => {
                  if (
                    editConcrete == true &&
                    recurrenceType == Strings.calendarSettings.doseNotRepeat
                  ) {
                    this.onPressDelDateTF("pump");
                  } else if (
                    editConcrete == true &&
                    deliveryStatus == Strings.deliveryStatusName.expired
                  ) {
                    this.onPressDelDateTF("pump");
                  } else if (editConcrete == true) {
                    this.setState({
                      showDateModal: false,
                      showPumpDateModal: false,
                    });
                  } else {
                    this.onPressDelDateTF("pump");
                  }
                }}
                imageSource={Images.calGray}
                placeholder={"Select"}
              />
            </View>
            <View
              style={{
                width: "90%",
                alignSelf: "center",
              }}
            >
              {/* Start Time  */}
              <TextField
                attrName={Strings.placeholders.pumpShowUpTime}
                title={Strings.placeholders.pumpShowUpTime}
                value={this.state.selectedPumpStartTime.toString()}
                updateMasterState={(key, value) => {
                  this.updateMasterState(key, value);
                }}
                mandatory={true}
                textInputStyles={{
                  // here you can add additional TextInput drStyles
                  color: Colors.black,
                  fontSize: 14,
                }}
                showButton={this.state.isEditDate}
                editable={this.state.isEditDate}
                onPress={() => {
                  this.onPressPumpStartDateTF();
                }}
                container={{
                  //width: wp("42%"),
                  alignSelf: "flex-start",
                }}
                imageSource={Images.clock}
                placeholder={"Select"}
              // progressWidth={wp("42%")}
              // buttonContainer={{
              //   width: wp("42%"),
              // }}
              />
            </View>
            <View
              style={{
                width: "90%",
                alignSelf: "center",
              }}
            >
              {/* End Time  */}
              <TextField
                attrName={Strings.placeholders.pumpcompletionTime}
                title={Strings.placeholders.pumpcompletionTime}
                value={this.state.selectedPumpEndTime.toString()}
                updateMasterState={(key, value) => {
                  this.updateMasterState(key, value);
                }}
                mandatory={true}
                textInputStyles={{
                  // here you can add additional TextInput drStyles
                  color: Colors.black,
                  fontSize: 14,
                }}
                showButton={this.state.isEditDate}
                editable={this.state.isEditDate}
                onPress={() => {
                  this.onPressPumpEndDateTF();
                }}
                container={{
                  //width: wp("42%"),
                  alignSelf: "flex-start",
                }}
                imageSource={Images.clock}
                placeholder={"Select"}
              // progressWidth={wp("42%")}
              // buttonContainer={{
              //   width: wp("42%"),
              // }}
              />
            </View>

            {/* pump location */}
            <TextField
              attrName={Strings.addConcrete.pumpLocation}
              title={Strings.addConcrete.pumpLocation}
              value={pumpLocation.toString()}
              updateMasterState={(key, value) => {
                // this.updateMasterState(key, value);
                this.setState({ pumpLocation: value });
              }}
              textInputStyles={CommonStyles.inputText}
              // placeholder={"Enter " + Strings.addConcrete.pumpLocation}
              mandatory={isPumpRequired ? true : false}
            />

            {/* <TextField
                  attrName={Strings.addDR.vehicleType}
                  title={Strings.addDR.vehicleType}
                  value={this.state.pumpVehicleType}
                  updateMasterState={this.updateMasterState}
                  // mandatory={true}
                  showButton={true}
                  onPress={() => {
                    Keyboard.dismiss();
                    this.setState({ pumpVehicleModelVisible:true });
                  }}
                  imageSource={Images.downArr}
                  placeholder={"Select"}
                  textInputStyles={{
                    color: Colors.black,
                    fontSize: 14,
                  }}
                /> */}

            <Text
              style={{ color: Colors.placeholder, fontSize: 16, margin: 10 }}
            >
              Carbon Tracking
            </Text>

            <View
              style={{
                borderWidth: 1,
                marginStart: 10,
                marginEnd: 10,
                paddingLeft: 10,
                borderLeftWidth: 10,
                borderLeftColor: "#00BBA1",
                borderRightColor: Colors.placeholder,
                borderTopColor: Colors.placeholder,
                borderBottomColor: Colors.placeholder,
              }}
            >
              <View style={{ marginTop: 10 }}>
                <Text
                  style={{ fontSize: 15, color: Colors.expiredColorObject }}
                >
                  {Strings.addDR.origin}
                </Text>

                <TouchableOpacity onPress={this.togglePumpModal}>
                  <View
                    style={{
                      borderBottomWidth: 1,
                      borderColor: "#dcdcdc",
                      paddingTop: 20,
                      paddingBottom: 20,
                    }}
                  >
                    <Text
                      style={{
                        paddingLeft: 10,
                        fontSize: this.state.pumpTextFieldValue ? 14 : 15,
                        color: this.state.pumpTextFieldValue
                          ? Colors.black
                          : Colors.expiredColorObject,
                      }}
                    >
                      {this.state.pumpTextFieldValue || "Origination Address"}
                    </Text>
                  </View>
                </TouchableOpacity>

                <Modal
                  animationType="slide"
                  transparent={true}
                  visible={this.state.isPumpModalVisible}
                  onRequestClose={this.togglePumpModal}
                  style={{
                    paddingTop: 45,
                    paddingBottom: 30,
                    margin: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.3)",
                    alignItems: "center",
                  }}
                >
                  <View
                    style={{
                      width: "90%",
                      height: "70%",
                      backgroundColor: "#fff",
                      borderRadius: 10,
                      padding: 20,
                    }}
                  >
                    <View style={{ flexDirection: "row" }}>
                      <Text
                        style={{
                          fontSize: 18,
                          color: Colors.expiredColorObject,
                        }}
                      >
                        {Strings.addDR.origin}
                      </Text>
                      <View style={{}}>
                        <TouchableOpacity onPress={this.togglePumpModal}>
                          <Image
                            source={Images.close_icon}
                            style={{
                              width: 30,
                              height: 30,

                              marginLeft: "60%",
                            }}
                          />
                        </TouchableOpacity>
                      </View>
                    </View>

                    <View style={{ marginTop: 10 }}>
                      <TextInput
                        value={this.state.pumpTextFieldValue}
                        placeholder="Enter Address"
                        style={{
                          fontSize: 14,

                          height: 60,

                          borderBottomWidth: 1,
                          borderBottomColor: Colors.expiredColorObject,
                        }}
                        multiline={true}
                        scrollEnabled={true}
                        numberOfLines={4}
                        onChangeText={(text) => {
                          this.setState({ pumpTextFieldValue: text });
                          this.getPlacePredictionPump(text);
                        }}
                      />
                    </View>

                    {this.state.pumpShowAutoComplete && (
                      <ScrollView
                        bounces={true}
                        onScroll={({ nativeEvent }) => {
                          const isCloseToBottom =
                            nativeEvent.layoutMeasurement.height +
                            nativeEvent.contentOffset.y >=
                            nativeEvent.contentSize.height;
                          if (isCloseToBottom) {
                            Keyboard.dismiss();
                          }
                        }}
                        scrollEventThrottle={400}
                      >
                        {this.state.pumpPredictionList.map((item) => (
                          <View key={item.place_id}>
                            <TouchableOpacity
                              onPress={() => this.onSelectPlacePump(item)}
                            >
                              <View
                                style={{
                                  paddingTop: 5,
                                  paddingBottom: 5,
                                  marginStart: 20,
                                  marginEnd: 20,
                                }}
                              >
                                <Text style={{ fontSize: 14 }}>
                                  {item.description}
                                </Text>
                              </View>
                            </TouchableOpacity>
                            <View
                              style={{
                                height: 1,
                                backgroundColor: "#dcdcdc",
                                marginVertical: 10,
                                marginStart: 20,
                                marginEnd: 20,
                              }}
                            />
                          </View>
                        ))}
                      </ScrollView>
                    )}
                  </View>
                </Modal>
              </View>

              <TextField
                attrName={Strings.addDR.vehicleType}
                title={Strings.addDR.vehicleType}
                value={this.state.pumpVehicleType}
                updateMasterState={this.updateMasterState}
                showButton={true}
                onPress={() => {
                  Keyboard.dismiss();
                  this.setState({ pumpVehicleModelVisible: true });
                }}
                imageSource={Images.downArr}
                placeholder={"choose vehicle Type"}
                textInputStyles={{
                  color: Colors.black,
                  fontSize: 14,
                }}
              />
            </View>

            <View style={[CommonStyles.rowContainer]}>
              <Text style={CommonStyles.questionHeader}>
                {Strings.addConcrete.pumpConfirmed}
                {/* {isPumpRequired && <Text style={{ color: Colors.red }}>*</Text>} */}
                {isPumpConfirmed && isPumpRequired && (
                  <Text
                    style={{
                      color: Colors.blackShade,
                      marginTop: Dimensions.scaleHeight(1),
                    }}
                  >
                    {`\n\n`}Confirmed on{" "}
                    {moment(pumpConfirmedDate).format(
                      "MMMM DD, YYYY, hh:mm:ss A",
                    )}
                  </Text>
                )}
              </Text>
              <Switch
                style={{ transform: [{ scaleX: 0.9 }, { scaleY: 0.8 }] }}
                trackColor={{ false: Colors.placeholder, true: "#11FF00" }}
                thumbColor={"#fff"}
                ios_backgroundColor="#3e3e3e"
                onValueChange={() => {
                  Keyboard.dismiss();
                  this.setState({
                    isPumpConfirmed: !this.state.isPumpConfirmed,
                  });
                  pumpConfirmedDate = new Date();
                }}
                value={isPumpRequired ? isPumpConfirmed : false}
                disabled={isPumpRequired ? false : true}
              />
            </View>
          </>
        )}
      </View>
    );
  };

  renderOtherDetails = () => {
    const { cubicYardsTotal } = this.state;
    return (
      <View>
        <View
          style={[
            CommonStyles.rowContainer,
            { marginTop: hp("3%"), width: "92%" },
          ]}
        >
          <Text
            style={[
              drStyles.idTitle,
              CommonStyles.mediumHeaderText,
              {
                fontSize: wp("4%"),
              },
            ]}
          >
            {Strings.addConcrete.otherDetails}
          </Text>
          <View style={CommonStyles.lineStyle} />
        </View>
        {/* hours to complete placement */}
        <Text
          style={[
            drStyles.idTitle,
            {
              marginLeft: 4,
              fontSize: wp("4%"),
              marginTop: hp("3%"),
              width: "90%",
              alignSelf: "center",
            },
          ]}
        >
          {Strings.addConcrete.completePlacementHrs}
        </Text>
        <View style={CommonStyles.rowContainer}>
          {/* complete placement spacing Hrs */}
          <TextField
            attrName={Strings.addConcrete.completePlacementHrs}
            value={
              this.state.completePlacementHrs
                ? `${this.state.completePlacementHrs.toString()}` +
                (this.state.completePlacementHrs == "1" ? " Hour" : " Hours")
                : this.state.completePlacementHrs == "0"
                  ? `${"0" + " Hour"}`
                  : ""
            }
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={false}
            textInputStyles={CommonStyles.inputText}
            showButton={true}
            onPress={() => {
              this.setState({
                hoursModal: true,
                showCompletePlacementHrsModal: true,
              });
            }}
            container={{
              width: Dimensions.scaleWidth(42),
              height: Dimensions.scaleHeight(4),
              alignSelf: "flex-start",
            }}
            placeholder={Strings.addConcrete.selectHrs}
            imageSource={Images.clock}
            progressWidth={Dimensions.scaleWidth(42)}
            buttonContainer={{
              width: Dimensions.scaleWidth(42),
            }}
          />

          {/* complete placement Time  */}
          <TextField
            attrName={Strings.addConcrete.completePlacementMins}
            value={
              this.state.completePlacementMins
                ? `${this.state.completePlacementMins.toString()}` +
                (this.state.completePlacementMins == "1" ? " Min" : " Mins")
                : this.state.completePlacementMins == "0"
                  ? `${"0" + " Min"}`
                  : ""
            }
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={false}
            textInputStyles={CommonStyles.inputText}
            showButton={true}
            onPress={() => {
              this.setState({
                minsModal: true,
                showCompletePlacementMinsModal: true,
              });
            }}
            container={{
              width: Dimensions.scaleWidth(42),
              height: Dimensions.scaleHeight(4),
              alignSelf: "flex-start",
            }}
            imageSource={Images.clock}
            placeholder={Strings.addConcrete.selectMins}
            progressWidth={Dimensions.scaleWidth(42)}
            buttonContainer={{
              width: Dimensions.scaleWidth(42),
            }}
          />
        </View>
        {/* cubic yards*/}
        <TextField
          attrName={Strings.addConcrete.cubicYardsTotal}
          title={Strings.addConcrete.cubicYardsTotal}
          value={cubicYardsTotal.toString()}
          updateMasterState={(key, value) => {
            // this.updateMasterState(key, value);
            this.setState({ cubicYardsTotal: value });
          }}
          textInputStyles={CommonStyles.inputText}
        />
      </View>
    );
  };

  onStartEndTimeDonePressed = () => {
    let event = { type: "set" };
    if (!this.state.selectedStartTime) {
      this.onChangeStart(event, selectedDate);
    }
    if (!this.state.selectedEndTime) {
      this.onChangeEndTime(event, selectedDate);
    }
    this.setState({
      showStartTimeModal: false,
      showEndTimeModal: false,
    });
  };
  onPumpStartEndTimeDonePressed = () => {
    let event = { type: "set" };
    if (!this.state.selectedPumpStartTime) {
      this.onChangePumpStart(event, selectedPumpDate);
    }
    if (!this.state.selectedPumpEndTime) {
      this.onChangePumpEndTime(event, selectedPumpDate);
    }
    this.setState({
      showPumpStartTimeModal: false,
      showPumpEndTimeModal: false,
    });
  };
  render() {
    const {
      selectedDate,
      calSelectedDate,
      recurrence,
      times,
      isMonthFirstCheck,
      isMonthSecondCheck,
      isMonthThirdCheck,
      monthlyLastDay,
      monthlyDay,
      isYearFirstCheck,
      isYearSecondCheck,
      isYearThirdCheck,
      editConcrete,
      endDateRecurrence,
      selectedDaysOccurs,
      listSetName,
      yearListSetName,
      selectedEndDateYear,
      recurrenceType,
      deliveryStatus,
      editRequestID,
    } = this.state;

    return (
      <>
        {this.state.isNetworkCheck ? (
          <NoInternet Refresh={() => this.networkCheck()} />
        ) : (
          <SafeAreaView style={drStyles.safeArea}>
            <View style={drStyles.parentContainer}>
              {this.renderHeader()}
              <KeyboardAwareScrollView
                keyboardShouldPersistTaps={true}
                enableResetScrollToCoords={false}
                scrollsToTop={false}
                extraScrollHeight={60}
              >
                <TextField
                  attrName={Strings.placeholders.description}
                  title={Strings.placeholders.description}
                  value={this.state.description}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  maxLength={150}
                  textInputStyles={CommonStyles.inputText}
                />

                <View style={drStyles.memberContainer}>
                  <Text style={drStyles.idTitle}>
                    {Strings.addConcrete.concreteId}{" "}
                    <Text style={{ color: Colors.red }}>*</Text>
                  </Text>

                  <Text style={drStyles.idText}>{this.state.concreteId}</Text>
                </View>
                <>
                  <Text style={styles.timeZoneText}>
                    {Strings.placeholders.location}
                    <Text style={styles.mandatory}>
                      {Strings.addNewEvent.mandatory}
                    </Text>
                  </Text>
                  <DropDownPicker
                    searchable={true}
                    items={this.state.locationDropdownList}
                    defaultValue={this.state.selectedLocationNew}
                    searchablePlaceholder={Strings.addNewEvent.timeZoneSearch}
                    placeholder={Strings.placeholders.location}
                    placeholderStyle={styles.dropDownPlaceHolderStyle}
                    containerStyle={styles.dropDownContainer}
                    style={styles.dropDownPickerStyle}
                    itemStyle={styles.dropDownItemStyle}
                    customArrowUp={(size) => (
                      <Image
                        source={Images.downArr}
                        style={[
                          styles.arrowDownStyle,
                          { width: size, height: size },
                        ]}
                      />
                    )}
                    customArrowDown={(size) => (
                      <Image
                        source={Images.downArr}
                        style={[
                          styles.arrowDownStyle,
                          { width: size, height: size },
                        ]}
                      />
                    )}
                    dropDownStyle={styles.dropDownListStyle}
                    selectedLabelStyle={styles.selectedDropDown}
                    onChangeItem={(item) => {
                      this.setState(
                        {
                          selectedLocationNew: item.value,
                          selectedLocationId: item.id,
                          // Reset selections when location changes
                          selectedGate: null,
                          selectedGateId: null,
                          selectedEquipName: null,
                          selectedEquipTypeId: null,
                          selectedEquipmentList: [],
                          // Also reset the equipment and gate lists to empty arrays
                          equipTypeList: [],
                          gateList: [],
                          storeEquipmentList: [],
                        },
                        () => {
                          // Filter equipment and gates based on the newly selected location
                          this.filterEquipmentAndGatesByLocation();
                        },
                      );
                    }}
                    zIndex={5000}
                  />
                </>

                <Text
                  style={[
                    drStyles.idTitle,
                    {
                      fontSize: wp("4%"),
                      marginLeft: 10,
                      marginTop: hp("3%"),
                      alignSelf: "center",
                      width: wp("90%"),
                    },
                  ]}
                >
                  {Strings.placeholders.equip}{" "}
                  {/* <Text style={{ color: Colors.red }}>*</Text> */}
                </Text>
                <MultiSelectDropDown
                  ref={(ref) => (this.multiSelectRef = ref)}
                  dataItems={this.state.equipTypeList}
                  title={"Select"}
                  selectedDataItem={this.getSelectedEquipmentList}
                />

                <TextField
                  attrName={Strings.placeholders.gate}
                  title={Strings.placeholders.gate}
                  value={this.state.selectedGate}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  // mandatory={true}
                  showButton={true}
                  onPress={() => {
                    Keyboard.dismiss();
                    this.setState({ 
                      gateModalVisible: true,
                      // Clear existing time slots when gate selection changes
                      selectedStartTime: '',
                      selectedEndTime: '',
                    });
                  }}
                  imageSource={Images.downArr}
                  placeholder={"Select"}
                  textInputStyles={{
                    // here you can add additional TextInput styles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                />

                <View
                  style={{ alignItems: "center", justifyContent: "center" }}
                >
                  <TouchableOpacity
                    style={[drStyles.submitButton]}
                    onPress={this.handleNavigation}
                  >
                    <Text style={drStyles.submitButtonText}>
                      Select Date and Time
                    </Text>
                  </TouchableOpacity>
                </View>

                <TextField
                  attrName={Strings.placeholders.additional_location}
                  title={Strings.placeholders.additional_location}
                  value={this.state.additionalLocationText}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={false}
                  maxLength={150}
                  textInputStyles={CommonStyles.inputText}
                />
                {/* <View style={{ 
                width: "90%",
                alignSelf: "center",
                marginTop: hp("3%"),}}>
              <Text
                style={[
                  drStyles.idTitle,
                  {
                    marginLeft: 4,
                    fontSize: wp("4%"),
                  },
                ]}
              >
                {Strings.addConcrete.placement}{" "}
                <Text style={{ color: Colors.red }}>*</Text>
              </Text>

              <Selectize
                tintColor={Colors.themeColor}
                items={this.state.placementsData}
                selectedItems={this.state.selectedPlacement}
                containerStyle={{
                  zIndex: 1,
                }}
                renderRow={(id, onPress, item, style) =>
                  this.renderSeleRow(id, onPress, item, style)
                }
                renderChip={(id, onClose, item, style, iconStyle) => (
                  <View style={[drStyles.root, style]}>
                    <View style={drStyles.container}>
                      <Text style={drStyles.text} numberOfLines={1}>
                        {id}
                      </Text>
                      <TouchableOpacity
                        style={[drStyles.iconWrapper, iconStyle]}
                        onPress={onClose}
                      >
                        <Text
                          style={[
                            drStyles.icon,
                            this.isIOS
                              ? drStyles.iconIOS
                              : drStyles.iconAndroid,
                          ]}
                        >
                          x
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
                onChangeSelectedItems={(text) => {
                  this.setState({
                    selectedPlacementsList: text,
                    placementsData:[],
                  });
                }}
                textInputProps={{
                  onChangeText:(text) => this.onChangeperson(text,Strings.addConcrete.placement),
                  style: CommonStyles.selctizeInput,
                }}
              />
            </View> */}
                <View
                  style={{
                    width: "90%",
                    alignSelf: "center",
                    marginTop: hp("3%"),
                  }}
                >
                  <Text
                    style={[
                      drStyles.idTitle,
                      {
                        fontSize: wp("4%"),
                        marginLeft: 4,
                      },
                    ]}
                  >
                    {Strings.placeholders.responisblePerson}{" "}
                    <Text style={{ color: Colors.red }}>*</Text>
                  </Text>
                  {!this.state.showMultipleSec && (
                    <Selectize
                      tintColor={Colors.themeColor}
                      items={this.state.responsiblePersonData}
                      selectedItems={this.state.selectedItem}
                      containerStyle={{
                        zIndex: 1,
                      }}
                      renderRow={(id, onPress, item, style) =>
                        this.renderSeleRow(id, onPress, item, style)
                      }
                      renderChip={(id, onClose, item, style, iconStyle) => (
                        <View style={[drStyles.root, style]}>
                          <View style={drStyles.container}>
                            <Text style={drStyles.text} numberOfLines={1}>
                              {id}
                            </Text>
                            {id != this.props.responsiblePersonData.name && (
                              <TouchableOpacity
                                style={[drStyles.iconWrapper, iconStyle]}
                                onPress={onClose}
                              >
                                <Text
                                  style={[
                                    drStyles.icon,
                                    this.isIOS
                                      ? drStyles.iconIOS
                                      : drStyles.iconAndroid,
                                  ]}
                                >
                                  x
                                </Text>
                              </TouchableOpacity>
                            )}
                          </View>
                        </View>
                      )}
                      onChangeSelectedItems={(text) => {
                        this.setState({
                          selectedItemList: text,
                          responsiblePersonData: [],
                        });
                      }}
                      textInputProps={{
                        onChangeText: (text) =>
                          this.onChangeperson(
                            text,
                            Strings.placeholders.responisblePerson,
                          ),
                        // placeholder: 'Enter ' + Strings.placeholders.responisblePerson,
                        style: CommonStyles.selctizeInput,
                      }}
                    />
                  )}
                  {this.state.showMultipleSec && (
                    <Selectize
                      tintColor={Colors.themeColor}
                      items={this.state.responsiblePersonData}
                      selectedItems={this.state.selectedItem}
                      containerStyle={{
                        zIndex: 1,
                      }}
                      renderRow={(id, onPress, item, style) =>
                        this.renderSeleRow(id, onPress, item, style)
                      }
                      renderChip={(id, onClose, item, style, iconStyle) => (
                        <View style={[drStyles.root, style]}>
                          <View style={drStyles.container}>
                            <Text style={drStyles.text} numberOfLines={1}>
                              {id}
                            </Text>
                            {id != this.props.responsiblePersonData.name && (
                              <TouchableOpacity
                                style={[drStyles.iconWrapper, iconStyle]}
                                onPress={onClose}
                              >
                                <Text
                                  style={[
                                    drStyles.icon,
                                    this.isIOS
                                      ? drStyles.iconIOS
                                      : drStyles.iconAndroid,
                                  ]}
                                >
                                  x
                                </Text>
                              </TouchableOpacity>
                            )}
                          </View>
                        </View>
                      )}
                      onChangeSelectedItems={(text) => {
                        this.setState({
                          selectedItemList: text,
                          responsiblePersonData: [],
                        });
                      }}
                      textInputProps={{
                        onChangeText: (text) =>
                          this.onChangeperson(
                            text,
                            Strings.placeholders.responisblePerson,
                          ),
                        // placeholder: 'Enter ' + Strings.placeholders.responisblePerson,
                        style: CommonStyles.selctizeInput,
                      }}
                    />
                  )}
                </View>
                {/* <View style={{ 
                width: "90%",
                alignSelf: "center",
                marginTop: hp("1%"),}}>
              <TextField
                attrName={Strings.addConcrete.date}
                title={Strings.addConcrete.date}
                value={this.state.selectedDate.toString()}
                updateMasterState={(key, value) => {
                  this.updateMasterState(key, value);
                }}
                mandatory={true}
                textInputStyles={(editConcrete == true&& deliveryStatus == Strings.deliveryStatusName.expired ) || (editConcrete == true && editRequestID ==1) ?  styles.textStyle : (editConcrete  == true) ? styles.textStyleEdited : styles.textStyle}
                showButton={this.state.isEditDate}
                editable={this.state.isEditDate}
                onPress={() => {
                  Keyboard.dismiss()
                  if(editConcrete == true && recurrenceType == Strings.calendarSettings.doseNotRepeat || editConcrete == true && editRequestID ==1 ){
                    this.onPressDelDateTF('concrete');
                  } else if(editConcrete == true && deliveryStatus == Strings.deliveryStatusName.expired ){
                    this.onPressDelDateTF('concrete');
                  } else if(editConcrete == true ){
                    this.setState({
                      showDateModal: false,
                      showPumpDateModal: false,
                    });
                  } else {
                    this.onPressDelDateTF('concrete');
                    }
                }}
                imageSource={Images.calGray}
                placeholder={"Select"}
              />
            </View>

              <View style={{ 
                width: "90%",
                alignSelf: "center",
                marginTop: hp("1%"),}}>
              <TextField
                attrName={Strings.placeholders.placementStartTime}
                title={Strings.placeholders.placementStartTime}
                value={this.state.selectedStartTime.toString()}
                updateMasterState={(key, value) => {
                  this.updateMasterState(key, value);
                }}
                mandatory={true}
                textInputStyles={{
                  // here you can add additional TextInput drStyles
                  color: Colors.black,
                  fontSize: 14,
                }}
                showButton={this.state.isEditDate}
                editable={this.state.isEditDate}
                onPress={() => {
                  Keyboard.dismiss()
                  this.onPressStartDateTF();
                }}
                container={{
                 // width: wp("42%"),
                  alignSelf: "flex-start",
                }}
                imageSource={Images.clock}
                placeholder={"Select"}
                //progressWidth={wp("42%")}
                // buttonContainer={{
                //   width: wp("42%"),
                // }}
              />
              </View>
             
              <View style={{ 
                width: "90%",
                alignSelf: "center",
                marginTop: hp("1%"),}}>
              <TextField
                attrName={Strings.placeholders.anticipatedCompletionTime}
                title={Strings.placeholders.anticipatedCompletionTime}
                value={this.state.selectedEndTime.toString()}
                updateMasterState={(key, value) => {
                  this.updateMasterState(key, value);
                }}
                mandatory={true}
                textInputStyles={{
                  // here you can add additional TextInput drStyles
                  color: Colors.black,
                  fontSize: 14,
                }}
                showButton={this.state.isEditDate}
                editable={this.state.isEditDate}
                onPress={() => {
                  Keyboard.dismiss()
                  this.onPressEndDateTF();
                }}
                container={{
                 // width: wp("42%"),
                  alignSelf: "flex-start",
                }}
                imageSource={Images.clock}
                placeholder={"Select"}
                //progressWidth={wp("42%")}
                // buttonContainer={{
                //   width: wp("42%"),
                // }}
              />
            </View> */}

                {this.state.selectedDate !== "" && (
                  <TextField
                    attrName={Strings.addConcrete.date}
                    title={Strings.addConcrete.date}
                    value={this.state.selectedDate.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={true}
                    textInputStyles={styles.textStyle}
                    editable={false}
                    showButton={false}
                    container={{
                      marginTop: hp("3%"),
                    }}
                    placeholder={"Select"}
                  />
                )}
                {(this.state.selectedStartTime !== "" ||
                  this.state.selectedEndTime !== "") && (
                    <View
                      style={{
                        width: wp("90%"),
                        alignSelf: "center",
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      <TextField
                        attrName={Strings.placeholders.placementStartTime}
                        title={Strings.placeholders.placementStartTime}
                        value={this.state.selectedStartTime.toString()}
                        updateMasterState={(key, value) => {
                          this.updateMasterState(key, value);
                        }}
                        mandatory={false}
                        textInputStyles={{
                          color: Colors.black,
                          fontSize: 14,
                        }}
                        editable={false}
                        showButton={false}
                        container={{
                          width: wp("42%"),
                          alignSelf: "flex-start",
                        }}
                        placeholder={"Select"}
                      />

                      <TextField
                        attrName={Strings.placeholders.anticipatedCompletionTime}
                        title={Strings.placeholders.anticipatedCompletionTime}
                        value={this.state.selectedEndTime.toString()}
                        updateMasterState={(key, value) => {
                          this.updateMasterState(key, value);
                        }}
                        mandatory={false}
                        textInputStyles={{
                          color: Colors.black,
                          fontSize: 14,
                        }}
                        editable={false}
                        showButton={false}
                        container={{
                          width: wp("42%"),
                          alignSelf: "flex-start",
                        }}
                        placeholder={"Select"}
                      />
                    </View>
                  )}
                {editConcrete == false ? (
                  <>
                    {/* <Text style={styles.timeZoneText}>
              {Strings.placeholders.timeZone}
              <Text style={styles.mandatory}>{Strings.addNewEvent.mandatory}</Text>
            </Text>
            <Text style={styles.timeZoneValue}>{this.state.selectedTimeZone}</Text> */}
                    {/* <Dropdown
                data={this.state.equipTypeList}
                title={Strings.addEquipment.type}
                value={this.state.selectedEquipName}
                closeBtn={() => this.setState({ eqipModalVisible: false })}
                onPress={(item) => this.onPressEqipType(item)}
                visible={this.state.eqipModalVisible}
                onbackPress={() => this.setState({ eqipModalVisible: false })}
                container={drStyles.equipmentContainer}
                customMainContainer={drStyles.renderEquipStyle}
                equipTextContainer={drStyles.equipTextStyle}
              /> */}

                    {/* <DropDownPicker
            searchable={true} 
            items={this.state.timeZoneList}
            defaultValue={this.state.selectedTimeZone}
            searchablePlaceholder={Strings.addNewEvent.timeZoneSearch}
            placeholder={Strings.placeholders.chooseTimezone}
            placeholderStyle={styles.dropDownPlaceHolderStyle}
            containerStyle={styles.dropDownContainer}
            style={styles.dropDownPickerStyle}
            itemStyle={styles.dropDownItemStyle}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={[styles.arrowDownStyle,{ width: size,height: size }]}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={[styles.arrowDownStyle,{ width: size,height: size }]}
              />
            )}
            dropDownStyle={styles.dropDownListStyle}
            selectedLabelStyle={styles.selectedDropDown}
            onChangeItem={(item) =>
              this.setState({
                selectedTimeZone: item.value,
                selectedTimeZoneId: item.id,
              })
            }
            zIndex={5000}
            /> */}
                  </>
                ) : null}

                {this.renderConcreteDetails()}
                {this.renderPumpDetails()}
                {this.state.editConcrete &&
                  this.state.status == "Completed" &&
                  this.renderOtherDetails()}
                <TextField
                  attrName={Strings.placeholders.notes}
                  title={Strings.placeholders.notes}
                  value={this.state.notes}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  container={{ height: hp("14%") }}
                  mandatory={false}
                  maxLength={150}
                  textInputStyles={{
                    // here you can add additional TextInput drStyles
                    color: Colors.black,
                    fontSize: 14,
                    height: hp("9%"),
                    marginTop: hp("2%"),
                    // paddingTop: hp("2%")
                  }}
                  multiline={true}
                  showButton={false}
                  imageSource={Images.calGray}
                />

                {/* Recurrence Component */}
                {editConcrete == false ? (
                  <RecurrenceComponent
                    fromDate={calSelectedDate}
                    recurrenceName={recurrence}
                    onChangeRecrrenceName={(item) =>
                      this.setState({ recurrence: item })
                    }
                    timeValue={times}
                    onChangeTimeValue={(item) => this.setState({ times: item })}
                    monthFirstCheck={isMonthFirstCheck}
                    onChangeMonthFirstCheck={(item) =>
                      this.setState({ isMonthFirstCheck: item })
                    }
                    monthSecondCheck={isMonthSecondCheck}
                    onChangeMonthSecondCheck={(item) =>
                      this.setState({ isMonthSecondCheck: item })
                    }
                    monthThirdCheck={isMonthThirdCheck}
                    onChangeMonthThirdCheck={(item) =>
                      this.setState({ isMonthThirdCheck: item })
                    }
                    onMonthDay={monthlyDay}
                    onChangeMonthDay={(item) =>
                      this.setState({ monthlyDay: item })
                    }
                    onMonthLastDay={monthlyLastDay}
                    onChangeMonthLastDay={(item) =>
                      this.setState({ monthlyLastDay: item })
                    }
                    yearlyFirstCheck={isYearFirstCheck}
                    onChangeYearlyFirstCheck={(item) =>
                      this.setState({ isYearFirstCheck: item })
                    }
                    yearlySecondCheck={isYearSecondCheck}
                    onChangeYearlySecondCheck={(item) =>
                      this.setState({ isYearSecondCheck: item })
                    }
                    yearlyThirdCheck={isYearThirdCheck}
                    onChangeYearlyThirdCheck={(item) =>
                      this.setState({ isYearThirdCheck: item })
                    }
                    endDateCheck={endDateRecurrence}
                    onChangeEndDate={(item) =>
                      this.setState({ endDateRecurrence: item })
                    }
                    selectedDayCheck={selectedDaysOccurs}
                    onChangeSelectedDay={(item) =>
                      this.setState({ selectedDaysOccurs: item })
                    }
                    onChangeSelectedDayArray={(item) =>
                      this.setState({ selectedDayArray: item })
                    }
                    listName={listSetName}
                    onChangeListName={(item) =>
                      this.setState({ listSetName: item })
                    }
                    yearListName={yearListSetName}
                    onChangeYearListName={(item) =>
                      this.setState({ yearListSetName: item })
                    }
                    selectedEndDate={selectedEndDateYear}
                    onChangeSelectedEndDate={(item) =>
                      this.setState({ selectedEndDateYear: item })
                    }
                    onFromDateChanged={() =>
                      this.setState({ isFromDate: false })
                    }
                    isFromDateChanged={this.state.isFromDate}
                    recurrenceTypeData={this.state.editConcrete}
                  />
                ) : editConcrete == true ? (
                  <RecurrenceComponent
                    fromDate={calSelectedDate}
                    recurrenceName={recurrence}
                    onChangeRecrrenceName={(item) =>
                      this.setState({ recurrence: item })
                    }
                    timeValue={times}
                    onChangeTimeValue={(item) => this.setState({ times: item })}
                    monthFirstCheck={isMonthFirstCheck}
                    onChangeMonthFirstCheck={(item) =>
                      this.setState({ isMonthFirstCheck: item })
                    }
                    monthSecondCheck={isMonthSecondCheck}
                    onChangeMonthSecondCheck={(item) =>
                      this.setState({ isMonthSecondCheck: item })
                    }
                    monthThirdCheck={isMonthThirdCheck}
                    onChangeMonthThirdCheck={(item) =>
                      this.setState({ isMonthThirdCheck: item })
                    }
                    onMonthDay={monthlyDay}
                    onChangeMonthDay={(item) =>
                      this.setState({ monthlyDay: item })
                    }
                    onMonthLastDay={monthlyLastDay}
                    onChangeMonthLastDay={(item) =>
                      this.setState({ monthlyLastDay: item })
                    }
                    yearlyFirstCheck={isYearFirstCheck}
                    onChangeYearlyFirstCheck={(item) =>
                      this.setState({ isYearFirstCheck: item })
                    }
                    yearlySecondCheck={isYearSecondCheck}
                    onChangeYearlySecondCheck={(item) =>
                      this.setState({ isYearSecondCheck: item })
                    }
                    yearlyThirdCheck={isYearThirdCheck}
                    onChangeYearlyThirdCheck={(item) =>
                      this.setState({ isYearThirdCheck: item })
                    }
                    endDateCheck={endDateRecurrence}
                    onChangeEndDate={(item) =>
                      this.setState({ endDateRecurrence: item })
                    }
                    selectedDayCheck={selectedDaysOccurs}
                    onChangeSelectedDay={(item) =>
                      this.setState({ selectedDaysOccurs: item })
                    }
                    onChangeSelectedDayArray={(item) =>
                      this.setState({ selectedDayArray: item })
                    }
                    listName={listSetName}
                    onChangeListName={(item) =>
                      this.setState({ listSetName: item })
                    }
                    yearListName={yearListSetName}
                    onChangeYearListName={(item) =>
                      this.setState({ yearListSetName: item })
                    }
                    selectedEndDate={selectedEndDateYear}
                    onChangeSelectedEndDate={(item) =>
                      this.setState({ selectedEndDateYear: item })
                    }
                    onFromDateChanged={() =>
                      this.setState({ isFromDate: false })
                    }
                    isFromDateChanged={this.state.isFromDate}
                    recurrenceTypeData={false}
                    selectedDay={this.state.selectedDay}
                  />
                ) : null}

                {this.bottomContainer()}
              </KeyboardAwareScrollView>
            </View>

            <Dropdown
              data={this.state.vehicleTypeOptions}
              title={Strings.addDR.vehicleType}
              value={this.state.pumpVehicleType}
              closeBtn={() => this.setState({ pumpVehicleModelVisible: false })}
              onPress={(item) => this.onPressPumpVehicleType(item)}
              visible={this.state.pumpVehicleModelVisible}
              onbackPress={() =>
                this.setState({ pumpVehicleModelVisible: false })
              }
              container={drStyles.equipmentContainer}
              customMainContainer={drStyles.renderEquipStyle}
              equipTextContainer={drStyles.equipTextStyle}
            />

            <Dropdown
              data={this.state.vehicleTypeOptions}
              title={"Vehicle Type"}
              value={this.state.vehicleType}
              closeBtn={() => this.setState({ vehicleModelVisible: false })}
              onPress={(item) => this.onPressVehicleType(item)}
              visible={this.state.vehicleModelVisible}
              onbackPress={() => this.setState({ vehicleModelVisible: false })}
              container={drStyles.equipmentContainer}
              customMainContainer={drStyles.renderEquipStyle}
              equipTextContainer={drStyles.equipTextStyle}
            />

            <Dropdown
              data={this.state.gateList}
              title={Strings.placeholders.gate}
              value={this.state.selectedGate}
              closeBtn={() => this.setState({ gateModalVisible: false })}
              onPress={(item) => this.onPressGateType(item)}
              visible={this.state.gateModalVisible}
              onbackPress={() => this.setState({ gateModalVisible: false })}
              container={drStyles.equipmentContainer}
              customMainContainer={drStyles.renderEquipStyle}
              equipTextContainer={drStyles.equipTextStyle}
            />

            {/* {this.renderDateModals()} */}
            {/* truck hours modal */}
            <Modal
              isVisible={this.state.hoursModal || this.state.minsModal}
              onBackdropPress={() => this.onCloseModals()}
              style={{
                paddingTop: 45,
                margin: 0,
                justifyContent: "flex-end",
                // backgroundColor: '#fff',
              }}
            >
              <HoursModal
                data={this.state.hoursModal ? hoursData : minutesData}
                isHours={this.state.hoursModal ? true : false}
                Clear={() => {
                  this.onCloseModals();
                  this.setState({
                    completePlacementHrs: "",
                    completePlacementMins: "",
                  });
                }}
                onSelected={this.onSelectPicker}
              />
            </Modal>
            {/* Calender iOS for concrete date*/}

            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showDateModal}
                onBackdropPress={() => {
                  this.setState({
                    showDateModal: false,
                  });
                }}
                animationInTiming={500}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  //TODO FOR LATER
                  // minimumDate={this.state.editConcrete?new Date(1950, 0, 1):this.state.minimumDate}
                  value={this.state.calSelectedDate}
                  //value={}
                  style={{
                    backgroundColor: Colors.white,
                    width: "100%",
                  }}
                  display={"inline"}
                  themeVariant="light"
                  accentColor={Colors.themeColor}
                  //themeVariant="light"
                  // mode={mode}
                  onChange={(time, date) => {
                    this.onchangeDate(time, date);
                  }}
                />
                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={styles.datePickerOkContainer}
                    onPress={() => {
                      this.onDatePickerDonePressed();
                    }}
                  >
                    <Text style={styles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Calender Android for concrete date*/}

            {Platform.OS == "android" && this.state.showDateModal && (
              <DateTimePicker
                testID="datePicker"
                //TODO FOR LATER
                // minimumDate={this.state.editConcrete?new Date(1950, 0, 1):this.state.minimumDate}
                value={this.state.calSelectedDate}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                //mode={mode}
                onChange={(time, date) => {
                  this.onchangeDate(time, date);
                }}
              />
            )}

            {/* Calender iOS for pump date*/}

            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showPumpDateModal}
                onBackdropPress={() => {
                  this.setState({
                    showPumpDateModal: false,
                  });
                }}
                animationInTiming={500}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  minimumDate={
                    this.state.editConcrete
                      ? new Date(1950, 0, 1)
                      : this.state.minimumDate
                  }
                  value={this.state.pumpSelectedDate}
                  //value={}
                  style={{
                    backgroundColor: Colors.white,
                    width: "100%",
                  }}
                  display={"inline"}
                  themeVariant="light"
                  accentColor={Colors.themeColor}
                  // mode={mode}
                  onChange={(time, date) => {
                    this.onchangePumpDate(time, date);
                  }}
                />
                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={styles.datePickerOkContainer}
                    onPress={() => {
                      this.onDatePickerDonePressed();
                    }}
                  >
                    <Text style={styles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Calender Android for pump date*/}

            {Platform.OS == "android" && this.state.showPumpDateModal && (
              <DateTimePicker
                testID="datePicker"
                minimumDate={
                  this.state.editConcrete
                    ? new Date(1950, 0, 1)
                    : this.state.minimumDate
                }
                value={this.state.pumpSelectedDate}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                //mode={mode}
                onChange={(time, date) => {
                  this.onchangePumpDate(time, date);
                }}
              />
            )}

            {/* Calender Android */}

            {/* {Platform.OS == "android" && this.state.showDateModal && (
          <DateTimePicker
            testID="datePicker"
            // timeZoneOffsetInMinutes={0}
            // minuteInterval={interval}
            minimumDate={this.state.minimumDate}
            value={this.state.calSelectedDate}
            style={{
              backgroundColor: "#fff",
            }}
            //mode={mode}
            onChange={(time, date) => {
              this.onchngeDate(time, date);
            }}
          />
        )} */}

            {/* Time picker iOS - start time */}
            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showStartTimeModal}
                onBackdropPress={() => {
                  this.setState({
                    showStartTimeModal: false,
                  });
                }}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  mode={"time"}
                  value={this.state.calSelectedStartTime}
                  textColor="black"
                  style={{
                    backgroundColor: "#fff",
                    width: "100%",
                  }}
                  display={"spinner"}
                  onChange={(time, date) => {
                    this.onChangeStart(time, date);
                  }}
                />

                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={CommonStyles.datePickerOkContainer}
                    onPress={() => this.onStartEndTimeDonePressed()}
                  >
                    <Text style={CommonStyles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Timepicker android - start Time */}
            {Platform.OS == "android" && this.state.showStartTimeModal && (
              <DateTimePicker
                testID="datePicker"
                mode={"time"}
                value={this.state.calSelectedStartTime}
                //TODO FOR LATER
                // minimumDate={this.state.minimumDate}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                onChange={(time, date) => {
                  this.onChangeStart(time, date);
                }}
              />
            )}

            {/* Timepicker - ios End Time */}
            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showEndTimeModal}
                onBackdropPress={() => {
                  this.setState({
                    showEndTimeModal: false,
                  });
                }}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  mode={"time"}
                  //TODO FOR LATER
                  // minimumDate={this.state.minimumDate}
                  value={this.state.calSelectedEndTime}
                  textColor="black"
                  style={{
                    backgroundColor: "#fff",
                    width: "100%",
                  }}
                  display={"spinner"}
                  onChange={(time, date) => {
                    this.onChangeEndTime(time, date);
                  }}
                />
                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={CommonStyles.datePickerOkContainer}
                    onPress={() => this.onStartEndTimeDonePressed()}
                  >
                    <Text style={CommonStyles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Timepicker android - End Time */}
            {Platform.OS == "android" && this.state.showEndTimeModal && (
              <DateTimePicker
                testID="datePicker"
                mode={"time"}
                value={this.state.calSelectedEndTime}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                onChange={(time, date) => {
                  this.onChangeEndTime(time, date);
                }}
              />
            )}
            {/* Time picker iOS - Pump start time */}
            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showPumpStartTimeModal}
                onBackdropPress={() => {
                  this.setState({
                    showPumpStartTimeModal: false,
                  });
                }}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  mode={"time"}
                  value={this.state.calSelectedPumpStartTime}
                  textColor="black"
                  style={{
                    backgroundColor: "#fff",
                    width: "100%",
                  }}
                  display={"spinner"}
                  onChange={(time, date) => {
                    this.onChangePumpStart(time, date);
                  }}
                />

                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={CommonStyles.datePickerOkContainer}
                    onPress={() => this.onPumpStartEndTimeDonePressed()}
                  >
                    <Text style={CommonStyles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Timepicker android - Pump start Time */}
            {Platform.OS == "android" && this.state.showPumpStartTimeModal && (
              <DateTimePicker
                testID="datePicker"
                mode={"time"}
                value={this.state.calSelectedPumpStartTime}
                minimumDate={this.state.minimumDate}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                onChange={(time, date) => {
                  this.onChangePumpStart(time, date);
                }}
              />
            )}

            {/* Timepicker - ios Pump End Time */}
            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showPumpEndTimeModal}
                onBackdropPress={() => {
                  this.setState({
                    showPumpEndTimeModal: false,
                  });
                }}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  mode={"time"}
                  value={this.state.calSelectedPumpEndTime}
                  textColor="black"
                  style={{
                    backgroundColor: "#fff",
                    width: "100%",
                  }}
                  display={"spinner"}
                  onChange={(time, date) => {
                    this.onChangePumpEndTime(time, date);
                  }}
                />
                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={CommonStyles.datePickerOkContainer}
                    onPress={() => this.onPumpStartEndTimeDonePressed()}
                  >
                    <Text style={CommonStyles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Timepicker android - End Time */}
            {Platform.OS == "android" && this.state.showPumpEndTimeModal && (
              <DateTimePicker
                testID="datePicker"
                mode={"time"}
                // minimumDate={this.state.minimumDate}
                value={this.state.calSelectedPumpEndTime}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                onChange={(time, date) => {
                  this.onChangePumpEndTime(time, date);
                }}
              />
            )}
            <Modal
              isVisible={this.state.showInfo}
              onBackdropPress={() => {
                this.setState({ showInfo: false });
              }}
              style={{
                paddingTop: 45,
                paddingBottom: 30,
                margin: 0,
                backgroundColor: "rgba(0, 0, 0, 0.3)",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <View
                style={{
                  backgroundColor: "white",
                  width: wp("90%"),
                  borderRadius: 10,
                }}
              >
                <Text
                  style={{
                    color: Colors.textGray,
                    fontSize: wp("4%"),
                    fontFamily: Fonts.montserratMedium,
                    marginVertical: 15,
                    marginHorizontal: 10,
                  }}
                >
                  {Strings.addConcrete.info}
                </Text>
              </View>
            </Modal>

            {/* {this.renderBackupDateModal()} */}
            {this.state.showLoader && (
              <AppLoader viewRef={this.state.showLoader} />
            )}

            {this.state.showToaster && (
              <Toastpopup
                backPress={() => this.setState({ showToaster: false })}
                toastMessage={this.state.toastMessage}
                type={this.state.toastType}
                container={{ marginBottom: hp("12%") }}
              />
            )}
            {this.state.showCancel && (
              <DeletePop
                title={Strings.popup.cancel}
                desc={Strings.popup.cancel}
                acceptTap={() => {
                  this.setState({ showCancel: false });
                  this.props.cameBack(false);
                  this.props.navigation.goBack();
                }}
                container={{ bottom: 0 }}
                declineTap={() => {
                  this.setState({ showCancel: false });
                }}
              />
            )}
          </SafeAreaView>
        )}
      </>
    );
  }
}

const styles = StyleSheet.create({
  root: {
    justifyContent: "center",
    borderRadius: 50,
    backgroundColor: "#e0e0e0",
    paddingHorizontal: 10,
    paddingVertical: 4,
    height: 28,
    marginBottom: 4,
    marginRight: 4,
  },
  container: {
    flexDirection: "row",
    overflow: "hidden",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  text: {
    color: "rgba(0, 0, 0, 0.87)",
  },
  iconWrapper: {
    borderRadius: 50,
    backgroundColor: "#a6a6a6",
    height: 16,
    width: 16,
    overflow: "hidden",
    marginLeft: 4,
    justifyContent: "center",
    alignItems: "center",
  },
  icon: {
    textAlign: "center",
    color: Colors.borderGray,
  },
  iconIOS: {
    fontSize: 14,
  },
  iconAndroid: {
    fontSize: 13,
    lineHeight: 15,
  },
  datePickerOkLabel: {
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 6,
    paddingBottom: 6,
    fontFamily: Fonts.montserratBold,
  },
  datePickerOkContainer: {
    width: "100%",
    alignItems: "center",
    backgroundColor: Colors.white,
    paddingBottom: hp("2%"),
  },
  timeZoneText: {
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    marginLeft: 20,
  },
  mandatory: {
    color: Colors.themeColor,
  },
  dropDownPlaceHolderStyle: {
    color: Colors.placeholder,
    fontSize: 14,
    marginLeft: -5,
  },
  dropDownPickerStyle: {
    backgroundColor: Colors.white,
    width: wp("90%"),
    borderColor: Colors.borderTransparent,
    borderBottomColor: Colors.placeholder,
    alignSelf: "center",
    height: hp("5%"),
  },
  dropDownContainer: {
    height: hp("6%"),
    marginTop: 6,
    marginBottom: 20,
  },
  dropDownItemStyle: {
    justifyContent: "flex-start",
  },
  arrowDownStyle: {
    alignSelf: "flex-end",
  },
  dropDownListStyle: {
    backgroundColor: Colors.white,
    width: "90%",
    alignSelf: "center",
  },
  selectedDropDown: {
    color: Colors.black,
    marginLeft: -5,
  },
  textStyleEdited: {
    color: Colors.inlineGrey,
    fontSize: 14,
  },
  textStyle: {
    color: Colors.black,
    fontSize: 14,
  },
  timeZoneValue: {
    color: Colors.black,
    fontSize: 14,
    marginLeft: 25,
    paddingTop: 10,
  },
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedConcrete,
    userDetails,
    deliveryDetailsId,
    inspectionDetailsId,
    updateList,
    responsiblePersonData,
    concreteRequestId,
    selectedConcreteLocations,
    selectedConcretePlacements,
    selectedConcreteMixDesigns,
    selectedConcretePumpSizes,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedConcrete,
    userDetails,
    deliveryDetailsId,
    inspectionDetailsId,
    updateList,
    responsiblePersonData,
    concreteRequestId,
    selectedConcreteLocations,
    selectedConcretePlacements,
    selectedConcreteMixDesigns,
    selectedConcretePumpSizes,
  };
};
export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    cameBack,
    editConcreteRequest,
    updateList,
    refreshDashboard,
    refreshDeliveryList,
    refreshCalendar,
    selectedConcreteLocationsData,
    selectedConcreteMixDesignsData,
    selectedConcretePlacementsData,
    selectedConcretePumpSizesData,
  }),
  withBackHandler,
)(AddConcrete);

const drStyles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("78%"),
    fontSize: wp("5.6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratSemiBold,
  },
  memberContainer: {
    width: wp("90%"),
    height: hp("10%"),
    alignSelf: "center",
    marginTop: hp("1%"),
    justifyContent: "center",
    marginBottom: hp("1%"),
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
    marginLeft: 4,
  },
  idText: {
    width: wp("90%"),
    height: hp("5%"),
    backgroundColor: "#EFEFEF",
    marginTop: wp("1%"),
    padding: hp("1%"),
    paddingLeft: 15,
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("4%"),
  },
  escortContainer: {
    width: "90%",
    alignSelf: "center",
    marginTop: hp("3%"),
    justifyContent: "space-between",
    flexDirection: "row",
  },
  escortText: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    marginLeft: 4,
  },
  bottomContainer: {
    width: wp("90%"),
    flexDirection: "row",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: hp("4%"),
    marginTop: hp("8%"),
  },
  cancel: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonGray,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  root: {
    justifyContent: "center",
    borderRadius: 50,
    backgroundColor: "#e0e0e0",
    paddingHorizontal: 10,
    paddingVertical: 4,
    height: 28,
    marginBottom: 4,
    marginRight: 4,
  },
  container: {
    flexDirection: "row",
    overflow: "hidden",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  text: {
    color: "rgba(0, 0, 0, 0.87)",
  },
  iconWrapper: {
    borderRadius: 50,
    backgroundColor: "#a6a6a6",
    height: 16,
    width: 16,
    overflow: "hidden",
    marginLeft: 4,
    justifyContent: "center",
    alignItems: "center",
  },
  icon: {
    textAlign: "center",
    color: Colors.borderGray,
  },
  iconIOS: {
    fontSize: 14,
  },
  iconAndroid: {
    fontSize: 13,
    lineHeight: 15,
  },
  headerText: {
    // width: Dimensions.scaleWidth(10),
    flex: 1,
    height: 30,
    textAlign: "center",
  },
  submitButton: {
    flex: 1,
    backgroundColor: Colors.pendingEventColor,
    padding: 10,
    borderRadius: 25,
    // marginLeft: 10,
    alignItems: "center",
  },
  submitButtonText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: 16,
    paddingLeft: 70,
    paddingRight: 70,
  },
  renderEquipStyle: {
    marginBottom: 10,
  },
  equipTextStyle: {
    width: "100%",
    fontSize: 16,
    paddingTop: 2,
  },
});
