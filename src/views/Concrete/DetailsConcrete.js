import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  TextInput,
  Platform,
  KeyboardAvoidingView,

  Alert,
  Linking,
} from "react-native";

import {
  cameBack,
  showDeliverdetailsid,
  // showInspectiondetailsid,
  editINS,
  editData,
  tappedNotificationDetails,
  refreshPage,
  refreshDashboard,
  refreshDeliveryList,
  showCraneRequestId,
  editCraneRequest,
  refreshCalendar,
  editConcreteRequest,
  showConcreteRequestId,
  selectedConcreteLocationsData,
  selectedConcreteMixDesignsData,
  selectedConcretePlacementsData,
  selectedConcretePumpSizesData,
  clickAdd,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import {
  AppVie<PERSON>,
  AppLoader,
  Toastpopup,
  Dropdown,
  DeleteP<PERSON>,
  TextField,
} from "../../components";
import ConcreteDropDown from "../../components/dropdown/ConcreteDropDown";
import { launchImageLibrary, launchCamera } from "react-native-image-picker";
import AttachDropdown from "../../components/dropdown/attachDropdown";
import { Strings, Fonts, Images, Colors, isEmpty, editSeriesOption1, editSeriesOption2 } from "../../common";
import { TouchableWithoutFeedback } from "react-native-gesture-handler";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { trackEvent, trackScreen } from "../../Google Analytics/GoogleAnalytics";
import { TabView, TabBar } from 'react-native-tab-view';
import { Dimensions } from 'react-native';
import moment from "moment";

import DocumentPicker from "react-native-document-picker";

import ReactNativeBlobUtil from 'react-native-blob-util'

import Share from "react-native-share";
import { Avatar } from 'react-native-paper';
import ResDropdown from "../../components/dropdown/ResDropdown";
import { PERMISSIONS } from 'react-native-permissions';

import {
  GET_PROJECT_ROLE,
  GET_HISTORY_CONCRETE,
  GET_COMMENT_CONCRETE,
  CREATE_COMMENT_CONCRETE,
  ADD_ATTACHEMENT_CONCRETE,
  GET_ATTACHMENT_CONCRETE,
  REMOVE_ATTACHEMENT_CONCRETE,
  ADD_VOID_CONCRETE,
  GET_SINGLE_CONCRETE,
  UPDATE_STATUS_CONCRETE,
  LIST_ALL_MEMBER,
} from "../../api/Constants";
import {
  getDeliveryDetails,
  getHistoryDetails,
  getAttachmentDetails,
  getCommentDetails,
  createComment,
  updateStatus,
  addAttachment,
  removeAttachement,
  getprojectRole,
  addVoidConcrete,
  getAllMemberList,
} from "../../api/Api";

let HEADERDROPDOWNOPTIONS = [
  { id: "Save", image: Images.save, name: "Save" },
  { id: "Edit", image: Images.edit_blue, name: "Edit" },
  { id: "Void", image: Images.crossred, name: "Void" },
];

let HEADERDROPDOWNOPTIONSSC = [
  { id: "Edit", image: Images.edit_blue, name: "Edit" },
  { id: "Void", image: Images.crossred, name: "Void" },
];

import FileViewer from "react-native-file-viewer";
import RNFS from "react-native-fs";
import { mixPanelTrackEvent } from "../../MixPanel/MixPanel";
import NetInfo from '@react-native-community/netinfo';
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";
import NoInternet from "../../components/NoInternet/noInternet";
import { checkAndRequestPermission } from "../../utils/PermissionUtils";

var commentListHeight = 0.0;
var commentHeight = 0.0;
class DetailsConcrete extends Component {
  constructor(props) {
    super(props);

    this.state = {
      selectstatus: "",
      statusModal: false,
      statuslist: [
        { name: "Approved" },
        { name: "Declined" },
        { name: "Delivered" },
      ],
      gcStatusList: [{ name: "Completed" }],
      attachmentList: [],
      totalCount: 0,
      refreshing: false,
      lastId: 0,
      showToaster: false,
      itemdescription: "",
      historylist: [],
      commentList: [],
      newValue: "",
      height: 40,
      commenttext: "",
      concreteRequestId: this.props.concreteDetailsRequestID,
      //  lastId: 0,
      updatestatus: "",
      attachModel: false,
      addattachmentlist: [],
      showDelete: false,
      bottom: 10,
      showDropDown: false,
      gcStatusModal: false,
      showSave: false,
      projectRoleId: this.props.projectRoleId,
      projectId: this.props.projectDetails.id,
      notificationDetails: [],
      downloadoption: [{ name: "Download" }, { name: "Open" }],
      downloadModal: false,
      downloaditem: {},
      showDeliveredPop: false,
      deliveryCreatedRoleId: 0,
      responsiblePerson: [],
      onlyId: 0,
      deliveryItem: [],
      isIosDropDown: false,
      iosDropDown: [
        { name: "Camera" },
        { name: "Photos" },
        { name: "Document" },
      ],
      concreteID: 0,
      date: '',
      time: '',
      concreteSupplier: "",
      mixDesign: "",
      orderNumber: '',
      slump: '',
      truckSpacing: '',
      concreteRequestStatus: '',
      quantityOrdered: '',
      primerForPump: '',
      concreteConfirmed: '',
      pumpSize: '',
      pumpLocation: '',
      pumpOrdered: '',
      pumpShowUpTime: '',
      pumpConfirmed: '',
      hoursToCompletePlacement: '',
      totalCubicYardPlaced: '',
      locationDetails: [],
      location: '',
      placement: [],
      isComplete: false,
      wholeData: [],
      isCompleteDropdown: false,
      notes: '',
      mixpanelParam: {
        ProjectName: this.props.projectDetails.projectName,
        CompanyName: this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName: this.props.userDetails.firstName,
      },
      isCompletedModal: false,
      controlledByList: [],
      renderRespId: "",
      buttonValueBtn: '',
      completePlacementBtn: '',
      minsCompleteBtn: '',
      totalBtn: '',
      id: 0,
      isAccess: true,
      isNetworkCheck: false,
      isEditAccess: false,
      editDropDown: [],
      recurrenceType: '',
      isResponsiblePersonCount: false,
      responsiblePersonCount: 0,
      isModal: false,
      showPopUp: false,
      showText: '',
      index: 0,
      routes: [
        { key: 'details', title: 'Details' },
        { key: 'attachments', title: 'Attachments' },
        { key: 'comments', title: 'Comments' },
        { key: 'history', title: 'History' },
      ],
    };
    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;
  }
  async componentDidMount() {
    if (Platform.OS === 'ios') {
      this.networkCheck();
    } else {
      if (this.props.deliveryDetailsId) {
        this.setState({
          DeliveryRequestId: this.props.deliveryDetailsId.id,
          isDelivery: this.props.deliveryDetailsId.isDelivery,
          projectRoleId: this.props.projectRoleId,
          showLoader: true,
        });
      } else if (this.props.inspectionDetailsId) {
        this.setState({
          InspectionRequestId: this.props.inspectionDetailsId.id,
          isInspection: this.props.inspectionDetailsId.isInspection,
          projectRoleId: this.props.projectRoleId,
          showLoader: true,
        });
      }

      this.page_number = 1;

      if (this.props.notificationDetails.ProjectId) {
        this.setState(
          {
            notificationDetails: this.props.notificationDetails,
            projectId: this.props.notificationDetails.ProjectId,
          },
          async () => {
            this.props.tappedNotificationDetails({});
            await this.getProjectRole(this.state.notificationDetails);
          }
        );
      } else {
        this.setState({
          projectId: this.props.projectDetails.id,
        }, async () => {
          await this.getProjectRole({ ProjectId: this.state.projectId });
        });
      }

      //DO FOR LATER
      // await this.historyDetails();
      // await this.attachmentDetails();
      // await this.commentDetails();
      await this.getControlledByList();
    }
  }
  networkCheck = () => {
    NetInfo.addEventListener(async state => {
      if (!state.isConnected && !state.isInternetReachable) {
        this.setState({ isNetworkCheck: true })
      } else {
        this.setState({ isNetworkCheck: false })
        this.setState({
          DeliveryRequestId: this.props.deliveryDetailsId.id,
          isDelivery: this.props.deliveryDetailsId.isDelivery,
          projectRoleId: this.props.projectRoleId,
          showLoader: true,
        });

        this.page_number = 1;

        if (this.props.notificationDetails.ProjectId) {
          this.setState(
            {
              notificationDetails: this.props.notificationDetails,
              projectId: this.props.notificationDetails.ProjectId,
            },
            async () => {
              this.props.tappedNotificationDetails({});
              await this.getProjectRole(this.state.notificationDetails);
            }
          );
        } else {
          this.setState({
            projectId: this.props.projectDetails.id,
          }, async () => {
            await this.getProjectRole({ ProjectId: this.state.projectId });
          });

        }
        //DO FOR LATER
        // await this.historyDetails();
        // await this.attachmentDetails();
        // await this.commentDetails();
        await this.getControlledByList();
      }
    })
  }

  getControlledByList = () => {
    getAllMemberList(
      LIST_ALL_MEMBER +
      this.props.projectDetails.id +
      "/" +
      this.props.projectDetails.ParentCompany.id,
      {},
      () => { },
      (response) => {
        if (response.status) {
          if (response.data.message == Strings.popup.getMemEqui) {
            this.storeContactPerson(response.data.data);
          } else {
            this.setState({
              memberlist: [],
            });
          }
        } else {
          this.showError("error", Strings.errors.failed);
        }
      }
    );
  };

  storeContactPerson = (data) => {
    let memberList = [];

    for (let item of data) {
      if (item.User.firstName != null) {
        memberList.push({
          label: item.User.firstName + " " + item.User.lastName + " (" + item.User.email + ")",
          value: item.User.email,
          id: item.id,
          name: item.User.email,
        });
      } else {
        memberList.push({
          label: item.User.email,
          value: item.User.email,
          id: item.id,
        });
      }
    }
    this.setState({ controlledByList: memberList });
  };

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  getProjectRole = (data) => {
    if (data) {
      getprojectRole(
        GET_PROJECT_ROLE +
        data.ProjectId +
        `/${this.props.projectDetails.ParentCompany.id}`,
        {},
        () => null,
        (response) => {
          if (response.data) {
            this.setState(
              {
                projectRoleId: response.data.data.RoleId,
                id: response.data.data.id,
              },
              () => {
                this.concreteDetails();
              }
            );
          }
        }
      );
    }
  };

  concreteDetails = () => {

    this.setState({ showLoader: true });
    let url = `${GET_SINGLE_CONCRETE}/${this.state.concreteRequestId}/${this.state.projectId}/${this.props.projectDetails.ParentCompany.id}`;
    getDeliveryDetails(
      url,
      {},
      () => null,
      (resp) => {
        if (__DEV__) {
        }

        if (resp.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (resp.status) {
          if (resp.status == 200) {
            this.setState({ wholeData: resp?.data?.data })
            // process delivery details
            this.setState({
              recurrenceType: resp?.data?.data?.recurrence != null ? resp?.data?.data?.recurrence?.recurrence : Strings.calendarSettings.doseNotRepeat
            })
            this.processDeliveryDetailsResponse(
              resp?.data?.data
            );
          }
          else if (resp.status == 400) {
            this.setState({
              showLoader: false,
            });
            this.showErrorMessage("error", resp?.data?.message);
          } else {
            this.setState({
              showLoader: false,
            });
            this.showErrorMessage("error", resp.toString());
          }
        } else {
          this.setState({
            showLoader: false,
          });
          this.showErrorMessage("error", resp.toString());
        }

      })

  }
  async getResponsibilityCompany(companyDetails) {
    return companyDetails.map((e) => e.Company.companyName).join(",");
  }
  async getResponsiblePerson(responsiblePersonDetails) {
    let person = [];
    for (let sampleData of responsiblePersonDetails) {
      if (sampleData.Member.User.firstName == null) {
        let nullData = "uu";
        let nullName = `${sampleData.Member.User.email}`;
        let nullId = sampleData.Member.id;
        person.push({ "label": nullData, "name": nullName, 'email': nullData, 'id': nullId })
      } else {
        let data = `${sampleData.Member.User.firstName.charAt(0)}${sampleData.Member.User.lastName.charAt(0)}`;
        let name = `${sampleData.Member.User.firstName} ${sampleData.Member.User.lastName}`;
        let email = sampleData.Member.User.email;
        let phoneNumber = sampleData.Member.User.phoneCode != null ? sampleData.Member.User.phoneCode + sampleData.Member.User.phoneNumber : null
        let respoID = sampleData.Member.id
        person.push({ "label": data, "name": name, 'email': email, 'phoneNumber': phoneNumber, 'id': respoID })
      }

    }
    let count = responsiblePersonDetails.length - 3;
    this.setState({
      isResponsiblePersonCount: count > 0 ? true : false,
      responsiblePersonCount: count
    })
    return person;
  }

  async getResponsiblePersonID(responsiblePersonDetails) {
    let personID = [];
    if (responsiblePersonDetails != null) {
      for (let [index, item] of responsiblePersonDetails.entries()) {
        if (item.Member.User != null) {
          if (index == responsiblePersonDetails.length - 1) {

            let respoID =
              item.Member.id;
            personID.push({ 'id': respoID })
          }
        }
      }
    }
    return personID;
  }


  concreteConfirmed = (date) => {
    return 'Confirmed on ' + moment(date).format("MMM DD, YYYY, hh:mm A");
  }
  pumpOredered = (date) => {
    return 'Ordered on ' + moment(date).format("MM/DD/YYYY");
  }
  processDeliveryDetailsResponse = async (data) => {
    let times = moment.parseZone(data.concretePlacementStart).format('hh:mm A') + ' - ' + moment.parseZone(data.concretePlacementEnd).format('hh:mm A');
    //let truckspacing=(data.truckSpacingHours!=null?data.truckSpacingHours:'')+' Hours '+(data.truckSpacingMinutes!=null?data.truckSpacingMinutes:'')+' Minutes';
    let hours = (data.hoursToCompletePlacement != null && data.hoursToCompletePlacement != "") ? ((data.hoursToCompletePlacement != null ? data.hoursToCompletePlacement : '') + ' Hours ' + (data.minutesToCompletePlacement != null ? data.minutesToCompletePlacement : '') + ' Minutes') : '---';
    let loc = data.locationDetails.map((e) => e.ConcreteLocation.location).join(", ");
    // let place=data.placementDetails.map((e) => e.ConcretePlacement.placement).join(", ");
    let mix = data.mixDesignDetails.map((e) => e.ConcreteMixDesign.mixDesign).join(", ");
    let pumpsize = data.pumpSizeDetails.map((e) => e.ConcretePumpSize.pumpSize).join(", ");
    let pumpShowUpTime = data.pumpWorkStart != null ? moment(data.pumpWorkStart).format('hh:mm A') + ' - ' + moment(data.pumpWorkEnd).format('hh:mm A') : '---';

    this.setState({
      itemdescription: (data.description != "" || data.description != null) ? data.description : "",
      concreteID: data.id,
      vehicleType: data?.vehicleType || '',
      pumpVehicleType: data?.vehicleTypePump || '',
      originationAddress: data?.OriginationAddress || '',
      pumpOriginationAddress: data?.OriginationAddressPump || '',
      date: moment.parseZone(data.concretePlacementStart).format('MM/DD/YY'),
      time: times,
      concreteSupplier: data.concreteSupplierDetails.length > 0 ? await this.getResponsibilityCompany(
        data.concreteSupplierDetails
      ) : '---',
      responsiblePerson: data.memberDetails.length > 0 ? await this.getResponsiblePerson(data.memberDetails) : '---',
      renderRespId: data.memberDetails.length > 0 ? await this.getResponsiblePersonID(data.memberDetails) : '---',
      mixDesign: data.mixDesignDetails.length > 0 ? mix : '---',
      orderNumber: data.concreteOrderNumber != "" && data.concreteOrderNumber != null ? data.concreteOrderNumber : '---',
      slump: data.slump != '' && data.slump != null ? data.slump : '---',
      truckSpacing: data.truckSpacingHours != '' && data.truckSpacingHours != null ? data.truckSpacingHours : "---",
      concreteRequestStatus: data.status,
      quantityOrdered: data.concreteQuantityOrdered != '' && data.concreteQuantityOrdered != null ? data.concreteQuantityOrdered : '---',
      primerForPump: data.primerForPump != '' && data.primerForPump != null ? data.primerForPump : '---',
      concreteConfirmed: data.isConcreteConfirmed ? this.concreteConfirmed(data.concreteConfirmedOn) : "---",
      pumpSize: data.pumpSizeDetails.length > 0 ? pumpsize : '---',
      pumpOrdered: data.pumpOrderedDate != null ? this.pumpOredered(data.pumpOrderedDate) : '---',
      pumpLocation: data.pumpLocation != null ? (data.pumpLocation == '' ? '---' : data.pumpLocation) : '---',
      pumpShowUpTime: pumpShowUpTime != null ? pumpShowUpTime : '---',
      pumpConfirmed: data.isPumpConfirmed ? this.concreteConfirmed(data.pumpConfirmedOn) : '---',
      hoursToCompletePlacement: hours != null ? hours : '---',
      totalCubicYardPlaced: (data.cubicYardsTotal != null && data.cubicYardsTotal != '') ? data.cubicYardsTotal : '---',
      locationDetails: loc != '' ? loc : '---',
      location: data.location != null ? data.location?.locationPath : '---',
      notes: (data.notes != null && data.notes != '') ? data.notes : '---',
      selectstatus: data.status,
      updatestatus: data.status,
      showSave: false,
      deliveryCreatedRoleId: data.createdUserDetails.RoleId,
      repMemberIds: data.memberDetails,
    }
    )

    if (
      (data.createdUserDetails.RoleId == 2 &&
        this.props.projectRoleId == 4) ||
      (data.createdUserDetails.RoleId == 3 && this.props.projectRoleId == 4)
    ) {
      this.setState({ isAccess: false })
    }

    if (!moment(data.concretePlacementStart).isAfter(moment())) {
      this.setState({
        editDropDown: editSeriesOption2
      })

    } else {
      this.setState({
        editDropDown: editSeriesOption1
      })
    }

    if (this.props.projectRoleId === 4 && data.memberDetails != undefined) {
      const index = data.memberDetails.findIndex((i) => (i.Member.id === this.state.id));
      if (index !== -1) {
        this.setState({ isAccess: true })
      } else {
        this.setState({ isAccess: false })
      }
    }

    if (data.status == "Approved") {
      this.setState({ statuslist: [{ name: "Completed" }] })
    }
    else if (data.status == "Tentative") {
      this.setState({
        statuslist: [{ name: "Approved" }, { name: "Declined" }],
      });
    }
    if (data.status == "Completed") {
      this.setState({
        showDropDown: false,
        showGCDropDown: false,
      });
    } else {
      if (this.state.projectRoleId == 2) {
        this.setState({
          showDropDown: true,
        });
      } else if (this.state.projectRoleId == 3) {
        this.setState({
          showGCDropDown: true,
        });
      } else if (data.createdUserDetails.RoleId == 4) {
        this.setState({ showGCDropDown: true });
      }
    }
    setTimeout(() => {
      this.setState({
        showLoader: false,
      });
    }, 1000);
  }
  attachmentDetails = () => {
    this.setState({
      showLoader: true,
    });
    let url = `${GET_ATTACHMENT_CONCRETE}/${this.state.concreteRequestId}/${this.props.projectDetails.ParentCompany.id}/${this.state.projectId}`;
    getAttachmentDetails(
      url,
      {},
      () => null,
      (attachmentDetailsresp) => {
        this.setState({
          showLoader: false,
        });
        if (attachmentDetailsresp.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (attachmentDetailsresp.status) {
          if ((attachmentDetailsresp.status == 200)) {
            let data = attachmentDetailsresp.data.data;
            this.setState({
              attachmentList: data,
            });
          } else if (attachmentDetailsresp.data.data.message) {
            this.showErrorMessage(
              "error",
              attachmentDetailsresp.data.data.message
            );
          } else {
            this.showErrorMessage("error", attachmentDetailsresp.data.message);
          }
        } else {
          this.showErrorMessage("error", attachmentDetailsresp.toString());
        }
      }
    );
  };
  commentDetails = () => {
    this.setState({
      showLoader: true,
    });
    let url = `${GET_COMMENT_CONCRETE}/${this.state.concreteRequestId}/${this.props.projectDetails.ParentCompany.id}/${this.state.projectId}`;

    getCommentDetails(
      url,
      {},
      () => null,
      (commentDetailsresp) => {
        this.setState({
          showLoader: false,
        });
        if (commentDetailsresp.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (commentDetailsresp.status) {
          if (commentDetailsresp.status == 200) {
            let data = this.state.commentList;

            if (this.page_number == 1) {
              if (commentDetailsresp.data.data.count != 0) {
                this.setState({
                  commentList: commentDetailsresp.data.data.rows,
                  totalCount: commentDetailsresp.data.data.count,
                });
              } else {
                this.setState({
                  showNoData: true,
                  commentList: [],
                });
              }
            } else {
              let data1 = commentDetailsresp.data.data.rows;
              this.setState({
                commentList: data.concat(data1),
                totalCount: commentDetailsresp.data.data.count,
                //    lastId: commentDetailsresp.data.lastId.id,
              });
            }
          } else if (commentDetailsresp.data.data.message) {
            this.showErrorMessage(
              "error",
              commentDetailsresp.data.data.message
            );
          } else {
            this.showErrorMessage("error", commentDetailsresp.data.message);
          }
        } else {
          this.showErrorMessage("error", commentDetailsresp.toString());
        }
      }
    );
  };
  nextCommentClick = () => {
    this.setState({ showLoader: true });
    let param = {
      comment: this.state.commenttext,
      ConcreteRequestId: this.state.concreteRequestId,
      ParentCompanyId: this.props.projectDetails.ParentCompany.id,
      ProjectId: this.state.projectId,
    };
    let url = CREATE_COMMENT_CONCRETE;
    createComment(
      url,
      param,
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.message) {
            if (response.status == 200 || response.status == 201) {
              this.setState({ commenttext: "" });
              this.showErrorMessage("Success", response.data.message);
              this.commentDetails();
              trackEvent('Comment_Added_Against_Concrete_Request')
              mixPanelTrackEvent('Comment Added Against Concrete Request', this.state.mixpanelParam)
            } else {
              this.showErrorMessage("error", response.data.message);
            }
          } else if (response.data.message.message) {
            this.showErrorMessage("error", response.data.message.message);
          } else {
            this.showErrorMessage("error", Strings.errors.failed);
          }
        } else {
          this.showErrorMessage("error", response.toString());
        }
      }
    );
  };

  historyDetails = () => {
    this.setState({
      showLoader: true,
    });
    let url = `${GET_HISTORY_CONCRETE}/${this.state.concreteRequestId}/${this.props.projectDetails.ParentCompany.id}/${this.props.projectDetails.id}`;
    getHistoryDetails(
      url,
      {},
      () => null,
      (historyDetailsresp) => {
        this.setState({
          showLoader: false,
        });
        if (historyDetailsresp.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (historyDetailsresp.status) {
          if (historyDetailsresp.status == 200) {
            let data = historyDetailsresp.data.data;
            let newData = [];
            if (data != []) {
              data.forEach((e) => {
                if (e.type != "comment") {
                  newData.push(e);
                }
              });
            }
            this.setState({
              historylist: newData,
            });
          } else if (historyDetailsresp.data.data.message) {
            this.showErrorMessage(
              "error",
              historyDetailsresp.data.data.message
            );
          } else {
            this.showErrorMessage("error", historyDetailsresp.data.message);
          }
        } else {
          this.showErrorMessage("error", historyDetailsresp.toString());
        }
      }
    );
  };
  updateData = (data) => {
    //TODO FOR LATER
    //this.getProjectRole(this.state.notificationDetails);
    this.concreteDetails()
  };

  cancelPopupAcceptTap = () => {
    this.setState({ isCompletedModal: false })
    this.statusUpdate(this.state.buttonValueBtn, this.state.completePlacementBtn, this.state.minsCompleteBtn, this.state.totalBtn)
  }

  cancelPopupDeclineTap = () => {
    this.setState({ isCompletedModal: false })
  }
  deletePopupAcceptTap = () => {
    this.setState({ showDelete: false });
    this.deleteattachement(this.state.selectedAttachmentId);
  };

  deletePopupDeclineTap = () => {
    this.setState({
      showDelete: false,
      selectedAttachmentId: null,
    });
  };
  onChangeTab = ({ i }) => {
    if (i == 1) {
      this.attachmentDetails();
    } else if (i == 2) {
      this.commentDetails();
    } else if (i == 3) {
      this.historyDetails();
    }
  };
  showErrorMessage = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
      },
      () => {
        setTimeout(() => {
          this.setState({ showToaster: false });
        }, 2000);
      }
    );
  };
  hideToast = () => {
    setTimeout(() => this.setState({ showToaster: false }), 2000);
  };
  renderResponsible = ({ item, index }) => {
    if (index < 3) {
      return (
        <Avatar.Text size={24} label={item.label} color="white" theme="grey" style={{ backgroundColor: 'grey', marginLeft: 5 }} />
      )
    }
  }


  renderDetails = () => {
    return (
      <View style={styles.detailView}>
        <Text style={styles.detailsHeadingStyle}>
          {Strings.deliverydetails.itemdescription}
        </Text>
        <Text numberOfLines={2} style={styles.detailsTextStyle}>
          {this.state.itemdescription}
        </Text>
        <View style={styles.detailContainerView}>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.concreteId}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
              {this.state.concreteRequestId}
            </Text>
          </View>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.placeholders.additional_location}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
              {this.state.locationDetails}
            </Text>
          </View>

        </View>
        <View style={styles.detailContainerView}>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.deliverydetails.responsiblePerson}
            </Text>
            <TouchableOpacity onPress={() => { this.setState({ isModal: true }) }}
              style={styles.responsibleContainer}>
              <FlatList
                data={this.state.responsiblePerson}
                renderItem={this.renderResponsible}
                horizontal={true}
                scrollEnabled={false}
                style={styles.responsibleFlatlistContainer}
              />
              {this.state.isResponsiblePersonCount &&
                <Avatar.Text size={24} label={`+${this.state.responsiblePersonCount}`} color="white" theme="grey" style={[styles.avatarContainer, { marginRight: Platform.OS == 'android' ? 20 : 25 }]} />
              }
            </TouchableOpacity>
          </View>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.date}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
              {this.state.date}
            </Text>
          </View>
          {/* <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.placement}
            </Text>
            <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
             {this.state.placement}
            </Text>
          </View> */}
        </View>

        <View style={styles.detailContainerView}>

          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.location}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
              {this.state.location}
            </Text>
          </View>

          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.time}
            </Text>
            <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
              {this.state.time}
            </Text>
          </View>
        </View>
        <View style={styles.detailsSubHeading}>
          <Text
            style={styles.headingText}
          >
            {Strings.concreteDetails.title}
          </Text>
          <View
            style={styles.lineStyle}
          />
        </View>

        <View style={styles.detailContainerView}>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.concreteSupplier}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
              {this.state.concreteSupplier}
            </Text>
          </View>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.mixDesign}
            </Text>
            <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
              {this.state.mixDesign}
            </Text>
          </View>
        </View>

        <View style={styles.detailContainerView}>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.orderNumber}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
              {this.state.orderNumber}
            </Text>
          </View>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.slump}
            </Text>
            <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
              {this.state.slump}
            </Text>
          </View>
        </View>

        <View style={styles.detailContainerView}>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.truckSpacing}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
              {this.state.truckSpacing}
            </Text>
          </View>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.quantityOrdered}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
              {this.state.quantityOrdered}
            </Text>
          </View>
          {/* <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.concreteRequestStatus}
            </Text>
            <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
              {this.state.concreteRequestStatus}
            </Text>
          </View> */}
        </View>


        <Text style={styles.detailsHeadingStyle}>
          {Strings.concreteDetails.primerForPump}
        </Text>
        <Text numberOfLines={0} style={styles.detailsTextStyle}>
          {this.state.primerForPump}
        </Text>

        {/* <View style={{ flexDirection: 'row', marginTop: 20, flexWrap: 'wrap' }}> */}
        <View style={{ flex: 1 }}>
          <Text style={styles.detailsHeadingStyle}>{Strings.addDR.originTitle}</Text>
          <Text numberOfLines={2} style={styles.detailsTextStyle}>
            {this.state?.originationAddress ? this.state.originationAddress : '---'}
          </Text>
        </View>
        <View style={{ flex: 1 }}>
          <Text style={styles.detailsHeadingStyle}>{Strings.addDR.vehicleType}</Text>
          <Text numberOfLines={2} style={styles.detailsTextStyle}>
            {this.state?.vehicleType ? this.state.vehicleType : '---'}
          </Text>
        </View>


        <Text style={styles.detailsHeadingStyle}>
          {Strings.concreteDetails.concreteConfirmed}
        </Text>
        <Text numberOfLines={2} style={styles.detailsTextStyle}>
          {this.state.concreteConfirmed}
        </Text>

        <View style={styles.detailsSubHeading}>
          <Text
            style={styles.headingText}
          >
            {Strings.concreteDetails.pumpDetails}
          </Text>
          <View
            style={styles.lineStyle}
          />
        </View>

        <View style={styles.detailContainerView}>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.pumpSize}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
              {this.state.pumpSize}
            </Text>
          </View>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.pumpOrdered}
            </Text>
            <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
              {this.state.pumpOrdered}
            </Text>
          </View>
        </View>

        <View style={styles.detailContainerView}>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.pumpLocation}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
              {this.state.pumpLocation}
            </Text>
          </View>
          <View style={styles.detailTextView}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.pumpShowUpTime}
            </Text>
            <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
              {this.state.pumpShowUpTime}
            </Text>
          </View>
        </View>

        <View style={{ flex: 1 }}>
          <Text style={styles.detailsHeadingStyle}>{Strings.addDR.originTitle}</Text>
          <Text numberOfLines={2} style={styles.detailsTextStyle}>
            {this.state?.pumpOriginationAddress ? this.state?.pumpOriginationAddress : '---'}
          </Text>
        </View>
        <View style={{ flex: 1 }}>
          <Text style={styles.detailsHeadingStyle}>{Strings.addDR.vehicleType}</Text>
          <Text numberOfLines={2} style={styles.detailsTextStyle}>
            {this.state?.pumpVehicleType ? this.state?.pumpVehicleType : '---'}
          </Text>
        </View>


        <Text style={styles.detailsHeadingRowStyle}>
          {Strings.concreteDetails.pumpConfirmed}
        </Text>
        <Text numberOfLines={2} style={styles.detailsTextStyle}>
          {this.state.pumpConfirmed}
        </Text>

        {this.state.concreteRequestStatus == 'Completed' && <>
          <View style={styles.detailsSubHeading}>
            <Text
              style={styles.headingText}
            >
              {Strings.concreteDetails.otherDetails}
            </Text>
            <View
              style={styles.lineStyle}
            />
          </View>

          <Text style={styles.detailsHeadingStyle}>
            {Strings.concreteDetails.hoursToCompletePlacement}
          </Text>
          <Text numberOfLines={2} style={styles.detailsTextStyle}>
            {this.state.hoursToCompletePlacement}
          </Text>
          <Text style={styles.detailsHeadingStyle}>
            {Strings.concreteDetails.totalCubicYardPlaced}
          </Text>
          <Text numberOfLines={2} style={[styles.detailsTextStyle, { marginBottom: 10 }]}>
            {this.state.totalCubicYardPlaced}
          </Text>
        </>}
        <Text style={styles.detailsHeadingRowStyle}>
          {Strings.concreteDetails.notes}
        </Text>
        <Text numberOfLines={2} style={[styles.detailsTextStyle, { marginBottom: (this.state.concreteRequestStatus == 'Approved') ? 10 : 50 }]}>
          {this.state.notes}
        </Text>

        <View style={{ marginBottom: 100 }}>
          {this.state.showDropDown == true &&
            (this.state.selectstatus == "Approved" ||
              this.state.selectstatus == "Tentative") && (
              <TextField
                attrName={Strings.placeholders.selectstatus}
                title={Strings.placeholders.selectstatus}
                value={this.state.updatestatus}
                updateMasterState={(key, value) => {
                  this.updateMasterState(key, value);
                }}
                mandatory={true}
                textInputStyles={styles.textInputStatus}
                textTitleStyles={styles.textTitleStatus}
                showButton={true}
                onPress={() => {
                  if (this.state.projectRoleId != 4) {
                    this.setState({ statusModal: true });
                  }
                }}
                imageSource={Images.downArr}
              //   placeholder={'Select'}
              />
            )}

          {this.state.showGCDropDown == true &&
            this.state.selectstatus == "Approved" && (
              <TextField
                attrName={Strings.placeholders.selectstatus}
                title={Strings.placeholders.selectstatus}
                value={this.state.updatestatus}
                updateMasterState={(key, value) => {
                  this.updateMasterState(key, value);
                }}
                mandatory={true}
                textInputStyles={styles.textInputStatus}
                textTitleStyles={styles.textTitleStatus}
                showButton={true}
                onPress={() => {
                  if (this.state.projectRoleId == 3) {
                    this.setState({ gcStatusModal: true });
                  } else {
                    if (this.state.deliveryCreatedRoleId == 4) {
                      this.setState({ gcStatusModal: true });
                    }
                  }
                }}
                imageSource={Images.downArr}
              />
            )}
        </View>
        {/* {this.state.concreteRequestStatus=='Approved'&&
        <View
        style={styles.completeContainer}
      >
        <TouchableOpacity
          onPress={() =>{
            
            this.setState({
              isComplete: !this.state.isComplete,
              isCompleteDropdown:true,
            })
          }
        }
          style={[styles.completeTouch,{ backgroundColor: this.state.isComplete
            ? 'grey'
            : Colors.white,}]}
        ></TouchableOpacity>
        <Text style={styles.statusText}>
         Completed
        </Text>
      </View>
        } */}

      </View>
    );
  };
  statusUpdateAPI = (param) => {
    updateStatus(
      UPDATE_STATUS_CONCRETE,
      param,
      () => null,
      (response) => {

        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.message) {
            if (
              response.status
            ) {
              this.showErrorMessage("success", response.data.message);
              this.setState({ showLoader: true });
              this.concreteDetails();
              this.props.refreshDashboard(true, "Details Update");
              this.props.refreshCalendar(true);
            }
            else {
              this.showErrorMessage("error", response.data.message);
            }
          } else if (response.data.message.message) {
            this.showErrorMessage("error", response.data.message.message);
          } else {
            this.showErrorMessage("error", Strings.errors.failed);
          }
        } else {
          this.showErrorMessage("error", response.toString());
        }
      }
    );
  }

  statusUpdate = (value, complete, minsComplete, cubic) => {
    if (this.state.updatestatus == 'Completed') {
      param = {
        status: this.state.updatestatus,
        id: this.state.concreteID,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
        hoursToCompletePlacement: complete.toString(),
        minutesToCompletePlacement: minsComplete.toString(),
        cubicYardsTotal: cubic,
      };
      this.statusUpdateAPI(param)
    } else {
      param = {
        status: this.state.updatestatus,
        id: this.state.concreteID,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
      };
      this.statusUpdateAPI(param)
    }

  };
  renderFlatListItem = ({ item, index }) => {
    let showDelete = true;
    if (this.state.projectRoleId == 4) {
      showDelete = false;
    }
    return (
      <View
        style={{
          minHeight: 40,
          marginVertical: hp("3%"),
          width: wp("95%"),
          alignSelf: "center",
          flexDirection: "row",
        }}
      >
        <TouchableOpacity
          onPress={() => {
            this.setState({ downloadModal: true, downloaditem: item });
          }}
        >
          <View style={[styles.flatlistContainer, { width: wp("95%") }]}>
            <View style={styles.nameContainer}>
              <View
                style={{
                  width: wp("18%"),
                  minHeight: hp("10%"),
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                {(item.extension === "jpg" ||
                  item.extension === "jpeg" ||
                  item.extension === "png") && (
                    <Image
                      source={{ uri: item.attachment }}
                      style={styles.imagePlaceholder}
                    />
                  )}
                {item.extension === "pdf" && (
                  <Image
                    source={Images.pdf_place}
                    resizeMode={"contain"}
                    style={styles.imagePlaceholder}
                  />
                )}
                {item.extension === "doc" && (
                  <Image
                    source={Images.doc_place}
                    resizeMode={"contain"}
                    style={styles.imagePlaceholder}
                  />
                )}
              </View>
              <View style={styles.detailContainer}>
                <Text style={styles.nameText}>{item.filename}</Text>
                <Text style={styles.companyText}>
                  {moment(item.createdAt).format("MMMM DD, YYYY, hh:mm:ss a")}
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>

        {showDelete && (
          <View
            style={[
              styles.dotMenu,
              {
                width: wp("10%"),
                height: hp("8%"),
                position: "absolute",
                right: 10,
              },
            ]}
          >
            <TouchableWithoutFeedback
              onPress={() => {
                this.setState({
                  selectedAttachmentId: item.id,
                  showDelete: true,
                });
                // this.deleteattachement(item.id)
              }}
            >
              <Image source={Images.delete} />
            </TouchableWithoutFeedback>
          </View>
        )}
      </View>
    );
  };

  renderAttachement = () => {
    return (
      <View style={styles.renderAttachementContainer}>
        <FlatList
          data={this.state.attachmentList}
          renderItem={this.renderFlatListItem}
          keyExtractor={(item, index) => index.toString()}
          onRefresh={() => this._onResetAttachment()}
          refreshing={this.state.refreshing}
          style={styles.renderContainerStyle}
        />
        <View style={styles.flatlistContainer}>
          <TouchableWithoutFeedback
            onPress={() => {
              if (Platform.OS == "android") {
                this.setState({
                  iosDropDown: [{ name: "Camera" }, { name: "Document" }],
                });
              }
              this.setState({ isIosDropDown: !this.state.isIosDropDown });
            }}
          >
            <View style={{ width: wp("95%") }}>
              <View style={styles.nameContainer}>
                <View
                  style={styles.attachmentConctainer}
                >
                  <View
                    style={styles.attachmentImageContainer}
                  >
                    <Image
                      source={Images.plus_attach}
                      style={styles.attachmentImage}
                    />
                  </View>
                </View>
                <View style={styles.detailContainer}>
                  <Text
                    style={styles.attachmentText}
                  >
                    {Strings.deliverydetails.addattachment}
                  </Text>
                </View>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </View>
    );
  };
  onremovePressrole = (index) => {
    this.state.addattachmentlist.splice(index, 1);
    this.setState({
      addattachmentlist: this.state.addattachmentlist,
    });
    if (this.state.addattachmentlist.length === 0) {
      this.setState({ attachModel: false });
    }
  };
  deleteattachement = (id) => {
    this.setState({ showLoader: true });
    let url = `${REMOVE_ATTACHEMENT_CONCRETE}/${id}/${this.props.projectDetails.ParentCompany.id}/${this.state.projectId}`;

    removeAttachement(
      url,
      {},
      () => null,
      (response) => {
        this.setState({
          showLoader: false,
        });
        if (response.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (response.status) {
          if (response.status == 200) {
            this.showErrorMessage("success", response.data.message);
            trackEvent('Removed_Attachment')
            mixPanelTrackEvent('Removed Attachment', this.state.mixpanelParam)
            this.attachmentDetails();
          } else if (response.data.data.message) {
            this.showErrorMessage("error", response.data.data.message);
          } else {
            this.showErrorMessage("error", response.data.message);
          }
        } else {
          this.showErrorMessage("error", response.toString());
        }
      }
    );
  };
  onattachPressrole = (index) => {
    this.setState({ attachModel: false });
    let formData = new FormData();
    this.state.addattachmentlist.forEach((element) => {
      let filename =
        element.name == undefined ? element.fileName : element.name;
      let data = element;
      formData.append("attachment", data, filename);
    });
    this.setState({
      showLoader: true,
    });
    let url = `${ADD_ATTACHEMENT_CONCRETE}/${this.state.concreteRequestId}/${this.props.projectDetails.ParentCompany.id}/${this.state.projectId}`;
    addAttachment(
      url,
      formData,
      () => null,
      (response) => {
        if (response.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastType: "error",
              toastMessage: Strings.errors.checkInternet,
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                  showLoader: false,
                });
              }, 2000);
            }
          );
        }
        if (response.status) {
          if (response.status == 200 || response.status == 201) {
            this.attachmentDetails();
            this.setState(
              {
                showToaster: true,
                toastType: "success",
                toastMessage: "Uploaded Successfully.",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                    showLoader: false,
                  });
                }, 2000);
              }
            );
            trackEvent('Added_Attachment')
            mixPanelTrackEvent('Added Attachment', this.state.mixpanelParam)
          } else if (response.status == 413) {
            this.setState(
              {
                showToaster: true,
                toastType: "error",
                toastMessage: "Please upload valid profile or less then 1MB",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                    showLoader: false,
                  });
                }, 2000);
              }
            );
          } else if (
            response.status == 500 ||
            response.status == 400 ||
            response.status == 404
          ) {
            this.setState(
              {
                showToaster: true,
                toastType: "error",
                toastMessage: "Something Went Wrong",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                    showLoader: false,
                  });
                }, 2000);
              }
            );
          } else {
            this.setState(
              {
                showToaster: true,
                toastType: "error",
                toastMessage: "Please upload valid profile or less then 1MB",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                    showLoader: false,
                  });
                }, 2000);
              }
            );
          }
        }
      }
    );
  };
  onPressrole = (item) => {
    if (item.name == 'Completed') {
      this.setState({
        isCompleteDropdown: true, updatestatus: item.name,
        statusModal: false,
        gcStatusModal: false,
      })
    } else {
      this.setState({
        updatestatus: item.name,
        statusModal: false,
        gcStatusModal: false,
        showSave: true
      });
    }
  };
  onPressOption = async (items) => {
    let item = this.state.downloaditem;
    this.setState({ downloadModal: false });
    const url = item.attachment;
    const localFile = `${RNFS.DocumentDirectoryPath}/${item.filename}`;
    const androidPath = `${RNFS.DownloadDirectoryPath}/${item.filename}`;
    const options = {
      fromUrl: url,
      toFile: localFile,
    };

    if (items.name == this.state.downloadoption[1].name) {
      if (Platform.OS == "android") {
        RNFS.downloadFile(options)
          .promise.then(() =>
            FileViewer.open(localFile)
              .then((response) => console.log("response", response))
              .catch((error) => console.log(error))
          )
          .catch((error) => console.log(error));
      } else {
        RNFS.downloadFile(options).promise.then(() =>
          FileViewer.open(localFile)
            .then((response) => console.log("response", response))
            .catch((error) => console.log(error))
        );
      }
    } else {
      this.setState({ showLoader: true });
      if (Platform.OS == "android") {
        try {
          await checkAndRequestPermission(Platform.Version >= 33 ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE).then(res => {
            if (res) {
              ReactNativeBlobUtil.config({
                path: androidPath,
                fileCache: true,
                addAndroidDownloads: {
                  useDownloadManager: true,
                  path: androidPath,
                },
              })
                .fetch("GET", url)
                .then(async (resp) => {
                  if (resp) {
                    if (resp) {
                      this.setState(
                        {
                          showToaster: true,
                          toastType: "success",
                          toastMessage: `${Strings.popup.downloadStatus} at ${androidPath}`,
                        },
                        () => {
                          setTimeout(() => {
                            this.setState({
                              showToaster: false,
                              showLoader: false,
                            });
                          }, 2000);
                        }
                      );
                    }
                  }
                  this.setState({ showLoader: false });
                })
                .catch((err) => {
                  this.setState({ showLoader: false });
                });
            } else {
              this.setState({ showLoader: false, showPopUp: true, showText: Strings.permissions.files_media_permission });
            }
          })
        } catch (err) { }
      } else {
        RNFS.downloadFile(options)
          .promise.then(() => {
            Share.open({
              type: item.extension,
              urls: [localFile],
              saveToFiles: true,
            });
          })
          .then((res) => {
            this.setState({ showLoader: false });

            // success
          })
          .catch((error) => {
            this.setState({ showLoader: false });

            // error
          });
      }
    }
  };
  onPressIosDropDown = async (item) => {
    this.setState({ isIosDropDown: false })
    if (item.name == "Document") {
      this.pickupDocument();
    } else if (item.name == "Photos") {
      this.pickPhotos();
    } else if (item.name == "Camera") {
      checkAndRequestPermission(PERMISSIONS.ANDROID.CAMERA).then(async res => {
        if (res || Platform.OS == "ios") {
          const result = await launchCamera({ mediaType: "photo" });
          let assets = [];
          for (let item of result.assets) {
            assets.push({
              size: item.fileSize,
              fileCopyUri: item.uri,
              name: item.fileName,
              uri: item.uri,
              type: item.type,
            });
          }
          this.setState({
            isIosDropDown: false,
            attachModel: true,
            addattachmentlist: assets,
          });
        } else {
          this.setState({ showPopUp: true, showText: Strings.permissions.camera_permission });
        }
      })
    }
  };
  onPressEditDropDown = (item) => {
    this.onSelectDropdown("Edit", item.id)
    this.setState({ isEditAccess: false })
  }
  pickPhotos = async () => {
    const result = await launchImageLibrary({ mediaType: "photo" });
    let assets = [];
    for (let item of result.assets) {
      assets.push({
        size: item.fileSize,
        fileCopyUri: item.uri,
        name: item.fileName,
        uri: item.uri,
        type: item.type,
      });
    }
    // Need later
    // const filesize = Math.round(assets[0].size / 1024);
    // if (filesize > 2048) {
    //   this.setState({ isIosDropDown: false });
    //   this.showErrorMessage(
    //     "error",
    //     "Image size should not exceed greater than 2MB"
    //   );
    // } else {
    this.setState({
      isIosDropDown: false,
      attachModel: true,
      addattachmentlist: assets,
    });
    // }
  };
  pickupDocument = async () => {
    checkAndRequestPermission(Platform.Version >= 33 ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE).then(async res => {
      if (res || Platform.OS === 'ios') {
        try {
          const results = await DocumentPicker.pickMultiple({
            type: [DocumentPicker.types.allFiles],
          });
          let attach = results
            .filter((e) => {
              let type = ["pdf", "doc", "png", "jpg", "jpeg"].includes(
                e.type.split("/")[1]
              );
              if (!type) {
                //TODO FOR LATER
                // alert(
                //   "Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)"
                // );
              }
              return type;
            })
            .map((e) => e);
          if (attach.length) {
            this.setState({
              isIosDropDown: false,
              attachModel: true,
              addattachmentlist: attach,
            });
          }
        } catch (err) {
          if (DocumentPicker.isCancel(err)) {
            // User cancelled the picker, exit any dialogs or menus and move on
          } else {
            throw err;
          }
        }
      } else {
        this.setState({ showPopUp: true, showText: Strings.permissions.files_media_permission });
      }
    })
  };
  _onResetAttachment = () => {
    this.page_number = 1;
    this.setState(
      {
        attachmentList: [],
        showLoader: true,
      },
      () => {
        this.attachmentDetails();
      }
    );
  };
  onSelectDropdown = (options, editRequestID) => {
    if (options == "Save") {
      this.statusUpdate('', '', '', '');
    }

    if (options == "Edit") {
      this.editConcreteRequest(this.state.wholeData, 1, editRequestID);
    }

    if (options == "Void") {
      this.voidClick();
    }
  };

  editConcreteRequest = (item, index, editRequestID) => {
    this.props.showConcreteRequestId(item.id);
    this.props.editConcreteRequest({
      item: item,
      index: index,
    });

    let selectedLocationsData = [];
    if (item && item != null && item != undefined && item.locationDetails) {
      item.locationDetails.forEach(element => {
        if (element && element.ConcreteLocation) {
          let data = element.ConcreteLocation;
          if (data && data != undefined) {
            selectedLocationsData.push({ id: data.location, uniqueId: data.id, chosenFromDropdown: true })
          }
        }
      });
    }
    let selectedPlacementsData = [];
    if (item && item != null && item != undefined && item.placementDetails) {
      item.placementDetails.forEach(element => {
        if (element && element.ConcretePlacement) {
          let data = element.ConcretePlacement;
          if (data && data != undefined) {
            selectedPlacementsData.push({ id: data.placement, uniqueId: data.id, chosenFromDropdown: true })
          }
        }
      });
    }

    let selectedMixDesignsData = [];
    if (item && item != null && item != undefined && item.mixDesignDetails) {
      item.mixDesignDetails.forEach(element => {
        if (element && element.ConcreteMixDesign) {
          let data = element.ConcreteMixDesign;
          if (data && data != undefined) {
            selectedMixDesignsData.push({ id: data.mixDesign, uniqueId: data.id, chosenFromDropdown: true })
          }
        }
      });
    }

    let selectedPumpSizesData = [];
    if (item && item != null && item != undefined && item.pumpSizeDetails) {
      item.pumpSizeDetails.forEach(element => {
        if (element && element.ConcretePumpSize) {
          let data = element.ConcretePumpSize;
          if (data && data != undefined) {
            selectedPumpSizesData.push({ id: data.pumpSize, uniqueId: data.id, chosenFromDropdown: true })
          }
        }
      });
    }
    this.props.selectedConcreteLocationsData(selectedLocationsData);
    this.props.selectedConcretePlacementsData(selectedPlacementsData);
    this.props.selectedConcreteMixDesignsData(selectedMixDesignsData);
    this.props.selectedConcretePumpSizesData(selectedPumpSizesData);
    this.props.navigation.navigate('AddConcrete', {
      from: "DetailsConcrete",
      notificationDetails: this.state.notificationDetails,
      updateData: this.updateData,
      showEditRequestID: editRequestID
    })
  };
  voidClick = () => {
    this.setState({
      showLoader: true,
    });
    let param = {
      ConcreteRequestId: this.state.concreteID,
      ProjectId: this.props.projectDetails.id,
      ParentCompanyId: this.props.projectDetails.ParentCompany.id,
    };
    addVoidConcrete(
      `${ADD_VOID_CONCRETE}`,
      param,
      () => null,
      async (response) => {
        this.setState({
          showLoader: false,
        });

        if (response.toString() == Strings.errors.timeout) {
          this.showToaster("error", Strings.errors.timeout);
        } else if (response.status) {
          if (response.status == 200 || response.status == 201) {
            setTimeout(() => {
              this.props.refreshDashboard(true, "Details Create Void ");
              this.props.cameBack(false);
              this.props.refreshCalendar(true);
              //this.props.navigation.navigate("Plus");
              this.props.navigation.goBack();
            }, 2000);
            this.showErrorMessage(
              "success",
              "Concrete request marked as void Successfully"
            );
            trackEvent('Concrete_Request_voided')
            mixPanelTrackEvent('Concrete Request voided', this.state.mixpanelParam)
          } else if (response.status == 400) {
            this.showErrorMessage("error", "ValidationError");
          } else {
            this.showErrorMessage("error", "ValidationError");
          }
        } else {
          this.showErrorMessage("error", response.toString());
        }
      }
    );
  };

  renderCommentListItem = ({ item, index }) => {
    return (
      <View style={styles.flatlistContainer}>
        <View style={{ width: wp("95%") }}>
          <View style={{ flexDirection: "row", width: wp("90%") }}>
            <Image
              source={
                item.Member.User.profilePic
                  ? { uri: item.Member.User.profilePic }
                  : Images.placeholder
              }
              style={{
                width: wp("10%"),
                height: wp("10%"),
                borderRadius: wp("5%"),
                //  backgroundColor: "gray",
                marginRight: 10,
                marginLeft: 10,
              }}
            ></Image>
            <Text
              style={
                (styles.historyCompanyText, { marginTop: 10, width: wp("70%") })
              }
            >
              {item.Member.User.firstName + " " + item.Member.User.lastName}
            </Text>
          </View>
          <View style={styles.historyDetailContainer}>
            <Text style={styles.historyNameText}>{item.comment.trim()}</Text>
            <Text style={styles.historyDateText}>
              {moment(item.createdAt).format("MMMM DD,YYYY, hh:mm:ss a")}
            </Text>
          </View>
        </View>

        <View
          style={{ marginTop: 10, height: 0.5, backgroundColor: "#A8B2B9" }}
        ></View>
      </View>
    );
  };
  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        commentList: [],
        totalCount: 0,
        showLoader: true,
      },
      () => {
        this.commentDetails();
      }
    );
  };
  renderHistoryListItem = ({ item, index }) => {
    if (item.type !== "comment") {
      return (
        <View style={styles.flatlistContainer}>
          <View style={{ width: wp("95%") }}>
            <View style={styles.historyDetailContainer}>
              <Text style={styles.historyDateText}>
                {moment(item.createdAt).format("MMMM DD,YYYY, hh:mm:ss a")}
              </Text>
              <Text style={styles.historyCompanyText}>{item.description}</Text>
            </View>
            <View
              style={{ marginTop: 10, height: 0.5, backgroundColor: "#A8B2B9" }}
            ></View>
          </View>
        </View>
      );
    }
  };
  renderHistory = () => {
    return (
      <View style={{ flex: 1, marginBottom: hp("10%") }}>
        <FlatList
          data={this.state.historylist}
          renderItem={this.renderHistoryListItem}
          // ItemSeparatorComponent={this.itemSeparator}
          keyExtractor={(item, index) => index.toString()}
          // onEndReached={() => this.onEndReached()}
          onEndReachedThreshold={0}
          onMomentumScrollBegin={() => {
            this.onEndReachedCalledDuringMomentum = false;
          }}
          onRefresh={() => this._onResetHistory()}
          refreshing={this.state.refreshing}
          extraData={this.state}
        />
      </View>
    );
  };
  //HEADER COMPONENT
  renderHeader() {
    return (
      <View style={styles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            //this.props.navigation.navigate("Plus");
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.closeBlack} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={styles.title}>{Strings.concreteDetails.title}</Text>
        <View style={styles.closecontainer}></View>
      </View>
    );
  }
  renderBottomContainer = () => {


    return (
      <View style={styles.bottomContainer}>
        {this.state.showSave == true && (
          <View style={styles.bottomView}>
            <TouchableOpacity
              onPress={() => this.onSelectDropdown("Save", 0)}
              style={styles.footerAction}
            >
              <Image source={HEADERDROPDOWNOPTIONS[0].image} />
              <Text style={styles.footerActionText}>
                {HEADERDROPDOWNOPTIONS[0].name}
              </Text>
            </TouchableOpacity>
          </View>
        )}
        {(this.state.isAccess) && (
          <>
            <View style={styles.bottomView}>
              <TouchableOpacity
                onPress={() => {
                  if (this.state.recurrenceType == Strings.calendarSettings.doseNotRepeat) {
                    this.onSelectDropdown("Edit", 1)
                    trackScreen('Concrete Details')
                  } else {
                    this.setState({ isEditAccess: !this.state.isEditAccess })
                    trackScreen('Concrete Details')
                  }
                }}
                style={styles.footerAction}
              >
                <Image source={HEADERDROPDOWNOPTIONS[1].image} />
                <Text style={styles.footerActionText}>
                  {HEADERDROPDOWNOPTIONS[1].name}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.bottomView}>
              <TouchableOpacity
                onPress={() => this.onSelectDropdown("Void", 0)}
                style={styles.footerAction}
              >
                <Image source={HEADERDROPDOWNOPTIONS[2].image} />
                <Text style={styles.footerActionText}>
                  {HEADERDROPDOWNOPTIONS[2].name}
                </Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    );
  };
  updateSize = (height) => {
    this.setState({
      height,
    });
  };

  accepPopuptTap = () => {
    this.setState({ isCompletedModal: false })
    this.statusUpdate(this.state.buttonValueBtn, this.state.completePlacementBtn, this.state.minsCompleteBtn, this.state.totalBtn)
  }

  cancelPopupTap = () => {
    this.setState({ isCompletedModal: false })
    this.onSelectDropdown("Edit", 0)
  }
  submitBtn = (buttonValue, completePlacement, minsComplete, total) => {
    let isResponseValue = false;

    this.setState({ buttonValueBtn: buttonValue, completePlacementBtn: completePlacement, minsCompleteBtn: minsComplete, totalBtn: total })

    const arr1 = this.state.controlledByList;
    const arr2 = this.state.renderRespId

    const result = arr1.filter(o => arr2.some(({ id }) => o.id === id));

    if (this.state.renderRespId.length !== result.length) {
      isResponseValue = true;
    }

    if (isResponseValue) {
      this.setState({ isCompleteDropdown: false, isCompletedModal: true });
    } else {
      this.statusUpdate(buttonValue, completePlacement, minsComplete, total)
    }
  }

  renderScene = ({ route }) => {
    switch (route.key) {
      case 'details':
        return (
          <KeyboardAwareScrollView extraScrollHeight={50}>
            {this.renderDetails()}
          </KeyboardAwareScrollView>
        );
      case 'attachments':
        return (
          <KeyboardAwareScrollView scrollEnabled={false}>
            {this.renderAttachement()}
          </KeyboardAwareScrollView>
        );
      case 'comments':
        return (
          <KeyboardAvoidingView
            style={{ flex: 1 }}
            behavior="position"
            keyboardVerticalOffset={Platform.OS === "ios" ? 100 : 0}
          >
            <FlatList
              style={{ marginTop: 10, height: hp("42%") }}
              data={this.state.commentList}
              contentContainerStyle={{ paddingBottom: 50 }}
              renderItem={this.renderCommentListItem}
              keyExtractor={(item, index) => index.toString()}
              onRefresh={() => this._onReset()}
              refreshing={this.state.refreshing}
            />
            <Text style={styles.commentsTextBox}>
              {Strings.deliverydetails.entercomments}
            </Text>
            <View
              style={{
                height: hp("10%"),
                borderRadius: 5,
                borderColor: "#BEBEBE",
                borderWidth: 0.5,
                marginTop: 10,
                marginLeft: hp("2%"),
                marginRight: hp("2%"),
              }}
            >
              <TextInput
                placeholder="Your Placeholder"
                onChangeText={(value) =>
                  this.setState({ commenttext: value })
                }
                style={{ margin: 5 }}
                editable
                multiline
                maxLength={150}
                value={this.state.commenttext}
                onContentSizeChange={(e) =>
                  this.updateSize(e.nativeEvent.contentSize.height)
                }
              />
            </View>
            <TouchableOpacity
              onPress={() => {
                let comStr = this.state.commenttext.trim();
                if (isEmpty(comStr)) {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: "Please enter the comments",
                      toastType: "error",
                    },
                    () => this.hideToast()
                  );
                } else {
                  if (comStr.length < 3) {
                    this.setState(
                      {
                        showToaster: true,
                        toastMessage:
                          "comment lenght must be atleast 3 characters long",
                        toastType: "error",
                      },
                      () => this.hideToast()
                    );
                  } else {
                    this.nextCommentClick();
                  }
                }
              }}
              style={{
                width: wp("30%"),
                marginRight: hp("2%"),
                height: hp("4%"),
                marginTop: hp("3%"),
                backgroundColor: Colors.themeOpacity,
                borderRadius: hp("3.5%"),
                justifyContent: "center",
                alignItems: "center",
                alignSelf: "flex-end",
              }}
            >
              <View
                style={{
                  width: wp("50%"),
                  alignSelf: "center",
                  height: hp("7%"),
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text
                  style={{
                    fontSize: wp("4%"),
                    color: Colors.themeColor,
                    fontFamily: Fonts.montserratSemiBold,
                  }}
                >
                  {Strings.profile.submit}
                </Text>
              </View>
            </TouchableOpacity>
          </KeyboardAvoidingView>
        );
      case 'history':
        return (
          <KeyboardAwareScrollView extraScrollHeight={50}>
            {this.renderHistory()}
          </KeyboardAwareScrollView>
        );
      default:
        return null;
    }
  };

  renderTabBar = props => (
    <TabBar
      {...props}
      scrollEnabled={true}
      tabStyle={{ width: 'auto', marginHorizontal: wp('2%') }}
      indicatorStyle={{ backgroundColor: Colors.themeColor }}
      style={{ backgroundColor: Colors.white }}
      activeColor={Colors.black}
      inactiveColor={'#A8B2B9'}
      labelStyle={{
        fontSize: wp('3.8%'),
        fontFamily: Fonts.montserratExtraLight,
      }}
    />
  );

  render() {
    const showBottomContainer = (this.state.showSave == true) || (this.state.isAccess == true)
    return (
      <>
        {this.state.isNetworkCheck ?
          <NoInternet
            Refresh={() => this.networkCheck()} /> :
          <AppView>
            <View style={styles.parentContainer}>
              {this.renderHeader()}
              <TabView
                navigationState={{ index: this.state.index, routes: this.state.routes }}
                renderScene={this.renderScene}
                renderTabBar={this.renderTabBar}
                style={showBottomContainer ? { marginBottom: hp("10%") } : {}}
                onIndexChange={index => {
                  this.setState({ index });
                  // Call data loading methods on tab change
                  if (index === 1) this.attachmentDetails();
                  if (index === 2) this.commentDetails();
                  if (index === 3) this.historyDetails();
                }}
                initialLayout={{ width: Dimensions.get('window').width }}
              />
              {showBottomContainer && this.renderBottomContainer()}
            </View>
            {
              this.state.isCompleteDropdown &&
              <ConcreteDropDown
                title={Strings.profile.Are}
                value={""}
                closeBtn={() =>
                  this.setState({ isCompleteDropdown: false, isComplete: false })
                }
                skip={(buttonValue, completePlacement, minsComplete, total) => this.statusUpdate(buttonValue, completePlacement, minsComplete, total)}
                submit={(buttonValue, completePlacement, minsComplete, total) => this.submitBtn(buttonValue, completePlacement, minsComplete, total)}
                visible={this.state.isCompleteDropdown}
                onbackPress={() =>
                  this.setState({ isCompleteDropdown: false, isComplete: false })
                }
                container={{ justifyContent: "center", alignItems: "center" }}
                textContainer={{ fontSize: 14, textAlign: "center" }}
              />
            }


            {this.state.statusModal && (
              <Dropdown
                data={this.state.statuslist}
                title={Strings.placeholders.selectstatus}
                value={this.state.updatestatus}
                closeBtn={() => this.setState({ statusModal: false })}
                onPress={(item) => this.onPressrole(item)}
                visible={this.state.statusModal}
                onbackPress={() => this.setState({ statusModal: false })}
                textContainer={{
                  textAlign: "center",
                }}
              />
            )}
            {this.state.gcStatusModal && (
              <Dropdown
                data={this.state.gcStatusList}
                title={Strings.placeholders.selectstatus}
                value={this.state.updatestatus}
                closeBtn={() => this.setState({ gcStatusModal: false })}
                onPress={(item) => this.onPressrole(item)}
                visible={this.state.gcStatusModal}
                onbackPress={() => this.setState({ gcStatusModal: false })}
                textContainer={{
                  textAlign: "center",
                }}
              />
            )}
            {this.state.downloadModal && (
              <Dropdown
                data={this.state.downloadoption}
                title={Strings.profile.Choose}
                value={""}
                closeBtn={() =>
                  this.setState({ downloadModal: false, downloaditem: {} })
                }
                onPress={(item) => this.onPressOption(item)}
                visible={this.state.downloadModal}
                onbackPress={() =>
                  this.setState({ downloadModal: false, downloaditem: {} })
                }
                container={{ justifyContent: "center", alignItems: "center" }}
                textContainer={{ fontSize: 14, textAlign: "center" }}
              />
            )}
            {this.state.isIosDropDown && (
              <Dropdown
                data={this.state.iosDropDown}
                title={Strings.placeholders.chooseType}
                value={this.state.iosDropDown}
                closeBtn={() => this.setState({ isIosDropDown: false })}
                onPress={(item) => this.onPressIosDropDown(item)}
                visible={this.state.isIosDropDown}
                onbackPress={() => this.setState({ isIosDropDown: false })}
                textContainer={{
                  textAlign: "center",
                }}
              />
            )}
            {this.state.attachModel && (
              <AttachDropdown
                data={this.state.addattachmentlist}
                title={Strings.deliverydetails.files}
                value={this.state.updatestatus}
                closeBtn={() => this.setState({ attachModel: false })}
                onRemove={(item) => this.onremovePressrole(item)}
                onDone={(item) => this.onattachPressrole(item)}
                visible={this.state.attachModel}
                onbackPress={() => this.setState({ attachModel: false })}
              />
            )}
            {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

            {this.state.isEditAccess && (
              <Dropdown
                data={this.state.editDropDown}
                title={Strings.placeholders.chooseType}
                value={this.state.editDropDown}
                closeBtn={() => this.setState({ isEditAccess: false })}
                onPress={(item) => this.onPressEditDropDown(item)}
                visible={this.state.isEditAccess}
                onbackPress={() => this.setState({ isEditAccess: false })}
                textContainer={styles.editTextContainer}
              />
            )}

            {this.state.showToaster && (
              <Toastpopup
                backPress={() => this.setState({ showToaster: false })}
                toastMessage={this.state.toastMessage}
                type={this.state.toastType}
                container={{ marginBottom: hp("12%") }}
              />
            )}

            {this.state.isCompletedModal && (
              <DeletePop
                title={Strings.popup.success}
                desc={Strings.errors.emptyResponsiblePersonDetails}
                descStyles={styles.desstylePopUp}
                container={styles.desContainerStyle}
                acceptTap={this.accepPopuptTap}
                declineTap={this.cancelPopupTap}
              />
            )}

            {this.state.showAlert && (
              <Alert
                title={Strings.popup.success}
                desc={Strings.popup.changepasswordSuccess}
                okTap={() => {
                  this.okTap();
                }}
              />
            )}

            {this.state.showCancel && (
              <DeletePop
                title={Strings.popup.success}
                desc={Strings.popup.cancelSub}
                descStyles={{
                  width: "80%",
                }}
                container={{ bottom: 0 }}
                acceptTap={this.cancelPopupAcceptTap}
                declineTap={this.cancelPopupDeclineTap}
              />
            )}

            {this.state.showDelete && (
              <DeletePop
                container={{
                  bottom: 0,
                }}
                title={Strings.popup.success}
                desc={Strings.popup.delete}
                acceptTap={this.deletePopupAcceptTap}
                declineTap={this.deletePopupDeclineTap}
              />
            )}
            {this.state.isModal && (
              <ResDropdown
                data={this.state.responsiblePerson}
                title={Strings.deliverydetails.responsiblePerson}
                value={""}
                closeBtn={() =>
                  this.setState({ isModal: false, downloaditem: {} })
                }
                onPress={() => null}
                visible={this.state.isModal}
                onbackPress={() =>
                  this.setState({ isModal: false })
                }
                textContainer={{ fontSize: 16 }}
              />
            )
            }
            {this.state.showPopUp && (
              <DeletePop
                container={styles.containerStyles}
                descStyles={styles.descStyles}
                desc={this.state.showText}
                declineText={Strings.permissions.no_thanks}
                acceptText={Strings.permissions.go_to_settings}
                declineTap={() => this.setState({ showPopUp: false })}
                acceptTap={() => {
                  Linking.openSettings()
                  this.setState({ showPopUp: false })
                }}
                declineTextStyle={styles.textStyle}
                acceptTextStyle={styles.textStyle}
              />
            )}
          </AppView>
        }
      </>
    );
  }
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("80%"),
    fontSize: wp("6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratSemiBold,
  },
  // imageContainer: {
  //   width: wp("100%"),
  //   height: hp("13%"),
  //   alignItems: "center"
  // },
  imageButton: {
    width: wp("24%"),
    height: wp("24%"),
    borderRadius: wp("12%"),
    backgroundColor: "#F5F5F5",
    borderWidth: 1,
    borderColor: "#00000029",
  },
  placeholder: {
    width: wp("15%"),
    alignSelf: "center",
  },
  closecontainer: {
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("21%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    marginRight: wp("20%"),
    //outlineProvider: 'bounds'
  },
  detailContainerView: {
    flexDirection: "row",
    marginTop: 8,
  },
  detailTextView: {
    width: wp("50%"),
  },
  detailView: {
    flex: 1, margin: 20
  },
  detailsHeadingStyle: {
    width: wp("80%"),
    color: Colors.descriptionField,
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratBold,
    marginTop: 20,
  },
  detailsTextStyle: {
    width: wp("90%"),
    color: Colors.black,
    fontSize: wp("3.5%"),
    marginLeft: 2,
    marginTop: 8,
    fontFamily: Fonts.montserratMedium,
  },
  detailsHeadingRowStyle: {
    width: wp("40%"),
    color: Colors.descriptionField,
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratBold,
    marginTop: 20,
  },
  detailsTextRowStyle: {
    width: wp("40%"),
    color: Colors.black,
    fontSize: wp("3.5%"),
    marginLeft: 2,
    marginTop: 8,
    fontFamily: Fonts.montserratMedium,
  },
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 3,
    marginTop: 5,
    alignSelf: "center",
  },
  renderAttachementContainer: {
    flex: 1,
    height: Platform.OS == "ios" ? hp('65%') : hp('73%')
  },
  attachmentConctainer: {
    width: wp("18%"),
    minHeight: hp("10%"),
    justifyContent: "center",
    alignItems: "center",
  },
  attachmentImageContainer: {
    width: wp("15%"),
    height: wp("15%"),
    borderRadius: wp("3%"),
    marginLeft: wp("6%"),
    backgroundColor: "#ECECEC",
    justifyContent: "center",
  },
  attachmentText: {
    color: "#707070",
    fontSize: 14,
    fontFamily: Fonts.montserratSemiBold,
  },
  nameContainer: {
    minHeight: hp("10%"),
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  attachmentImage: {
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
  },
  detailContainer: {
    width: wp("53%"),
    marginLeft: 20,
    justifyContent: "center",
  },
  imagePlaceholder: {
    width: wp("15%"),
    height: wp("15%"),
    borderRadius: wp("1%"),
    marginLeft: wp("6%"),
  },
  nameText: {
    color: "#292529",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratSemiBold,
    marginTop: 10,
  },
  companyText: {
    color: "#A8B2B9",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle1: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    marginRight: wp("10%"),
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    //outlineProvider: 'bounds'
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  dotMenu: {
    marginTop: hp("2%"),
  },
  customheaderDropdownStyle: {
    justifyContent: "flex-end",
    alignItems: "flex-end",
    height: 40,
    width: wp("30%"),
  },
  historyDetailContainer: {
    width: wp("90%"),
    marginLeft: 20,
    justifyContent: "center",
  },
  historyNameText: {
    color: "#5B5B5B",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  historyDateText: {
    color: "#A8B2B9",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  historyCompanyText: {
    color: "#5B5B5B",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratSemiBold,
    marginTop: hp("1%"),
    width: wp("90%"),
    textAlign: "left",
  },
  historyUserText: {
    marginLeft: 20,
    color: "#5B5B5B",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratSemiBold,
  },
  commentsTextBox: {
    color: "#5B5B5B",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
    marginLeft: hp("2%"),
    // marginTop: 20,
  },
  bottomContainer: {
    position: "absolute",
    width: wp("100%"),
    borderTopWidth: 0.5,
    shadowOpacity: 1,
    elevation: 200,
    //shadowColor: "rgba(0,0,0,0.14)",
    backgroundColor: "#fff",
    shadowColor: Colors.placeholder,
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    height: hp("10%"),
    bottom: 0,
  },
  bottomView: {
    width: wp("30%"),
    justifyContent: "center",
    alignItems: "center",
    height: hp("8%"),
    marginHorizontal: 20,
  },
  footerAction: { justifyContent: "center", alignItems: "center" },
  footerActionText: {
    fontFamily: Fonts.montserratRegular,
    marginTop: 5,
  },
  completeContainer: {
    flexDirection: "row",
    //marginLeft: 10,
    marginTop: 20,
  },
  completeTouch: {
    height: 15,
    width: 15,
    borderRadius: 30,
    borderColor: Colors.lightGrey,
    borderWidth: 1,
    marginRight: 10,
  },
  headingText: {
    color: Colors.black,
    fontSize: 14,
    fontFamily: Fonts.montserratBold,
  },
  lineStyle: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.greyContainer,
    marginLeft: 10,
    marginTop: 10,
  },
  detailsSubHeading: {
    flexDirection: "row",
    marginTop: 20
  },
  statusText: {
    color: Colors.black,
    fontSize: 14,
    fontFamily: Fonts.montserratSemiBold,
    marginBottom: 100,

  },
  dropDownContainer: {
    height: hp("30%"),
    width: wp("100%"),
    position: "absolute",
    backgroundColor: Colors.white,
    bottom: 0,
    zIndex: 999,
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0.8,
    // shadowOffset: {width: 10, height: 10},
    shadowOpacity: 1,
    elevation: 200,
    shadowColor: "rgba(0,0,0,0.14)",
    borderTopEndRadius: hp("5%"),
    borderTopStartRadius: hp("5%"),
  },
  desstylePopUp: {
    width: "80%"
  },
  desContainerStyle: {
    bottom: 0,
  },
  textInputStatus: {
    fontFamily: Fonts.montserratMedium,
    color: Colors.black,
    fontSize: wp("3.5%"),
  },
  textTitleStatus: {
    fontFamily: Fonts.montserratBold,
    fontSize: wp("3.5%"),
    color: Colors.descriptionField
  },
  renderContainerStyle: {
    height: hp("70%")
  },
  editTextContainer: {
    textAlign: "center",
  },
  responsibleFlatlistContainer: {
    marginTop: 5
  },
  avatarContainer: {
    backgroundColor: 'grey',
    marginTop: 5,
  },
  responsibleContainer: {
    flexDirection: 'row',
    width: '110%'
  },
  containerStyles: {
    bottom: 0
  },
  descStyles: {
    fontSize: wp("4.5%"),
    marginRight: wp("5%"),
  },
  textStyle: {
    fontSize: wp("4%")
  },
});
const mapStateToProps = (state) => {
  const {
    projectDetails,
    deliveryDetailsId,
    inspectionDetailsId,
    projectRoleId,
    notificationDetails,
    concreteDetailsRequestID,
    userDetails,
  } = state.LoginReducer;

  return {
    projectDetails,
    deliveryDetailsId,
    inspectionDetailsId,
    projectRoleId,
    notificationDetails,
    concreteDetailsRequestID,
    userDetails,
  };
};

export default compose(
  connect(mapStateToProps, {
    cameBack,
    showDeliverdetailsid,
    // showInspectiondetailsid,
    editINS,
    editData,
    tappedNotificationDetails,
    refreshPage,
    refreshDashboard,
    refreshDeliveryList,
    showCraneRequestId,
    editCraneRequest,
    refreshCalendar,
    editConcreteRequest,
    showConcreteRequestId,
    selectedConcreteLocationsData,
    selectedConcreteMixDesignsData,
    selectedConcretePlacementsData,
    selectedConcretePumpSizesData,
    clickAdd,
  }),
  withBackHandler
)(DetailsConcrete);