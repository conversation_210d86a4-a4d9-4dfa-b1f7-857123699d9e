import React, { Component } from "react";
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
  Switch,
  Alert,
} from "react-native";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";
import { TextField } from "../../components/textinput/addMemberTextinput";
import { isEmpty } from "../../common/validators";
import Modal from "react-native-modal";
import { Selectize } from "react-native-material-selectize";
import DateTimePicker from "@react-native-community/datetimepicker";
import DropDownPicker from "../../components/dropdown/DropDownPicker";
import moment from "moment";
import MultiSelectDropDown from "../../components/multi-select-dropdown/multiSelectdropDown";
import DeletePop from "../../components/toastpopup/logoutPop";
import AppLoader from "../../components/apploader/AppLoader";
import {
  changeTab,
  showSideMenu,
  cameBack,
  updateList,
  refreshDashboard,
  refreshDeliveryList,
  showCraneRequestId,
  editCraneRequest,
  refreshCalendar,
} from "../../actions/postAction";

import {
  GET_NEW_COMPANIES,
  DEFINABLE_FEATURE,
  SEARCH_MEMBER,
  EDIT_CRANE,
  ADD_CRANE,
  GET_SINGLE_CRANE,
  CRANE_EQUIPMENT_LIST,
  GET_LAST_CRANE_ID,
  GET_PROJECT_ROLE,
  LIST_ALL_MEMBER,
  GET_SINGLE_PROJECT,
  GET_TIMEZONE,
  GET_LOCATION_DETAILS,
  GET_GATE_LIST,
  GET_PROJECT_SETTING_DETAILS,
  NO_EQUIPMENT_NEEDED,
} from "../../api/Constants";
import {
  getNewCompanyList,
  getDefinableFeature,
  searchMember,
  addCraneRequest,
  getSingleCraneRequest,
  getCraneEquipmentList,
  getLastCraneRequestId,
  _getData,
  getAllMemberList,
  getTimeZone,
  getProjectSettings,
  getLocationSettings,
  getGateList,
} from "../../api/Api";
let selectedDeliveryDate = new Date();
let selectedStartDate = "";
let selectedEndDate = "";
import {
  trackScreen,
  trackEvent,
} from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent } from "../../MixPanel/MixPanel";
import Dropdown from "../../components/dropdown/dropdown";
import RecurrenceComponent from "../../components/Recurrence/RecurrenceComponent";
import NetInfo from "@react-native-community/netinfo";
import NoInternet from "../../components/NoInternet/noInternet";
import * as RNLocalize from "react-native-localize";
import AsyncStorage from "@react-native-async-storage/async-storage";

class AddCrane extends Component {
  handleNavigation = () => {
    const {
      selectedLocationNew,
      selectedLocationId, // Add this to your state if not already there
      selectedEquipmentList,
      equipTypeList,
      selectedGate,
      selectedGateId, // Add this to your state if not already there
      selectedTimeZone, // Add this to your state if not already there
      selectedTimeZoneId, // Add this to your state if not already there
      selectedDate,
      selectedStartTime,
      selectedEndTime,
      projectId,
    } = this.state;

    let errorMessage = "";

    if (!selectedLocationNew) {
      errorMessage = "Please select a location";
    } 
    // else if (!selectedGate) {
    //   errorMessage = "Please select a gate";
    // } else if (
    //   (!selectedEquipmentList || selectedEquipmentList.length === 0) &&
    //   (!equipTypeList || equipTypeList.length === 0)
    // ) {
    //   errorMessage = "Please select at least one equipment";
    // }

    if (errorMessage) {
      Alert.alert("Validation Error", errorMessage);
      return;
    }
    const equipmentSource =
      selectedEquipmentList && selectedEquipmentList.length > 0
        ? selectedEquipmentList
        : equipTypeList;

    const equipmentToSend = Array.isArray(equipmentSource)
      ? equipmentSource.filter((item) => item && item.selected === true)
      : [];

    // if (equipmentToSend.length === 0) {
    //   Alert.alert("Validation Error", "Please select at least one equipment");
    //   return;
    // }

    const navParams = {
      location: selectedLocationNew,
      locationId: selectedLocationId, // Add location ID
      equipment: equipmentToSend,
      gate: selectedGate,
      gateId: selectedGateId, // Add gate ID
      timeZone: selectedTimeZone, // Add time zone
      timeZoneId: selectedTimeZoneId, // Add time zone ID
      bookingId: this.state.bookingId || null,
      projectId: projectId,
      ...(selectedDate && { date: selectedDate.toString() }),
      // Only include times if they exist
      ...(selectedStartTime && { startTime: selectedStartTime.toString() }),
      ...(selectedEndTime && { endTime: selectedEndTime.toString() }),
      prevParams: this.props.route.params,
    };

    this.props.navigation.navigate("timeSlotCrane", navParams);
  };
  constructor(props) {
    super(props);
    this.state = {
      showMultipleSec: false,
      showInfo: false,
      dfowList: [],
      selecteddfow: [],
      selectedItems: [],
      responsiblePersonData: [],
      editDR: false,
      selectedItem: [
        {
          id: `${this.props.responsiblePersonData.name}`,
          email: this.props.userDetails.email,
          userId: this.props.responsiblePersonData.id,
        },
      ],
      selectedItemIndex: 0,
      selectedItemList: [],
      equipTypeList: [],
      selectedEquipmentList: [], // Array of selected equipment objects
      selectedEquipName: null,
      selectedResponsibleCompany: null,
      responisbleCompanyList: [],
      gateList: [],
      selectedGate: null,
      definableList: [],
      selectedDefinableList: null,
      deliveryId: 0,
      description: "",
      escortNeeded: false,
      delVehicleDetails: "",
      additionalNotes: "",
      disableSubmit: false,
      showDateModal: false,
      showStartTimeModal: false,
      showEndTimeModal: false,
      selectedDate: "",
      selectedStartTime: "",
      selectedEndTime: "",
      calSelectedDate: new Date(), //moment(new Date).add(7, 'day').format('YYYY-MM-DD'),
      calSelectedStartTime: new Date(),
      calSelectedEndTime: new Date(), //new Date(dt.setHours(dt.getHours() + 1)),
      minimumDate: new Date(),
      minimumStartTime: new Date(),
      minimumEndTime: new Date(),
      projectId: this.props.projectDetails.id,
      showCancel: false,
      comparision: [],
      pickFrom: "",
      pickTo: "",
      mixpanelParam: {
        ProjectName: this.props.projectDetails.projectName,
        CompanyName:
          this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName: this.props.userDetails.firstName,
      },
      isEditDate: true,
      roleId: 2,
      eqipModalVisible: false,
      gateModalVisible: false,
      deactiveDateChecker: new Date(),
      controlledByList: [],
      // Recurrence Compoment Props
      recurrence: Strings.calendarSettings.doseNotRepeat,
      isRecurrence: false,
      daysList: [
        { key: 1, name: "Sunday", label: "S", selected: true },
        { key: 2, name: "Monday", label: "M", selected: true },
        { key: 3, name: "Tuesday", label: "T", selected: true },
        { key: 4, name: "Wednesday", label: "W", selected: true },
        { key: 5, name: "Thursday", label: "T", selected: true },
        { key: 6, name: "Friday", label: "F", selected: true },
        { key: 7, name: "Saturday", label: "S", selected: true },
      ],
      times: "1",
      isMonthFirstCheck: true,
      isMonthSecondCheck: false,
      isMonthThirdCheck: false,
      isYearFirstCheck: true,
      isYearSecondCheck: false,
      isYearThirdCheck: false,
      monthlyDay: "",
      monthlyLastDay: "",
      endDateRecurrence: moment(new Date()).format("MM/DD/YYYY"),
      selectedDaysOccurs: "",
      selectedDayArray: "",
      listSetName: "",
      yearListSetName: "",
      selectedEndDateYear: new Date(),
      timeZoneList: [],
      selectedTimeZone: "",
      selectedTimeZoneId: 0,
      editedZoneID: "",
      isFromDate: false,
      isNetworkCheck: false,
      deliveryWindowTime: "",
      deliveryWindowTimeUnit: "",
      editCurrentDate: "",
      editRequestID: "",
      recurrenceSeriesID: "",
      recurrenceEndDateSeries: "",
      recurrenceDeliverStartDate: "",
      recurrenceDeliverEndDate: "",
      timeZoneParam: RNLocalize.getTimeZone(),
      recurrenceType: Strings.calendarSettings.doseNotRepeat,
      recurrenceEndDateRes: "",
      deliveryStatus: "",
      selectedLocationId: 0,
      locationDropdownList: [],
      selectedLocationNew: "",
      selectedDay: [],
      craneStartDate: null,
      craneEndDate: null,
      timeSlotExplicitlyChanged: false, // Flag to track if user explicitly changed time via time slot picker
      craneEndDate: null,
      timeSlotExplicitlyChanged: false, // Flag to track if user explicitly changed time via time slot picker
    };
    this.searchMember = this.searchMember.bind(this);
  }

  componentDidUpdate(prevProps) {
    this.loadData();
  }

  loadData = async () => {
    const back = await AsyncStorage.getItem("Isback");
    try {
      if (back === "true") {
        const savedData = await AsyncStorage.getItem("DRDateTime");

        if (savedData) {
          const { date, fromTime, toTime } = JSON.parse(savedData);

          // Convert loaded strings to Date objects
          const loadedDate = moment(date, "MM/DD/YYYY").toDate();
          const loadedStartTime = this.parseTimeString(fromTime, loadedDate);
          const loadedEndTime = this.parseTimeString(toTime, loadedDate);

          this.setState(
            {
              selectedDate: date,
              calSelectedDate: loadedDate,
              selectedStartTime: fromTime,
              calSelectedStartTime: loadedStartTime,
              selectedEndTime: toTime,
              calSelectedEndTime: loadedEndTime,
            },
            () => { },
          );

          // Update global variables
          selectedDeliveryDate = loadedDate;
          selectedStartDate = loadedStartTime;
          selectedEndDate = loadedEndTime;

          // Set flag to indicate user explicitly changed time via time slot picker
          this.setState({ timeSlotExplicitlyChanged: true });
        }
        await AsyncStorage.setItem("Isback", "false");
      }
    } catch (error) {
      console.error("[LOAD] Error:", error);
    }
  };
  parseTimeString = (timeString, baseDate) => {
    if (!timeString) return new Date(baseDate);

    const timeParts = timeString.split(/:| /);
    let hours = parseInt(timeParts[0]);
    const minutes = parseInt(timeParts[1]);
    const period = timeParts[2].toLowerCase();

    // Convert to 24-hour format
    if (period === "pm" && hours < 12) hours += 12;
    if (period === "am" && hours === 12) hours = 0;

    return new Date(
      baseDate.getFullYear(),
      baseDate.getMonth(),
      baseDate.getDate(),
      hours,
      minutes,
    );
  };

  UNSAFE_componentWillMount() {
    var date = new Date();
    // old code == > date.setDate(date.getDate() + 1);
    date.setDate(date.getDate());
    date.setHours(0);
    date.setMinutes(0);
    date.setSeconds(0);
    selectedDeliveryDate = date;

    //initial calselectedStartDate should be 7 am
    let initialStartTime = this.state.calSelectedStartTime;
    initialStartTime.setHours(0);
    initialStartTime.setMinutes(0);
    initialStartTime.setSeconds(0);

    //initially end time would be 1 hr ahead then the startTime
    let initialEndTime = this.state.calSelectedEndTime;
    initialEndTime.setHours(initialStartTime.getHours() + 1);
    initialEndTime.setMinutes(0);
    initialEndTime.setSeconds(0);

    this.setState({
      selectedDate: "",
      minimumDate: date,
      calSelectedStartTime: "",
      calSelectedEndTime: "",
      selectedStartTime: "",
      selectedEndTime: "",
    });
    selectedStartDate = initialStartTime;
    selectedEndDate = initialEndTime;
  }
  componentDidMount() {
    if (Platform.OS === "ios") {
      this.networkCheck();
    } else {
      this.getInitialTimeZone();
      this.getProjectSettingTime();
      this.getLocationDetail();
      this.setState({
        deliveryId: this.props.lastCraneRequestId,
      });

      this.checkAndUpdateSelectedDate();

      this.setState({
        editRequestID: this.props.route.params
          ? this.props.route.params != undefined &&
          this.props.route.params.showEditRequestID
          : 1,
      });
      if (
        this.props.route.params?.notificationDetails &&
        this.props.route.params?.notificationDetails.Project
      ) {
        this.setState(
          {
            projectId: this.props.route.params?.notificationDetails.Project.id,
          },
          () => {
            this.loadInitial();
          },
        );
      } else {
        this.setState(
          {
            projectId: this.props.projectDetails.id,
          },
          () => {
            this.loadInitial();
          },
        );
      }
    }
  }

  checkAndUpdateSelectedDate = () => {
    // Set selectedDeliveryDate from navigation params if passed
    if (this.props.route.params?.selectedDate) {
      const navigationDateString = this.props.route.params.selectedDate;
      const parsedMoment = moment(navigationDateString, "YYYY-MM-DD");

      // Create a new date using local date components to avoid timezone issues
      const localDate = new Date(
        parsedMoment.year(),
        parsedMoment.month(), // moment months are 0-based, same as Date constructor
        parsedMoment.date(),
        0,
        0,
        0,
        0,
      );

      selectedDeliveryDate = localDate;

      // Also update component state with the selected date
      this.setState(
        {
          selectedDate: moment(localDate).format("MM/DD/YYYY"),
          calSelectedDate: localDate,
        },
        () => { },
      );
    }
  };

  networkCheck = () => {
    NetInfo.addEventListener((state) => {
      if (!state.isConnected && !state.isInternetReachable) {
        this.setState({ isNetworkCheck: true });
      } else {
        this.setState({ isNetworkCheck: false });
        this.getInitialTimeZone();
        this.getProjectSettingTime();
        this.getLocationDetail();
        this.setState({
          deliveryId: this.props.lastCraneRequestId,
        });

        this.checkAndUpdateSelectedDate();

        this.setState({
          editRequestID: this.props.route.params
            ? this.props.route.params != undefined &&
            this.props.route.params.showEditRequestID
            : 1,
        });
        if (
          this.props.route.params?.notificationDetails &&
          this.props.route.params?.notificationDetails.Project
        ) {
          this.setState(
            {
              projectId:
                this.props.route.params?.notificationDetails.Project.id,
            },
            () => {
              this.loadInitial();
            },
          );
        } else {
          this.setState(
            {
              projectId: this.props.projectDetails.id,
            },
            () => {
              this.loadInitial();
            },
          );
        }
      }
    });
  };

  getProjectSettingTime = () => {
    let URL = `${GET_PROJECT_SETTING_DETAILS}?ProjectId=${this.props.projectDetails.id}`;
    getProjectSettings(
      URL,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.status == 200) {
            this.setState({
              deliveryWindowTime: response.data.data.deliveryWindowTime,
              deliveryWindowTimeUnit: response.data.data.deliveryWindowTimeUnit,
            });
          }
        }
        this.setState({
          editCurrentDate: moment()
            .clone()
            .add(
              this.state.deliveryWindowTime,
              this.state.deliveryWindowTimeUnit,
            ),
        });
      },
    );
  };

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  loadInitial = async () => {
    this.getResponsibleCompanies();
    this.getdefinableList();
    this.getControlledByList();
    //  this.getGateList();
    //   await this.getEquiptypes();
    await this.getLastCraneId();

    // this.searchMember(true, this.props.userDetails.firstName);

    // setTimeout(() => {
    //   this.checkEdit();
    // }, 1000);
  };

  getControlledByList = () => {
    getAllMemberList(
      LIST_ALL_MEMBER +
      this.props.projectDetails.id +
      "/" +
      this.props.projectDetails.ParentCompany.id,
      {},
      () => { },
      (response) => {
        if (response.status) {
          if (response.data.message == Strings.popup.getMemEqui) {
            this.storeContactPerson(response.data.data);
          } else {
            this.setState({
              memberlist: [],
            });
          }
        } else {
          this.showError("error", Strings.errors.failed);
        }
      },
    );
  };

  getInitialTimeZone = () => {
    this.setState({ showLoader: true });
    getTimeZone(
      `${GET_SINGLE_PROJECT}/${this.props.projectDetails.id}`,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.status == 200) {
            let temp =
              response.data.data != null ? response.data.data.TimeZoneId : "";
            this.timeZone(temp);
          }
        }
      },
    );
  };

  getLocationDetail = () => {
    let URL = `${GET_LOCATION_DETAILS}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`;
    getLocationSettings(
      URL,
      {},
      () => null,
      (response) => {
        // Process location data
        let defaultLocation = {};
        let locationDropdownList = [];
        let fullLocationData = []; // Store full location data including equipment and gates

        response.data.data.forEach((item) => {
          // Build location dropdown
          locationDropdownList.push({
            id: item.id,
            label: item.locationPath,
            value: item.locationPath,
          });
          fullLocationData.push(item);

          // Set default location
          if (item.isDefault) {
            defaultLocation = {
              id: item.id,
              label: item.locationPath,
              value: item.locationPath,
            };
          }
        });

        // Update state with all data
        this.setState(
          {
            locationDropdownList,
            selectedLocationNew: defaultLocation?.value || null,
            selectedLocationId: defaultLocation?.id || null,
            fullLocationData, // Store full data for equipment and gate filtering
          },
          () => {
            // Only filter equipment and gates for new bookings (not edit mode)
            // For edit mode, this will be called after getSingleCrane() sets all the edit data
            // Use editDR state as well since it's more reliable
            if (!this.state.editDR) {
              this.filterEquipmentAndGatesByLocation();
            }
          },
        );
      },
    );
  };

  filterEquipmentAndGatesByLocation = (
    overrideEquipId,
    overrideGateId,
    overrideEquipName,
    overrideGateName,
  ) => {
    const {
      selectedLocationId,
      fullLocationData,
      editDR,
      selectedEquipTypeId,
      selectedGateId,
    } = this.state;

    // Use override values if provided (for edit mode), otherwise use state values
    const equipIdToUse =
      overrideEquipId !== undefined ? overrideEquipId : selectedEquipTypeId;
    const gateIdToUse =
      overrideGateId !== undefined ? overrideGateId : selectedGateId;
    const equipNameToUse =
      overrideEquipName !== undefined
        ? overrideEquipName
        : this.state.selectedEquipName;
    const gateNameToUse =
      overrideGateName !== undefined
        ? overrideGateName
        : this.state.selectedGate;

    if (!selectedLocationId || !fullLocationData || !fullLocationData.length) {
      return;
    }

    let equipTypeList = [];
    let storeEquipmentList = [];
    let gateList = [];

    // Find the selected location from stored data
    const selectedLocation = fullLocationData.find(
      (item) => item.id === selectedLocationId,
    );

    // Check if "No Equipment Needed" was selected
    let isNoEquipmentSelected = false;
    if (editDR && equipIdToUse) {
      const selectedIds =
        typeof equipIdToUse === "string"
          ? equipIdToUse.split(",").map((id) => id.trim())
          : [String(equipIdToUse)];
      isNoEquipmentSelected = selectedIds.some(
        (id) => String(id) === "0" || Number(id) === 0,
      );
    }

    // Add "No Equipment Needed" option first (though rarely used for crane requests)
    const NoEquipmentOption = {
      ...NO_EQUIPMENT_NEEDED,
      visible: true, // Initially visible
      disabled: false, // Initially enabled
      selected: isNoEquipmentSelected,
    };
    equipTypeList.push(NoEquipmentOption);

    // Filter equipment based on selected location - ONLY include crane equipment
    if (
      selectedLocation &&
      selectedLocation.EquipmentId &&
      Array.isArray(selectedLocation.EquipmentId)
    ) {
      selectedLocation.EquipmentId.forEach((equip) => {
        const isCrane = equip.PresetEquipmentType?.isCraneType || false;

        // Only include crane equipment for AddCrane
        if (isCrane) {
          // Check if this equipment was previously selected (for edit mode)
          let isSelected = false;
          if (editDR && equipIdToUse) {
            const selectedIds =
              typeof equipIdToUse === "string"
                ? equipIdToUse.split(",").map((id) => id.trim())
                : [String(equipIdToUse)];
            isSelected = selectedIds.some(
              (id) => String(id) === String(equip.id),
            );
          }

          equipTypeList.push({
            id: equip.id,
            value: equip.equipmentName,
            name: equip.equipmentName,
            label: equip.equipmentName,
            selected: isSelected,
            visible: true, // Initially visible
            disabled: false, // Initially enabled
            isCrane: true,
          });
          storeEquipmentList.push(equip);
        }
      });
    }

    // In edit mode: Add previously selected equipment even if not in location's equipment list
    // This handles cases where equipment was deactivated or removed from location after crane request was created
    if (editDR && equipIdToUse && equipNameToUse) {
      const selectedIds =
        typeof equipIdToUse === "string"
          ? equipIdToUse.split(",").map((id) => id.trim())
          : [String(equipIdToUse)];
      const selectedNames = equipNameToUse
        .split(",")
        .map((name) => name.trim());

      selectedIds.forEach((id, index) => {
        const numId = Number(id);
        // Check if this equipment is already in the list
        const alreadyInList = equipTypeList.some(
          (item) => Number(item.id) === numId,
        );

        // If not in list and not "No Equipment Needed", add it
        if (!alreadyInList && numId !== 0) {
          equipTypeList.push({
            id: numId,
            value: selectedNames[index] || `Equipment ${id}`,
            name: selectedNames[index] || `Equipment ${id}`,
            label: selectedNames[index] || `Equipment ${id}`,
            selected: true,
            visible: true,
            disabled: false,
            isCrane: true,
          });
        }
      });
    }

    // Filter gates based on selected location
    // Handle both API structures: gateDetails (from GET_LOCATION_DETAILS) and GateId (from getSingleCrane)
    const gateSource =
      selectedLocation?.gateDetails || selectedLocation?.GateId;
    if (selectedLocation && gateSource && Array.isArray(gateSource)) {
      gateSource.forEach((gate) => {
        // Check if this gate was previously selected (for edit mode)
        let isSelected = false;
        if (editDR && gateIdToUse) {
          const selectedIds =
            typeof gateIdToUse === "string"
              ? gateIdToUse.split(",").map((id) => id.trim())
              : [String(gateIdToUse)];
          isSelected = selectedIds.some((id) => String(id) === String(gate.id));
        }

        gateList.push({
          id: gate.id,
          name: gate.gateName,
          value: gate.gateName,
          label: gate.gateName,
          selected: isSelected,
        });
      });
    }

    // In edit mode: Add previously selected gates even if not in location's gate list
    // This handles cases where gates were deactivated or removed from location after crane request was created
    if (editDR && gateIdToUse && gateNameToUse) {
      const selectedIds =
        typeof gateIdToUse === "string"
          ? gateIdToUse.split(",").map((id) => id.trim())
          : [String(gateIdToUse)];
      const selectedNames = gateNameToUse.split(",").map((name) => name.trim());

      selectedIds.forEach((id, index) => {
        const numId = Number(id);
        // Check if this gate is already in the list
        const alreadyInList = gateList.some(
          (item) => Number(item.id) === numId,
        );

        // If not in list, add it
        if (!alreadyInList) {
          gateList.push({
            id: numId,
            name: selectedNames[index] || `Gate ${id}`,
            value: selectedNames[index] || `Gate ${id}`,
            label: selectedNames[index] || `Gate ${id}`,
            selected: true,
          });
        }
      });
    }

    // Update equipment and gate lists
    const uniqueEquipTypeList = [
      ...new Map(equipTypeList.map((item) => [item.id, item])).values(),
    ];
    const uniqueGateList = [
      ...new Map(gateList.map((item) => [item.id, item])).values(),
    ];

    // Build selectedEquipmentList from items marked as selected (critical for edit mode display)
    const selectedEquipmentList = uniqueEquipTypeList.filter(
      (item) => item.selected === true,
    );
    const selectedEquipmentNames = selectedEquipmentList
      .map((item) => item.name)
      .join(",");
    const selectedEquipmentIds = selectedEquipmentList
      .map((item) => item.id)
      .join(",");

    // Extract selected gate items for edit mode
    const selectedGateItems = uniqueGateList.filter(
      (item) => item.selected === true,
    );
    const selectedGateNames = selectedGateItems
      .map((item) => item.name)
      .join(",");
    const selectedGateIds = selectedGateItems.map((item) => item.id).join(",");

    this.setState(
      {
        equipTypeList: uniqueEquipTypeList,
        storeEquipmentList,
        gateList: uniqueGateList,
        isCraneEquipList: [...equipTypeList], // Store crane equipment list
        selectedEquipmentList: selectedEquipmentList, // This is essential for dropdown to show selections
      },
      () => {
        if (this.state.editDR) {
          // Only update display values if we found matches in the filtered list
          // Don't clear existing values if filtered list is empty (preserves API data)
          if (selectedEquipmentList.length > 0) {
            // Update the selected equipment display string based on filtered equipment
            this.setState({
              selectedEquipName: selectedEquipmentNames,
              selectedEquipTypeId: selectedEquipmentIds,
            });
            // Trigger the callback to update the selected equipment display
            this.getSelectedEquipmentList(this.state.equipTypeList);
          }

          if (selectedGateItems.length > 0) {
            // Update the selected gate display string based on filtered gates
            this.setState({
              selectedGate: selectedGateNames,
              selectedGateId: selectedGateIds,
            });
          }
        }
      },
    );
  };

  timeZone = (timeZone) => {
    getTimeZone(
      GET_TIMEZONE,
      {},
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.status == 200) {
            let list = [];
            response.data.data.forEach((e) => {
              let temp = {};
              temp = {
                id: e.id,
                value: e.location,
                label: e.location,
                selected: false,
                name: e.location,
              };
              list.push(temp);
            });
            const match =
              timeZone != null
                ? list.find((element) => element.id === timeZone)
                : null;
            this.setState(
              {
                showLoader: false,
                timeZoneList: list,
                selectedTimeZone: match ? match.value : null,
                selectedTimeZoneId: match ? match.id : "",
              },
              () => this.checkEdit(),
            );
          }
        }
      },
    );
  };

  storeContactPerson = (data) => {
    let memberList = [];

    for (let item of data) {
      if (item.User.firstName != null) {
        memberList.push({
          label:
            item.User.firstName +
            " " +
            item.User.lastName +
            " (" +
            item.User.email +
            ")",
          value: item.User.email,
          id: item.id,
          name: item.User.email,
        });
      } else {
        memberList.push({
          label: item.User.email,
          value: item.User.email,
          id: item.id,
        });
      }
    }
    this.setState({ controlledByList: memberList });
  };

  getRole = async () => {
    let url =
      GET_PROJECT_ROLE +
      this.state.projectId +
      "/" +
      this.props.projectDetails.ParentCompany.id;
    this.setState({ showLoader: true });
    let response = await _getData(url);
    this.setState({ showLoader: false });
    if (response.data) {
      this.setState({
        roleId: response.data.data.RoleId,
        editedZoneID: response.data.data.TimeZoneId,
      });
    }
  };
  getLastCraneId = () => {
    let url = `${GET_LAST_CRANE_ID}${this.state.projectId}/${this.props.projectDetails.ParentCompany.id}`;
    getLastCraneRequestId(
      url,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.status == 200) {
            if (response.data) {
              this.setState({
                deliveryId: response.data.lastId.CraneRequestId,
              });
            }
          } else if (response.status == 400) {
            this.showToaster("error", response.data.message);
          } else {
            this.showToaster("error", Strings.errors.something);
          }
        }
      },
    );
  };
  checkEdit = () => {
    if (this.props.editedCrane.item) {
      this.setState({ showLoader: true });
      let data = this.props.editedCrane.item;

      this.setState(
        {
          deliveryId: data.CraneRequestId,
          DeliveryRequestId: this.props.craneRequestId,
          editDR: true,
        },
        () => {
          this.getRole();
          this.getSingleCrane();
        },
      );
      trackScreen("Edit Pick Request");
      this.props.editCraneRequest({});
    } else {
      trackScreen("New Pick Request");
    }
  };

  getSingleCrane = () => {
    let url = `${GET_SINGLE_CRANE}${this.state.deliveryId}/${this.state.projectId}/${this.props.projectDetails.ParentCompany.id}/`;
    getSingleCraneRequest(
      url,
      {},
      () => null,
      (craneResponse) => {
        this.setState({
          showLoader: false,
        });
        const tz = this.state.timeZoneList.find(
          (element) => element.id === this.state.editedZoneID,
        );

        if (craneResponse.toString() == Strings.errors.timeout) {
          this.showToaster("error", Strings.errors.checkInternet);
        } else if (craneResponse.status) {
          if (craneResponse.status == 200) {
            let data = craneResponse.data.data;
            let selectedCompanyList = [];
            let selectedDefinableList = [];
            let selectedEquipmentList = [];

            this.setState({
              recurrenceType:
                craneResponse.data.data.recurrence != null
                  ? craneResponse.data.data.recurrence.recurrence
                  : Strings.calendarSettings.doseNotRepeat,
              deliveryStatus:
                craneResponse.data.data.status != null
                  ? craneResponse.data.data.status
                  : null,
            });
            for (let item of this.state.responisbleCompanyList) {
              if (
                data.companyDetails.some(
                  (person) => person.Company.companyName === item.name,
                )
              ) {
                selectedCompanyList.push({
                  id: item.id,
                  label: item.name,
                  name: item.name,
                  selected: true,
                });
              } else {
                selectedCompanyList.push({
                  id: item.id,
                  label: item.name,
                  name: item.name,
                  selected: false,
                });
              }
            }
            for (let item of this.state.definableList) {
              if (
                data.defineWorkDetails.some(
                  (person) => person.DeliverDefineWork.DFOW === item.name,
                )
              ) {
                selectedDefinableList.push({
                  id: item.id,
                  label: item.name,
                  name: item.name,
                  selected: true,
                });
              } else {
                selectedDefinableList.push({
                  id: item.id,
                  label: item.name,
                  name: item.name,
                  selected: false,
                });
              }
            }
            // Build equipment list strictly from the response location's equipment set
            const responseLocationId = data.location?.id || null;
            let locationEquipList = [];
            let locationGateList = [];
            if (
              responseLocationId &&
              Array.isArray(this.state.fullLocationData)
            ) {
              const loc = this.state.fullLocationData.find(
                (l) => l.id === responseLocationId,
              );
              if (loc) {
                if (Array.isArray(loc.EquipmentId)) {
                  locationEquipList = loc.EquipmentId.map((eq) => ({
                    id: eq.id,
                    value: eq.equipmentName,
                    name: eq.equipmentName,
                    label: eq.equipmentName,
                    selected: false,
                    isCrane: eq?.PresetEquipmentType?.isCraneType || false,
                  }));
                }
                if (Array.isArray(loc.gateDetails)) {
                  locationGateList = loc.gateDetails.map((gate) => ({
                    id: gate.id,
                    name: gate.gateName,
                    value: gate.gateName,
                    label: gate.gateName,
                    selected: false,
                  }));
                }
              }
            }

            const responseEquipIds = new Set(
              (data.equipmentDetails || []).map((entry) =>
                String(entry?.Equipment?.id),
              ),
            );

            selectedEquipmentList = locationEquipList.map((item) => ({
              ...item,
              selected: responseEquipIds.has(String(item.id)),
            }));

            let selectedPersons = [];
            for (let item of data.memberDetails) {
              if (item.Member != null) {
                if (item.Member.User.firstName != null) {
                  selectedPersons.push({
                    id: `${item.Member.User.firstName} ${item.Member.User.lastName}(${item.Member.User.email})`,
                    email: item.Member.User.email,
                    userId: item.Member.id,
                  });
                } else {
                  selectedPersons.push({
                    id: item.Member.User.email,
                    email: item.Member.User.email,
                    userId: item.Member.id,
                  });
                }
              }
            }

            const startMoment = moment.parseZone(data.craneDeliveryStart);
            const endMoment = moment.parseZone(data.craneDeliveryEnd);

            // Extract values
            const startHours = startMoment.format("hh"); // 12-hour format
            const startMinutes = startMoment.format("mm");
            const ampm = startMoment.format("a"); // am/pm

            const endHours = endMoment.format("hh");
            const endMinutes = endMoment.format("mm");
            const endampm = endMoment.format("a");

            // Year, month (0-based for Date), date
            const fullYear = startMoment.year();
            const startMonthIndex = startMoment.month(); // 0-based
            const endMonthIndex = endMoment.month(); // 0-based
            const startDate = startMoment.date();
            const endDate = endMoment.date();

            // Formatted strings
            const strTime = `${startHours}:${startMinutes} ${ampm}`;
            const endTime = `${endHours}:${endMinutes} ${endampm}`;

            const delStartTime = new Date(
              fullYear,
              startMonthIndex,
              startDate,
              new Date(data.craneDeliveryStart).getHours(),
              new Date(data.craneDeliveryStart).getMinutes(),
            );

            const delEndTime = new Date(
              fullYear,
              endMonthIndex,
              endDate,
              new Date(data.craneDeliveryEnd).getHours(),
              new Date(data.craneDeliveryEnd).getMinutes(),
            );
            let selectedDateRequest = `${startMonthIndex + 1
              }/${startDate}/${fullYear}`;
            selectedEndDate = delEndTime;
            selectedStartDate = delStartTime;
            selectedDeliveryDate = new Date(data.craneDeliveryStart);
            if (data.status == "Delivered" || data.status == "Completed") {
              if (this.state.roleId == 3 || this.state.roleId == 4) {
                if (this.state.editDR) {
                  this.setState({ isEditDate: false });
                }
              }
            }
            this.setState(
              {
                selectedDayArray: data.recurrence.days,
                selectedDay: data.recurrence.days,
                description: data.description,
                deliveryId: data.CraneRequestId,
                craneStartDate: data.craneDeliveryStart,
                craneEndDate: data.craneDeliveryEnd,
                deliverydetails:
                  moment(data.craneDeliveryStart).format(
                    "MMMM DD,YYYY, hh:mm:ss a",
                  ) +
                  " " +
                  "-" +
                  " " +
                  moment(data.craneDeliveryEnd).format(
                    "MMMM DD,YYYY, hh:mm:ss a",
                  ),
                responsiblecompany: data.companyDetails
                  .map((e) => e.Company.companyName)
                  .join(",")
                  .toString(),
                selectedGateId:
                  locationGateList && locationGateList.length > 0
                    ? locationGateList
                      .filter((g) =>
                        (data.gateDetails || []).some(
                          (e) => String(e?.Gate?.id) === String(g.id),
                        ),
                      )
                      .map((g) => g.id)
                      .join(",")
                    : 0,
                selectedGate:
                  locationGateList && locationGateList.length > 0
                    ? locationGateList
                      .filter((g) =>
                        (data.gateDetails || []).some(
                          (e) => String(e?.Gate?.id) === String(g.id),
                        ),
                      )
                      .map((g) => g.name)
                      .join(",")
                      .toString()
                    : null,
                selectedEquipTypeId:
                  data.equipmentDetails && data.equipmentDetails.length > 0
                    ? data.equipmentDetails
                      .map((e) => e?.Equipment?.id)
                      .filter(Boolean)
                      .join(",")
                    : 0,
                selectedEquipName:
                  data.equipmentDetails && data.equipmentDetails.length > 0
                    ? data.equipmentDetails
                      .map((e) => e?.Equipment?.equipmentName)
                      .filter(Boolean)
                      .join(",")
                    : null,
                // selectedResponsibleCompany: data.companyDetails.map(e => e.Company.companyName).join(',').toString(),
                selectedResponsibleCompanyId: (data.companyDetails || [])
                  .map((e) => e?.Company?.id)
                  .filter(Boolean)
                  .join(","),
                selectedDefinableList: (data.defineWorkDetails || [])
                  .map((e) => e?.DeliverDefineWork?.DFOW)
                  .filter(Boolean)
                  .join(","),
                selectedDefinableId: (data.defineWorkDetails || [])
                  .map((e) => e?.DeliverDefineWork?.id)
                  .filter(Boolean)
                  .join(","),
                delVehicleDetails: data.vehicleDetails,
                appliedby: data.createdUserDetails.User.firstName,
                additionalNotes: data.additionalNotes,
                selectstatus: data.status,
                updatestatus: data.status,
                escortNeeded: data.isEscortNeeded,
                definableList: selectedDefinableList,
                responisbleCompanyList: selectedCompanyList,
                selectedEndTime: endTime,
                calSelectedEndTime: new Date(data.craneDeliveryEnd),
                calSelectedStartTime: new Date(data.craneDeliveryStart),
                selectedStartTime: strTime,
                selectedDate: moment(selectedDateRequest).format("MM/DD/YYYY"),
                calSelectedDate: selectedDeliveryDate,
                selectedItem: selectedPersons,
                showMultipleSec: true,
                comparision: data,
                bookingId: data.id,
                pickFrom: data.pickUpLocation,
                pickTo: data.dropOffLocation,
                deactiveDateChecker: selectedDeliveryDate,
                selectedTimeZoneId: tz ? tz.id : "",
                equipTypeList:
                  selectedEquipmentList.length > 0
                    ? selectedEquipmentList
                    : this.state.equipTypeList,
                // Ensure selectedEquipName reflects the chosen equipment on edit
                selectedEquipName:
                  selectedEquipmentList.length > 0
                    ? selectedEquipmentList
                      .filter((e) => e.selected)
                      .map((e) => e.name)
                      .join(",")
                    : this.state.selectedEquipName,
                // Bind location and gates to the response location
                selectedLocationId:
                  responseLocationId || this.state.selectedLocationId,
                gateList:
                  locationGateList && locationGateList.length > 0
                    ? locationGateList
                    : this.state.gateList,
                recurrenceSeriesID:
                  craneResponse.data.data.recurrence != null
                    ? craneResponse.data.data.recurrence.id
                    : null,
                recurrenceEndDateSeries:
                  craneResponse.data.data.recurrence != null
                    ? craneResponse.data.data.recurrence.recurrenceEndDate
                    : null,
                recurrenceDeliverStartDate:
                  craneResponse.data.data.craneDeliveryStart != null
                    ? craneResponse.data.data.craneDeliveryStart
                    : null,
                recurrenceDeliverEndDate:
                  craneResponse.data.data.craneDeliveryEnd != null
                    ? craneResponse.data.data.craneDeliveryEnd
                    : null,
                recurrenceEndDateRes:
                  craneResponse.data.data.recurrence != null
                    ? craneResponse.data.data.recurrence
                    : null,
                endDateRecurrence:
                  craneResponse.data.data.recurrence != null
                    ? moment(
                      craneResponse.data.data.recurrence.recurrenceEndDate,
                    ).format("MM/DD/YYYY")
                    : null,
                selectedEndDateYear:
                  this.state.editDR == false
                    ? craneResponse.data.data.recurrence != null
                      ? craneResponse.data.data.recurrence.recurrenceEndDate
                      : null
                    : new Date(),
                recurrence:
                  craneResponse.data.data.recurrence != null
                    ? craneResponse.data.data.recurrence.recurrence
                    : this.state.recurrence,
                selectedLocationNew:
                  data.location?.locationPath != null
                    ? data.location?.locationPath
                    : null,
                editDR: true,
              },
              () => {
                // Extract equipment and gate values from API response to pass to filter
                const equipTypeId =
                  data.equipmentDetails.length > 0
                    ? data.equipmentDetails.map((e) => e.Equipment.id).join(",")
                    : 0;
                const equipName =
                  data.equipmentDetails.length > 0
                    ? data.equipmentDetails
                      .map((e) => e.Equipment.equipmentName)
                      .join(",")
                      .toString()
                    : null;
                const gateId =
                  data.gateDetails.length > 0
                    ? data.gateDetails.map((e) => e.Gate.id).join(",")
                    : 0;
                const gateName =
                  data.gateDetails.length > 0
                    ? data.gateDetails
                      .map((e) => e.Gate.gateName)
                      .join(",")
                      .toString()
                    : null;

                // Update fullLocationData with the location from the API response
                // This ensures the location data (including GateId and EquipmentId) is available for filtering
                if (data.location) {
                  const existingLocationIndex =
                    this.state.fullLocationData?.findIndex(
                      (loc) => loc.id === data.location.id,
                    );
                  if (existingLocationIndex >= 0) {
                    // Update existing location
                    const updatedFullLocationData = [
                      ...(this.state.fullLocationData || []),
                    ];
                    updatedFullLocationData[existingLocationIndex] =
                      data.location;
                    this.setState(
                      { fullLocationData: updatedFullLocationData },
                      () => {
                        // Pass the API values directly to avoid async state issues
                        this.filterEquipmentAndGatesByLocation(
                          equipTypeId,
                          gateId,
                          equipName,
                          gateName,
                        );
                      },
                    );
                  } else {
                    // Add new location
                    this.setState(
                      {
                        fullLocationData: [
                          ...(this.state.fullLocationData || []),
                          data.location,
                        ],
                      },
                      () => {
                        // Pass the API values directly to avoid async state issues
                        this.filterEquipmentAndGatesByLocation(
                          equipTypeId,
                          gateId,
                          equipName,
                          gateName,
                        );
                      },
                    );
                  }
                } else {
                  // No location in response, just filter with existing data
                  this.filterEquipmentAndGatesByLocation(
                    equipTypeId,
                    gateId,
                    equipName,
                    gateName,
                  );
                }
              },
            );
          } else if (craneResponse.status == 400) {
          }
        }
      },
    );
  };
  getResponsibleCompanies = () => {
    //#fff;
    getNewCompanyList(
      GET_NEW_COMPANIES +
      this.state.projectId +
      `/${this.props.projectDetails.ParentCompany.id}`,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.data.length !== 0) {
            let responsibleCompany = [];
            for (let item of response.data.data) {
              responsibleCompany.push({
                id: item.id,
                name: item.companyName,
                label: item.companyName,
                selected:
                  this.props.selected_Company.companyName === item.companyName
                    ? true
                    : false,
              });
            }
            this.setState({
              responisbleCompanyList: responsibleCompany,
            });
          }
        }
      },
    );
  };
  getdefinableList = () => {
    getDefinableFeature(
      DEFINABLE_FEATURE +
      this.state.projectId +
      "/" +
      this.props.projectDetails.ParentCompany.id,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.data.length !== 0) {
            let definableList = [];

            for (let item of response.data.data) {
              definableList.push({
                id: item.id,
                name: item.DFOW,
                label: item.DFOW,
                selected: false,
              });
            }

            this.setState({
              definableList: definableList,
            });
          }
        }
      },
    );
  };

  //   getGateList = () => {
  //     let param = { isFilter: true, showActivatedAlone: true };
  //     getGateList(
  //       GET_GATE_LIST +
  //         this.state.projectId +
  //         "/0/0/" +
  //         this.props.projectDetails.ParentCompany.id,
  //       param,
  //       () => null,
  //       (response) => {
  //         if (response.status) {
  //           if (response.data.data.length !== 0) {
  //             let gateList = [];

  //             for (let item of response.data.data) {
  //               gateList.push({
  //                 id: item.id,
  //                 name: item.gateName,
  //                 value: item.gateName,
  //                 label: item.gateName,
  //                 selected: false,
  //               });
  //             }

  //             this.setState({
  //               gateList: gateList,
  //             });
  //           }
  //         }
  //       }
  //     );
  //   };

  // getEquiptypes = () => {
  //   let param = { showActivatedAlone: true };
  //   let url = `${CRANE_EQUIPMENT_LIST}${this.state.projectId}/0/0/${this.props.projectDetails.ParentCompany.id}`;
  //   getCraneEquipmentList(
  //     url,
  //     param,
  //     () => null,
  //     (response) => {
  //       if (response.status) {
  //         if (response.data.data.length !== 0) {
  //           let equipTypelist = [];

  //           for (let item of response.data.data.rows) {
  //             equipTypelist.push({
  //               id: item.id,
  //               value: item.equipmentName,
  //               name: item.equipmentName,
  //               label: item.equipmentName,
  //               selected: false,
  //             });
  //           }

  //           this.setState({
  //             equipTypeList: equipTypelist,
  //           });
  //         }
  //       }
  //     }
  //   );
  // };

  getSelectedCompanyList = (data) => {
    this.setState({
      responisbleCompanyList: data,
    });
  };
  getSelectedDefinableList = (data) => {
    this.setState({
      definableList: data,
    });
  };

  getSelectedEquipmentList = (data) => {
    if (data && data.length > 0) {
      // Create a new array with the updated selection state
      let updatedList = data.map((item) => {
        if (item && typeof item.selected !== "undefined") {
          return {
            ...item,
            selected: item.selected,
            disabled: false, // Initialize disabled state
          };
        }
        return item;
      });

      // Check if "No Equipment Needed" is selected
      const noEquipmentNeeded = updatedList.find(
        (item) => item && item.id === 0,
      ); // NO_EQUIPMENT_NEEDED has id: 0
      const otherEquipmentSelected = updatedList.some(
        (item) => item && item.id !== 0 && item.selected === true,
      );

      // Check if "Select All" scenario (all non-"No Equipment" items selected)
      const nonNoEquipmentItems = updatedList.filter(
        (item) => item && item.id !== 0,
      );
      const allOtherEquipmentSelected =
        nonNoEquipmentItems.length > 0 &&
        nonNoEquipmentItems.every((item) => item && item.selected === true);

      // Apply mutual exclusion logic
      if (noEquipmentNeeded && noEquipmentNeeded.selected === true) {
        // RULE 3: When "No Equipment Needed" is selected
        // - Deselect all other equipment options
        // - Disable all other equipment options
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            if (item.id === 0) {
              return {
                ...item,
                selected: true,
                disabled: false,
                visible: true,
              };
            } else {
              return {
                ...item,
                selected: false, // Automatically deselect
                disabled: true, // Disable other options when "No Equipment" is selected
                visible: true,
              };
            }
          }
          return item;
        });
      } else if (allOtherEquipmentSelected && !noEquipmentNeeded?.selected) {
        // RULE 1: When "Select All" is chosen
        // - Select all equipment EXCEPT "No Equipment Needed"
        // - Hide "No Equipment Needed" from visible options
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            if (item.id === 0) {
              return {
                ...item,
                selected: false,
                disabled: false,
                visible: false, // Hide from visible options
              };
            } else {
              return {
                ...item,
                selected: true,
                disabled: false,
                visible: true,
              };
            }
          }
          return item;
        });
      } else if (otherEquipmentSelected) {
        // RULE 4: When any individual equipment is selected
        // - Automatically unselect "No Equipment Needed"
        // - Keep "No Equipment Needed" visible but unselected
        // - Allow multiple selection of other equipment
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            if (item.id === 0) {
              return {
                ...item,
                selected: false, // Automatically unselect
                disabled: false,
                visible: true, // Keep visible but unselected
              };
            } else {
              return {
                ...item,
                disabled: false,
                visible: true,
              };
            }
          }
          return item;
        });
      } else {
        // RULE 2: When "Select All" is unselected or no selections
        // - Show all equipment options including "No Equipment Needed"
        // - Enable all options
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            return {
              ...item,
              disabled: false,
              visible: true,
            };
          }
          return item;
        });
      }

      const selectedEquipment = updatedList.filter(
        (item) => item && item.selected === true,
      );

      // Update the state with the new data and clear time slots
      this.setState(
        {
          equipTypeList: updatedList,
          selectedEquipmentList: selectedEquipment,
          isAssociatedWithCraneRequest: true, // Always true for crane requests
          // Clear existing time slots when equipment selection changes
          selectedStartTime: '',
          selectedEndTime: '',
        },
        () => {
          // Force a re-render of the MultiSelectDropDown component
          if (this.multiSelectEquipRef) {
            this.multiSelectEquipRef.setState({
              dataItems: updatedList,
              isAllChecked:
                allOtherEquipmentSelected && !noEquipmentNeeded?.selected,
            });
          }
        },
      );
    } else {
      // Handle case where no data is provided
      this.setState({
        equipTypeList: [],
        selectedEquipmentList: [],
        isAssociatedWithCraneRequest: true, // Always true for crane requests
      });
    }
  };

  searchMember = (initial, text) => {
    searchMember(
      SEARCH_MEMBER +
      this.state.projectId +
      `/${text}` +
      `/${this.props.projectDetails.ParentCompany.id}`,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.length !== 0) {
            let searchList = [];
            let selectedItem = [];
            let userId = -1;

            for (let item of response.data) {
              if (
                this.state.selectedItem.filter(
                  (data) => data.email == item.emails,
                ) == true ||
                (item.emails == this.props.userDetails.email) == true
              ) {
              } else {
                searchList.push({
                  id: `${item.email}`,
                  email: item.email,
                  userId: item.id,
                });
              }

              if (
                (item.emails == this.props.userDetails.email) == true &&
                initial == true
              ) {
                selectedItem.push({
                  id: item.email,
                  email: item.email,
                  userId: item.id,
                });
                userId = item.id;
              } else {
              }
            }

            if (userId == -1) {
              this.setState({
                responsiblePersonData: searchList,
                selectedItem: selectedItem,
              });
            } else {
              this.setState({
                responsiblePersonData: searchList,
                selectedItem: selectedItem,
                selectedItemIndex: userId,
              });
            }
          }
        }
      },
    );
  };

  // Helper function to extract timezone offset from selectedTimeZone string
  getTimezoneOffset = () => {
    const timeZoneString = this.state.selectedTimeZone;
    if (!timeZoneString) return "+00:00";

    // Extract offset from strings like "(UTC-10:00) Hawaii" or "(UTC+05:30) India"
    const offsetMatch = timeZoneString.match(/\(UTC([+-]\d{1,2}:\d{2})\)/);
    if (offsetMatch) {
      return offsetMatch[1];
    }
    return "+00:00";
  };

  // Helper function to format date with timezone offset
  formatDateWithTimezone = (date) => {
    if (!date) return null;

    const offset = this.getTimezoneOffset();

    // Use local date components to avoid timezone conversion issues
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${offset}`;
  };

  submit = () => {
    const {
      recurrence,
      times,
      isMonthFirstCheck,
      isMonthSecondCheck,
      isYearFirstCheck,
      isYearSecondCheck,
      selectedDayArray,
      endDateRecurrence,
      editDR,
      listSetName,
      yearListSetName,
      selectedEndDateYear,
    } = this.state;
    let responsibleData = [];

    let selectedArrayItem = [];
    selectedArrayItem.push({
      id: `${this.props.responsiblePersonData.name}`,
      email: this.props.userDetails.email,
      userId: this.props.responsiblePersonData.id,
    });
    let responsibleValue = true;
    if (this.state.selectedItemList.length == 0) {
      for (let item of this.state.selectedItem) {
        responsibleData.push({ userId: item.userId });
      }
    } else if (
      this.state.selectedItemList != undefined ||
      this.state.selectedItemList != null
    ) {
      let entities = this.state.selectedItemList.entities;
      let results = this.state.selectedItemList.result;
      for (let item of results) {
        if (item == this.props.userDetails.email) {
          responsibleData.push({ userId: this.state.selectedItemIndex });
        } else {
          if (entities.item[item].userId == undefined) {
            responsibleValue = false;
          } else {
            responsibleData.push({
              userId: entities.item[item].userId,
            });
          }
        }
      }
    }

    let isResponseValue = false;

    let isGateType = false;
    let isEquipType = false;
    if (
      this.state.calSelectedDate > new Date() &&
      this.state.calSelectedDate > this.state.deactiveDateChecker
    ) {
      let equipTypeData = this.state.equipTypeList;
      isEquipType = equipTypeData.find((item) => {
        if (item.id == this.state.selectedEquipTypeId) {
          return true;
        }
      });

      let gateTypeData = this.state.gateList;
      isGateType = gateTypeData.find((item) => {
        if (item.id == this.state.selectedGateId) {
          return true;
        }
      });
    }

    if (
      this.state.calSelectedDate > new Date() &&
      this.state.calSelectedDate > this.state.deactiveDateChecker
    ) {
      const arr1 = this.state.controlledByList;
      const arr2 = responsibleData;
      const result = arr1.filter((o) =>
        arr2.some(({ userId }) => o.id === userId),
      );

      if (responsibleData.length !== result.length) {
        isResponseValue = true;
      }
    }

    Keyboard.dismiss();
    let selectedCompanyList = [];
    let selectedDefinableList = [];
    let selectedEquipmentList = [];

    for (let item of this.state.responisbleCompanyList) {
      if (item.selected == true) {
        selectedCompanyList.push(item.id);
      }
    }

    for (let item of this.state.definableList) {
      if (item.selected == true) {
        selectedDefinableList.push(item.id);
      }
    }

    for (let item of this.state.equipTypeList) {
      if (item.selected == true) {
        selectedEquipmentList.push(item.id);
      }
    }

    if (
      isEmpty(this.state.description) ||
      this.state.description.trim() == ""
    ) {
      this.showToaster("error", Strings.errors.emptyDescription);
    } else if (this.state.description.length < 3) {
      this.showToaster("error", "Description " + Strings.errors.lengthError);
    } else if (isEmpty(selectedCompanyList)) {
      this.showToaster("error", Strings.errors.emptyRespCompany);
    } else if (
      isEmpty(this.state.selectedItem) &&
      isEmpty(this.state.selectedItemList)
    ) {
      this.showToaster("error", Strings.errors.emptyResponsiblePerson);
    } else if (!responsibleValue) {
      this.showToaster("error", Strings.errors.validResponsiblePerson);
    } else if (isEmpty(selectedDeliveryDate.toString())) {
      this.showToaster("error", Strings.errors.emptydeliverData);
    } else if (isEmpty(selectedStartDate.toString())) {
      this.showToaster("error", Strings.errors.emptyStartTime);
    } else if (isEmpty(selectedEndDate.toString())) {
      this.showToaster("error", Strings.errors.emptyEndTime);
    } else if (
      !this.state.editDR &&
      selectedDeliveryDate < new Date().setHours(0, 0, 0, 0)
    ) {
      // Check if selected date is in the past (only for new entries, not for edits)
      this.showToaster("error", Strings.errors.futureDate);
    } else {
      // Check if selected time is in the past when date is today (only for new entries)
      if (
        !this.state.editDR &&
        moment(selectedDeliveryDate).format("MM/DD/YYYY") ===
        moment(new Date()).format("MM/DD/YYYY")
      ) {
        // Create a date object with selected date and time
        const selectedDateTime = new Date(
          selectedDeliveryDate.getFullYear(),
          selectedDeliveryDate.getMonth(),
          selectedDeliveryDate.getDate(),
          this.state.calSelectedStartTime.getHours(),
          this.state.calSelectedStartTime.getMinutes(),
          0,
          0
        );

        // Get current time with same precision
        const currentDateTime = new Date();
        currentDateTime.setSeconds(0, 0); // Reset seconds and milliseconds for fair comparison

        // Check if selected time is in the past
        if (selectedDateTime < currentDateTime) {
          this.showToaster("error", Strings.errors.futureTime);
          return;
        }
      }

      if (isEmpty(selectedEquipmentList)) {
        this.showToaster("error", Strings.errors.emptyEquip);
      } else if (isEmpty(this.state.selectedGate)) {
        this.showToaster("error", Strings.errors.emptyGate);
      } else if (this.state.selectedDate === "") {
        this.showToaster("error", "Please select date and time");
      } else if (isEmpty(this.state.pickFrom)) {
        this.showToaster("error", Strings.errors.emptyPickFrom);
      } else if (isEmpty(this.state.pickTo)) {
        this.showToaster("error", Strings.errors.emptyPickTo);
      } else if (
        this.state.editDR == true &&
        this.state.calSelectedDate > new Date() &&
        this.state.calSelectedDate > this.state.deactiveDateChecker &&
        (!isEquipType || !isGateType || isResponseValue)
      ) {
        if (!isEquipType) {
          this.showToaster("error", Strings.errors.emptyEquipValue);
        } else if (!isGateType) {
          this.showToaster("error", Strings.errors.emptyGateValue);
        } else if (isResponseValue) {
          this.showToaster("error", Strings.errors.emptyResponseValue);
        }
      } else {
        let values = [];

        if (this.state.selectedItemList.length == 0) {
          for (let item of this.state.selectedItem) {
            values.push(item.userId);
          }
        } else if (
          this.state.selectedItemList != undefined ||
          this.state.selectedItemList != null
        ) {
          let entities = this.state.selectedItemList.entities;
          let results = this.state.selectedItemList.result;
          for (let item of results) {
            if (item == this.props.userDetails.email) {
              values.push(this.state.selectedItemIndex);
            } else {
              if (entities.item[item].userId != undefined) {
                values.push(entities.item[item].userId);
              }
            }
          }
        }
        // For edit mode, preserve original delivery dates if no time changes were made
        let startTime, endTime;

        if (
          this.state.editDR &&
          this.state.craneStartDate &&
          this.state.craneEndDate
        ) {
          // FOOLPROOF APPROACH: Check if user explicitly changed times via time slot picker
          const userExplicitlyChangedTime =
            this.state.timeSlotExplicitlyChanged === true;

          // AGGRESSIVE PRESERVATION: If user didn't explicitly change time, always preserve
          let shouldPreserve;
          if (!userExplicitlyChangedTime) {
            shouldPreserve = true;
          } else {
            // Only use comparison logic if user explicitly changed time
            const originalStartTime = moment(this.state.craneStartDate)
              .local()
              .format("HH:mm");
            const originalEndTime = moment(this.state.craneEndDate)
              .local()
              .format("HH:mm");
            const currentStartTime = moment(selectedStartDate).format("HH:mm");
            const currentEndTime = moment(selectedEndDate).format("HH:mm");
            const timesMatch =
              originalStartTime === currentStartTime &&
              originalEndTime === currentEndTime;
            shouldPreserve = timesMatch;
          }
          if (shouldPreserve) {
            startTime = new Date(this.state.craneStartDate);
            endTime = new Date(this.state.craneEndDate);
          } else {
            startTime = new Date(
              selectedDeliveryDate.getFullYear(),
              selectedDeliveryDate.getMonth(),
              selectedDeliveryDate.getDate(),
              new Date(selectedStartDate).getHours(),
              new Date(selectedStartDate).getMinutes(),
              0,
            );
            endTime = new Date(
              selectedDeliveryDate.getFullYear(),
              selectedDeliveryDate.getMonth(),
              selectedDeliveryDate.getDate(),
              new Date(selectedEndDate).getHours(),
              new Date(selectedEndDate).getMinutes(),
              0,
            );
          }
        } else {
          // New crane request, construct times normally
          startTime = new Date(
            selectedDeliveryDate.getFullYear(),
            selectedDeliveryDate.getMonth(),
            selectedDeliveryDate.getDate(),
            new Date(selectedStartDate).getHours(),
            new Date(selectedStartDate).getMinutes(),
            0,
          );
          endTime = new Date(
            selectedDeliveryDate.getFullYear(),
            selectedDeliveryDate.getMonth(),
            selectedDeliveryDate.getDate(),
            new Date(selectedEndDate).getHours(),
            new Date(selectedEndDate).getMinutes(),
            0,
          );
        }

        let endRecurrenceTime = new Date(
          selectedEndDateYear.getFullYear(),
          selectedEndDateYear.getMonth(),
          selectedEndDateYear.getDate(),
          new Date(selectedEndDate).getHours(),
          new Date(selectedEndDate).getMinutes(),
          0,
        );

        let startTimeValue = new Date(
          selectedDeliveryDate.getFullYear(),
          selectedDeliveryDate.getMonth(),
          selectedDeliveryDate.getDate(),
        );

        let endTimeValue = new Date(
          selectedDeliveryDate.getFullYear(),
          selectedDeliveryDate.getMonth(),
          selectedDeliveryDate.getDate(),
        );

        let endRecurrenceTimeValue = new Date(
          selectedEndDateYear.getFullYear(),
          selectedEndDateYear.getMonth(),
          selectedEndDateYear.getDate(),
        );

        const editStartDate = moment(startTime);
        const editEndDate = moment(endTime);

        let startPickerValue = moment(selectedStartDate).format("HH:mm");
        let endPickerValue = moment(selectedEndDate).format("HH:mm");

        let days = selectedDayArray;
        let param;
        const deliveryStartTime = moment(
          this.state.calSelectedStartTime,
        ).format("HH:mm");
        const deliveryEndTime = moment(this.state.calSelectedEndTime).format(
          "HH:mm",
        );

        let timeDifference = endTime.valueOf() - startTime.valueOf();
        if (endTime.valueOf() < startTime.valueOf()) {
          this.showToaster(Strings.toast.error, Strings.errors.notValidTime);
        } else if (endTime.valueOf() == startTime.valueOf()) {
          this.showToaster(
            Strings.toast.error,
            Strings.errors.notValidSameTime,
          );
        } /*  else if (timeDifference < 3600000) {
        this.showToaster("error", Strings.errors.startTimeAndEnd);
      } */ else if (
          editDR == false &&
          recurrence != Strings.calendarSettings.doseNotRepeat &&
          endRecurrenceTime.valueOf() < startTimeValue.valueOf()
        ) {
          this.showToaster(Strings.toast.error, Strings.errors.errorEnddate);
        } else if (
          (this.state.roleId == 3 || this.state.roleId == 4) &&
          editDR == true &&
          !editStartDate.isAfter(this.state.editCurrentDate) &&
          !editEndDate.isAfter(this.state.editCurrentDate)
        ) {
          this.showToaster("error");
        } else if (
          (this.state.roleId == 3 || this.state.roleId == 4) &&
          editDR == true &&
          !editStartDate.isAfter(this.state.editCurrentDate) &&
          editEndDate.isAfter(this.state.editCurrentDate)
        ) {
          this.showToaster("error");
        } else {
          let commonParam = {
            ParentCompanyId: this.props.projectDetails.ParentCompany.id,
            EquipmentId: selectedEquipmentList,
            GateId: this.state.selectedGateId,
            ProjectId: this.state.projectId,
            companies: selectedCompanyList,
            definableFeatureOfWorks: selectedDefinableList,
            craneDeliveryEnd: this.formatDateWithTimezone(endTime),
            craneDeliveryStart: this.formatDateWithTimezone(startTime),
            description: this.state.description,
            isEscortNeeded: this.state.escortNeeded,
            additionalNotes: this.state.additionalNotes,
            responsiblePersons:
              isResponseValue == false ? values : responsibleData,
            id: this.state.DeliveryRequestId,
            isAssociatedWithDeliveryRequest: false,
            pickUpLocation: this.state.pickFrom,
            dropOffLocation: this.state.pickTo,
            LocationId: this.state.selectedLocationId,
          };
          if (editDR == false && recurrence === "Does Not Repeat") {
            param = {
              ...commonParam,
              recurrence: "Does Not Repeat",
              repeatEveryCount: null,
              repeatEveryType: null,
              chosenDateOfMonth: false,
              dateOfMonth: null,
              monthlyRepeatType: "",
              craneDeliveryEnd: moment(startTime).format("YYYY MM DD 00:00:00"),
              craneDeliveryStart: moment(startTime).format(
                "YYYY MM DD 00:00:00",
              ),
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
            };
          } else if (editDR == false && recurrence === "Daily") {
            let dailyDays = [];
            this.state.daysList.forEach((e) => {
              if (e.selected) {
                dailyDays.push(e.name);
              }
            });
            param = {
              ...commonParam,
              craneDeliveryEnd: moment(endRecurrenceTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              craneDeliveryStart: moment(startTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              recurrence: "Daily",
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Days" : "Day",
              chosenDateOfMonth: false,
              dateOfMonth: null,
              monthlyRepeatType: "",
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
              days: dailyDays,
            };
          } else if (editDR == false && recurrence === "Weekly") {
            param = {
              ...commonParam,
              craneDeliveryEnd: moment(endRecurrenceTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              craneDeliveryStart: moment(startTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              recurrence: "Weekly",
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Weeks" : "Week",
              chosenDateOfMonth: false,
              dateOfMonth: "",
              monthlyRepeatType: "",
              days: days,
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
            };
          } else if (editDR == false && recurrence === "Monthly") {
            let repeat = null;
            let choseDate = false;
            if (isMonthFirstCheck == true) {
              repeat = null;
              choseDate = true;
            } else if (isMonthSecondCheck == true) {
              repeat = listSetName;
              choseDate = false;
            } else {
              repeat = listSetName;
              choseDate = false;
            }
            param = {
              ...commonParam,
              craneDeliveryEnd: moment(endRecurrenceTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              craneDeliveryStart: moment(startTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              recurrence: "Monthly",
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Months" : "Month",
              chosenDateOfMonth: choseDate,
              dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
              monthlyRepeatType: repeat,
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
            };
          } else if (editDR == false && recurrence === "Yearly") {
            let repeat = null;
            let choseDate = false;
            if (isYearFirstCheck == true) {
              repeat = null;
              choseDate = true;
            } else if (isYearSecondCheck == true) {
              repeat = yearListSetName;
              choseDate = false;
            } else {
              repeat = yearListSetName;
              choseDate = false;
            }

            param = {
              ...commonParam,
              craneDeliveryEnd: moment(endRecurrenceTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              craneDeliveryStart: moment(startTimeValue).format(
                "YYYY MM DD 00:00:00",
              ),
              recurrence: "Yearly",
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Years" : "Year",
              chosenDateOfMonth: choseDate,
              dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
              monthlyRepeatType: repeat,
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
            };
          } else if (editDR === true) {
            if (recurrence === Strings.calendarSettings.doseNotRepeat) {
              // Handle "Does Not Repeat" case first
              commonParam = {
                ...commonParam,
                // CraneRequestId: null,
                // DeliveryId: this.state.deliveryId,
                chosenDateOfMonthValue: null,
                // craneDropOffLocation: ,
                // cranePickUpLocation: null,
                chosenDateOfMonth: true,
                chosenDateOfMonthValue: 1,
                recurrence: "Does Not Repeat",
                recurrenceEdited: true,
                dateOfMonth: "",
                days: [],
                // define: [],
                monthlyRepeatType: "",
                repeatEveryCount: null,
                repeatEveryType: null,
                // craneDeliveryStart: moment(startTime).format("YYYY MM DD 00:00:00"),
                // craneDeliveryEnd: moment(endRecurrenceTime).format("YYYY MM DD 00:00:00"),
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.daily) {
              // Handle "Daily" recurrence
              let dailyDays = [];
              this.state.daysList.forEach((e) => {
                if (e.selected) {
                  dailyDays.push(e.name);
                }
              });
              commonParam = {
                ...commonParam,
                craneDeliveryStart: this.formatDateWithTimezone(
                  this.state.calSelectedStartTime,
                ),
                craneDeliveryEnd: this.formatDateWithTimezone(
                  this.state.calSelectedEndTime,
                ),
                recurrence: "Daily",
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Days" : "Day",
                chosenDateOfMonth: false,
                recurrenceEdited: true,
                dateOfMonth: null,
                monthlyRepeatType: "",
                // DeliveryId: this.state.deliveryId,
                // CraneRequestId: null,
                chosenDateOfMonthValue: null,
                // craneDropOffLocation: null,
                // cranePickUpLocation: null,
                days: dailyDays,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.weekly) {
              // Handle "Weekly" recurrence
              commonParam = {
                ...commonParam,
                craneDeliveryStart: this.formatDateWithTimezone(startTime),
                craneDeliveryEnd:
                  this.formatDateWithTimezone(endRecurrenceTime),
                recurrence: "Weekly",
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Weeks" : "Week",
                chosenDateOfMonth: false,
                // DeliveryId: this.state.deliveryId,
                dateOfMonth: null,
                monthlyRepeatType: "",
                days: days,
                recurrenceEdited: true,
                // CraneRequestId: null,
                chosenDateOfMonthValue: null,
                // craneDropOffLocation: null,
                // cranePickUpLocation: null,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.monthly) {
              // Handle "Monthly" recurrence
              let repeat = null;
              let choseDate = false;
              if (isMonthFirstCheck === true) {
                repeat = null;
                choseDate = true;
              } else if (isMonthSecondCheck === true) {
                repeat = listSetName;
                choseDate = false;
              }
              commonParam = {
                ...commonParam,
                recurrence: "Monthly",
                craneDeliveryStart: this.formatDateWithTimezone(startTime),
                craneDeliveryEnd:
                  this.formatDateWithTimezone(endRecurrenceTime),
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Months" : "Month",
                chosenDateOfMonth: choseDate,
                // DeliveryId: this.state.deliveryId,
                recurrenceEdited: true,
                // CraneRequestId: null,
                chosenDateOfMonthValue: null,
                // craneDropOffLocation: null,
                // cranePickUpLocation: null,
                dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
                monthlyRepeatType: repeat,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.yearly) {
              // Handle "Yearly" recurrence
              let repeat = "";
              let choseDate = false;
              if (isYearFirstCheck === true) {
                repeat = null;
                choseDate = true;
              } else if (isYearSecondCheck === true) {
                repeat = yearListSetName;
                choseDate = false;
              }
              commonParam = {
                ...commonParam,
                recurrence: "Yearly",
                craneDeliveryStart: this.formatDateWithTimezone(startTime),
                craneDeliveryEnd:
                  this.formatDateWithTimezone(endRecurrenceTime),
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Years" : "Year",
                chosenDateOfMonth: choseDate,
                // DeliveryId: this.state.deliveryId,
                recurrenceEdited: true,
                // CraneRequestId: null,
                chosenDateOfMonthValue: null,
                // craneDropOffLocation: null,
                // cranePickUpLocation: null,
                dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
                monthlyRepeatType: repeat,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            }
          }

          this.setState({
            showLoader: true,
            disableSubmit: true,
          });
          try {
            addCraneRequest(
              this.state.editDR == true ? EDIT_CRANE : ADD_CRANE,
              this.state.editDR == true ? commonParam : param,
              () => null,
              (addDRResp) => {
                this.setState({
                  showLoader: false,
                });

                if (addDRResp.toString() == Strings.errors.timeout) {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: Strings.errors.checkInternet,
                      type: "error",
                    },
                    () => {
                      setTimeout(() => {
                        this.setState({
                          showToaster: false,
                          disableSubmit: false,
                        });
                      }, 2000);
                    },
                  );
                } else if (addDRResp.status) {
                  if (addDRResp.status == 200 || addDRResp.status == 201) {
                    this.setState(
                      {
                        showToaster: true,
                        toastMessage:
                          this.state.editDR == true
                            ? "Crane Request Updated Successfully."
                            : "Crane Request Created Successfully.",
                        toastType: "success",
                      },
                      () => {
                        setTimeout(() => {
                          this.props.cameBack(false);

                          // Handle different navigation sources
                          if (
                            this.props.route.params?.from == "search" ||
                            this.props.route.params?.from == "detailDRPage" ||
                            this.props.route.params?.from == "detailINPage"
                          ) {
                            // Call the updateData function passed from previous page
                            if (this.props.route.params.updateData) {
                              this.props.route.params.updateData("data");
                            }
                          }

                          // Always refresh these for all scenarios
                          this.props.refreshDashboard(true, "Add Crane Submit");
                          this.props.updateList(false);

                          // Force refresh of any list pages that might be in the stack
                          if (this.props.refreshPage) {
                            this.props.refreshPage(true);
                          }

                          // Add a small delay to ensure all state updates complete
                          setTimeout(() => {
                            // Refresh calendar and force crane list update
                            // First reset the delivery flag, then set it to true to ensure state change
                            this.props.refreshDeliveryList(false);
                            setTimeout(() => {
                              this.props.refreshDeliveryList(true);
                            }, 100);

                            this.props.refreshCalendar(true);

                            // Also trigger a direct refresh of calendar data if available
                            if (this.props.route?.params?.refreshCalendarData) {
                              this.props.route.params.refreshCalendarData();
                            }
                            // Direct callback to refresh crane list if available
                            if (this.props.route?.params?.refreshCraneList) {
                              this.props.route.params.refreshCraneList();
                            }

                            // Add a small delay to ensure the refresh action is processed
                            setTimeout(() => {
                              this.props.navigation.goBack();
                              this.setState({ showToaster: false });
                            }, 100);
                          }, 300);
                        }, 2000);
                      },
                    );
                    if (this.state.editDR) {
                      trackEvent("Edited_Crane_Request");
                      mixPanelTrackEvent(
                        "Edited Crane Request",
                        this.state.mixpanelParam,
                      );
                    } else {
                      trackEvent("Created_New_Crane_Request");
                      mixPanelTrackEvent(
                        "Created New Crane Request",
                        this.state.mixpanelParam,
                      );
                    }
                  } else if (addDRResp.data.message.message) {
                    this.showToaster("error", addDRResp.data.message.message);
                  } else {
                    this.showToaster("error", addDRResp.data.message);
                  }
                } else {
                  this.showToaster("error", addDRResp.toString());
                }
              },
            );
          } catch (e) {
            //alert("Inside Submit==>", e);
          }
        }
      }
    }
  };

  showToaster = (type, message) => {
    Keyboard.dismiss();
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
        disableSubmit: false,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showToaster: false,
          });
        }, 2000);
      },
    );
  };

  onChangeperson = (text) => {
    if (text != "") {
      this.searchMember(false, text);
    } else {
      this.setState({ responsiblePersonData: [] });
    }
  };
  onPressDelDateTF = () => {
    this.setState({
      showDateModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedDate) {
        this.onchngeDate("", this.state.calSelectedDate);
      }
    }
  };
  onDatePickerDonePressed() {
    this.setState({ showDateModal: false });
  }
  onPressStartDateTF = () => {
    this.setState({
      showStartTimeModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedStartTime) {
        this.onChangeStart("", this.state.calSelectedStartTime);
      }
    }
  };
  onPressEndDateTF = () => {
    this.setState({
      showEndTimeModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedEndTime) {
        this.onChangeEndTime("", this.state.calSelectedEndTime);
      }
    }
  };
  onchngeDate = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showDateModal: false,
          isFromDate: true,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      const fullYear = selectedValue.getFullYear();
      const fullMonth = selectedValue.getMonth();
      const date = selectedValue.getDate();

      let sampleSelectDate = `${fullMonth + 1}/${date}/${fullYear}`;

      this.setState({
        selectedDate: moment(sampleSelectDate).format("MM/DD/YYYY"),
        calSelectedDate: selectedValue,
        isFromDate: true,
        // selectedStartTime: "",
        // selectedEndTime: "",
      });

      selectedDeliveryDate = selectedValue;
      // selectedStartDate = "";
      // selectedEndDate = "";
    }
  };

  onChangeStart = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showStartTimeModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      let deliveryDate = selectedDeliveryDate;
      const fullYear = deliveryDate.getFullYear();
      const fullMonth = deliveryDate.getMonth();
      const date = deliveryDate.getDate();
      let hours = selectedValue.getHours();
      let minutes = selectedValue.getMinutes();

      const delStartTime = new Date(fullYear, fullMonth, date, hours, minutes);

      // Determine AM/PM
      const ampm = hours >= 12 ? "pm" : "am";

      // Convert to 12-hour format correctly
      let hours12 = hours % 12;
      if (hours12 === 0) {
        hours12 = 12;
      }
      hours12 = hours12 < 10 ? `0${hours12}` : hours12;
      minutes = minutes < 10 ? `0${minutes}` : minutes;

      const strTime = `${hours12}:${minutes} ${ampm}`;

      // Update end time: 1 hour ahead of the selected start time
      const delEndTime = moment(delStartTime, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .toDate();
      let updatedEndTimes = moment(selectedValue, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .toDate();
      let updatedEndTimeStrTime = moment(selectedValue, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .format("hh:mm a");

      this.setState({
        selectedStartTime: strTime,
        calSelectedStartTime: selectedValue,
        selectedEndTime: updatedEndTimeStrTime,
        calSelectedEndTime: updatedEndTimes,
      });
      selectedStartDate = delStartTime;
      selectedEndDate = delEndTime;
    }
  };

  onChangeEndTime = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showEndTimeModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      let deliveryDate = selectedDeliveryDate;
      //let deliveryDate = selectedValue;
      const fullYear = deliveryDate.getFullYear();
      const fullMonth = deliveryDate.getMonth();
      const date = deliveryDate.getDate();
      let hours = selectedValue.getHours();
      let minutes = selectedValue.getMinutes();

      const delEndTime = new Date(fullYear, fullMonth, date, hours, minutes);

      const ampm = hours >= 12 ? "pm" : "am";

      hours %= 12;
      hours = (hours < 10 ? `0${hours}` : hours) || 12;
      minutes = minutes < 10 ? `0${minutes}` : minutes;

      const endTime = `${hours}:${minutes} ${ampm}`;

      this.setState({
        selectedEndTime: endTime,
        calSelectedEndTime: selectedValue,
      });

      selectedEndDate = delEndTime;
    }
  };

  updateMasterState = (key, value) => {
    if (key == Strings.placeholders.description) {
      this.setState({
        description: value,
      });
    } else if (key == Strings.placeholders.vechicleDetails) {
      this.setState({
        delVehicleDetails: value,
      });
    } else if (key == Strings.addCraneRequest.pickingFrom) {
      this.setState({
        pickFrom: value,
      });
    } else if (key == Strings.addCraneRequest.pickingTo) {
      this.setState({
        pickTo: value,
      });
    } else if (key == Strings.placeholders.equip) {
      this.setState({ type: value });
    } else if (key == Strings.placeholders.additional) {
      this.setState({
        additionalNotes: value,
      });
    }
  };

  onPressCancel = () => {
    if (this.state.editDR) {
      let data = this.state.comparision;

      let selectedCompanyList = [];
      let selectedDefinableList = [];
      let selectedEquipmentList = [];

      for (let item of this.state.responisbleCompanyList) {
        if (
          data.companyDetails.some(
            (person) => person.Company.companyName === item.name,
          )
        ) {
          selectedCompanyList.push({
            id: item.id,
            label: item.name,
            name: item.name,
            selected: true,
          });
        } else {
          selectedCompanyList.push({
            id: item.id,
            label: item.name,
            name: item.name,
            selected: false,
          });
        }
      }
      for (let item of this.state.definableList) {
        if (
          data.defineWorkDetails.some(
            (person) => person.DeliverDefineWork.DFOW === item.name,
          )
        ) {
          selectedDefinableList.push({
            id: item.id,
            label: item.name,
            name: item.name,
            selected: true,
          });
        } else {
          selectedDefinableList.push({
            id: item.id,
            label: item.name,
            name: item.name,
            selected: false,
          });
        }
      }

      for (let item of this.state.equipTypeList) {
        let isSelected = false;

        // Check for regular equipment match
        if (
          data.equipmentDetails.some(
            (person) => person.Equipment.equipmentName === item.name,
          )
        ) {
          isSelected = true;
        }

        // Special handling for "No Equipment Needed" case
        // Check if API returned "No Equipment Needed" and current item is the no-equipment option
        if (
          data.equipmentDetails.some(
            (person) =>
              person.Equipment.equipmentName === "No Equipment Needed" &&
              person.Equipment.id === 0,
          ) &&
          item.id === 0
        ) {
          isSelected = true;
        }

        selectedEquipmentList.push({
          id: item.id,
          label: item.name,
          name: item.name,
          selected: isSelected,
          isCrane: item.isCrane,
        });
      }

      let selectedPersons = [];

      for (let item of data.memberDetails) {
        selectedPersons.push({
          id: item.Member.User.email,
          email: item.Member.User.email,
          userId: item.Member.id,
        });
      }

      // Parse API times with original offset preserved
      const startMoment = moment.parseZone(data.craneDeliveryStart);
      const endMoment = moment.parseZone(data.craneDeliveryEnd);

      // Extract values
      const startHours = startMoment.format("hh"); // 12-hour format
      const startMinutes = startMoment.format("mm");
      const ampm = startMoment.format("a"); // am/pm

      const endHours = endMoment.format("hh");
      const endMinutes = endMoment.format("mm");
      const endampm = endMoment.format("a");

      // Year, month, date if needed
      const fullYear = startMoment.year();
      const startMonthIndex = startMoment.month(); // 0-based
      const startDate = startMoment.date();
      const endDate = endMoment.date();

      // Formatted strings
      const strTime = `${startHours}:${startMinutes} ${ampm}`;
      const endTime = `${endHours}:${endMinutes} ${endampm}`;

      const delStartTime = new Date(
        fullYear,
        startMonthIndex,
        startDate,
        new Date(data.craneDeliveryStart).getHours(),
        new Date(data.craneDeliveryStart).getMinutes(),
      );

      const endMonthIndex = endMoment.month(); // 0-based
      const delEndTime = new Date(
        fullYear,
        endMonthIndex,
        endDate,
        new Date(data.craneDeliveryEnd).getHours(),
        new Date(data.craneDeliveryEnd).getMinutes(),
      );
      selectedEndDate = delEndTime;
      selectedStartDate = delStartTime;
      selectedDeliveryDate = new Date(data.craneDeliveryStart);

      let responisbleCompanyList = selectedCompanyList;
      let description = data.description;
      let definableList = selectedDefinableList;
      let selectedGate = data.gateDetails
        .map((e) => e.Gate.gateName)
        .join(",")
        .toString();
      let selectedEquipName = data.equipmentDetails
        .map((e) => e.Equipment.equipmentName)
        .join(",")
        .toString();
      let additionalNotes = data.additionalNotes;
      let selectedStartTime = strTime;
      let selectedEndTime = endTime;
      let selectedDate = `${startMonthIndex + 1}/${startDate}/${fullYear}`;
      let selectedItem = selectedPersons;

      if (
        description == this.state.description &&
        JSON.stringify(responisbleCompanyList) ===
        JSON.stringify(this.state.responisbleCompanyList) &&
        JSON.stringify(definableList) ===
        JSON.stringify(this.state.definableList) &&
        JSON.stringify(selectedItem) ===
        JSON.stringify(this.state.selectedItem) &&
        selectedDate == this.state.selectedDate &&
        selectedEndTime == this.state.selectedEndTime &&
        selectedStartTime == this.state.selectedStartTime &&
        selectedEquipName == this.state.selectedEquipName &&
        selectedGate == this.state.selectedGate &&
        additionalNotes == this.state.additionalNotes
      ) {
        this.props.cameBack(false);
        this.props.navigation.goBack();
      } else {
        this.setState({ showCancel: true });
      }
    } else {
      this.setState({ showCancel: true });
    }
  };
  renderSeleRow = (id, onPress, item, style) => {
    return (
      <TouchableOpacity
        activeOpacity={0.6}
        key={id}
        onPress={onPress}
        style={{
          width: wp("90%"),
          //height: 30,
          alignSelf: "center",
          justifyContent: "center",
        }}
      >
        <Text
          /* style={{
            color: "rgba(0, 0, 0, 0.87)",
            width: wp("90%"),
            marginLeft: 10,
            marginTop: 2,
          }} */
          style={{
            color: "rgba(0, 0, 0, 0.87)",
            width: wp("90%"),
            marginLeft: 10,
            marginTop: 2,
            padding: 5,
          }}
        >
          {item.email}
        </Text>
      </TouchableOpacity>
    );
  };

  renderHeader = () => {
    return (
      <View style={drStyles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.props.cameBack(false);
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={drStyles.title}>
          {this.state.editDR
            ? Strings.addCraneRequest.edit
            : Strings.addCraneRequest.tittle}
        </Text>
        <TouchableOpacity
          style={{
            width: 25,
            height: 25,
            borderRadius: 25 / 2,
            marginRight: 15,
            justifyContent: "center",
            alignItems: "center",
            borderWidth: 2,
            borderColor: Colors.black,
          }}
          onPress={() => {
            this.setState({ showInfo: true });
          }}
        >
          <Text style={{ color: Colors.black, fontSize: wp("5%") }}>i</Text>
        </TouchableOpacity>
      </View>
    );
  };
  bottomContainer = () => {
    return (
      <View style={drStyles.bottomContainer}>
        <TouchableOpacity onPress={this.onPressCancel}>
          <View style={drStyles.cancel}>
            <Text style={drStyles.cancelText}>{Strings.addMember.cancel}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            if (this.state.disableSubmit == false) {
              this.submit();
            }
          }}
        >
          <View style={drStyles.submit}>
            <Text style={drStyles.submitText}>
              {this.state.editDR == true
                ? Strings.addMember.update
                : Strings.addMember.submit}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  onPressEqipType = (item) => {
    this.setState({
      selectedEquipName: item.value,
      selectedEquipTypeId: item.id,
      eqipModalVisible: false,
    });
  };

  onPressGateType = (item) => {
    this.setState({
      selectedGate: item.value,
      selectedGateId: item.id,
      gateModalVisible: false,
    });
  };

  render() {
    const {
      calSelectedDate,
      recurrence,
      times,
      isMonthFirstCheck,
      isMonthSecondCheck,
      isMonthThirdCheck,
      monthlyDay,
      monthlyLastDay,
      isYearFirstCheck,
      isYearSecondCheck,
      isYearThirdCheck,
      endDateRecurrence,
      selectedDaysOccurs,
      editDR,
      recurrenceType,
      deliveryStatus,
      listSetName,
      yearListSetName,
      selectedEndDateYear,
      editRequestID,
    } = this.state;
    return (
      <>
        {this.state.isNetworkCheck ? (
          <NoInternet Refresh={() => this.networkCheck()} />
        ) : (
          <SafeAreaView style={drStyles.safeArea}>
            <View style={drStyles.parentContainer}>
              {this.renderHeader()}

              <KeyboardAwareScrollView
                keyboardShouldPersistTaps={true}
                enableResetScrollToCoords={false}
                scrollsToTop={false}
                extraScrollHeight={60}
              >
                <TextField
                  attrName={Strings.placeholders.description}
                  title={Strings.placeholders.description}
                  value={this.state.description}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  maxLength={150}
                  textInputStyles={{
                    // here you can add additional TextInput drStyles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                />
                <View style={drStyles.memberContainer}>
                  <Text style={drStyles.idTitle}>
                    {Strings.addCraneRequest.pickId}
                  </Text>

                  <Text style={drStyles.idText}>{this.state.deliveryId}</Text>
                </View>

                <Text
                  style={[
                    styles.timeZoneText,
                    { fontFamily: Fonts.montserratMedium, fontSize: wp("4%") },
                  ]}
                >
                  {Strings.placeholders.location}
                  <Text style={styles.mandatory}>
                    {Strings.addNewEvent.mandatory}
                  </Text>
                </Text>

                <DropDownPicker
                  searchable={true}
                  items={this.state.locationDropdownList}
                  defaultValue={this.state.selectedLocationNew}
                  searchablePlaceholder={Strings.addNewEvent.timeZoneSearch}
                  placeholder={Strings.placeholders.location}
                  placeholderStyle={styles.dropDownPlaceHolderStyle}
                  containerStyle={styles.dropDownContainer}
                  style={styles.dropDownPickerStyle}
                  itemStyle={styles.dropDownItemStyle}
                  customArrowUp={(size) => (
                    <Image
                      source={Images.downArr}
                      style={[
                        styles.arrowDownStyle,
                        { width: size, height: size },
                      ]}
                    />
                  )}
                  customArrowDown={(size) => (
                    <Image
                      source={Images.downArr}
                      style={[
                        styles.arrowDownStyle,
                        { width: size, height: size },
                      ]}
                    />
                  )}
                  dropDownStyle={styles.dropDownListStyle}
                  selectedLabelStyle={styles.selectedDropDown}
                  onChangeItem={(item) => {
                    this.setState(
                      {
                        selectedLocationNew: item.value,
                        selectedLocationId: item.id,
                        // Reset selections when location changes
                        selectedGate: null,
                        selectedGateId: null,
                        selectedEquipName: null,
                        selectedEquipTypeId: null,
                        selectedEquipmentList: [],
                        // Also reset the equipment and gate lists to empty arrays
                        equipTypeList: [],
                        gateList: [],
                        storeEquipmentList: [],
                      },
                      () => {
                        // Filter equipment and gates based on the newly selected location
                        this.filterEquipmentAndGatesByLocation();
                      },
                    );
                  }}
                  zIndex={5000}
                />

                <Text style={[drStyles.idTitleText, { marginTop: hp("0%") }]}>
                  {Strings.placeholders.responsibleCompany}{" "}
                  <Text style={{ color: Colors.red }}>*</Text>
                </Text>

                <MultiSelectDropDown
                  dataItems={this.state.responisbleCompanyList}
                  title={"Select"}
                  selectedDataItem={this.getSelectedCompanyList}
                />
                <Text style={drStyles.idTitleText}>
                  {Strings.placeholders.definable}{" "}
                </Text>

                <MultiSelectDropDown
                  dataItems={this.state.definableList}
                  title={"Select"}
                  selectedDataItem={this.getSelectedDefinableList}
                />

                <View style={drStyles.escortContainer}>
                  <Text style={drStyles.escortText}>
                    {Strings.addDR.escortNeeded}{" "}
                  </Text>

                  <Switch
                    style={{ transform: [{ scaleX: 0.9 }, { scaleY: 0.8 }] }}
                    trackColor={{ false: Colors.placeholder, true: "#11FF00" }}
                    thumbColor={"#fff"}
                    ios_backgroundColor="#3e3e3e"
                    onValueChange={() =>
                      this.setState(
                        { escortNeeded: !this.state.escortNeeded },
                        () => {
                          if (this.state.escortNeeded == false) {
                            this.setState({
                              responsiblePersonData: [],
                            });
                          }
                        },
                      )
                    }
                    value={this.state.escortNeeded}
                  />
                </View>
                <View
                  style={[
                    drStyles.escortContainer,
                    { flexDirection: "column", marginTop: hp("2%") },
                  ]}
                // pointerEvents={this.state.escortNeeded ? "auto" : "none"}
                >
                  <Text style={drStyles.escortText}>
                    {Strings.addDR.responsible}{" "}
                    <Text style={{ color: Colors.red }}>*</Text>
                  </Text>

                  {this.state.showMultipleSec === false && (
                    <Selectize
                      tintColor={Colors.themeColor}
                      items={this.state.responsiblePersonData}
                      selectedItems={this.state.selectedItem}
                      containerStyle={{
                        zIndex: 1,
                      }}
                      // listStyle={{
                      //   position: "absolute",
                      // }}
                      renderRow={(id, onPress, item, style) =>
                        this.renderSeleRow(id, onPress, item, style)
                      }
                      renderChip={(id, onClose, item, style, iconStyle) => (
                        <View style={[styles.root, style]}>
                          <View style={styles.container}>
                            <Text style={styles.text} numberOfLines={1}>
                              {id}
                            </Text>
                            {id != this.props.responsiblePersonData.name && (
                              <TouchableOpacity
                                style={[styles.iconWrapper, iconStyle]}
                                onPress={onClose}
                              >
                                <Text
                                  style={[
                                    styles.icon,
                                    this.isIOS
                                      ? styles.iconIOS
                                      : styles.iconAndroid,
                                  ]}
                                >
                                  x
                                </Text>
                              </TouchableOpacity>
                            )}
                          </View>
                        </View>
                      )}
                      onChangeSelectedItems={(text) => {
                        this.setState({
                          selectedItemList: text,
                          responsiblePersonData: [],
                        });
                      }}
                      textInputProps={{
                        onChangeText: this.onChangeperson,
                      }}
                    />
                  )}

                  {this.state.showMultipleSec === true && (
                    <Selectize
                      tintColor={Colors.themeColor}
                      items={this.state.responsiblePersonData}
                      selectedItems={this.state.selectedItem}
                      containerStyle={{
                        zIndex: 1,
                      }}
                      // listStyle={{
                      //   position: "relative",
                      // }}
                      renderRow={(id, onPress, item, style) =>
                        this.renderSeleRow(id, onPress, item, style)
                      }
                      renderChip={(id, onClose, item, style, iconStyle) => (
                        <View style={[styles.root, style]}>
                          <View style={styles.container}>
                            <Text style={styles.text} numberOfLines={1}>
                              {id}
                            </Text>

                            {id != this.props.responsiblePersonData.name && (
                              <TouchableOpacity
                                style={[styles.iconWrapper, iconStyle]}
                                onPress={onClose}
                              >
                                <Text
                                  style={[
                                    styles.icon,
                                    this.isIOS
                                      ? styles.iconIOS
                                      : styles.iconAndroid,
                                  ]}
                                >
                                  x
                                </Text>
                              </TouchableOpacity>
                            )}
                          </View>
                        </View>
                      )}
                      // renderChip={(id, onClose, item, style, iconStyle) => {this.renderChip(id, onClose, item, style, iconStyle)}}
                      // listStyle={{
                      //   position: "absolute",
                      // }}
                      onChangeSelectedItems={(text) => {
                        this.setState({
                          selectedItemList: text,
                          responsiblePersonData: [],
                        });
                      }}
                      textInputProps={{
                        onChangeText: this.onChangeperson,
                      }}
                    />
                  )}
                </View>
                {/* <TextField
                  attrName={Strings.addCraneRequest.pickDate}
                  title={Strings.addCraneRequest.pickDate}
                  value={this.state.selectedDate.toString()}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  textInputStyles={
                    (editDR == true &&
                      deliveryStatus == Strings.deliveryStatusName.expired) ||
                    (editDR == true && editRequestID == 1)
                      ? styles.textStyle
                      : editDR == true
                      ? styles.textStyleEdited
                      : styles.textStyle
                  }
                  showButton={this.state.isEditDate}
                  editable={this.state.isEditDate}
                  onPress={() => {
                    Keyboard.dismiss();
                    if (
                      (editDR == true &&
                        recurrenceType ==
                          Strings.calendarSettings.doseNotRepeat) ||
                      (editDR == true && editRequestID == 1)
                    ) {
                      this.onPressDelDateTF();
                    } else if (
                      editDR == true &&
                      deliveryStatus == Strings.deliveryStatusName.expired
                    ) {
                      this.onPressDelDateTF();
                    } else if (editDR == true) {
                      this.setState({ showDateModal: false });
                    } else {
                      this.onPressDelDateTF();
                    }
                  }}
                  container={{
                    marginTop: hp("3%"),
                  }}
                  imageSource={Images.calGray}
                  placeholder={"Select"}
                />

                <View
                  style={{
                    width: wp("90%"),
                    alignSelf: "center",
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                
                  <TextField
                    attrName={Strings.placeholders.startTime}
                    title={Strings.placeholders.startTime}
                    value={this.state.selectedStartTime.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={true}
                    textInputStyles={{
                      // here you can add additional TextInput drStyles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    showButton={this.state.isEditDate}
                    editable={this.state.isEditDate}
                    onPress={() => {
                      Keyboard.dismiss();
                      this.onPressStartDateTF();
                    }}
                    container={{
                      width: wp("42%"),
                      alignSelf: "flex-start",
                    }}
                    imageSource={Images.clock}
                    placeholder={"Select"}
                    progressWidth={wp("42%")}
                    buttonContainer={{
                      width: wp("42%"),
                    }}
                  />

             
                  <TextField
                    attrName={Strings.placeholders.endTime}
                    title={Strings.placeholders.endTime}
                    value={this.state.selectedEndTime.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={true}
                    textInputStyles={{
                      // here you can add additional TextInput drStyles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    showButton={this.state.isEditDate}
                    editable={this.state.isEditDate}
                    onPress={() => {
                      Keyboard.dismiss();
                      this.onPressEndDateTF();
                    }}
                    container={{
                      width: wp("42%"),
                      alignSelf: "flex-start",
                    }}
                    imageSource={Images.clock}
                    placeholder={"Select"}
                    progressWidth={wp("42%")}
                    buttonContainer={{
                      width: wp("42%"),
                    }}
                  />
                </View> */}

                {editDR == false ? (
                  <>
                    {/* <Text style={styles.timeZoneText}>
                      {Strings.placeholders.timeZone}
                      <Text style={styles.mandatory}>
                        {Strings.addNewEvent.mandatory}
                      </Text>
                    </Text>
                    <Text style={styles.timeZoneValue}>{this.state.selectedTimeZone}</Text> */}
                    {/* <DropDownPicker
                      searchable={true}
                      items={this.state.timeZoneList}
                      defaultValue={this.state.selectedTimeZone}
                      searchablePlaceholder={Strings.addNewEvent.timeZoneSearch}
                      placeholder={Strings.placeholders.chooseTimezone}
                      placeholderStyle={styles.dropDownPlaceHolderStyle}
                      containerStyle={styles.dropDownContainer}
                      style={styles.dropDownPickerStyle}
                      itemStyle={styles.dropDownItemStyle}
                      customArrowUp={(size) => (
                        <Image
                          source={Images.downArr}
                          style={[
                            styles.arrowDownStyle,
                            { width: size, height: size },
                          ]}
                        />
                      )}
                      customArrowDown={(size) => (
                        <Image
                          source={Images.downArr}
                          style={[
                            styles.arrowDownStyle,
                            { width: size, height: size },
                          ]}
                        />
                      )}
                      dropDownStyle={styles.dropDownListStyle}
                      selectedLabelStyle={styles.selectedDropDown}
                      onChangeItem={(item) =>
                        this.setState({
                          selectedTimeZone: item.value,
                          selectedTimeZoneId: item.id,
                        })
                      }
                      zIndex={5000}
                    /> */}
                  </>
                ) : null}

                <Text style={drStyles.idTitleText}>
                  {Strings.placeholders.equip}{" "}
                  <Text style={{ color: Colors.red }}>*</Text>
                </Text>

                <MultiSelectDropDown
                  ref={(ref) => (this.multiSelectEquipRef = ref)}
                  key={`equipment-${this.state.selectedLocationId}-${this.state.editDR}-${this.state.equipTypeList?.length || 0}`}
                  dataItems={this.state.equipTypeList || []}
                  title={Strings.placeholders.equip}
                  selectedDataItem={this.getSelectedEquipmentList}
                />

                <TextField
                  attrName={Strings.placeholders.gate}
                  title={Strings.placeholders.gate}
                  value={this.state.selectedGate}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  showButton={true}
                  onPress={() => {
                    Keyboard.dismiss();
                    this.setState({ gateModalVisible: true });
                  }}
                  imageSource={Images.downArr}
                  placeholder={"Select"}
                  textInputStyles={{
                    // here you can add additional TextInput styles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                />
                {/* 
                <TextField
                  attrName={Strings.placeholders.equip}
                  title={Strings.placeholders.equip}
                  value={this.state.selectedEquipName}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  textInputStyles={{
                    // here you can add additional TextInput styles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                  showButton={true}
                  onPress={() => {
                    Keyboard.dismiss();
                    this.setState({ eqipModalVisible: true });
                  }}
                  imageSource={Images.downArr}
                  placeholder={"Select"}
                /> */}

                {/* <DropDownPicker
              items={this.state.equipTypeList}
              defaultValue={this.state.selectedEquipName}
              placeholder={"Select"}
              placeholderStyle={{
                color: Colors.placeholder,
                fontSize: 14,
              }}
              customArrowUp={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              customArrowDown={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              //     containerStyle={{ height: hp("5%") }}
              style={{
                backgroundColor: Colors.white,
                width: wp("90%"),
                borderColor: "#0000",
                borderBottomColor: Colors.placeholder,
                alignSelf: "center",
                //    height: hp("3%"),
              }}
              itemStyle={{
                justifyContent: "flex-start",
                fontSize: 14,
              }}
              dropDownStyle={{
                backgroundColor: Colors.white,
                width: "90%",
                alignSelf: "center",
              }}
              onChangeItem={(item) =>
                this.setState({
                  selectedEquipName: item.value,
                  selectedEquipTypeId: item.id,
                })
              }
              labelStyle={{
                fontSize: 14,
                fontFamily: Fonts.montserratRegular,
              }}
              arrowStyle={{
                height: hp("2%"),
              }}
              selectedLabelStyle={{ color: Colors.black }}
              zIndex={3000}
            /> */}
                {/* <TextField
              attrName={Strings.placeholders.vechicleDetails}
              title={Strings.placeholders.vechicleDetails}
              value={this.state.delVehicleDetails}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput drStyles
                color: Colors.black,
                fontSize: 14,
              }}
              showButton={false}
              imageSource={Images.calGray}
              //placeholder={"Select"}
            /> */}
                <TextField
                  attrName={Strings.addCraneRequest.pickingFrom}
                  title={Strings.addCraneRequest.pickingFrom}
                  value={this.state.pickFrom}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  textInputStyles={{
                    // here you can add additional TextInput drStyles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                  placeholder={Strings.addCraneRequest.pickingFrom}
                />
                <TextField
                  attrName={Strings.addCraneRequest.pickingTo}
                  title={Strings.addCraneRequest.pickingTo}
                  value={this.state.pickTo}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  textInputStyles={{
                    // here you can add additional TextInput drStyles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                  mandatory={true}
                  showButton={false}
                  imageSource={Images.calGray}
                  placeholder={Strings.addCraneRequest.pickingTo}
                />

                <View
                  style={{ alignItems: "center", justifyContent: "center" }}
                >
                  <TouchableOpacity
                    style={[drStyles.submitButton]}
                    onPress={this.handleNavigation}
                  >
                    <Text style={drStyles.submitButtonText}>
                      Select Date and Time
                    </Text>
                  </TouchableOpacity>
                </View>

                {this.state.selectedDate !== "" && (
                  <TextField
                    attrName={Strings.placeholders.deliveryDate}
                    title={Strings.placeholders.deliveryDate}
                    value={this.state.selectedDate.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={true}
                    textInputStyles={styles.textStyle}
                    editable={false}
                    showButton={false}
                    container={{
                      marginTop: hp("3%"),
                    }}
                    placeholder={"Select"}
                  />
                )}
                {(this.state.selectedStartTime !== "" ||
                  this.state.selectedEndTime !== "") && (
                    <View
                      style={{
                        width: wp("90%"),
                        alignSelf: "center",
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      <TextField
                        attrName={Strings.placeholders.startTime}
                        title="Start Time Slot"
                        value={this.state.selectedStartTime.toString()}
                        updateMasterState={(key, value) => {
                          this.updateMasterState(key, value);
                        }}
                        mandatory={false}
                        textInputStyles={{
                          color: Colors.black,
                          fontSize: 14,
                        }}
                        editable={false}
                        showButton={false}
                        container={{
                          width: wp("42%"),
                          alignSelf: "flex-start",
                        }}
                        placeholder={"Select"}
                      />

                      <TextField
                        attrName={Strings.placeholders.endTime}
                        title="End Time Slot"
                        value={this.state.selectedEndTime.toString()}
                        updateMasterState={(key, value) => {
                          this.updateMasterState(key, value);
                        }}
                        mandatory={false}
                        textInputStyles={{
                          color: Colors.black,
                          fontSize: 14,
                        }}
                        editable={false}
                        showButton={false}
                        container={{
                          width: wp("42%"),
                          alignSelf: "flex-start",
                        }}
                        placeholder={"Select"}
                      />
                    </View>
                  )}

                <TextField
                  attrName={Strings.placeholders.additional}
                  title={Strings.placeholders.additional}
                  value={this.state.additionalNotes}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  container={{ height: hp("14%") }}
                  mandatory={false}
                  maxLength={150}
                  textInputStyles={{
                    // here you can add additional TextInput drStyles
                    color: Colors.black,
                    fontSize: 14,
                    height: hp("9%"),
                    marginTop: hp("2%"),
                    // paddingTop: hp("2%")
                  }}
                  multiline={true}
                  showButton={false}
                  imageSource={Images.calGray}
                />
                {editDR == false ? (
                  <RecurrenceComponent
                    fromDate={calSelectedDate}
                    recurrenceName={recurrence}
                    onChangeRecrrenceName={(item) =>
                      this.setState({ recurrence: item })
                    }
                    timeValue={times}
                    onChangeTimeValue={(item) => this.setState({ times: item })}
                    monthFirstCheck={isMonthFirstCheck}
                    onChangeMonthFirstCheck={(item) =>
                      this.setState({ isMonthFirstCheck: item })
                    }
                    monthSecondCheck={isMonthSecondCheck}
                    onChangeMonthSecondCheck={(item) =>
                      this.setState({ isMonthSecondCheck: item })
                    }
                    monthThirdCheck={isMonthThirdCheck}
                    onChangeMonthThirdCheck={(item) =>
                      this.setState({ isMonthThirdCheck: item })
                    }
                    onMonthDay={monthlyDay}
                    onChangeMonthDay={(item) =>
                      this.setState({ monthlyDay: item })
                    }
                    onMonthLastDay={monthlyLastDay}
                    onChangeMonthLastDay={(item) =>
                      this.setState({ monthlyLastDay: item })
                    }
                    yearlyFirstCheck={isYearFirstCheck}
                    onChangeYearlyFirstCheck={(item) =>
                      this.setState({ isYearFirstCheck: item })
                    }
                    yearlySecondCheck={isYearSecondCheck}
                    onChangeYearlySecondCheck={(item) =>
                      this.setState({ isYearSecondCheck: item })
                    }
                    yearlyThirdCheck={isYearThirdCheck}
                    onChangeYearlyThirdCheck={(item) =>
                      this.setState({ isYearThirdCheck: item })
                    }
                    endDateCheck={endDateRecurrence}
                    onChangeEndDate={(item) =>
                      this.setState({ endDateRecurrence: item })
                    }
                    selectedDayCheck={selectedDaysOccurs}
                    onChangeSelectedDay={(item) =>
                      this.setState({ selectedDaysOccurs: item })
                    }
                    onChangeSelectedDayArray={(item) =>
                      this.setState({ selectedDayArray: item })
                    }
                    listName={listSetName}
                    onChangeListName={(item) =>
                      this.setState({ listSetName: item })
                    }
                    yearListName={yearListSetName}
                    onChangeYearListName={(item) =>
                      this.setState({ yearListSetName: item })
                    }
                    selectedEndDate={selectedEndDateYear}
                    onChangeSelectedEndDate={(item) =>
                      this.setState({ selectedEndDateYear: item })
                    }
                    onFromDateChanged={() =>
                      this.setState({ isFromDate: false })
                    }
                    isFromDateChanged={this.state.isFromDate}
                    recurrenceTypeData={this.state.editDR}
                  />
                ) : editDR == true && this.state.editRequestID != 1 ? (
                  <RecurrenceComponent
                    fromDate={calSelectedDate}
                    recurrenceName={recurrence}
                    onChangeRecrrenceName={(item) =>
                      this.setState({ recurrence: item })
                    }
                    timeValue={times}
                    onChangeTimeValue={(item) => this.setState({ times: item })}
                    monthFirstCheck={isMonthFirstCheck}
                    onChangeMonthFirstCheck={(item) =>
                      this.setState({ isMonthFirstCheck: item })
                    }
                    monthSecondCheck={isMonthSecondCheck}
                    onChangeMonthSecondCheck={(item) =>
                      this.setState({ isMonthSecondCheck: item })
                    }
                    monthThirdCheck={isMonthThirdCheck}
                    onChangeMonthThirdCheck={(item) =>
                      this.setState({ isMonthThirdCheck: item })
                    }
                    onMonthDay={monthlyDay}
                    onChangeMonthDay={(item) =>
                      this.setState({ monthlyDay: item })
                    }
                    onMonthLastDay={monthlyLastDay}
                    onChangeMonthLastDay={(item) =>
                      this.setState({ monthlyLastDay: item })
                    }
                    yearlyFirstCheck={isYearFirstCheck}
                    onChangeYearlyFirstCheck={(item) =>
                      this.setState({ isYearFirstCheck: item })
                    }
                    yearlySecondCheck={isYearSecondCheck}
                    onChangeYearlySecondCheck={(item) =>
                      this.setState({ isYearSecondCheck: item })
                    }
                    yearlyThirdCheck={isYearThirdCheck}
                    onChangeYearlyThirdCheck={(item) =>
                      this.setState({ isYearThirdCheck: item })
                    }
                    endDateCheck={endDateRecurrence}
                    onChangeEndDate={(item) =>
                      this.setState({ endDateRecurrence: item })
                    }
                    selectedDayCheck={selectedDaysOccurs}
                    onChangeSelectedDay={(item) =>
                      this.setState({ selectedDaysOccurs: item })
                    }
                    onChangeSelectedDayArray={(item) =>
                      this.setState({ selectedDayArray: item })
                    }
                    listName={listSetName}
                    onChangeListName={(item) =>
                      this.setState({ listSetName: item })
                    }
                    yearListName={yearListSetName}
                    onChangeYearListName={(item) =>
                      this.setState({ yearListSetName: item })
                    }
                    selectedEndDate={selectedEndDateYear}
                    onChangeSelectedEndDate={(item) =>
                      this.setState({ selectedEndDateYear: item })
                    }
                    onFromDateChanged={() =>
                      this.setState({ isFromDate: false })
                    }
                    isFromDateChanged={this.state.isFromDate}
                    recurrenceTypeData={false}
                    selectedDay={this.state.selectedDay}
                  />
                ) : null}

                {this.bottomContainer()}
              </KeyboardAwareScrollView>
            </View>

            <Dropdown
              data={this.state.equipTypeList}
              title={Strings.placeholders.equip}
              value={this.state.selectedEquipName}
              closeBtn={() => this.setState({ eqipModalVisible: false })}
              onPress={(item) => this.onPressEqipType(item)}
              visible={this.state.eqipModalVisible}
              onbackPress={() => this.setState({ eqipModalVisible: false })}
              container={styles.equipmentContainer}
              customMainContainer={styles.renderEquipStyle}
              equipTextContainer={styles.equipTextStyle}
            />

            <Dropdown
              data={this.state.gateList}
              title={Strings.placeholders.gate}
              value={this.state.selectedGate}
              closeBtn={() => this.setState({ gateModalVisible: false })}
              onPress={(item) => this.onPressGateType(item)}
              visible={this.state.gateModalVisible}
              onbackPress={() => this.setState({ gateModalVisible: false })}
              container={drStyles.equipmentContainer}
              customMainContainer={drStyles.renderEquipStyle}
              equipTextContainer={drStyles.equipTextStyle}
            />
            <Modal
              isVisible={this.state.showInfo}
              onBackdropPress={() => {
                this.setState({ showInfo: false });
              }}
              style={{
                paddingTop: 45,
                paddingBottom: 30,
                margin: 0,
                backgroundColor: "rgba(0, 0, 0, 0.3)",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <View
                style={{
                  backgroundColor: "white",
                  width: wp("90%"),
                  borderRadius: 10,
                }}
              >
                <Text
                  style={{
                    color: "#575E63",
                    fontSize: wp("4%"),
                    fontFamily: Fonts.montserratMedium,
                    marginVertical: 15,
                    marginHorizontal: 10,
                  }}
                >
                  {Strings.addCraneRequest.info}
                </Text>
              </View>
            </Modal>

            {/* Calender iOS */}

            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showDateModal}
                onBackdropPress={() => {
                  this.setState({
                    showDateModal: false,
                  });
                }}
                animationInTiming={500}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  //TODO FOR LATER
                  // minimumDate={this.state.editDR?new Date(1950, 0, 1):this.state.minimumDate}
                  value={this.state.calSelectedDate}
                  //value={}
                  style={{
                    backgroundColor: Colors.white,
                    width: "100%",
                  }}
                  display={"inline"}
                  themeVariant="light"
                  accentColor={Colors.themeColor}
                  onChange={(time, date) => {
                    this.onchngeDate(time, date);
                  }}
                />
                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={styles.datePickerOkContainer}
                    onPress={() => {
                      this.onDatePickerDonePressed();
                    }}
                  >
                    <Text style={styles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Calender Android */}

            {Platform.OS == "android" && this.state.showDateModal && (
              <DateTimePicker
                testID="datePicker"
                //TODO FOR LATER
                // minimumDate={this.state.editDR?new Date(1950, 0, 1):this.state.minimumDate}
                value={this.state.calSelectedDate}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                //mode={mode}
                onChange={(time, date) => {
                  this.onchngeDate(time, date);
                }}
              />
            )}

            {/* Time picker iOS - start time */}

            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showStartTimeModal}
                onBackdropPress={() => {
                  this.setState({
                    showStartTimeModal: false,
                  });
                }}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  mode={"time"}
                  // timeZoneOffsetInMinutes={0}
                  // minuteInterval={interval}
                  value={this.state.calSelectedStartTime}
                  textColor="black"
                  style={{
                    backgroundColor: "#fff",
                    width: "100%",
                  }}
                  //mode={mode}
                  display={"spinner"}
                  onChange={(time, date) => {
                    this.onChangeStart(time, date);
                  }}
                />

                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={styles.datePickerOkContainer}
                    onPress={() => {
                      this.setState({
                        showStartTimeModal: false,
                      });
                    }}
                  >
                    <Text style={styles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Timepicker android - start Time */}

            {Platform.OS == "android" && this.state.showStartTimeModal && (
              <DateTimePicker
                testID="datePicker"
                mode={"time"}
                // timeZoneOffsetInMinutes={0}
                // minuteInterval={interval}
                value={this.state.calSelectedStartTime}
                minimumDate={this.state.minimumDate}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                // mode={mode}
                onChange={(time, date) => {
                  this.onChangeStart(time, date);
                }}
              />
            )}

            {/* Timepicker - ios End Time */}

            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showEndTimeModal}
                onBackdropPress={() => {
                  this.setState({
                    showEndTimeModal: false,
                  });
                }}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  mode={"time"}
                  //TODO FOR LATER
                  // timeZoneOffsetInMinutes={0}
                  // minuteInterval={interval}
                  // minimumDate={this.state.minimumDate}
                  value={this.state.calSelectedEndTime}
                  textColor="black"
                  style={{
                    backgroundColor: "#fff",
                    width: "100%",
                  }}
                  display={"spinner"}
                  // mode={mode}
                  onChange={(time, date) => {
                    this.onChangeEndTime(time, date);
                  }}
                />
                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={styles.datePickerOkContainer}
                    onPress={() => {
                      this.setState({
                        showEndTimeModal: false,
                      });
                    }}
                  >
                    <Text style={styles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Timepicker android - End Time */}

            {Platform.OS == "android" && this.state.showEndTimeModal && (
              <DateTimePicker
                testID="datePicker"
                mode={"time"}
                //TODO FOR LATER
                // timeZoneOffsetInMinutes={0}
                // minuteInterval={interval}
                // minimumDate={this.state.minimumDate}
                value={this.state.calSelectedEndTime}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                // mode={mode}
                onChange={(time, date) => {
                  this.onChangeEndTime(time, date);
                }}
              />
            )}

            {this.state.showLoader && (
              <AppLoader viewRef={this.state.showLoader} />
            )}

            {this.state.showToaster && (
              <Toastpopup
                backPress={() => this.setState({ showToaster: false })}
                toastMessage={this.state.toastMessage}
                type={this.state.toastType}
                container={{ marginBottom: hp("12%") }}
              />
            )}
            {this.state.showCancel && (
              <DeletePop
                title={Strings.popup.cancel}
                desc={Strings.popup.cancel}
                acceptTap={() => {
                  this.setState({ showCancel: false });
                  this.props.cameBack(false);
                  this.props.navigation.goBack();
                }}
                container={{ bottom: 0 }}
                declineTap={() => {
                  this.setState({ showCancel: false });
                }}
              />
            )}
          </SafeAreaView>
        )}
      </>
    );
  }
}
const styles = StyleSheet.create({
  root: {
    justifyContent: "center",
    borderRadius: 50,
    backgroundColor: "#e0e0e0",
    paddingHorizontal: 10,
    paddingVertical: 4,
    height: 28,
    marginBottom: 4,
    marginRight: 4,
  },
  container: {
    flexDirection: "row",
    overflow: "hidden",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  text: {
    color: "rgba(0, 0, 0, 0.87)",
  },
  iconWrapper: {
    borderRadius: 50,
    backgroundColor: "#a6a6a6",
    height: 16,
    width: 16,
    overflow: "hidden",
    marginLeft: 4,
    justifyContent: "center",
    alignItems: "center",
  },
  icon: {
    textAlign: "center",
    color: "#e0e0e0",
  },
  iconIOS: {
    fontSize: 14,
  },
  iconAndroid: {
    fontSize: 13,
    lineHeight: 15,
  },
  datePickerOkLabel: {
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 6,
    paddingBottom: 6,
    fontFamily: Fonts.montserratBold,
  },
  datePickerOkContainer: {
    width: "100%",
    alignItems: "center",
    backgroundColor: Colors.white,
    paddingBottom: hp("2%"),
  },
  equipmentContainer: {
    height: hp("4%"),
    paddingBottom: 5,
  },
  renderEquipStyle: {
    marginBottom: 10,
  },
  equipTextStyle: {
    width: "100%",
    fontSize: 16,
    paddingTop: 2,
  },
  timeZoneText: {
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    marginLeft: 20,
  },
  mandatory: {
    color: Colors.themeColor,
  },
  dropDownPlaceHolderStyle: {
    color: Colors.placeholder,
    fontSize: 14,
    marginLeft: -5,
  },
  dropDownPickerStyle: {
    backgroundColor: Colors.white,
    width: wp("90%"),
    borderColor: Colors.borderTransparent,
    borderBottomColor: Colors.placeholder,
    alignSelf: "center",
    height: hp("5%"),
  },
  dropDownContainer: {
    height: hp("6%"),
    marginTop: 6,
  },
  dropDownItemStyle: {
    justifyContent: "flex-start",
  },
  arrowDownStyle: {
    alignSelf: "flex-end",
  },
  dropDownListStyle: {
    backgroundColor: Colors.white,
    width: "90%",
    alignSelf: "center",
  },
  selectedDropDown: {
    color: Colors.black,
    marginLeft: -5,
  },
  textStyleEdited: {
    color: Colors.inlineGrey,
    fontSize: 14,
  },
  textStyle: {
    color: Colors.black,
    fontSize: 14,
  },
  timeZoneValue: {
    color: Colors.black,
    fontSize: 14,
    marginLeft: 25,
    paddingTop: 10,
  },
});
const drStyles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("78%"),
    fontSize: wp("5.6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratSemiBold,
  },
  memberContainer: {
    width: wp("90%"),
    height: hp("10%"),
    alignSelf: "center",
    marginTop: hp("1%"),
    justifyContent: "center",
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  idTitleText: {
    fontSize: wp("4%"),
    marginLeft: 10,
    marginTop: hp("3%"),
    alignSelf: "center",
    width: wp("90%"),
    color: Colors.placeholder,
    fontFamily: Fonts.montserratMedium,
  },
  idText: {
    width: wp("90%"),
    height: hp("5%"),
    backgroundColor: "#EFEFEF",
    marginTop: wp("1%"),
    padding: hp("1%"),
    paddingLeft: 15,
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("4%"),
  },
  escortContainer: {
    width: "90%",
    alignSelf: "center",
    marginTop: hp("4%"),
    justifyContent: "space-between",
    flexDirection: "row",
  },
  escortText: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
  },
  bottomContainer: {
    width: wp("90%"),
    flexDirection: "row",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: hp("4%"),
    marginTop: hp("8%"),
  },
  cancel: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: "#757575",
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  root: {
    justifyContent: "center",
    borderRadius: 50,
    backgroundColor: "#e0e0e0",
    paddingHorizontal: 10,
    paddingVertical: 4,
    height: 28,
    marginBottom: 4,
    marginRight: 4,
  },
  submitButton: {
    flex: 1,
    backgroundColor: Colors.pendingEventColor,
    padding: 10,
    borderRadius: 25,
    // marginLeft: 10,
    alignItems: "center",
  },
  submitButtonText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: 16,
    paddingLeft: 70,
    paddingRight: 70,
  },
  renderEquipStyle: {
    marginBottom: 10,
  },
  equipTextStyle: {
    width: "100%",
    fontSize: 16,
    paddingTop: 2,
  },
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    deliveryDetailsId,
    inspectionDetailsId,
    updateList,
    responsiblePersonData,
    lastCraneRequestId,
    craneRequestId,
    editedCrane,
    selected_Company,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    deliveryDetailsId,
    inspectionDetailsId,
    updateList,
    responsiblePersonData,
    lastCraneRequestId,
    craneRequestId,
    editedCrane,
    selected_Company,
  };
};

export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    cameBack,
    updateList,
    refreshDashboard,
    refreshDeliveryList,
    showCraneRequestId,
    editCraneRequest,
    refreshCalendar,
  }),
  withBackHandler,
)(AddCrane);
