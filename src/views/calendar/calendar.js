import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Image,
  ActivityIndicator,

  Keyboard
} from "react-native";

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import moment from "moment";

import { ExpandableCalendar, CalendarProvider } from "react-native-calendars";
import { Images, Strings, Fonts, Colors } from "../../common";

import {
  GET_EVENT_NDR,
  GET_EVENT_INS,
  GET_SINGLE_NDR,
  GET_SINGLE_INS,
  GET_NEW_COMPANIES,
  GET_GATE_LIST,
  LIST_ALL_MEMBER,
  GET_COMPANY_PROJECT_LIST,
  GET_ACCOUNT_PROJECT,
  GET_PROJECT_ROLE,
  CRANE_CALENDER,
  CRANE_DELIVERY_LIST,
  GET_EQUIP_LIST,
  GET_CALENDAR_CONCRETE,
  CONCRETE_DROPDOWN_DETAILS,
} from "../../api/Constants";
import {
  eventNDR,
  eventINS,
  getDeliveryDetails,
  getInspectionDetails,
  getNewCompanyList,
  getGateList,
  getAllMemberList,
  getCompanyprojectlist,
  getProjectList,
  getprojectRole,
  getCraneCalendarList,
  getCraneDeliveryList,
  getEquipList,
  calendarConcreteList,
  getConcreteDropDownDetails,
} from "../../api/Api";

import { AppView, AppLoader, Timeline, TextField } from "../../components";

import { FlatList } from "react-native-gesture-handler";
import { connect } from "react-redux";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Modal from "react-native-modal";
import {
  showDeliverdetailsid,
  showInspectiondetailsid,
  onTapDetail,
  cameBack,
  storeLastid,
  storeProjectRole,
  toggleAddCalendar,
  lastCraneId,
  refreshDashboard,
  refreshCalendar,
  refreshDeliveryList,
  concreteDetailsID,
  onPressConcreteDetail,
  eventDisplayPage,
  eventDisplayData,
  enableEditEvents,
  setSelectedCalendarDate,
} from "../../actions/postAction";
import CalendarHeader from "../../components/headerComponent/CalendarHeader";
import { trackScreen } from "../../Google Analytics/GoogleAnalytics";
import _ from "lodash";
import Dropdown from "../../components/dropdown/dropdown";
import DateTimePicker from "@react-native-community/datetimepicker";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";
const testIDs = {
  CONTAINER: "agenda",
  ITEM: "item",
};

let colorobj = {
  Approved: Colors.approvedColorObject,
  Pending: Colors.TentativeEventColor,
  Declined: Colors.declinedColorObject,
  Expired: Colors.expiredColorObject,
  Delivered: Colors.deliveredColorObject,
  Pass: Colors.deliveredColorObject,
  Fail: Colors.deliveredColorObject,
  Completed: Colors.deliveredColorObject,
  Tentative: Colors.TentativeEventColor,
  CalendarEvent: Colors.calendarSettingsText
};
let eventcolorobj = {
  Pending: Colors.pendingEventColor,
  Declined: Colors.declinedEventColor,
  Expired: Colors.expiredEventColor,
  Delivered: Colors.deliveredEventColor,
  Completed: Colors.deliveredEventColor,
  Pass: Colors.deliveredEventColor,
  Fail: Colors.deliveredEventColor,
  Tentative: Colors.TentativeEventColor,
  CalendarEvent: Colors.calendarSettingsText,
};

let textcolorobj = {
  Approved: Colors.darkBlue,
  Pending: Colors.darkBlue,
  Declined: Colors.darkBlue,
  Expired: Colors.darkBlue,
  Delivered: Colors.darkBlue,
  Pass: Colors.darkBlue,
  Fail: Colors.darkBlue,
  Completed: Colors.darkBlue,
  Tentative: Colors.darkBlue,
  CalendarEvent: Colors.darkBlue,
};
let Orderobj = {
  Approved: 5,
  Pending: 3,
  Declined: 2,
  Expired: 1,
  Delivered: 4,
  Completed: 4,
  Pass: 4,
  Fail: 4,
  Tentative: 3,
  CalendarEvent: 9
};

const inspection_type = [
  { type: "Material" },
  { type: "Quality Control" },
  { type: "Special Inspection" },
  { type: "Equipment" },
  { type: "Safety" },
  { type: "Other" },
];

const inspection_Status = [
  { status: "Pass" },
  { status: "Fail" },
]

const inspectionTypeOptions = inspection_type.map(item => ({
  label: item.type,
  value: item.type,
  name: item.type,
}));

const inspectionStatusOptions = inspection_Status.map(item => ({
  label: item.status,
  value: item.status,
  name: item.status,
}));



let selectedDate = new Date()
class Calendar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      calendarReady: false,
      items: {},
      currentDate: moment(new Date()).format("YYYY-MM-DD"),
      showLoader: false,
      markedDates: {},
      monthStart: moment(new Date())
        .startOf("week")
        .toISOString(),
      monthEnd: moment(new Date())
        .endOf("week")
        .add(1, "day")
        .toISOString(),
      DeliveryRequestId: "",
      //  InspectionRequestId: "",
      event: [],
      listClick: false,
      showFilter: false,
      filter: false,
      statusList: [
        {
          label: "Approved",
          value: "Approved",
          id: "1",
          name: "Approved",
        },
        {
          label: "Declined",
          value: "Declined",
          id: "2",
          name: "Declined",
        },
        {
          label: "Delivered",
          value: "Delivered",
          id: "3",
          name: "Delivered",
        },
        {
          label: "Pending",
          value: "Pending",
          id: "4",
          name: "Pending",
        },
      ],
      selectedStatusName: "",
      selectedResponsibleNameId: 0,
      selectedGateNameId: 0,
      selectedEquipNameId: 0,
      selectedStatusId: 0,
      descriptionFilter: "",
      companyFilterList: [],
      selectedCompanyName: null,
      responiblePersonList: [],
      selectedResponsibleName: null,
      gateList: [],
      selectedGateName: null,
      equipmentList: [],
      selectedEquipmentName: null,
      selectedCompanyId: 0,
      refreshing: false,
      searchText: "",
      showNoData: false,
      parentcompanyid: 0,
      refresh: false,
      currentMonth: moment(new Date()).format("MMM YYYY").toString(),
      selectedCalendar: "Delivery Calendar",
      pickFrom: "",
      pickTo: "",
      currentScreen: "",
      locationsList: [],
      selectedLocationName: null,
      selectedLocationId: 0,
      concreteSuppliersList: [],
      selectedConcreteSupplier: null,
      selectedSupplierId: 0,
      selectedMixDesignName: null,
      selectedMixDesignId: 0,
      mixDesignList: [],
      inspectionType: null,
      inspectionTypeOptions: inspectionTypeOptions,
      inspectionStatus: null,
      inspectionStatusOptions: inspectionStatusOptions,
      filterCountData: 0,
      locationModalVisible: false,
      supplierModalVisible: false,
      mixDesignDropdown: false,
      selectStatusDropdown: false,
      companyModalVisible: false,
      responisblePersonModal: false,
      gateModalVisible: false,
      equipModalVisible: false,
      statusModalVisible: false,
      isDeliveryDate: false,
      dateFilter: "",
      isNetworkCheck: false,
      eventColor: []
      // InspectionStatusList: [
      //   {
      //     label: "Approved",
      //     value: "Approved",
      //     id: "1",
      //     name:"Approved",
      //   },
      //   {
      //     label: "Declined",
      //     value: "Declined",
      //     id: "2",
      //     name:"Declined",
      //   },
      //   {
      //     label: "Completed",
      //     value: "Completed",
      //     id: "3",
      //     name:"Completed",
      //   },
      //   {
      //     label: "Pending",
      //     value: "Pending",
      //     id: "4",
      //     name:"Pending",
      //   },
      // ],
    };
    this.onPressInspectionType = this.onPressInspectionType.bind(this);
    this.onPressInspectionStatus = this.onPressInspectionStatus.bind(this);
    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;
    this.calendarRef = React.createRef()
  }

  onPressInspectionType(item) {
    this.setState({
      inspectionType: item.value,
      inspectionModelVisible: false,
      filter: true, // Enable filter mode
    }, () => {

      this.renderInital();
    });
  }

  onPressInspectionStatus(item) {
    this.setState({
      inspectionStatus: item.value,
      inspectionStatusModelVisible: false,
      filter: true, // Enable filter mode
    }, () => {
      // Automatically refresh calendar data with new filter

      this.renderInital();
    });
  }

  // Helper method to update filter count when dropdowns change
  updateFilterCount = () => {
    let count = 0;

    if (this.state.descriptionFilter !== "") count++;
    if (this.state.selectedCompanyId !== 0) count++;
    if (this.state.selectedResponsibleNameId !== 0) count++;
    if (this.state.selectedGateNameId !== 0) count++;
    if (this.state.selectedEquipNameId !== 0) count++;
    if (this.state.selectedLocationId !== 0) count++;
    if (this.state.selectedSupplierId !== 0) count++;
    if (this.state.selectedMixDesignId !== 0) count++;
    if (this.state.selectedStatusName !== "") count++;
    if (this.state.pickFrom !== "") count++;
    if (this.state.pickTo !== "") count++;
    if (this.state.inspectionType !== null) count++;
    if (this.state.inspectionStatus !== null) count++;
    if (this.state.dateFilter !== "") count++;

    this.setState({ filterCountData: count });
    return count;
  }

  componentDidMount() {
    if (Platform.OS === 'ios') {
      this.networkCheck();
    } else {
      this.onFocusSubscribe = this.props.navigation.addListener("focus", () => {
        this.setState(
          {
            showFilter: false,
            selectedControlledBy: null,
            selectedControlledById: 0,
            selectedEquipId: 0,
            searchId: "",
            searchText: "",
            descriptionFilter: "",
            selectedCompanyName: null,
            selectedCompanyId: 0,
            selectedResponsibleNameId: 0,
            selectedResponsibleName: null,
            selectedEquipName: null,
            selectedEquipNameId: 0,
            selectedGateName: null,
            selectedGateNameId: 0,
            selectedStatusName: "",
            pickFrom: "",
            pickTo: "",
            dateFilter: "",
            event: [],
            markedDates: {},
            showLoader: true
          },
          () => {
            if (this.state.filter == true) {
              this.setState({ filter: false }, () => {
                this.renderInital();
                // Reset refresh flag if it was set
                if (this.props.isRefreshCalendar === true) {
                  this.props.refreshCalendar(false);
                }
                // Ensure crane data is refreshed if on crane calendar
                if (this.state.selectedCalendar === "Crane Calendar") {
                  setTimeout(() => {
                    this.getEventCraneList();
                  }, 100);
                }
              });
            } else {
              // Always refresh calendar when coming into focus
              this.renderInital();
              // Reset refresh flag if it was set
              if (this.props.isRefreshCalendar === true) {
                this.props.refreshCalendar(false);
              }
              // Ensure crane data is refreshed if on crane calendar
              if (this.state.selectedCalendar === "Crane Calendar") {
                setTimeout(() => {
                  this.getCraneRequest();
                }, 100);
              }
            }
          }
        )
      });

      this.subs = this.props.navigation.addListener("focus", () => {
        // Force a complete refresh when calendar comes into focus
        this.setState({
          event: [],
          markedDates: {},
          showLoader: true
        }, () => {
          this.renderInital();
          // Reset refresh flag if it was set
          if (this.props.isRefreshCalendar === true) {
            this.props.refreshCalendar(false);
          }
          // Ensure crane data is refreshed if on crane calendar
          if (this.state.selectedCalendar === "Crane Calendar") {
            setTimeout(() => {
              this.getCraneRequest();
            }, 100);
          }
        });
      });
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.checkCameBack === true) {
      this.renderInital();
    }

    if (this.props.projectSwitched != nextProps.projectSwitched) {
      this.renderInital();
    }
    if (nextProps.isRefreshCalendar === true) {
      // Force a complete refresh with state reset to ensure new data is loaded
      this.setState({
        event: [],
        markedDates: {},
        showLoader: true
      }, () => {
        this.renderInital();
        this.props.refreshCalendar(false);
      });
    }
  }

  componentDidUpdate(prevProps) {
    // Handle refresh calendar state changes
    if (this.props.isRefreshCalendar === true && prevProps.isRefreshCalendar === false) {
      // Force a complete refresh with state reset to ensure new data is loaded
      this.setState({
        event: [],
        markedDates: {},
        showLoader: true
      }, () => {
        this.renderInital();
        this.props.refreshCalendar(false);
      });
    }

    // Handle refresh delivery state changes - specifically for crane delivery list
    if (this.props.refreshDelivery === true && prevProps.refreshDelivery === false) {
      this.getEventCraneList();
      setTimeout(() => {
        this.props.refreshDeliveryList(false);
      }, 200);
    }

    // Handle other prop changes
    if (this.props.checkCameBack === true && prevProps.checkCameBack === false) {
      this.renderInital();
    }

    if (this.props.projectSwitched !== prevProps.projectSwitched) {
      this.renderInital();
    }
  }

  componentWillUnmount() {
    this.subs?.();
    this.onFocusSubscribe?.();
  }

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  networkCheck = () => {
    NetInfo.addEventListener(state => {
      if (!state.isConnected && !state.isInternetReachable) {
        this.setState({ isNetworkCheck: true })
      } else {
        this.setState({ isNetworkCheck: false })
        this.onFocusSubscribe = this.props.navigation.addListener("focus", () => {
          this.props.setSelectedCalendarDate(this.state.currentDate);
          this.setState(
            {
              showFilter: false,
              selectedControlledBy: null,
              selectedControlledById: 0,
              selectedEquipId: 0,
              searchId: "",
              searchText: "",
              descriptionFilter: "",
              selectedCompanyName: null,
              selectedCompanyId: 0,
              selectedResponsibleNameId: 0,
              selectedResponsibleName: null,
              selectedEquipName: null,
              selectedEquipNameId: 0,
              selectedGateName: null,
              selectedGateNameId: 0,
              selectedStatusName: "",
              pickFrom: "",
              pickTo: "",
              dateFilter: "",
              event: [],
              markedDates: {},
              showLoader: true
            },
            () => {
              if (this.state.filter == true) {
                this.setState({ filter: false }, () => {
                  this.renderInital();
                  // Reset refresh flag if it was set
                  if (this.props.isRefreshCalendar === true) {
                    this.props.refreshCalendar(false);
                  }
                  // Ensure crane data is refreshed if on crane calendar
                  if (this.state.selectedCalendar === "Crane Calendar") {
                    setTimeout(() => {
                      this.getCraneRequest();
                    }, 100);
                  }
                });
              } else {
                this.renderInital();
                // Reset refresh flag if it was set
                if (this.props.isRefreshCalendar === true) {
                  this.props.refreshCalendar(false);
                }
                // Ensure crane data is refreshed if on crane calendar
                if (this.state.selectedCalendar === "Crane Calendar") {
                  setTimeout(() => {
                    this.getEventCraneList();
                  }, 100);
                }
              }
            }
          )
        });

        // this.subs = this.props.navigation.addListener("focus", () =>
        //   this.renderInital()
        // );
      }
    })
  }


  renderInital = () => {
    this.setState({ event: [], markedDates: {} });
    this.page_number = 1;
    if (this.state.selectedCalendar == "Delivery Calendar") {
      this.props.toggleAddCalendar("Delivery");
      this.getCelendarEvent_NDR_list();
    } else if (this.state.selectedCalendar == "Crane Calendar") {
      this.props.toggleAddCalendar("Crane");
      this.getEventCraneList();
    } else if (this.state.selectedCalendar == "Inspection Calendar") {
      this.props.toggleAddCalendar("Inspection");
      this.getCelendarEvent_INS_list();
    }
    else {
      this.props.toggleAddCalendar("Concrete");
      this.getCalendarConcrete();
    }
    this.getDropdownDetails();
    this.getCompanyListItems();
    this.getComapnyList();
    this.getResponsibelList();
    this.getGateList();
    this.getEquipmentList();
  };

  getDropdownDetails = () => {
    let url = `${CONCRETE_DROPDOWN_DETAILS}?ProjectId=${this?.props?.projectDetails?.id}&ParentCompanyId=${this?.props?.projectDetails?.ParentCompany?.id}`;
    try {
      getConcreteDropDownDetails(
        url,
        {},
        () => null,
        (response) => {
          if (response.toString() == Strings.errors.timeout) {
            this.setState(
              {
                showToaster: true,
                toastMessage: Strings.errors.checkInternet,
                type: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                    disableSubmit: false,
                  });
                }, 2000);
              }
            );
          } else if (response.status) {
            if (response.status == 200 || response.status == 201) {
              this.storeDropDownList(response.data.data);
            } else if (response.data.message.message) {
              this.showToaster("error", response.data.message.message);
            } else {
              this.showToaster("error", response.data.message);
            }
          } else {
            this.showToaster("error", response.toString());
          }
        }
      );
    } catch (e) { }
  };

  storeDropDownList = (data) => {
    let locList = [];
    if (
      data.locationDropdown != null &&
      data.locationDropdown != undefined &&
      data.locationDropdown.length > 0
    ) {
      for (let item of data.locationDropdown) {
        locList.push({
          label: item.location,
          value: item.location,
          id: item.id,
          name: item.location,
        });
      }
      this.setState({ locationsList: locList });
    }

    let supplierList = [];
    if (
      data.concreteSupplierDropdown != null &&
      data.concreteSupplierDropdown != undefined &&
      data.concreteSupplierDropdown.length > 0
    ) {
      for (let item of data.concreteSupplierDropdown) {
        supplierList.push({
          label: item.companyName,
          value: item.companyName,
          id: item.id,
          name: item.companyName,
        });
      }
      this.setState({ concreteSuppliersList: supplierList });
    }

    let mixDesignList = [];
    if (
      data.mixDesignDropdown != null &&
      data.mixDesignDropdown != undefined &&
      data.mixDesignDropdown.length > 0
    ) {
      for (let item of data.mixDesignDropdown) {
        mixDesignList.push({
          label: item.mixDesign,
          value: item.mixDesign,
          id: item.id,
          name: item.mixDesign,
        });
      }
      this.setState({ mixDesignList: mixDesignList });
    }
  };

  getCompanyListItems = () => {
    getCompanyprojectlist(
      GET_COMPANY_PROJECT_LIST,
      {},
      () => null,
      (resp) => {
        if (resp.status == 200) {
          this.setState({ parentcompanyid: resp.data.data[0].id }, () => {
            this.getCompanyProject();
          });
        }
      }
    );
  };

  getCompanyProject = () => {
    getProjectList(
      GET_ACCOUNT_PROJECT + `${this.state.parentcompanyid}`,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.data) {
            let data = [];
            if (this?.props?.projectDetails?.id) {
              data = this?.props?.projectDetails;
            } else {
              data = response.data.data[0];
            }
            this.getRole(data);
          }
        }
      }
    );
  };

  getRole = (data) => {
    let url = GET_PROJECT_ROLE + data.id + "/" + data.ParentCompany.id;

    getprojectRole(
      url,
      {},
      () => null,
      (response) => {
        if (response.data) {
          this.props.storeProjectRole(response.data.data.RoleId);
        }
      }
    );
  };

  getEquipmentList = () => {
    let param = {
      isFilter: true,
      showActivatedAlone: true
    };
    let url = `${GET_EQUIP_LIST}${this?.props?.projectDetails?.id}/20/${this.page_number}/${this?.props?.projectDetails?.ParentCompany?.id}`;
    getEquipList(
      url,
      param,
      () => null,
      (equipResp) => {
        if (equipResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (equipResp.status) {
          if (equipResp.data.data.rows.length !== 0) {
            let equipTypelist = [];
            for (let i = 0; i < equipResp.data.data.rows.length; i++) {
              if (equipResp.data.data.rows[i].id) {
                equipTypelist.push({
                  id: equipResp.data.data.rows[i].id,
                  value: equipResp.data.data.rows[i].equipmentName,
                  label: equipResp.data.data.rows[i].equipmentName,
                  name: equipResp.data.data.rows[i].equipmentName,
                });
              }
            }
            const newArray = equipTypelist.reduce((arr, item) => {
              const exists = !!arr.find((x) => x.value === item.value);
              if (!exists) {
                arr.push(item);
              }
              return arr;
            }, []);

            this.setState({
              equipmentList: newArray,
            });
          }
        }
      }
    );
  };

  getGateList = () => {
    let param = { isFilter: true, showActivatedAlone: true };
    getGateList(
      GET_GATE_LIST +
      this?.props?.projectDetails?.id +
      "/0/0/" +
      this?.props?.projectDetails?.ParentCompany?.id,
      param,
      () => null,
      (gateResp) => {
        if (gateResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (gateResp.status) {
          if (gateResp.data.data.length !== 0) {
            let gateList = [];

            for (let gate of gateResp.data.data) {
              gateList.push({
                id: gate.id,
                value: gate.gateName,
                label: gate.gateName,
                name: gate.gateName,
              });
            }

            this.setState({
              gateList: gateList,
            });
          }
        }
      }
    );
  };

  getResponsibelList = () => {
    getAllMemberList(
      `${LIST_ALL_MEMBER}${this?.props?.projectDetails?.id}/${this?.props?.projectDetails?.ParentCompany?.id}`,
      {},
      () => null,
      (memResp) => {
        if (memResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (memResp.status) {
          if (memResp.data.message == "Member listed Successfully.") {
            this.storeMemberList(memResp.data.data);
          }
        }
      }
    );
  };

  getComapnyList = () => {
    getNewCompanyList(
      `${GET_NEW_COMPANIES}${this?.props?.projectDetails?.id}/${this?.props?.projectDetails?.ParentCompany?.id}`,
      {},
      () => null,
      (compResp) => {
        if (compResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (compResp.status) {
          if (compResp.data.message == "Company list.") {
            this.storeCompanyList(compResp.data.data);
          }
        }
      }
    );
  };

  storeMemberList = (data) => {
    let memberList = [];

    for (let item of data) {
      if (item.User != null) {
        if (item.User.firstName != null) {
          memberList.push({
            label:
              item.User.firstName +
              " " +
              item.User.lastName +
              " (" +
              item.User.email +
              ")",
            value: item.User.email,
            id: item.id,
            name: item.User.email,
          });
        } else {
          memberList.push({
            label: item.User.email,
            value: item.User.email,
            id: item.id,
            name: item.User.email,
          });
        }
      }
    }

    this.setState({ responiblePersonList: memberList });
  };

  storeCompanyList = (data) => {
    let companyList = [];

    for (let item of data) {
      companyList.push({
        label: item.companyName,
        value: item.companyName,
        id: item.id,
        name: item.companyName,
      });
    }

    this.setState({ companyFilterList: companyList });
  };

  cardData = (e, label) => {
    switch (label) {
      case 'Description':
        return e.description

      case 'Responsible Company':
        return e?.companyDetails[0].Company.companyName;

      case 'Responsible Person':
        return `${e?.memberDetails[0].Member.User.firstName} ${e.memberDetails[0].Member.User.lastName}`;

      case 'Gate':
        return e?.gateDetails[0]?.Gate?.gateName;

      case 'Delivery ID':
        return e?.DeliveryId;

      case 'Definable Feature Of Work':
        return e?.defineWorkDetails.length > 0 ? e.defineWorkDetails[0].DeliverDefineWork?.DFOW : '';

      case 'Equipment':
        return e?.equipmentDetails.length > 0 ? e.equipmentDetails.map(item => item?.Equipment?.equipmentName || '').join(', ') : '';


      case 'Crane Pick ID':
        return e?.CraneRequestId;

      case 'Picking To':
        return e?.requestType == Strings.events.deliveryRequestWithCrane ? e?.craneDropOffLocation : e?.dropOffLocation;

      case 'Picking From':
        return e?.requestType == Strings.events.deliveryRequestWithCrane ? e?.cranePickUpLocation : e?.pickUpLocation;

      case 'Concrete Request ID':
        return e?.ConcreteRequestId;

      case 'Location':
        return e?.locationDetails[0].ConcreteLocation.location;

      case 'Concrete Supplier':
        return e?.concreteSupplierDetails[0].Company.companyName;
      default:
        return ''
    }
  }
  colorDisplay = (colorData, defaultValue) => {

    colorobj = {
      Approved: Colors.approvedColorObject,
      Pending: Colors.TentativeEventColor,
      Declined: Colors.declinedColorObject,
      Expired: Colors.expiredColorObject,
      Delivered: Colors.deliveredColorObject,
      Pass: Colors.deliveredColorObject,
      Fail: Colors.deliveredColorObject,
      Completed: Colors.deliveredColorObject,
      Tentative: Colors.TentativeEventColor,
      CalendarEvent: Colors.calendarSettingsText
    };
    eventcolorobj = {
      Pending: Colors.pendingEventColor,
      Declined: Colors.declinedEventColor,
      Expired: Colors.expiredEventColor,
      Delivered: Colors.deliveredEventColor,
      Completed: Colors.deliveredEventColor,
      Pass: Colors.deliveredEventColor,
      Fail: Colors.deliveredEventColor,
      Tentative: Colors.TentativeEventColor,
      CalendarEvent: Colors.calendarSettingsText,
    };
    textcolorobj = {
      Approved: Colors.darkBlue,
      Pending: Colors.darkBlue,
      Declined: Colors.darkBlue,
      Expired: Colors.darkBlue,
      Delivered: Colors.darkBlue,
      Completed: Colors.darkBlue,
      Pass: Colors.darkBlue,
      Fail: Colors.darkBlue,
      Tentative: Colors.darkBlue,
      CalendarEvent: Colors.darkBlue,
    };
    if (!defaultValue) {
      colorData.map((data) => {
        if (data.status == 'approved') {
          eventcolorobj.Approved = data.backgroundColor
          colorobj.Approved = data.fontColor
          textcolorobj.Approved = data.fontColor
        } else if (data.status == 'pending') {
          eventcolorobj.Pending = data.backgroundColor
          eventcolorobj.Tentative = data.backgroundColor
          colorobj.Pending = data.fontColor
          colorobj.Tentative = data.fontColor
          textcolorobj.Pending = data.fontColor
          textcolorobj.Tentative = data.fontColor
        } else if (data.status == 'delivered') {
          eventcolorobj.Delivered = data.backgroundColor
          eventcolorobj.Completed = data.backgroundColor
          eventcolorobj.Pass = data.backgroundColor
          eventcolorobj.Fail = data.backgroundColor
          colorobj.Delivered = data.backgroundColor
          colorobj.Completed = data.backgroundColor
          colorobj.Pass = data.backgroundColor
          colorobj.Fail = data.backgroundColor
          textcolorobj.Delivered = data.fontColor
          textcolorobj.Completed = data.fontColor
          textcolorobj.Pass = data.fontColor
          textcolorobj.Fail = data.fontColor
        } else if (data.status == 'rejected') {
          eventcolorobj.Declined = data.backgroundColor
          colorobj.Declined = data.fontColor
          textcolorobj.Declined = data.fontColor
        } else if (data.status == 'expired') {
          eventcolorobj.Expired = data.backgroundColor
          colorobj.Expired = data.fontColor
          textcolorobj.Expired = data.fontColor
        }
      })
    }
  }
  getCalendarConcrete = async () => {
    this.setState({
      showLoader: true,
      currentScreen: "Concrete",
    });
    let param = "";
    if (this.state.filter == true) {
      param = {
        start: moment(this.state.monthStart).format('YYYY-MM-DD'),
        end: moment(this.state.monthEnd).format('YYYY-MM-DD'),
        filterCount: this.state.filterCountData,
        locationFilter: this.state.selectedLocationName,
        descriptionFilter: this.state.descriptionFilter,
        statusFilter: this.state.selectedStatusName,
        orderNumberFilter: this.state.orderNumber,
        mixDesignFilter: this.state.selectedMixDesignName,
        concreteSupplierFilter: this.state.selectedConcreteSupplier,
        search: this.state.searchText,
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
        calendarView: "Month",
      };
    } else {
      param = {
        start: this.state.monthStart,
        end: this.state.monthEnd,
        search: this.state.searchText,
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
        filterCount: 0,
      };
    }
    let concreteUrl = `${GET_CALENDAR_CONCRETE}/${this?.props?.projectDetails?.id
      }/${"0"}`;
    await calendarConcreteList(
      concreteUrl,
      param,
      () => null,
      (response) => {

        this.setState({
          showLoader: false,
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          showNoData: false,
        });
        if (response.status) {

          if (response.status == 200) {
            if (response.data.data.length <= 0) {
              this.setState({ showNoData: true });
            }
            if (response.data.data) {
              if (response.data.data == "") {
                this.setState({ showLoader: false });
              }

              const colorData = JSON.parse(response.data.statusData.statusColorCode)
              this.colorDisplay(colorData, response.data.statusData.isDefaultColor)
              let concreteData = response.data.data;

              let concreteMarkeddatess = {};
              let concreteEvents = [];

              let label1;
              let label2;
              let concreteDescriptionData = JSON.parse(response.data.cardData.concreteCard)
              concreteDescriptionData.map((data) => {
                if (data.line == 1) {
                  label1 = data.label
                } else if (data.line == 2) {
                  label2 = data.label
                }
              })
              concreteData.forEach((e) => {
                let markedstartdate = moment.parseZone(e.requestType == Strings.events.calendarEvent ? e.fromDate : e.concretePlacementStart).format(
                  "YYYY-MM-DD"
                );
                if (concreteMarkeddatess[markedstartdate]) {
                  if (
                    concreteMarkeddatess[markedstartdate].order >
                    Orderobj[e.requestType == Strings.events.calendarEvent ? 'CalendarEvent' : e.status]
                  ) {
                    concreteMarkeddatess[markedstartdate] = {
                      marked: true,
                      dotColor: colorobj[e.requestType == Strings.events.calendarEvent ? 'CalendarEvent' : e.status],
                      order: Orderobj[e.requestType == Strings.events.calendarEvent ? 'CalendarEvent' : e.status],
                    };
                  }
                } else {
                  concreteMarkeddatess[markedstartdate] = {
                    marked: true,
                    dotColor: colorobj[e.requestType == Strings.events.calendarEvent ? 'CalendarEvent' : e.status],
                    order: Orderobj[e.requestType == Strings.events.calendarEvent ? 'CalendarEvent' : e.status],
                  };
                }
                if (e.requestType != 'calendarEvent') {
                  var endTimeConcreteData = new Date(
                    new Date(e.concretePlacementStart).getFullYear(),
                    new Date(e.concretePlacementStart).getMonth(),
                    new Date(e.concretePlacementStart).getDate(),
                    new Date(e.concretePlacementEnd).getHours(),
                    new Date(e.concretePlacementEnd).getMinutes(),
                    0
                  );
                }

                concreteEvents.push({
                  start: moment.parseZone(e.requestType === Strings.events.calendarEvent ? e.fromDate : e.concretePlacementStart).format(
                    "YYYY-MM-DD HH:mm:ss"
                  ),
                  end: moment.parseZone(e.requestType === Strings.events.calendarEvent ? e.toDate : e.concretePlacementEnd).format(
                    "YYYY-MM-DD HH:mm:ss"
                  ),
                  title: e.description,
                  summary:
                    e.additionalNotes == undefined ? "" : e.additionalNotes,
                  color:
                    e.requestType == Strings.events.calendarEvent ? Colors.calendarSettingsColor : eventcolorobj[
                      e.status
                    ],
                  textColor: e.requestType == 'calendarEvent' ? Colors.calendarSettingsColor : textcolorobj[e.status],
                  day: moment(e.requestType === Strings.events.calendarEvent ? e.fromDate : e.concretePlacementStart).format("YYYY-MM-DD"),
                  id: e.id,
                  requestId: e.ConcreteRequestId,
                  isDelivery: e.requestType == Strings.events.calendarEvent ? false : true,
                  requestType: e.requestType,
                  isAllDay: e.requestType == Strings.events.calendarEvent ? e.isAllDay : false,
                  repeatEveryType: e.requestType == Strings.events.calendarEvent ? e.repeatEveryType : '',
                  repeatEveryCount: e.requestType == Strings.events.calendarEvent ? e.repeatEveryCount : '',
                  days: e.requestType == Strings.events.calendarEvent ? (e.repeatEveryType == Strings.events.weeks || e.repeatEveryType == Strings.events.week) ? e.days : [] : [],
                  chosenDateOfMonth: e.requestType == Strings.events.calendarEvent ? e.chosenDateOfMonth : '',
                  dateOfMonth: e.requestType == Strings.events.calendarEvent ? e.dateOfMonth : '',
                  monthlyRepeatType: e.requestType == Strings.events.calendarEvent ? e.monthlyRepeatType : '',
                  occurTime: e.requestType == Strings.events.calendarEvent ? e.endTime : '',
                  companyDetails: e.requestType == Strings.events.calendarEvent ? '' : this.getResponsibilityCompany(e.concreteSupplierDetails),
                  label1Data: e.requestType == 'calendarEvent' ? e.description : this.cardData(e, label1),
                  label2Data: e.requestType == 'calendarEvent' ? "" : this.cardData(e, label2),
                });
              });

              concreteEvents.sort((a, b) => moment(a.start) - moment(b.start));
              let days = [];
              concreteEvents.forEach((e) => {
                if (days.includes(e.day)) {
                  delete e.day;
                } else {
                  days.push(e.day);
                }
              });
              this.setState({
                markedDates: concreteMarkeddatess,
                event: concreteEvents,
                showLoader: false,
              });
            }
            this.setState({ showLoader: false, showFilter: false });
          } else if (response.status == 400) {
            this.setState({ showLoader: false });
            this.showErrorMessage("error", response.data.message);
          } else {
            this.setState({ showLoader: false });
            this.showErrorMessage("error", response.data.message);
          }
        } else {
          this.setState({ showLoader: false });
          this.showErrorMessage("error", "Please Wait");
        }
      }
    );
    // this.setState({ showLoader: false, showFilter: false });
  };

  getEquipmentDetails(equipmentDetails) {
    return equipmentDetails.map((e) => e.Equipment?.equipmentName).join(", ");
  }

  // Method to refresh crane data - can be called from other components
  refreshCraneData = () => {
    if (this.state.selectedCalendar === "Crane Calendar") {
      this.setState({
        event: [],
        markedDates: {},
        showLoader: true
      }, () => {
        this.getEventCraneList();
      });
    }
  };

  getCraneRequest = () => {
    this.setState({
      showLoader: true,
    });
    let craneParam = {};
    if (this.state.filter == true) {
      craneParam = {
        start: moment.parseZone(this.state.monthStart).format("YYYY-MM-DD"),
        end: moment(this.state.monthEnd).format("YYYY-MM-DD"),
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
        companyFilter: this.state.selectedCompanyId,
        descriptionFilter: this.state.descriptionFilter,
        memberFilter: this.state.selectedResponsibleNameId,
        equipmentFilter: this.state.selectedEquipName,
        statusFilter: this.state.selectedStatusName,
        pickFrom: this.state.pickFrom,
        pickTo: this.state.pickTo,
        search: this.state.searchText,
        gateFilter: this.state.selectedGateNameId,
        filterCount: this.state.filterCountData,
        dateFilter: this.state.dateFilter ? moment(this.state.dateFilter).format("YYYY-MM-DD") : '',
        calendarView: "Month",
        _t: Date.now(), // Add timestamp to prevent caching
      };
    } else {
      craneParam = {
        start: moment.parseZone(this.state.monthStart).format("YYYY-MM-DD"),
        end: moment.parseZone(this.state.monthEnd).format("YYYY-MM-DD"),
        search: this.state.searchText,
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
        filterCount: 0,
        companyFilter: 0,
        dateFilter: "",
        descriptionFilter: "",
        equipmentFilter: "",
        gateFilter: 0,
        memberFilter: 0,
        pickFrom: "",
        pickTo: "",
        statusFilter: "",
        calendarView: "Month",
        _t: Date.now(), // Add timestamp to prevent caching

      };
    }
    let craneUrl = `${CRANE_CALENDER}${this?.props?.projectDetails?.id + "/"
      }${"0"}`;
    getCraneCalendarList(
      craneUrl,
      craneParam,
      () => null,
      (response) => {

        this.setState({
          showLoader: false,
          showIndicator: false,
          showNoData: false,
          clearSearch: this.state.searchText ? true : false,
        });
        if (response.status) {
          // Remove invalid object comparison; instead, check for empty data
          if (!response.data || !response.data.data || response.data.data.length === 0) {
            this.setState({
              showLoader: false,
              showNoData: true,
            });
          }
          if (response.status == 200) {
            if (response.data.data == "" || response.data.data.length <= 0) {
              this.setState({
                showLoader: false,
                showNoData: true,
                event: [],
              });
            }
            if (response.data.data) {
              const colorData = JSON.parse(response.data.statusData.statusColorCode)
              this.colorDisplay(colorData, response.data.statusData.isDefaultColor)
              let craneData = response.data.data;
              let craneMarkeddatess = {};
              let craneEvents = [];
              let label1;
              let label2;
              let craneDescriptionData = JSON.parse(response.data.cardData.craneCard)
              craneDescriptionData.map((data) => {
                if (data.line == 1) {
                  label1 = data.label
                } else if (data.line == 2) {
                  label2 = data.label
                }
              })
              craneData.forEach((e) => {
                let markedstartdate = '';
                if (e.requestType === 'calendarEvent') {
                  markedstartdate = moment.parseZone(e.fromDate).format("YYYY-MM-DD");
                } else {
                  markedstartdate = moment.parseZone(
                    e.isAssociatedWithCraneRequest
                      ? e.deliveryStart
                      : e.craneDeliveryStart
                  ).format("YYYY-MM-DD");
                }
                if (craneMarkeddatess[markedstartdate]) {
                  if (
                    craneMarkeddatess[markedstartdate].order >
                    Orderobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status]
                  ) {
                    craneMarkeddatess[markedstartdate] = {
                      marked: true,
                      dotColor: colorobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status],
                      order: Orderobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status],
                    };
                  }
                } else {
                  craneMarkeddatess[markedstartdate] = {
                    marked: true,
                    dotColor: colorobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status],
                    order: Orderobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status],
                  };
                }
                // For crane requests, use the actual end time from the data
                var endTimeCraneData;
                if (e.requestType != 'calendarEvent') {
                  if (e.isAssociatedWithCraneRequest) {
                    endTimeCraneData = e.deliveryEnd;
                  } else {
                    endTimeCraneData = e.craneDeliveryEnd;
                  }
                }

                // Create the crane event with proper start and end times
                const startTime = e.isAssociatedWithCraneRequest
                  ? e.deliveryStart
                  : e.requestType === 'calendarEvent' ? e.fromDate : e.craneDeliveryStart;
                const endTime = e.requestType === 'calendarEvent' ? e.toDate : endTimeCraneData;



                craneEvents.push({
                  start: moment.parseZone(startTime).format("YYYY-MM-DD HH:mm:ss"),
                  end: moment.parseZone(endTime).format("YYYY-MM-DD HH:mm:ss"),
                  title: e.description,
                  summary:
                    e.additionalNotes == undefined ? "" : e.additionalNotes,
                  color: e.requestType == 'calendarEvent' ? Colors.calendarSettingsColor : eventcolorobj[e.status],
                  textColor: e.requestType == 'calendarEvent' ? Colors.calendarSettingsColor : textcolorobj[e.status],
                  day: moment.parseZone(
                    e.isAssociatedWithCraneRequest
                      ? e.deliveryStart
                      : e.requestType === 'calendarEvent' ? e.fromDate : e.craneDeliveryStart
                  ).format("YYYY-MM-DD"),
                  id: e.id,
                  requestId: e.isAssociatedWithCraneRequest
                    ? e.DeliveryId
                    : e.CraneRequestId,
                  isDelivery: e.requestType == "craneRequest" ? false : true,
                  requestType: e.requestType,
                  isAllDay: e.requestType == 'calendarEvent' ? e.isAllDay : false,
                  repeatEveryType: e.requestType == 'calendarEvent' ? e.repeatEveryType : '',
                  repeatEveryCount: e.requestType == 'calendarEvent' ? e.repeatEveryCount : '',
                  days: e.requestType == 'calendarEvent' ? (e.repeatEveryType == 'Weeks' || e.repeatEveryType == 'Week') ? e.days : [] : [],
                  chosenDateOfMonth: e.requestType == 'calendarEvent' ? e.chosenDateOfMonth : '',
                  dateOfMonth: e.requestType == 'calendarEvent' ? e.dateOfMonth : '',
                  monthlyRepeatType: e.requestType == 'calendarEvent' ? e.monthlyRepeatType : '',
                  occurTime: e.requestType == 'calendarEvent' ? e.endTime : '',
                  companyDetails: e.requestType == Strings.events.calendarEvent ? '' : this.getResponsibilityCompany(e.companyDetails),
                  equipmentDetails: e.requestType == Strings.events.calendarEvent ? '' : this.getEquipmentDetails(e.equipmentDetails),
                  label1Data: e.requestType == 'calendarEvent' ? e.description : this.cardData(e, label1),
                  label2Data: e.requestType == 'calendarEvent' ? "" : this.cardData(e, label2),
                });
              });
              craneEvents.sort((a, b) => moment(a.start) - moment(b.start));
              let days = [];
              craneEvents.forEach((e) => {
                if (days.includes(e.day)) {
                  delete e.day;
                } else {
                  days.push(e.day);
                }
              });
              if (craneEvents.length <= 0) {
                this.setState({
                  showLoader: false,
                  showNoData: true,
                  event: [],
                });
              }
              this.setState({
                markedDates: craneMarkeddatess,
                event: craneEvents,
                showLoader: false,
              });
            }
          } else if (response.status == 400) {
            this.setState({ showLoader: false });
            this.showErrorMessage("error", response.data.message);
          } else {
            this.setState({ showLoader: false });
            this.showErrorMessage("error", response.data.message);
          }
        } else {
          this.setState({ showLoader: false });
          this.showErrorMessage("error", "Please Wait");
        }
      }
    );
  };
  getResponsibilityCompany(companyDetails) {
    return companyDetails.map((e) => e.Company.companyName).join(", ");
  }
  process(data) {
    let deliveryDates = {};
    let drevents = [];
    let drEventData = JSON.parse(data.data.cardData.deliveryCard);
    drEventData.map((data) => {
      if (data.line == 1) {

        label1 = data.label
      } else if (data.line == 2) {
        label2 = data.label
      }
    })
    const colorData = JSON.parse(data.data.statusData.statusColorCode)
    this.colorDisplay(colorData, data.data.statusData.isDefaultColor)

    data.data.data.forEach((e) => {
      let markedstartdate = e.requestType == 'calendarEvent' ? moment.parseZone(e?.fromDate).format("YYYY-MM-DD") : moment.parseZone(e.deliveryStart).format("YYYY-MM-DD");
      if (deliveryDates[markedstartdate]) {
        if (deliveryDates[markedstartdate].order > Orderobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status]) {
          deliveryDates[markedstartdate] = {
            marked: true,
            dotColor: colorobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status],
            order: Orderobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status],
          };
        }
      } else {
        deliveryDates[markedstartdate] = {
          marked: true,
          dotColor: colorobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status],
          order: Orderobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status],
        };
      }

      if (e.requestType != 'calendarEvent') {
        var endTimeData = new Date(
          new Date(e.deliveryStart).getFullYear(),
          new Date(e.deliveryStart).getMonth(),
          new Date(e.deliveryStart).getDate(),
          new Date(e.deliveryEnd).getHours(),
          new Date(e.deliveryEnd).getMinutes(),
          0
        );
      }

      drevents.push({
        start: e.requestType == 'calendarEvent' ? moment.parseZone(e?.fromDate).format("YYYY-MM-DD HH:mm:ss") : moment.parseZone(e.deliveryStart).format("YYYY-MM-DD HH:mm:ss"),
        end: e.requestType == 'calendarEvent' ? moment.parseZone(e?.toDate).format("YYYY-MM-DD HH:mm:ss") : moment.parseZone(e.deliveryEnd).format("YYYY-MM-DD HH:mm:ss"),
        title: e.description,
        summary: e.requestType == 'calendarEvent' ? null : e.notes,
        color: e.requestType == 'calendarEvent' ? Colors.calendarSettingsColor : eventcolorobj[e.status],
        textColor: e.requestType == 'calendarEvent' ? Colors.calendarSettingsColor : textcolorobj[e.status],
        day: e.requestType == 'calendarEvent' ? moment(e.fromDate).format("YYYY-MM-DD") : moment(e.deliveryStart).format("YYYY-MM-DD"),
        id: e.id,
        requestId: e.requestType == 'calendarEvent' ? e.id : e.DeliveryId,
        isDelivery: true,
        requestType: e.requestType,
        isAllDay: e.requestType == 'calendarEvent' ? e.isAllDay : false,
        repeatEveryType: e.requestType == 'calendarEvent' ? e.repeatEveryType : '',
        repeatEveryCount: e.requestType == 'calendarEvent' ? e.repeatEveryCount : '',
        days: e.requestType == 'calendarEvent' ? (e.repeatEveryType == 'Weeks' || e.repeatEveryType == 'Week') ? e.days : [] : [],
        chosenDateOfMonth: e.requestType == 'calendarEvent' ? e.chosenDateOfMonth : '',
        dateOfMonth: e.requestType == 'calendarEvent' ? e.dateOfMonth : '',
        monthlyRepeatType: e.requestType == 'calendarEvent' ? e.monthlyRepeatType : '',
        occurTime: e.requestType == 'calendarEvent' ? e.endTime : '',
        companyDetails: e.requestType == Strings.events.calendarEvent ? '' : this.getResponsibilityCompany(e.companyDetails),
        equipmentDetails: e.requestType == Strings.events.calendarEvent ? '' : this.getEquipmentDetails(e.equipmentDetails),
        label1Data: e.requestType == 'calendarEvent' ? e.description : this.cardData(e, label1),
        label2Data: e.requestType == 'calendarEvent' ? "" : this.cardData(e, label2),
      });
    });
    drevents.sort((a, b) => moment(a.start) - moment(b.start));
    let days = [];
    drevents.forEach((e) => {
      if (days.includes(e.day)) {
        delete e.day;
      } else {
        days.push(e.day);
      }
    });
    if (drevents.length <= 0) {
      this.setState({ showNoData: true, event: [] });
    }
    this.setState({
      markedDates: deliveryDates,
      event: drevents,
      showLoader: false,
    });
    // this.setState({
    //   markedDates: markeddatess,
    //   event: drevents,
    // });

  }

  getCelendarEvent_NDR_list = async () => {
    this.setState({
      showLoader: true,
    });
    let param = {};
    if (this.state.filter == true) {
      param = {
        start: moment.parseZone(this.state.monthStart).format('YYYY-MM-DD'),
        end: moment.parseZone(this.state.monthEnd).format('YYYY-MM-DD'),
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
        companyFilter: this.state.selectedCompanyId,
        descriptionFilter: this.state.descriptionFilter,
        memberFilter: this.state.selectedResponsibleNameId,
        gateFilter: this.state.selectedGateNameId,
        equipmentFilter: this.state.selectedEquipNameId,
        statusFilter: this.state.selectedStatusName,
        pickFrom: this.state.pickFrom,
        pickTo: this.state.pickTo,
        search: this.state.searchText,
        filterCount: this.state.filterCountData,
        dateFilter: this.state.dateFilter ? moment(this.state.dateFilter).format('YYYY-MM-DD') : '',
        calendarView: "Month",
      };
    } else {
      param = {
        start: moment.parseZone(this.state.monthStart).format('YYYY-MM-DD'),
        end: moment.parseZone(this.state.monthEnd).format('YYYY-MM-DD'),
        search: this.state.searchText,
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
        filterCount: 0,
        companyFilter: 0,
        dateFilter: "",
        descriptionFilter: "",
        equipmentFilter: 0,
        gateFilter: 0,
        memberFilter: 0,
        pickFrom: "",
        pickTo: "",
        statusFilter: "",
        calendarView: "Month",
      };
    }

    let url = `${GET_EVENT_NDR}${this?.props?.projectDetails?.id + "/"}${"0"}`;

    let timezoneoffset = `${moment().utcOffset()}`;
    let timehours = `${moment(moment().utcOffset()).format('hh:mm')}`;

    await eventNDR(
      url,
      param,
      timezoneoffset,
      timehours,
      () => null,
      (resp) => {

        this.setState({
          showLoader: false,
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          showNoData: false,
        });
        if (resp.status && resp.status == 200) {

          this.process(resp);
        } else if (resp.status == 500) {
          this.showErrorMessage("error", resp.data.message);
        } else if (resp.data.message) {
          this.showErrorMessage("error", resp.data.message);
        } else {
          this.showErrorMessage("error", resp.data.message);
        }
      }
    );
  };

  processInspection(data) {

    let inspectionDates = {};
    let inevents = [];
    let inEventData = JSON.parse(data.data.cardData.deliveryCard);
    inEventData.map((data) => {
      if (data.line == 1) {

        label1 = data.label
      } else if (data.line == 2) {
        label2 = data.label
      }
    })
    const colorData = JSON.parse(data.data.statusData.statusColorCode)
    this.colorDisplay(colorData, data.data.statusData.isDefaultColor)

    data.data.data.forEach((e) => {
      let markedstartdate = e.requestType == 'calendarEvent' ? moment.parseZone(e?.fromDate).format("YYYY-MM-DD") : moment.parseZone(e.inspectionStart).format("YYYY-MM-DD");
      if (inspectionDates[markedstartdate]) {
        if (inspectionDates[markedstartdate].order > Orderobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status]) {
          inspectionDates[markedstartdate] = {
            marked: true,
            dotColor: colorobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status],
            order: Orderobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status],
          };
        }
      } else {
        inspectionDates[markedstartdate] = {
          marked: true,
          dotColor: colorobj[e.inspectionStatus ? e.inspectionStatus : e.status],
          order: Orderobj[e.requestType == 'calendarEvent' ? 'CalendarEvent' : e.status],
        };
      }

      if (e.requestType != 'calendarEvent') {
        var endTimeData = new Date(
          new Date(e.inspectionStart).getFullYear(),
          new Date(e.inspectionStart).getMonth(),
          new Date(e.inspectionStart).getDate(),
          new Date(e.inspectionEnd).getHours(),
          new Date(e.inspectionEnd).getMinutes(),
          0
        );
      }

      inevents.push({
        start: e.requestType == 'calendarEvent' ? moment.parseZone(e?.fromDate).format("YYYY-MM-DD HH:mm:ss") : moment.parseZone(e.inspectionStart).format("YYYY-MM-DD HH:mm:ss"),
        end: e.requestType == 'calendarEvent' ? moment.parseZone(e?.toDate).format("YYYY-MM-DD HH:mm:ss") : moment.parseZone(e.inspectionEnd).format("YYYY-MM-DD HH:mm:ss"),
        title: e.description,
        summary: e.requestType == 'calendarEvent' ? null : e.notes,
        // color: e.inspectionStatus ?  eventcolorobj[e.inspectionStatus]: null,
        color: e.requestType === 'calendarEvent'
          ? Colors.calendarSettingsColor
          : (e.inspectionStatus !== null && e.inspectionStatus !== undefined && e.inspectionStatus !== ""
            ? eventcolorobj[e.inspectionStatus]
            : eventcolorobj[e.status]),
        textColor: e.requestType == 'calendarEvent' ? Colors.calendarSettingsColor : textcolorobj[e.status],
        day: e.requestType == 'calendarEvent' ? moment(e.fromDate).format("YYYY-MM-DD") : moment(e.inspectionStart).format("YYYY-MM-DD"),
        id: e.id,
        requestId: e.requestType == 'calendarEvent' ? e.id : e.InspectionId,
        isInspection: true,
        status: e.inspectionStatus,
        requestType: e.requestType,
        isAllDay: e.requestType == 'calendarEvent' ? e.isAllDay : false,
        repeatEveryType: e.requestType == 'calendarEvent' ? e.repeatEveryType : '',
        repeatEveryCount: e.requestType == 'calendarEvent' ? e.repeatEveryCount : '',
        days: e.requestType == 'calendarEvent' ? (e.repeatEveryType == 'Weeks' || e.repeatEveryType == 'Week') ? e.days : [] : [],
        chosenDateOfMonth: e.requestType == 'calendarEvent' ? e.chosenDateOfMonth : '',
        dateOfMonth: e.requestType == 'calendarEvent' ? e.dateOfMonth : '',
        monthlyRepeatType: e.requestType == 'calendarEvent' ? e.monthlyRepeatType : '',
        occurTime: e.requestType == 'calendarEvent' ? e.endTime : '',
        companyDetails: e.requestType == Strings.events.calendarEvent ? '' : this.getResponsibilityCompany(e.companyDetails),
        equipmentDetails: e.requestType == Strings.events.calendarEvent ? '' : this.getEquipmentDetails(e.equipmentDetails),
        label1Data: e.requestType == 'calendarEvent' ? e.description : this.cardData(e, label1),
        label2Data: e.requestType == 'calendarEvent' ? "" : this.cardData(e, label2),
      });
    });
    inevents.sort((a, b) => moment(a.start) - moment(b.start));
    let days = [];
    inevents.forEach((e) => {
      if (days.includes(e.day)) {
        delete e.day;
      } else {
        days.push(e.day);
      }
    });
    if (inevents.length <= 0) {
      this.setState({ showNoData: true, event: [] });
    }

    this.setState({
      markedDates: inspectionDates,
      event: inevents,
      showLoader: false,
    });
    // this.setState({
    //   markedDates: markeddatess,
    //   event: drevents,
    // });
  }





  getCelendarEvent_INS_list = async () => {
    this.setState({
      showLoader: true,
      currentScreen: "Inspection",
    });
    // this.setState({
    //   showLoader: true,
    // });
    let param = {};
    if (this.state.filter == true) {
      param = {
        start: moment.parseZone(this.state.monthStart).format('YYYY-MM-DD'),
        end: moment.parseZone(this.state.monthEnd).format('YYYY-MM-DD'),
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
        companyFilter: this.state.selectedCompanyId,
        descriptionFilter: this.state.descriptionFilter,
        memberFilter: this.state.selectedResponsibleNameId,
        gateFilter: this.state.selectedGateNameId,
        equipmentFilter: this.state.selectedEquipNameId,
        statusFilter: this.state.selectedStatusName,
        pickFrom: this.state.pickFrom,
        pickTo: this.state.pickTo,
        inspectionTypeFilter: this.state.inspectionType,
        inspectionStatusFilter: this.state.inspectionStatus,
        search: this.state.searchText,
        filterCount: this.state.filterCountData,
        dateFilter: this.state.dateFilter ? moment(this.state.dateFilter).format('YYYY-MM-DD') : '',
        calendarView: "Month",
      };
    } else {
      param = {
        start: moment.parseZone(this.state.monthStart).format('YYYY-MM-DD'),
        end: moment.parseZone(this.state.monthEnd).format('YYYY-MM-DD'),
        search: this.state.searchText,
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
        filterCount: 0,
        companyFilter: 0,
        dateFilter: "",
        descriptionFilter: "",
        equipmentFilter: 0,
        gateFilter: 0,
        memberFilter: 0,
        pickFrom: "",
        pickTo: "",
        statusFilter: "",
        calendarView: "Month",
      };
    }

    let url = `${GET_EVENT_INS}${this?.props?.projectDetails?.id + "/"}${"0"}`;

    let timezoneoffset = `${moment().utcOffset()}`;
    let timehours = `${moment(moment().utcOffset()).format('hh:mm')}`;

    await eventINS(
      url,
      param,
      timezoneoffset,
      timehours,
      () => null,
      (resp) => {

        this.setState({
          showLoader: false,
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          showNoData: false,
        });
        if (resp.status && resp.status == 200) {

          this.processInspection(resp);
        } else if (resp.status == 500) {
          this.showErrorMessage("error", resp.data.message);
        } else if (resp.data.message) {
          this.showErrorMessage("error", resp.data.message);
        } else {
          this.showErrorMessage("error", resp.data.message);
        }
      }
    );
  };
  /**
   * Crane Delivery List
   */
  getEventCraneList = () => {
    this.setState({
      showLoader: true,
    });
    let param = {};

    if (this.state.filter == true) {
      param = {
        companyFilter: this.state.selectedCompanyId,
        descriptionFilter: this.state.descriptionFilter,
        memberFilter: this.state.selectedResponsibleNameId,
        equipmentFilter: this.state.selectedEquipName,
        statusFilter: this.state.selectedStatusName,
        search: this.state.searchText,
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
        pickFrom: this.state.pickFrom,
        pickTo: this.state.pickTo,
      };
    } else {
      param = {
        search: this.state.searchText,
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
      };
    }

    let url = `${CRANE_DELIVERY_LIST}${this?.props?.projectDetails?.id}/21/${this.page_number}/0`;
    getCraneDeliveryList(url, param, () => null, (response) => {
      this.setState({
        showLoader: false,
        showIndicator: false,
        clearSearch: this.state.searchText ? true : false,
        showNoData: false,
      });
      if (response.toString() == Strings.errors.timeout) {
        this.showErrorMessage("error", Strings.errors.checkInternet);
      } else if (response.status) {
        if (response.status == 200) {
          let data = [];
          let markeddatess = {};
          let events = [];
          if (this.page_number == 1) {
            if (response.data.data.count == 0) {
              this.setState({ showNoData: true, event: [] });
            } else {
              data = response.data.data;
              data.rows.forEach((e) => {
                // Handle different date fields based on request type
                let startDate, endDate;
                if (e.requestType === 'calendarEvent') {
                  startDate = e.fromDate;
                  endDate = e.toDate;
                } else {
                  startDate = e.isAssociatedWithCraneRequest ? e.deliveryStart : e.craneDeliveryStart;
                  endDate = e.isAssociatedWithCraneRequest ? e.deliveryEnd : e.craneDeliveryEnd;
                }

                markeddatess[moment.parseZone(startDate).format("YYYY-MM-DD")] = {
                  marked: true,
                  dotColor: e.requestType === 'calendarEvent' ? colorobj['CalendarEvent'] : colorobj[e.status],
                };
                const eventData = {
                  start: moment.parseZone(startDate).format("YYYY-MM-DD HH:mm:ss"),
                  end: moment.parseZone(endDate).format("YYYY-MM-DD HH:mm:ss"),
                  title: e.description || "No Title",
                  summary: e.notes || e.description || "No Summary",
                  label1Data: e.description || "No Title", // Timeline component expects this
                  label2Data: e.notes || e.description || "No Summary", // Timeline component expects this
                  color: e.requestType === 'calendarEvent' ? Colors.calendarSettingsColor : eventcolorobj[e.status],
                  day: moment.parseZone(startDate).format("YYYY-MM-DD"),
                  id: e.id,
                  requestId: e.isAssociatedWithCraneRequest ? e.DeliveryId : e.CraneRequestId,
                  isDelivery: e.requestType == "craneRequest" ? false : true,
                  request: e.requestType,
                  requestType: e.requestType,
                  isAllDay: e.requestType === 'calendarEvent' ? e.isAllDay : false,
                  repeatEveryType: e.requestType === 'calendarEvent' ? e.repeatEveryType : '',
                  repeatEveryCount: e.requestType === 'calendarEvent' ? e.repeatEveryCount : '',
                  days: e.requestType === 'calendarEvent' ? (e.repeatEveryType === 'Weeks' || e.repeatEveryType === 'Week') ? e.days : [] : [],
                  chosenDateOfMonth: e.requestType === 'calendarEvent' ? e.chosenDateOfMonth : '',
                  dateOfMonth: e.requestType === 'calendarEvent' ? e.dateOfMonth : '',
                  monthlyRepeatType: e.requestType === 'calendarEvent' ? e.monthlyRepeatType : '',
                  occurTime: e.requestType === 'calendarEvent' ? e.endTime : '',
                };
                events.push(eventData);
              });
              events.sort((a, b) => moment(b.start) - moment(a.start));
              let days = [];
              events.forEach((e) => {
                if (days.includes(e.day)) {
                  delete e.day;
                } else {
                  days.push(e.day);
                }
              });
              this.setState({
                markedDates: markeddatess,
                event: events,
              });
              this.setState({
                totalCount: response.data.data.count,
                lastId: response.data.lastId.CraneRequestId,
              });
            }
          } else {
            let data1 = response.data.data;
            data1.rows.forEach((e) => {
              // Handle different date fields based on request type
              let startDate, endDate;
              if (e.requestType === 'calendarEvent') {
                startDate = e.fromDate;
                endDate = e.toDate;
              } else {
                startDate = e.isAssociatedWithCraneRequest ? e.deliveryStart : e.craneDeliveryStart;
                endDate = e.isAssociatedWithCraneRequest ? e.deliveryEnd : e.craneDeliveryEnd;
              }

              markeddatess[moment.parseZone(startDate).format("YYYY-MM-DD")] = {
                marked: true,
                dotColor: e.requestType === 'calendarEvent' ? colorobj['CalendarEvent'] : colorobj[e.status],
              };
              const eventData = {
                start: moment.parseZone(startDate).format("YYYY-MM-DD HH:mm:ss"),
                end: moment.parseZone(endDate).format("YYYY-MM-DD HH:mm:ss"),
                title: e.description || "No Title",
                summary: e.notes || e.description || "No Summary",
                label1Data: e.description || "No Title", // Timeline component expects this
                label2Data: e.notes || e.description || "No Summary", // Timeline component expects this
                color: e.requestType === 'calendarEvent' ? Colors.calendarSettingsColor : eventcolorobj[e.status],
                day: moment.parseZone(startDate).format("YYYY-MM-DD"),
                id: e.id,
                requestId: e.isAssociatedWithCraneRequest ? e.DeliveryId : e.CraneRequestId,
                isDelivery: e.requestType == "craneRequest" ? false : true,
                requestType: e.requestType,
                isAllDay: e.requestType === 'calendarEvent' ? e.isAllDay : false,
                repeatEveryType: e.requestType === 'calendarEvent' ? e.repeatEveryType : '',
                repeatEveryCount: e.requestType === 'calendarEvent' ? e.repeatEveryCount : '',
                days: e.requestType === 'calendarEvent' ? (e.repeatEveryType === 'Weeks' || e.repeatEveryType === 'Week') ? e.days : [] : [],
                chosenDateOfMonth: e.requestType === 'calendarEvent' ? e.chosenDateOfMonth : '',
                dateOfMonth: e.requestType === 'calendarEvent' ? e.dateOfMonth : '',
                monthlyRepeatType: e.requestType === 'calendarEvent' ? e.monthlyRepeatType : '',
                occurTime: e.requestType === 'calendarEvent' ? e.endTime : '',
              };
              events.push(eventData);
            });
            events.sort((a, b) => moment(a.start) - moment(b.start));
            let days = [];
            events.forEach((e) => {
              if (days.includes(e.day)) {
                delete e.day;
              } else {
                days.push(e.day);
              }
            });
            this.setState({
              markedDates: markeddatess,
              event: this.state.event.concat(events),
            });
            this.setState({
              //  drList: data.concat(data1),
              totalCount: response.data.data.count,
              lastId: response.data.lastId.CraneRequestId,
            });
          }
        } else if (response.status == 400) {
          this.showErrorMessage("error", response.data.message);
        }
        else if (response.status == 401) {
          this.showErrorMessage("error", response.data.message);
        }
        else {
          this.showErrorMessage("error", 'Something Went Wrong');
        }
      }
    })
  }

  // getevent_NDR_list = () => {
  //   this.setState({
  //     showLoader: true,
  //   });
  //   let param = {};

  //   if (this.state.filter == true) {
  //     param = {
  //       companyFilter: this.state.selectedCompanyId,
  //       descriptionFilter: this.state.descriptionFilter,
  //       memberFilter: this.state.selectedResponsibleNameId,
  //       gateFilter: this.state.selectedGateNameId,
  //       equipmentFilter: this.state.selectedEquipNameId,
  //       statusFilter: this.state.selectedStatusName,
  //       search: this.state.searchText,
  //       ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
  //       queuedNdr: false,
  //       pickFrom:this.state.pickFrom,
  //       pickTo:this.state.pickTo,
  //     };
  //   } else {
  //     param = {
  //       search: this.state.searchText,
  //       ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
  //       queuedNdr: false,
  //     };
  //   }
  //   let url = "";
  //   url = `${GET_DR_LIST}${this?.props?.projectDetails?.id}/20/${this.page_number}/0`;
  //   eventNDR(
  //     url,
  //     param,
  //     () => null,
  //     (resp) => {
  //       this.setState({
  //         showLoader: false,
  //         showIndicator: false,
  //         clearSearch: this.state.searchText ? true : false,
  //         showNoData: false,
  //       });
  //       if (resp.toString() == Strings.errors.timeout) {
  //         this.showErrorMessage("error", Strings.errors.checkInternet);
  //       } else if (resp.status) {
  //         if (resp.data.message == "Delivery Request listed Successfully.") {
  //           let data = [];
  //           let markeddatess = {};
  //           let events = [];
  //           if (this.page_number == 1) {
  //             if (resp.data.data.count == 0) {
  //               this.setState({ showNoData: true, event: [] });
  //             } else {
  //               data = resp.data.data;
  //               data.rows.forEach((e) => {
  //                 markeddatess[moment(e.deliveryStart).format("YYYY-MM-DD")] = {
  //                   marked: true,
  //                   dotColor: colorobj[e.status],
  //                 };
  //                 events.push({
  //                   start: moment(e.deliveryStart).format(
  //                     "YYYY-MM-DD HH:mm:ss"
  //                   ),
  //                   end: moment(e.deliveryEnd).format("YYYY-MM-DD HH:mm:ss"),
  //                   title: e.description,
  //                   summary: e.notes,
  //                   color: eventcolorobj[e.status],
  //                   day: moment(e.deliveryStart).format("YYYY-MM-DD"),
  //                   id: e.id,
  //                   requestId:e.DeliveryId,
  //                   isDelivery:true,
  //                   requestType:e.requestType,
  //                 });
  //               });
  //               events.sort((a, b) => moment(a.start) - moment(b.start));
  //               let days = [];
  //               events.forEach((e) => {
  //                 if (days.includes(e.day)) {
  //                   delete e.day;
  //                 } else {
  //                   days.push(e.day);
  //                 }
  //               });
  //               this.setState({
  //                 markedDates: markeddatess,
  //                 event: events,
  //               });
  //               this.setState({
  //                 totalCount: resp.data.data.count,
  //                 lastId: resp.data.lastId.DeliveryId,
  //               });
  //             }
  //           } else {
  //             let data1 = resp.data.data;
  //             data1.rows.forEach((e) => {
  //               markeddatess[moment(e.deliveryStart).format("YYYY-MM-DD")] = {
  //                 marked: true,
  //                 dotColor: colorobj[e.status],
  //               };
  //               events.push({
  //                 start: moment(e.deliveryStart).format("YYYY-MM-DD HH:mm:ss"),
  //                 end: moment(e.deliveryEnd).format("YYYY-MM-DD HH:mm:ss"),
  //                 title: e.description,
  //                 summary: e.notes,
  //                 color: eventcolorobj[e.status],
  //                 day: moment(e.deliveryStart).format("YYYY-MM-DD"),
  //                 id: e.id,
  //                 requestId:e.DeliveryId,
  //                 isDelivery:true,
  //                 requestType:e.requestType,
  //               });
  //             });
  //             events.sort((a, b) => moment(a.start) - moment(b.start));
  //             let days = [];
  //             events.forEach((e) => {
  //               if (days.includes(e.day)) {
  //                 delete e.day;
  //               } else {
  //                 days.push(e.day);
  //               }
  //             });
  //             this.setState({
  //               markedDates: markeddatess,
  //               event: this.state.event.concat(events),
  //             });
  //             this.setState({
  //               //  drList: data.concat(data1),
  //               totalCount: resp.data.data.count,
  //               lastId: resp.data.lastId.DeliveryId,
  //             });
  //           }
  //         } else if (resp.data.data.message) {
  //           this.showErrorMessage("error", resp.data.data.message);
  //         } else {
  //           this.showErrorMessage("error", resp.data.message);
  //         }
  //       } else {
  //         this.showErrorMessage("error", resp.toString());
  //       }
  //     }
  //   );
  // };

  deliveryDetails = () => {
    this.setState({
      showLoader: true,
    });
    let url = `${GET_SINGLE_NDR}${this.state.DeliveryRequestId}/${this?.props?.projectDetails?.ParentCompany?.id}`;
    getDeliveryDetails(
      url,
      {},
      () => null,
      (deliveryDetailsresp) => {

        this.setState({
          showLoader: false,
          showIndicator: false,
        });

        if (deliveryDetailsresp.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (deliveryDetailsresp.status) {
          if (
            deliveryDetailsresp.data.message ==
            "Delivery Request listed Successfully."
          ) {
            this.setState({});
          } else if (deliveryDetailsresp.data.data.message) {
            this.showErrorMessage(
              "error",
              deliveryDetailsresp.data.data.message
            );
          } else {
            this.showErrorMessage("error", deliveryDetailsresp.data.message);
          }
        } else {
          this.showErrorMessage("error", deliveryDetailsresp.toString());
        }
      }
    );
  };


  inspectionDetails = () => {
    this.setState({
      showLoader: true,
    });

    let url = `${GET_SINGLE_INS}${this.state.DeliveryRequestId}/${this?.props?.projectDetails?.ParentCompany?.id}`;
    getInspectionDetails(
      url,
      {},
      () => null,
      (inspectionDetailsresp) => {

        this.setState({
          showLoader: false,
          showIndicator: false,
        });

        if (inspectionDetailsresp.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (inspectionDetailsresp.status) {
          if (
            inspectionDetailsresp.data.message ==
            "Inspection Request listed Successfully."
          ) {
            this.setState({});
          } else if (inspectionDetailsresp.data.data.message) {
            this.showErrorMessage(
              "error",
              inspectionDetailsresp.data.data.message
            );
          } else {
            this.showErrorMessage("error", inspectionDetailsresp.data.message);
          }
        } else {
          this.showErrorMessage("error", inspectionDetailsresp.toString());
        }
      }
    );
  };



  showErrorMessage = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showToaster: false,
          });
        }, 2000);
      }
    );
  };

  onDateChanged = (date) => {
    // Update selected date and fetch events for the appropriate range
    const selected = moment(date);
    const startRange = (this.state.filter
      ? selected.clone().startOf("month")
      : selected.clone().startOf("week")).toISOString();
    const endRange = (this.state.filter
      ? selected.clone().endOf("month")
      : selected.clone().endOf("week").add(1, "day")).toISOString();

    this.setState(
      {
        currentDate: date,
        monthStart: startRange,
        monthEnd: endRange,
        currentMonth: selected.format("MMM YYYY"),
      },
      () => {
        if (this.state.selectedCalendar == "Delivery Calendar") {
          this.getCelendarEvent_NDR_list();
        } else if (this.state.selectedCalendar == "Inspection Calendar") {
          this.getCelendarEvent_INS_list();
        } else if (this.state.selectedCalendar == "Crane Calendar") {
          this.getEventCraneList();
        } else {
          this.getCalendarConcrete();
        }
      }
    );
    // Store selected date in Redux for use in add screens
    this.props.setSelectedCalendarDate(date);
  };

  onMonthChange = (month) /* month, updateSource */ => {
    let afterMonthChange = moment(month.dateString)
      .format("MMM YYYY")
      .toString();
    // Compute range: full month when filter is on, otherwise week + next day
    const startOfMonth = (this.state.filter
      ? moment(month.dateString).startOf("month")
      : moment(month.dateString).startOf("week")).toISOString();
    const endOfMonth = (this.state.filter
      ? moment(month.dateString).endOf("month")
      : moment(month.dateString).endOf("week").add(1, "day")).toISOString();
    this.setState(
      {
        monthStart: startOfMonth,
        monthEnd: endOfMonth,
        currentMonth: afterMonthChange,
      },
      () => {
        if (this.state.selectedCalendar == "Delivery Calendar") {
          this.getCelendarEvent_NDR_list();
        } else if (this.state.selectedCalendar == "Inspection Calendar") {
          this.getCelendarEvent_INS_list();
        }
        else if (this.state.selectedCalendar == "Crane Calendar") {
          this.getEventCraneList();
        } else {
          this.getCalendarConcrete();
        }
      }
    );
  };

  renderEmptyItem() {
    return (
      <View style={styles.emptyItem}>
        <Text style={styles.emptyItemText}>No Events Planned</Text>
      </View>
    );
  }

  renderItem = ({ item }) => {
    if (_.isEmpty(item)) {
      return this.renderEmptyItem();
    }

    return (
      <TouchableOpacity style={styles.item}>
        <View>
          <Text style={styles.itemHourText}>{item.hour}</Text>
          <Text style={styles.itemDurationText}>{item.duration}</Text>
        </View>
        <Text style={styles.itemTitleText}>{item.title}</Text>
        <View style={styles.itemButtonContainer}>
          <Button title={"Info"} />
        </View>
      </TouchableOpacity>
    );
  };

  getTheme = () => {
    const themeColor = "#2E2E2E";
    const disabledColor = "#a6acb1";
    const white = "#ffffff";

    return {
      // arrows
      arrowColor: "#CFD5DA",
      arrowStyle: { paddingLeft: 15, paddingRight: 15 },
      // month
      monthTextColor: "#292529",
      textMonthFontSize: wp("4.5%"),
      textMonthFontFamily: Fonts.montserratBold,
      textMonthFontWeight: "bold",
      // day names
      textSectionTitleColor: "#2C3593",
      textDayHeaderFontSize: wp("3%"),
      textDayHeaderFontFamily: Fonts.montserratSemiBold,
      // textDayHeaderFontWeight: 'medium',
      // today
      //todayBackgroundColor: lightThemeColor,
      //todayTextColor: themeColor,
      // dates
      dayTextColor: themeColor,
      textDayFontSize: wp("4%"),
      textDayFontFamily: Fonts.montserratRegular,
      // textDayFontWeight: '500',
      textDayStyle: { marginTop: Platform.OS === "android" ? 2 : 4 },
      // selected date
      selectedDayBackgroundColor: Colors.themeColor,
      selectedDayTextColor: "white",

      // disabled datet
      textDisabledColor: disabledColor,
      // dot (marked date)
      dotColor: themeColor,
      selectedDotColor: white,
      disabledDotColor: disabledColor,
      dotStyle: { marginTop: -2, height: 8, width: 8, borderRadius: 4 },
    };
  };

  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        event: [],
        totalCount: 0,
        showLoader: true,
        filterCountData: 0,
        //load: true,
      },
      () => {
        if (this.state.selectedCalendar == "Delivery Calendar") {
          this.getCelendarEvent_NDR_list();
        } else if (this.state.selectedCalendar == "Inspection Calendar") {
          this.getCelendarEvent_INS_list();
        }
        else if (this.state.selectedCalendar == "Crane Calendar") {
          this.getEventCraneList();
        } else {
          this.getCalendarConcrete();
        }
      }
    );
  };

  onEndReached = () => {
    if (this.state.event.length < this.state.totalCount) {
      this.page_number = this.page_number + 1;
      if (this.state.selectedCalendar == "Delivery Calendar") {
        this.getevent_NDR_list();
      } else if (this.state.selectedCalendar == "Inspection Calendar") {
        this.getevent_NDR_list();
      }
      else if (this.state.selectedCalendar == "Crane Calendar") {
        this.getEventCraneList();
      }
      this.onEndReachedCalledDuringMomentum = true;
    }
  };
  onchangeDate = (tevent, date1) => {
    if (Platform.OS == "android") {
      if (tevent.type == Strings.datePicker.set || tevent.type == Strings.datePicker.dismissed) {
        this.setState({
          isDeliveryDate: false,
        });
      }
    }
    if (Platform.OS == Strings.platforms.ios || tevent.type == Strings.datePicker.set) {
      this.setState({
        dateFilter: moment(date1).format("MM/DD/YYYY")
      });
      selectedDate = date1;

    }
  };
  renderFlatListItem = ({ item, index }) => {
    return (
      <View style={styles.flatlistContainer}>
        <TouchableOpacity
          onPress={() => {
            if (item.requestType === 'concreteRequest') {
              trackScreen('Concrete Details')
            } else if (item.requestType === 'craneRequest') {
              trackScreen('Crane Details')
            }
            else if (item.requestType === 'Inspection') {
              trackScreen('Inspection Details')
            }
            else {
              trackScreen('Delivery Details')
            }

            if (item.requestType === "concreteRequest") {
              this.props.concreteDetailsID(item.requestId);
              this.props.onPressConcreteDetail("ConcreteDetails");
            }
            else if (item.requestType === "calendarEvent") {
              this.props.eventDisplayData(item);
              this.props.enableEditEvents(false);
              this.props.eventDisplayPage('EventDisplay');
            }
            else if (item.requestType === "inspectionRequest") {
              let decidingData = {
                id: item.isInspection ? item.id : item.requestId,
                isInspection: item.isInspection,
              };
              this.props.showDeliverdetailsid(decidingData);
              this.props.onTapDetail("drdetailspage");
            }
            else {
              let decidingData = {
                id: item.isDelivery ? item.id : item.requestId,
                isDelivery: item.isDelivery,
              };
              this.props.showDeliverdetailsid(decidingData);
              this.props.onTapDetail("drdetailspage");
            }
          }}
        >
          <View style={{ flexDirection: "column" }}>
            {item.day && (
              <Text
                style={{
                  marginTop: hp("1%"),
                  marginBottom: hp("1%"),
                  color: "#292529",
                  fontSize: wp("4.5%"),
                  fontFamily: "Montserrat-bold",
                }}
              >
                {moment(item.day).format("ddd, MMM DD")}
              </Text>
            )}
            <View style={{ flexDirection: "row", margin: 10 }}>
              <View style={{ flexDirection: "column", marginRight: 10 }}>
                <Text
                  style={{
                    fontSize: wp("3.5%"),
                    fontFamily: "Montserrat-regular",
                    color: "#BEBEBE",
                    marginBottom: hp("3%"),
                    marginTop: hp("1%"),
                  }}
                >
                  {moment(item.start).format("HH:mm")}
                </Text>
                <Text
                  style={{
                    fontSize: wp("3.5%"),
                    fontFamily: "Montserrat-regular",
                    color: "#BEBEBE",
                  }}
                >
                  {moment(item.end).format("HH:mm")}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: "column",
                  width: wp("80%"),
                  backgroundColor: item.color,
                  borderRadius: 5,
                  marginTop: hp("1%"),
                }}
              >
                <View style={{ flexDirection: 'row' }}>
                  {item.isAllDay &&
                    <Image source={Images.allday} style={{
                      marginLeft: 10, marginRight: -10,
                      marginTop: hp("1%"), width: 16, height: 16
                    }} />
                  }
                  <Text
                    style={{
                      marginLeft: 20,
                      marginTop: hp("1%"),
                      marginBottom: hp("0.5%"),
                      color: item.requestType == 'calendarEvent' ? Colors.darkBlue : item.textColor,
                      fontSize: wp("4%"),
                      fontFamily: "Montserrat-semibold",
                    }}
                    numberOfLines={1}
                  >
                    {item.label1Data}
                  </Text>
                </View>
                {item.isAllDay && <Text style={styles.allDayText}>All Day Calendar Event </Text>}
                <Text style={{
                  marginLeft: 20, color: item.textColor,
                }}>{item.label2Data}</Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  };
  6;
  itemSeparator = () => (
    <View
      style={{
        backgroundColor: "rgb(216,216,216)",
        height: 0.5,
      }}
    />
  );

  applyFilter = () => {
    this.setState(
      {
        filter: true,
        showFilter: false,
      },
      () => {
        this.renderInital();
      }
    );
  };

  // _onReset = () => {
  //   this.setState(
  //     {
  //         totalCount: 0,
  //       showLoader: true,
  //       showIndicator: false,

  //       //load: true,
  //     },
  //     () => {
  //       this.renderInital();
  //     }
  //   );
  // };
  renderSearchBar = () => {
    if (this.state.searchbarShow == true) {
      return this.searchBar();
    } else {
      return this.renderHeader();
    }
  };

  searchBar = () => {
    return (
      <View style={searchStyles.searchHeader}>
        <View style={searchStyles.mainContainer}>
          <TouchableOpacity
            style={searchStyles.closeBtn}
            onPress={() => {
              this.setState(
                {
                  searchbarShow: false,
                  searchText: "",
                },
                this.clearSearch()
              );
            }}
          >
            <Image
              resizeMode={"contain"}
              source={Images.closeBlack}
              style={searchStyles.closeImg}
            />
          </TouchableOpacity>

          <View style={searchStyles.searchPageTitle}>
            <Text style={searchStyles.titleText}>{Strings.search.title}</Text>
          </View>

          <TouchableOpacity
            style={searchStyles.closeBtn}
            onPress={() => {
              if (this.state.showright) {
                this.setState({ showAllDelete: true });
              }
            }}
          >
            {this.state.showright == true && (
              <Image
                resizeMode={"contain"}
                source={Images.delete1}
                style={searchStyles.closeImg}
              />
            )}
          </TouchableOpacity>
        </View>

        <View style={{ flexDirection: "row", justifyContent: "center" }}>
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.SearchHere}
            title={Strings.placeholders.SearchHere}
            value={this.state.searchText}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={searchStyles.txtInputStyles}
            textTitleStyles={searchStyles.txtTitleStyles}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />

          <View style={searchStyles.clearSearchView}>
            {this.state.showIndicator == true && (
              <ActivityIndicator style={{ marginBottom: 5 }} />
            )}
            {this.state.clearSearch == true && (
              <TouchableOpacity onPress={() => this.clearSearch()}>
                <Image source={Images.closeBlack} style={{ marginBottom: 5 }} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    );
  };

  renderHeader() {
    const { event } = this.state
    let count = 0;
    if (this.state.descriptionFilter !== "") {
      count = count + 1;
      this.state.filterCountData = count;
    }

    if (this.state.selectedCompanyId !== 0) {
      count = count + 1;
      this.state.filterCountData = count;
    }

    if (this.state.selectedResponsibleNameId !== 0) {
      count = count + 1;
      this.state.filterCountData = count;
    }

    if (this.state.selectedGateNameId !== 0) {
      count = count + 1;
      this.state.filterCountData = count;
    }

    if (this.state.selectedEquipNameId !== 0) {
      count = count + 1;
      this.state.filterCountData = count;
    }

    if (this.state.selectedLocationId !== 0) {
      count = count + 1;
      this.state.filterCountData = count;
    }
    if (
      this.state.selectedSupplierId !== 0 &&
      this.state.selectedCalendar == "Concrete Calendar"
    ) {
      count = count + 1;
      this.state.filterCountData = count;
    }
    if (this.state.selectedMixDesignId !== 0) {
      count = count + 1;
      this.state.filterCountData = count;
    }

    if (
      this.state.orderNumber !== undefined &&
      this.state.orderNumber !== null &&
      this.state.orderNumber !== ""
    ) {
      count = count + 1;
      this.state.filterCountData = count;
    }

    if (
      this.state.selectedStatusName !== "" &&
      this.state.selectedStatusName !== null
    ) {
      count = count + 1;
      this.state.filterCountData = count;
    }
    if (this.state.pickFrom !== "" && this.state.pickFrom !== null) {
      count = count + 1;
      this.state.filterCountData = count;
    }
    if (this.state.pickTo !== "" && this.state.pickTo !== null) {
      count = count + 1;
      this.state.filterCountData = count;
    }
    if (this.state.inspectionType !== null) {
      count = count + 1;
    }
    if (this.state.inspectionStatus !== null) {
      count = count + 1;
    }

    if (this.state.dateFilter !== "") {
      count = count + 1;
      this.state.filterCountData = count;
    }
    this.state.filterCountData = count;
    let title = "";
    if (this.state.selectedCalendar == "Delivery Calendar") {
      title = Strings.calender.deliveryCalendar;
    } else if (this.state.selectedCalendar == "Inspection Calendar") {
      title = Strings.calender.inspectionCalender;
    }
    else if (this.state.selectedCalendar == "Crane Calendar") {
      title = Strings.calender.craneCalendar;
    } else {
      title = Strings.calender.concreteCalendar;
    }
    return (
      <View style={styles.headerContainer}>
        <Text style={styles.title}>{title}</Text>

        <View style={styles.headerRowContainer}>
          {/* <TouchableOpacity
           style={styles.image}
           onPress={()=>{
            this.setState({ event: [], markedDates: {} });
            this.page_number = 1;
            if(this.state.selectedCalendar=='Delivery Calendar'){
            if (this.state.listClick === true) {
              this.getevent_NDR_list();
            } else {
              this.getCelendarEvent_NDR_list();
            }
          }else{
            if (this.state.listClick === true) {
              this.getEventCraneList();
            }else{
            this.getCraneRequest();
          }
          }
           }}
           >
          <Image
              source={Images.refresh}
              style={{height:hp("2.8%"),width:wp("4.7%")}}
            />
          </TouchableOpacity> */}
          {event.length > 0 &&
            <TouchableOpacity
              style={[
                styles.image,
                {
                  marginTop: wp("1%"),
                  marginBottom:
                    this.state.listClick === true ? hp("1%") : hp("2%"),
                },
              ]}
              onPress={() => {
                this.setState(
                  { listClick: !this.state.listClick, event: [] },
                  () => {
                    this.setState({
                      markedDates: {},
                      event: [],
                    });

                    this.page_number = 1;

                    if (this.state.selectedCalendar == "Delivery Calendar") {
                      this.getCelendarEvent_NDR_list();
                    } else if (this.state.selectedCalendar == "Inspection Calendar") {
                      this.getCelendarEvent_INS_list();
                    }
                    else if (this.state.selectedCalendar == "Crane Calendar") {
                      this.getCraneRequest();
                    } else {
                      this.getCalendarConcrete();
                    }
                  }
                );
              }}
            >
              <Image
                source={
                  this.state.listClick === true ? Images.ham : Images.list_unclick
                }
                style={{
                  height: this.state.listClick ? 28.1 : 18,
                  width: this.state.listClick ? 28 : 18,
                }}
              />
            </TouchableOpacity>
          }

          <TouchableOpacity
            style={[styles.image, { marginTop: wp("1%") }]}
            onPress={() => this.setState({ showFilter: true })}
          >
            <Image source={Images.filter} />

            {this.state.filter == true && (
              <View style={styles.filterCountView}>
                <Text style={{ color: "white" }}>
                  {this.state.filterCountData}
                </Text>
              </View>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.image}
            onPress={() => {
              this.setState({
                searchbarShow: true,
                event: [],
                markedDates: {},
              });
            }}
          >
            <Image source={Images.Search1} style={{ height: 21, width: 21 }} />
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  updateMasterState = (key, value) => {
    this.setState(
      {
        searchText: value,
      },
      () => {
        this.searchList();
      }
    );
  };
  updateMasterStateFilter = (key, value) => {
    if (key == Strings.filter.pickFrom) {
      this.setState({
        pickFrom: value,
      });
    } else if (key == Strings.filter.pickTO) {
      this.setState({
        pickTo: value,
      });
    }
  };

  searchList = () => {
    this.setState(
      { showIndicator: true, clearSearch: false, showLoader: true },
      () => {
        this.renderInital();
      }
    );
  };

  clearSearch = () => {
    this.setState({
      clearSearch: false,
      searchText: "",
      showIndicator: true,
    });
    this.searchList();
  };

  renderNoDeliveryRequest = () => {
    if (this.state.selectedCalendar == "Delivery Calendar") {
      return (
        this.state.showNoData &&
        this.state.listClick && (
          <View style={styles.noDRView}>
            <Text style={styles.noDRText}>No Delivery Requests Found</Text>
          </View>
        )
      );
    } else if (this.state.selectedCalendar == "Crane Calendar") {
      return (
        this.state.showNoData &&
        this.state.listClick && (
          <View style={styles.noDRView}>
            <Text style={styles.noDRText}>No Crane Requests Found</Text>
          </View>
        )
      );
    } else if (this.state.selectedCalendar == "Inspection Calendar") {
      return (
        this.state.showNoData &&
        this.state.listClick && (
          <View style={styles.noDRView}>
            <Text style={styles.noDRText}>No Inspection Request Found</Text>
          </View>
        )
      );
    }
    else if (this.state.selectedCalendar == "Concrete Calendar") {
      return (
        this.state.showNoData &&
        this.state.listClick && (
          <View style={styles.noDRView}>
            <Text style={styles.noDRText}>No Concrete Requests Found</Text>
          </View>
        )
      );
    }
  };
  onReset = () => {
    this.renderInital();
  };
  onSwitchPreformed = async (text) => {
    this.setState(
      {
        showLoader: true,
        filterCountData: 0,
        filter: false,
        descriptionFilter: "",
        selectedMixDesignName: null,
        selectedMixDesignId: 0,
        selectedConcreteSupplier: null,
        selectedSupplierId: 0,
        selectedLocationName: null,
        selectedLocationId: 0,
        selectedStatusName: null,
        selectedStatusId: 0,
        showFilter: false,
        selectedControlledBy: null,
        selectedControlledById: 0,
        selectedEquipId: 0,
        searchId: "",
        searchText: "",
        selectedCompanyName: null,
        selectedCompanyId: 0,
        selectedResponsibleNameId: 0,
        selectedResponsibleName: null,
        selectedEquipName: null,
        selectedEquipNameId: 0,
        selectedGateName: null,
        selectedGateNameId: 0,
        pickFrom: "",
        pickTo: "",
        event: [],
        markedDates: {},
        dateFilter: "",
      },
      async () => {
        if (this.state.currentScreen === "Concrete") {
          this.setState({ currentScreen: "" });
          trackScreen('Concrete Calendar')
        }
        if (this.state.searchbarShow) {
          this.setState(
            {
              searchbarShow: false,
              searchText: "",
              showLoader: true,
              filterCountDate: 0,
            },
            this.clearSearch()
          );
        }
        this.setState({ selectedCalendar: text });
        if (text == "Crane Calendar") {
          trackScreen('Crane Calendar')
          this.setState({
            statusList: [
              {
                label: "Approved",
                value: "Approved",
                id: "1",
                name: "Approved",
              },
              {
                label: "Completed",
                value: "Completed",
                id: "2",
                name: "Completed",
              },
              {
                label: "Declined",
                value: "Declined",
                id: "3",
                name: "Declined",
              },
              {
                label: "Delivered",
                value: "Delivered",
                id: "4",
                name: "Delivered",
              },
              {
                label: "Pending",
                value: "Pending",
                id: "5",
                name: "Pending",
              },
            ],
            filterCountData: 0,
            filter: false,
          });
          this.getEventCraneList();
          await this.props.toggleAddCalendar("Crane");
        }
        // if (text == "Inspection") {
        //   trackScreen('Inspection')
        //   this.setState({
        //     statusList: [
        //       {
        //         label: "Approved",
        //         value: "Approved",
        //         id: "1",
        //         name:"Approved",
        //       },
        //       {
        //         label: "Completed",
        //         value: "Completed",
        //         id: "2",
        //         name:"Completed",
        //       },
        //       {
        //         label: "Declined",
        //         value: "Declined",
        //         id: "3",
        //         name:"Declined",
        //       },
        //       {
        //         label: "Delivered",
        //         value: "Delivered",
        //         id: "4",
        //         name:"Delivered",
        //       },
        //       {
        //         label: "Pending",
        //         value: "Pending",
        //         id: "5",
        //         name:"Pending",
        //       },
        //     ],
        //     filterCountData:0,
        //     filter: false,
        //   });
        //   await this.getCelendarEvent_NDR_list();
        //   await this.props.toggleAddCalendar("Inspection");
        // }
        if (text === "Delivery Calendar" || text === "Inspection Calendar") {

          trackScreen('Delivery Calendar')
          if (text === "Delivery Calendar") {
            this.setState({
              statusList: [
                {
                  label: "Approved",
                  value: "Approved",
                  name: "Approved",
                  id: "1",
                },
                {
                  label: "Declined",
                  value: "Declined",
                  id: "2",
                  name: "Declined",
                },
                {
                  label: "Delivered",
                  value: "Delivered",
                  id: "3",
                  name: "Delivered",
                },
                {
                  label: "Pending",
                  value: "Pending",
                  id: "4",
                  name: "Pending",
                },
              ],
              filterCountData: 0,
              filter: false,
            });

          } else if (text === "Inspection Calendar") {
            this.setState({
              statusList: [
                {
                  label: "Approved",
                  value: "Approved",
                  name: "Approved",
                  id: "1",
                },
                {
                  label: "Declined",
                  value: "Declined",
                  id: "2",
                  name: "Declined",
                },
                {
                  label: "Completed",
                  value: "Completed",
                  id: "3",
                  name: "Completed",
                },
                {
                  label: "Pending",
                  value: "Pending",
                  id: "4",
                  name: "Pending",
                },
              ],
              filterCountData: 0,
              filter: false,
            });

          }

          if (text === "Delivery Calendar") {
            await this.getCelendarEvent_NDR_list();
          } else if (text === "Inspection Calendar") {
            await this.getCelendarEvent_INS_list();
          }
          await this.props.toggleAddCalendar(text.split(" ")[0]);
        }
        if (text == "Concrete Calendar") {
          this.setState({
            statusList: [
              {
                label: "Approved",
                value: "Approved",
                id: "1",
                name: "Approved",
              },
              {
                label: "Completed",
                value: "Completed",
                id: "2",
                name: "Completed",
              },
              {
                label: "Expired",
                value: "Expired",
                id: "3",
                name: "Expired",
              },
              {
                label: "Tentative",
                value: "Tentative",
                id: "4",
                name: "Tentative",
              },
            ],
            filterCountData: 0,
            filter: false,
          });
          this.getCalendarConcrete();
          await this.props.toggleAddCalendar("Concrete");
        }
      }
    )
  };
  render() {
    const { listClick, event } = this.state


    return (
      <>
        {this.state.isNetworkCheck ?
          <NoInternet
            Refresh={() => this.networkCheck()} /> :
          <AppView>
            <View onLayout={() => {
              this.setState({ ...this.state, calendarReady: true });

            }} style={styles.parentContainer}>
              {this.renderSearchBar()}
              {/* <ScrollView 
          refreshControl={
            <RefreshControl
              refreshing={this.state.refresh}
              onRefresh={this.onReset}/>
          }> */}
              {listClick && event.length > 0 && (
                <FlatList
                  data={this.state.event}
                  renderItem={this.renderFlatListItem}
                  ItemSeparatorComponent={this.itemSeparator}
                  keyExtractor={(item, index) => index.toString()}
                  onEndReached={() => this.onEndReached()}
                  onEndReachedThreshold={0}
                  onMomentumScrollBegin={() => {
                    this.onEndReachedCalledDuringMomentum = false;
                  }}
                  onRefresh={() => this._onReset()}
                  refreshing={this.state.refreshing}
                  style={styles.eventFlatListStyle}
                />
              )}

              {/* {!this.state.listClick && ( */}
              {/* <CalendarProvider
              // date={ITEMS[0].title}
              date={this.state.currentDate}
              onDateChanged={this.onDateChanged}
              onMonthChange={this.onMonthChange}
              theme={{ todayButtonTextColor: "#2E2E2E" }}
              showTodayButton={false}
              disabledOpacity={0.6}
              //style={{ marginTop: Platform.OS === "ios" ? 100 : 0 }}
              // todayBottomMargin={16}
            >
              <ExpandableCalendar

                renderHeader={(date) => {
                  return (
                    <CalendarHeader
                    onLeftArrowPressed={() =>
                        this.calendarRef.contentRef.onLeftArrowPressed()
                      }
                      onRightArrowPressed={() =>
                        this.calendarRef.contentRef.onPressArrowRight()
                      }
                      monthName={this.state.currentMonth}
                      swtichCalendar={(text) => this.onSwitchPreformed(text)}
                      currentCalendar={this.state.selectedCalendar}
                      
                    />
                  );
                }}
                // horizontal={false}
                // hideArrows={true}
                ref={(reff) => (this.calendarRef = reff)}
                // disablePan
                // hideKnob
                // initialPosition={ExpandableCalendar.positions.OPEN}
                firstDay={0}
                //showWeekNumbers={true}
                markedDates={this.state.markedDates}
                // markedDates={{
                //   '2020-11-20': { marked: true, dotColor: 'red' },
                //   '2020-11-17': { marked: true },
                //   '2020-11-18': { marked: true, dotColor: 'red' },
                //   '2020-11-19': { dotColor: 'red', marked: true }
                // }}
                //  markedDates={this.getMarkedDates()} // {'2019-06-01': {marked: true}, '2019-06-02': {marked: true}, '2019-06-03': {marked: true}};
                // markedDates={() => {'2019-06-01': {marked: true}, '2019-06-02': {marked: true}, '2019-06-03': {marked: true}}};
                theme={this.getTheme()}
                leftArrowImageSource={Images.arrow_left}
                rightArrowImageSource={Images.arrow_right}
                // calendarStyle={styles.calendar}
                // headerStyle={styles.calendar} // for horizontal only
                // disableWeekScroll
                // dayComponent={({date, state}) => {
                //   return (
                //     <View>
                //       <Text style={{textAlign: 'center', color: state === 'disabled' ? 'gray' : 'black'}}>
                //         {date.day}
                //       </Text>
                //     </View>
                //   );
                //  }}
              />
              <Timeline
                format24h={true}
                eventTapped={(e) => this.eventClick(e)}
                events={this.state.event.filter((event) =>
                  moment(event.start).isSame(this.state.currentDate, "day")
                )}
                currentCalendar={this.state.selectedCalendar}
                //  scrollToFirst={true}
                //start={7}
                // end={24}
              />
            </CalendarProvider> */}

              <CalendarProvider

                // date={ITEMS[0].title}
                date={this.state.currentDate}
                onDateChanged={this.onDateChanged}
                onMonthChange={this.onMonthChange}
                theme={{ todayButtonTextColor: "#2E2E2E" }}
                showTodayButton={false}
                disabledOpacity={0.6}
              // todayBottomMargin={16}
              >

                {this.state.calendarReady && <ExpandableCalendar

                  renderHeader={(date) => {
                    return (
                      <CalendarHeader
                        monthName={this.state.currentMonth}
                        swtichCalendar={(text) => this.onSwitchPreformed(text)}
                        currentCalendar={this.state.selectedCalendar}
                      />
                    );
                  }}
                  // horizontal={false}
                  hideArrows={false}
                  ref={(reff) => (this.calendarRef = reff)}
                  // disablePan
                  // hideKnob
                  // initialPosition={ExpandableCalendar.positions.OPEN}
                  firstDay={0}
                  //showWeekNumbers={true}
                  markedDates={this.state.markedDates}
                  // markedDates={{
                  //   '2020-11-20': { marked: true, dotColor: 'red' },
                  //   '2020-11-17': { marked: true },
                  //   '2020-11-18': { marked: true, dotColor: 'red' },
                  //   '2020-11-19': { dotColor: 'red', marked: true }
                  // }}
                  //  markedDates={this.getMarkedDates()} // {'2019-06-01': {marked: true}, '2019-06-02': {marked: true}, '2019-06-03': {marked: true}};
                  // markedDates={() => {'2019-06-01': {marked: true}, '2019-06-02': {marked: true}, '2019-06-03': {marked: true}}};
                  theme={this.getTheme()}
                  leftArrowImageSource={Images.arrow_left}
                  rightArrowImageSource={Images.arrow_right}
                  calendarStyle={styles.calendar}
                // headerStyle={styles.calendar} // for horizontal only
                // disableWeekScroll
                // dayComponent={({date, state}) => {
                //   return (
                //     <View>
                //       <Text style={{textAlign: 'center', color: state === 'disabled' ? 'gray' : 'black'}}>
                //         {date.day}
                //       </Text>
                //     </View>
                //   );
                //  }}
                />}

                <Timeline
                  format24h={false}
                  eventTapped={(e) => this.eventClick(e)}
                  events={(() => {
                    const filteredEvents = this.state.event.filter((event) => {
                      const eventDate = moment.parseZone(event.start);
                      const currentDate = moment.parseZone(this.state.currentDate);
                      const isSame = eventDate.isSame(currentDate, 'day');
                      return isSame;
                    });
                    return filteredEvents;
                  })()}
                  currentCalendar={this.state.selectedCalendar}
                //  scrollToFirst={true}
                //start={7}
                // end={24}
                />
              </CalendarProvider>
              {/* )} */}

              {/* {this.state.listClick && this.state.event.length > 0 && (
            <FlatList
              data={this.state.event}
              renderItem={this.renderFlatListItem}
              ItemSeparatorComponent={this.itemSeparator}
              keyExtractor={(item, index) => index.toString()}
              onEndReached={() => this.onEndReached()}
              onEndReachedThreshold={0}
              onMomentumScrollBegin={() => {
                this.onEndReachedCalledDuringMomentum = false;
              }}
              onRefresh={() => this._onReset()}
              refreshing={this.state.refreshing}
            />
          )} */}


              {/* </ScrollView> */}
            </View>

            {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}
            {this.state.showFilter && (
              <Modal
                isVisible={this.state.showFilter}
                style={modalStyles.filtermodal}
              >
                {/* {this.renderFilter()} */}
                {this.state.currentScreen == "Concrete"
                  ? this.renderConcreteFilter()
                  : this.renderFilter()}
              </Modal>
            )}
          </AppView>
        }
      </>
    );
  }

  eventClick = (item) => {
    if (item.requestType === 'concreteRequest') {
      trackScreen('Concrete Details')
    } else if (item.requestType === 'craneRequest') {
      trackScreen('Crane Details')
    }
    else if (item.requestType === 'inspectionRequest') {
      trackScreen('Inspection Details')
    }
    else {
      trackScreen('Delivery Details')
    }
    if (item.requestType === "concreteRequest") {
      this.props.concreteDetailsID(item.requestId);
      this.props.onPressConcreteDetail("ConcreteDetails");
    } else if (item.requestType === "calendarEvent") {
      this.props.eventDisplayData(item);
      this.props.enableEditEvents(false);
      this.props.eventDisplayPage('EventDisplay');
    } else if (item.requestType === "inspectionRequest") {

      let decidingData = {
        id: item.isInspection ? item.id : item.requestId,
        isInspection: item.isInspection,
      };
      this.props.showDeliverdetailsid(decidingData);
      this.props.onTapDetail("drdetailspage");
    }
    else if (item.requestType === "craneRequest") {

      let decidingData = {
        id: item.isDelivery ? item.id : item.requestId,
        isDelivery: item.isDelivery,
      };
      this.props.showDeliverdetailsid(decidingData);
      this.props.onTapDetail("drdetailspage");

    }
    else {

      let decidingData = {
        id: item.isDelivery ? item.id : item.requestId,
        isDelivery: item.isDelivery,
      };
      this.props.showDeliverdetailsid(decidingData);
      this.props.onTapDetail("drdetailspage");
    }
  };

  onPressLocationType = (item) => {
    this.setState({
      selectedLocationName: item.value,
      selectedLocationId: item.id,
      locationModalVisible: false,
      filter: true, // Enable filter mode
    }, () => {
      // Automatically refresh calendar data with new filter
      this.renderInital();
    })
  }

  onPressSupplierType = (item) => {
    this.setState({
      selectedConcreteSupplier: item.value,
      selectedSupplierId: item.id,
      supplierModalVisible: false,
      filter: true, // Enable filter mode
    }, () => {
      // Automatically refresh calendar data with new filter
      this.renderInital();
    })
  }

  onPressMixDesignType = (item) => {
    this.setState({
      selectedMixDesignName: item.value,
      selectedMixDesignId: item.id,
      mixDesignDropdown: false,
      filter: true, // Enable filter mode
    }, () => {
      // Automatically refresh calendar data with new filter
      this.renderInital();
    })
  }

  onPressSelectStatusType = (item) => {
    this.setState({
      selectedStatusName: item.value,
      selectedStatusId: item.id,
      selectStatusDropdown: false,
      filter: true, // Enable filter mode
    }, () => {
      // Automatically refresh calendar data with new filter
      this.renderInital();
    })
  }

  renderConcreteFilter = () => {
    return (
      <View style={modalStyles.container}>
        <View style={modalStyles.topContainer}>
          <TouchableOpacity
            onPress={() => this.setState({ showFilter: false })}
            style={{ width: 40 }}
          >
            <Image source={Images.closeBlack} />
          </TouchableOpacity>
          <View style={modalStyles.titleContainer}>
            <Text style={modalStyles.title}>{Strings.filter.title}</Text>
          </View>
          <View style={{ width: 40, height: 40 }} />
        </View>

        <KeyboardAwareScrollView>
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.description}
            title={Strings.placeholders.description}
            value={this.state.descriptionFilter}
            updateMasterState={(key, value) => {
              this.setState({
                descriptionFilter: value,
              });
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />

          <TextField
            attrName={Strings.calender.selectLocation}
            title={Strings.calender.selectLocation}
            value={this.state.selectedLocationName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ locationModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          {/* <DropDownPicker
            items={this.state.locationsList}
            defaultValue={this.state.selectedLocationName}
            placeholder={Strings.placeholders.location}
            placeholderStyle={modalStyles.filterPlaceholder}
            containerStyle={{ height: hp("6%"), marginTop: 8 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedLocationName: item.value,
                selectedLocationId: item.id,
              })
            }
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={5000}
          /> */}

          <TextField
            attrName={Strings.placeholders.supplier}
            title={Strings.placeholders.supplier}
            value={this.state.selectedConcreteSupplier}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ supplierModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          {/* <DropDownPicker
            items={this.state.concreteSuppliersList}
            defaultValue={this.state.selectedConcreteSupplier}
            placeholder={Strings.placeholders.supplier}
            placeholderStyle={modalStyles.filterPlaceholder}
            containerStyle={{ height: hp("6%"), marginTop: 30 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedConcreteSupplier: item.value,
                selectedSupplierId: item.id,
              })
            }
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={4000}
          /> */}

          <TextField
            attrName={Strings.calender.selectMixPanel}
            title={Strings.calender.selectMixPanel}
            value={this.state.selectedMixDesignName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ mixDesignDropdown: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          {/* <DropDownPicker
            items={this.state.mixDesignList}
            defaultValue={this.state.selectedMixDesignName}
            placeholder={Strings.placeholders.mixDesign}
            placeholderStyle={modalStyles.filterPlaceholder}
            containerStyle={{ height: hp("6%"), marginTop: 30 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedMixDesignName: item.value,
                selectedMixDesignId: item.id,
              })
            }
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={3000}
          /> */}

          <TextField
            showLeft={true}
            attrName={Strings.placeholders.orderNumber}
            title={Strings.placeholders.orderNumber}
            value={this.state.orderNumber}
            updateMasterState={(key, value) => {
              this.setState({
                orderNumber: value,
              });
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />

          <TextField
            attrName={Strings.placeholders.selectstatus}
            title={Strings.placeholders.selectstatus}
            value={this.state.selectedStatusName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ selectStatusDropdown: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          {/* <DropDownPicker
            items={this.state.statusList}
            defaultValue={this.state.selectedStatusName}
            placeholder={Strings.placeholders.selectstatus}
            placeholderStyle={modalStyles.filterPlaceholder}
            containerStyle={{ height: hp("6%"), marginTop: 8 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
              // height: hp("20%"),
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedStatusName: item.value,
                selectedStatusId: item.id,
              })
            }
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={1000}
          /> */}

          <View style={[modalStyles.buttonContainer, { marginTop: 50 }]}>
            <TouchableOpacity
              style={
                this.state.filter == true
                  ? [
                    modalStyles.cancelButton,
                    { backgroundColor: Colors.themeOpacity },
                  ]
                  : modalStyles.cancelButton
              }
              onPress={() =>
                this.setState(
                  {
                    showFilter: false,
                    searchId: "",
                    searchText: "",
                  },
                  () => {
                    if (this.state.filter == true) {
                      this.setState(
                        {
                          filter: false,
                          descriptionFilter: "",
                          selectedMixDesignName: null,
                          selectedMixDesignId: 0,
                          selectedConcreteSupplier: null,
                          selectedSupplierId: 0,
                          selectedLocationName: null,
                          selectedLocationId: 0,
                          selectedStatusName: null,
                          selectedStatusId: 0,
                          filterCountData: 0,
                          orderNumber: "",
                        },
                        () => {
                          this.renderInital();
                        }
                      );
                    }
                  }
                )
              }
            >
              <Text
                style={[
                  modalStyles.cancelText,
                  {
                    color:
                      this.state.filter == true
                        ? Colors.themeColor
                        : Colors.buttonBackground,
                  },
                ]}
              >
                {this.state.filter == true
                  ? Strings.addMember.reset
                  : Strings.addMember.cancel}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={modalStyles.applyButton}
              onPress={() => {
                this.applyFilter();
              }}
            >
              <Text style={modalStyles.applyText}>{Strings.filter.apply}</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAwareScrollView>

        <Dropdown
          data={this.state.locationsList}
          title={Strings.placeholders.location}
          value={this.state.selectedLocationName}
          closeBtn={() => this.setState({ locationModalVisible: false })}
          onPress={(item) => this.onPressLocationType(item)}
          visible={this.state.locationModalVisible}
          onbackPress={() => this.setState({ locationModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.concreteSuppliersList}
          title={Strings.placeholders.supplier}
          value={this.state.selectedConcreteSupplier}
          closeBtn={() => this.setState({ supplierModalVisible: false })}
          onPress={(item) => this.onPressSupplierType(item)}
          visible={this.state.supplierModalVisible}
          onbackPress={() => this.setState({ supplierModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />
        <Dropdown
          data={this.state.mixDesignList}
          title={Strings.placeholders.mixDesign}
          value={this.state.selectedMixDesignName}
          closeBtn={() => this.setState({ mixDesignDropdown: false })}
          onPress={(item) => this.onPressMixDesignType(item)}
          visible={this.state.mixDesignDropdown}
          onbackPress={() => this.setState({ mixDesignDropdown: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.statusList}
          title={Strings.placeholders.selectstatus}
          value={this.state.selectedStatusName}
          closeBtn={() => this.setState({ selectStatusDropdown: false })}
          onPress={(item) => this.onPressSelectStatusType(item)}
          visible={this.state.selectStatusDropdown}
          onbackPress={() => this.setState({ selectStatusDropdown: false, })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />
      </View>
    );
  };

  onPressCompanyType = (item) => {
    this.setState({
      selectedCompanyName: item.value,
      selectedCompanyId: item.id,
      companyModalVisible: false,
      filter: true, // Enable filter mode
    }, () => {
      // Update filter count and refresh calendar data
      this.updateFilterCount();
      this.renderInital();
    })
  }

  onPressResPersonType = (item) => {
    this.setState({
      selectedResponsibleName: item.value,
      selectedResponsibleNameId: item.id,
      responisblePersonModal: false,
      filter: true, // Enable filter mode
    }, () => {
      // Automatically refresh calendar data with new filter
      this.renderInital();
    })
  }

  onPressGateModalType = (item) => {
    this.setState({
      selectedGateName: item.value,
      selectedGateNameId: item.id,
      gateModalVisible: false,
      filter: true, // Enable filter mode
    }, () => {
      // Automatically refresh calendar data with new filter
      this.renderInital();
    })
  }

  onPressEquipModalType = (item) => {
    this.setState({
      selectedEquipName: item.value,
      selectedEquipNameId: item.id,
      equipModalVisible: false,
      filter: true, // Enable filter mode
    }, () => {
      // Automatically refresh calendar data with new filter
      this.renderInital();
    })
  }

  onPressStatusModalType = (item) => {
    this.setState({
      selectedStatusName: item.value,
      selectedStatusId: item.id,
      statusModalVisible: false,
      filter: true, // Enable filter mode
    }, () => {
      // Automatically refresh calendar data with new filter
      this.renderInital();
    })
  }

  renderFilter = () => {

    return (
      <View style={modalStyles.container}>
        <View style={modalStyles.topContainer}>
          <TouchableOpacity
            onPress={() => this.setState({ showFilter: false })}
            style={{ width: 40 }}
          >
            <Image source={Images.closeBlack} />
          </TouchableOpacity>
          <View style={modalStyles.titleContainer}>
            <Text style={modalStyles.title}>{Strings.filter.title}</Text>
          </View>
          <View style={{ width: 40, height: 40 }} />
        </View>

        <KeyboardAwareScrollView>
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.description}
            title={Strings.placeholders.description}
            value={this.state.descriptionFilter}
            updateMasterState={(key, value) => {
              this.setState({
                descriptionFilter: value,
              });
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />
          <TextField
            attrName={Strings.placeholders.deliveryDate}
            title={Strings.placeholders.deliveryDate}
            value={this.state.dateFilter}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={modalStyles.textFieldInput}
            textTitleStyles={modalStyles.textFieldTittle}
            showButton={true}
            onPress={() => {
              this.setState({
                isDeliveryDate: true,
              });
            }}
            container={modalStyles.dateFilterContainer}
            imageSource={Images.calGray}
          />

          <TextField
            attrName={Strings.placeholders.company}
            title={Strings.placeholders.company}
            value={this.state.selectedCompanyName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ companyModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          {/* <DropDownPicker
            items={this.state.companyFilterList}
            defaultValue={this.state.selectedCompanyName}
            placeholder={Strings.placeholders.company}
            placeholderStyle={{
              color: Colors.placeholder,
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            containerStyle={{ height: hp("6%"), marginTop: 10 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            itemStyle={{
              justifyContent: "flex-start",
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            labelStyle={{ fontSize: 14, fontFamily: Fonts.montserratMedium }}
            activeLabelStyle={{
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedCompanyName: item.value,
                selectedCompanyId: item.id,
              })
            }
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={5000}
          /> */}

          <TextField
            attrName={Strings.placeholders.responisblePerson}
            title={Strings.placeholders.responisblePerson}
            value={this.state.selectedResponsibleName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ responisblePersonModal: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          {/* <DropDownPicker
            items={this.state.responiblePersonList}
            defaultValue={this.state.selectedResponsibleName}
            placeholder={Strings.placeholders.responisblePerson}
            placeholderStyle={{
              color: Colors.placeholder,
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            containerStyle={{ height: hp("6%"), marginTop: 30 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedResponsibleName: item.value,
                selectedResponsibleNameId: item.id,
              })
            }
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            labelStyle={{
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            activeLabelStyle={{
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={4000}
          /> */}

          <TextField
            attrName={Strings.placeholders.gate}
            title={Strings.placeholders.gate}
            value={this.state.selectedGateName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ gateModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          {/* <DropDownPicker
            items={this.state.gateList}
            defaultValue={this.state.selectedGateName}
            placeholder={Strings.placeholders.gate}
            placeholderStyle={{
              color: Colors.placeholder,
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            containerStyle={{ height: hp("6%"), marginTop: 30 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedGateName: item.value,
                selectedGateNameId: item.id,
              })
            }
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            labelStyle={{ fontSize: 14, fontFamily: Fonts.montserratMedium }}
            activeLabelStyle={{
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={3000}
          /> */}

          <TextField
            attrName={Strings.placeholders.equip}
            title={Strings.placeholders.equip}
            value={this.state.selectedEquipName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ equipModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          {/* <DropDownPicker
            items={this.state.equipmentList}
            defaultValue={this.state.selectedEquipName}
            placeholder={Strings.placeholders.equip}
            placeholderStyle={{
              color: Colors.placeholder,
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            containerStyle={{ height: hp("6%"), marginTop: 30 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            labelStyle={{ fontSize: 14, fontFamily: Fonts.montserratMedium }}
            activeLabelStyle={{
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedEquipName: item.value,
                selectedEquipNameId: item.id,
              })
            }
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={2000}
          /> */}

          <TextField
            attrName={Strings.placeholders.status}
            title={Strings.placeholders.status}
            value={this.state.selectedStatusName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ statusModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          {/* <DropDownPicker
            items={this.state.statusList}
            defaultValue={this.state.selectedStatusName}
            placeholder={"Status"}
            placeholderStyle={{
              color: Colors.placeholder,
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            containerStyle={{ height: hp("6%"), marginTop: 30 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            labelStyle={{ fontSize: 14, fontFamily: Fonts.montserratMedium }}
            activeLabelStyle={{
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedStatusName: item.value,
                selectedStatusId: item.id,
              })
            }
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={1000}
          /> */}

          <TextField
            attrName={Strings.filter.pickFrom}
            title={Strings.filter.pickFrom}
            value={this.state.pickFrom}
            updateMasterState={(key, value) => {
              this.updateMasterStateFilter(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput drStyles
              color: Colors.black,
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
              marginLeft: wp("4%"),
              paddingTop: 10,
            }}
            textTitleStyles={{
              fontSize: 14.6,
              fontFamily: Fonts.montserratMedium,
            }}
            titleInActiveSize={14.6}
          //placeholder={Strings.addCraneRequest.pickingFrom}
          />
          <TextField
            attrName={Strings.filter.pickTO}
            title={Strings.filter.pickTO}
            value={this.state.pickTo}
            updateMasterState={(key, value) => {
              this.updateMasterStateFilter(key, value);
            }}
            textInputStyles={{
              // here you can add additional TextInput drStyles
              color: Colors.black,
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
              marginLeft: wp("4%"),
              paddingTop: 10,
            }}
            textTitleStyles={{
              fontSize: 14.6,
              fontFamily: Fonts.montserratMedium,
            }}
            titleInActiveSize={14.6}
          />
          {this.state.currentScreen === 'Inspection' &&
            (<TextField
              attrName={Strings.addDR.inspectionType}
              title={Strings.addDR.inspectionType}
              value={this.state.inspectionType}
              updateMasterState={this.updateMasterState}
              mandatory={true}
              showButton={true}
              onPress={() => {
                Keyboard.dismiss();
                this.setState({ inspectionModelVisible: true });
              }}
              imageSource={Images.downArr}
              textInputStyles={{
                color: Colors.black,
                fontSize: 14,
              }}
            />)}
          {this.state.currentScreen === 'Inspection' &&
            (<TextField
              attrName={Strings.addDR.inspectionStatus}
              title={Strings.addDR.inspectionStatus}
              value={this.state.inspectionStatus}
              updateMasterState={this.updateMasterState}
              mandatory={true}
              showButton={true}
              onPress={() => {
                Keyboard.dismiss();
                this.setState({ inspectionStatusModelVisible: true });
              }}
              imageSource={Images.downArr}
              // placeholder={"Select"}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
            />
            )}


          {Platform.OS == Strings.platforms.ios && (
            <Modal
              isVisible={this.state.isDeliveryDate}
              onBackdropPress={() => {
                this.setState({ isDeliveryDate: false, });
              }}
              animationInTiming={500}
              style={modalStyles.iosDateModal}
            >
              <DateTimePicker
                value={selectedDate}
                style={modalStyles.iosDatePicker}
                display={Strings.datePicker.inline}
                themeVariant={Strings.datePicker.light}
                accentColor={Colors.themeColor}
                onChange={(time, date) => {
                  this.onchangeDate(time, date);
                }}
              />
              <View>
                <TouchableOpacity
                  activeOpacity={0.5}
                  style={styles.datePickerOkContainer}
                  onPress={() => {
                    this.setState({ isDeliveryDate: false, });
                  }}
                >
                  <Text style={styles.datePickerOkLabel}>Done</Text>
                </TouchableOpacity>
              </View>
            </Modal>)}
          {Platform.OS == Strings.platforms.android && this.state.isDeliveryDate && (
            <DateTimePicker
              value={selectedDate}
              style={modalStyles.androidDatePicker}
              display={Strings.datePicker.default}
              onChange={(time, date) => {
                this.onchangeDate(time, date);
              }}

            />
          )}
          <View style={[modalStyles.buttonContainer, { marginTop: 50 }]}>
            <TouchableOpacity
              style={
                this.state.filter == true
                  ? [
                    modalStyles.cancelButton,
                    { backgroundColor: Colors.themeOpacity },
                  ]
                  : modalStyles.cancelButton
              }
              onPress={() => {
                selectedDate = new Date()
                this.setState(
                  {
                    showFilter: false,
                    selectedControlledBy: null,
                    selectedControlledById: 0,
                    selectedEquipId: 0,
                    searchId: "",
                    searchText: "",
                    descriptionFilter: "",
                    selectedCompanyName: null,
                    selectedCompanyId: 0,
                    selectedResponsibleNameId: 0,
                    selectedResponsibleName: null,
                    // selectedEquipId: 0,
                    selectedEquipName: null,
                    selectedEquipNameId: 0,
                    selectedGateName: null,
                    selectedGateNameId: 0,
                    selectedStatusName: null,
                    pickFrom: "",
                    pickTo: "",
                    inspectionType: null,
                    inspectionStatus: null,
                    dateFilter: "",
                  },
                  () => {
                    if (this.state.filter == true) {
                      this.setState({ filter: false }, () => {
                        this.renderInital();
                      });
                    }
                  }
                )
              }
              }
            >
              <Text
                style={[
                  modalStyles.cancelText,
                  {
                    color:
                      this.state.filter == true
                        ? Colors.themeColor
                        : Colors.buttonBackground,
                  },
                ]}
              >
                {this.state.filter == true
                  ? Strings.addMember.reset
                  : Strings.addMember.cancel}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={modalStyles.applyButton}
              onPress={() => this.applyFilter()}
            >
              <Text style={modalStyles.applyText}>{Strings.filter.apply}</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAwareScrollView>
        <Dropdown
          data={this.state.companyFilterList}
          title={Strings.placeholders.company}
          value={this.state.selectedCompanyName}
          closeBtn={() => this.setState({ companyModalVisible: false })}
          onPress={(item) => this.onPressCompanyType(item)}
          visible={this.state.companyModalVisible}
          onbackPress={() => this.setState({ companyModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.responiblePersonList}
          title={Strings.placeholders.responisblePerson}
          value={this.state.selectedResponsibleName}
          closeBtn={() => this.setState({ responisblePersonModal: false })}
          onPress={(item) => this.onPressResPersonType(item)}
          visible={this.state.responisblePersonModal}
          onbackPress={() => this.setState({ responisblePersonModal: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.gateList}
          title={Strings.placeholders.gate}
          value={this.state.selectedGateName}
          closeBtn={() => this.setState({ gateModalVisible: false })}
          onPress={(item) => this.onPressGateModalType(item)}
          visible={this.state.gateModalVisible}
          onbackPress={() => this.setState({ gateModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.equipmentList}
          title={Strings.placeholders.equip}
          value={this.state.selectedEquipName}
          closeBtn={() => this.setState({ equipModalVisible: false })}
          onPress={(item) => this.onPressEquipModalType(item)}
          visible={this.state.equipModalVisible}
          onbackPress={() => this.setState({ equipModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.statusList}
          title={Strings.placeholders.status}
          value={this.state.selectedStatusName}
          closeBtn={() => this.setState({ statusModalVisible: false })}
          onPress={(item) => this.onPressStatusModalType(item)}
          visible={this.state.statusModalVisible}
          onbackPress={() => this.setState({ statusModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />
        <Dropdown
          data={this.state.inspectionTypeOptions}
          title={"Inspection Type"}
          value={this.state.inspectionType}
          closeBtn={() => this.setState({ inspectionModelVisible: false })}
          onPress={(item) => this.onPressInspectionType(item)}
          visible={this.state.inspectionModelVisible}
          onbackPress={() => this.setState({ inspectionModelVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.inspectionStatusOptions}
          title={"Inspection Status"}
          value={this.state.inspectionStatus}
          closeBtn={() => this.setState({ inspectionStatusModelVisible: false })}
          onPress={(item) => this.onPressInspectionStatus(item)}
          visible={this.state.inspectionStatusModelVisible}
          onbackPress={() => this.setState({ inspectionStatusModelVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />


      </View>
    );
  };
}

const mapStateToProps = (state) => {
  const {
    projectDetails,
    checkCameBack,
    projectSwitched,
    refresh_dashboard,
    isRefreshCalendar,
    refreshDelivery,
  } = state.LoginReducer;
  return {
    projectDetails,
    checkCameBack,
    projectSwitched,
    refresh_dashboard,
    isRefreshCalendar,
    refreshDelivery,
  };
};

export default compose(
  connect(mapStateToProps, {
    showDeliverdetailsid,
    // showInspectiondetailsid,
    onTapDetail,
    cameBack,
    storeLastid,
    storeProjectRole,
    toggleAddCalendar,
    lastCraneId,
    refreshDashboard,
    refreshCalendar,
    refreshDeliveryList,
    concreteDetailsID,
    onPressConcreteDetail,
    eventDisplayPage,
    eventDisplayData,
    enableEditEvents,
    setSelectedCalendarDate,
  }),
  withBackHandler
)(Calendar);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
    // height: hp("50%"),
  },
  parentContainer: {
    flex: 1,
    backgroundColor: "#FCFBFC",
  },
  headerContainer: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderBottomWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  image: {
    marginRight: wp("6%"),
    marginBottom: hp("2%"),
  },
  headerRowContainer: {
    flex: 1,
    height: hp("15%"),
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  title: {
    color: Colors.black,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratBold,
    marginBottom: hp("2%"),
    marginLeft: wp("4%"),
    width: wp("50%"),
  },
  calendar: {
    paddingLeft: 0,
  },
  section: {
    backgroundColor: "#f0f4f7",
    color: "#79838a",
  },
  item: {
    padding: 20,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#e8ecf0",
    flexDirection: "row",
  },
  itemHourText: {
    color: "black",
  },
  itemTitleText: {
    color: "black",
    marginLeft: 16,
    fontWeight: "bold",
    fontSize: wp("4.5%"),
    fontFamily: "Montserrat-semibold",
  },
  itemButtonContainer: {
    flex: 1,
    alignItems: "flex-end",
  },
  emptyItem: {
    paddingLeft: 20,
    height: 52,
    justifyContent: "center",
    borderBottomWidth: 1,
    borderBottomColor: "#e8ecf0",
  },
  emptyItemText: {
    color: "#79838a",
    fontSize: 14,
  },
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 3,
    alignSelf: "center",
    flexDirection: "row",
    flex: 1,
  },
  noDRView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  noDRText: {
    alignSelf: "center",
    position: "absolute",
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("45%"),
  },
  filterCountView: {
    position: "absolute",
    marginTop: -10,
    right: -10,
    backgroundColor: Colors.themeColor,
    width: 16,
    justifyContent: "center",
    alignItems: "center",
    height: 16,
    borderRadius: 8,
  },
  allDayText: {
    marginLeft: 20,
    color: 'grey',
    fontSize: wp("3%"),
    fontFamily: Fonts.montserratMedium,
  },
  equipmentContainer: {
    height: hp("4%"),
    paddingBottom: 5
  },
  renderEquipStyle: {
    marginBottom: 10,
  },
  equipTextStyle: {
    width: '100%',
    fontSize: 16,
    paddingTop: 2
  },
  datePickerOkLabel: {
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 6,
    paddingBottom: 6,
    fontFamily: Fonts.montserratBold,
  },
  datePickerOkContainer: {
    width: "100%",
    alignItems: "center",
    backgroundColor: Colors.white,
    paddingBottom: hp("2%"),
  },
  eventFlatListStyle: {
    height: '100%'
  },
});

const modalStyles = StyleSheet.create({
  container: {
    flex: 1,
    width: wp("100%"),
    height: hp("95%"),
  },
  topContainer: {
    flexDirection: "row",
    margin: 20,
    height: 40,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 20,
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  buttonContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-end",
    marginBottom: hp("15%"),
    justifyContent: "space-around",
  },
  cancelButton: {
    backgroundColor: `rgba(117,117,117,0.2)`,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonBackground,
    fontSize: wp("4.5%"),
    fontFamily: Fonts.montserratBold,
  },
  applyButton: {
    backgroundColor: Colors.themeOpacity,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  applyText: {
    color: Colors.themeColor,
    fontSize: wp("4.5%"),
    fontFamily: Fonts.montserratBold,
  },
  filtermodal: {
    paddingTop: 45,
    paddingBottom: 30,
    margin: 0,
    backgroundColor: Colors.white,
  },
  filterPlaceholder: {
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,

  },
  textFieldInput: {
    color: Colors.black,
    fontSize: 14,
  },
  textFieldTittle: {
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
  },
  dateFilterContainer: {
    marginTop: 5,
  },
  iosDateModal: {
    paddingTop: 45,
    margin: 0,
    justifyContent: "flex-end",

  },
  iosDatePicker: {
    backgroundColor: Colors.white,
    width: '100%',
    height: hp("40%")
  },
  androidDatePicker: {
    backgroundColor: Colors.white,
    width: '100%',
  },
});

const searchStyles = StyleSheet.create({
  searchHeader: {
    marginTop: hp("2%"),
    height: hp("18%"),
    width: wp("95%"),
    alignSelf: "center",
  },
  mainContainer: {
    width: "100%",
    height: hp("6%"),
    flexDirection: "row",
    alignItems: "center",
  },
  closeBtn: {
    width: wp("15%"),
    height: hp("8%"),
    marginLeft: wp("2%"),
    justifyContent: "center",
    alignItems: "center",
  },
  closeImg: {
    width: wp("5%"),
    height: hp("5%"),
  },
  titleText: {
    color: Colors.black,
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratSemiBold,
  },
  searchPageTitle: { flex: 1, justifyContent: "center", alignItems: "center" },
  txtInputStyles: {
    color: Colors.black,
    fontSize: 14,
    width: "75%",
    marginLeft: wp("10%"),
    fontFamily: Fonts.montserratMedium,
    paddingTop: 10,
  },
  txtTitleStyles: {
    marginLeft: wp("10%"),
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  clearSearchView: {
    position: "absolute",
    right: wp("5%"),
    width: wp("10%"),
    height: hp("5%"),
    marginTop: hp("3%"),
    justifyContent: "flex-end",
    alignItems: "center",
  },
});