import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Platform,
  Linking,
} from "react-native";
import axios from "axios";
import {
  changeTab,
  showSideMenu,
  countryList,
  cameBack,
  editData,
  updateList,
  refreshPage,
  refreshDashboard,
  updateInviteMember,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import {
  AppView,
  AppLoader,
  Toastpopup,
  Alert,
  Dropdown,
  AutoCompleteList,
} from "../../components";
import { TextField } from "../../components/textinput/addMemberTextinput";

import {
  isCompany,
  isEmpty,
  getuniqId,
  Colors,
  Images,
  Strings,
  Fonts,
} from "../../common/";

import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

import {
  ADD_COMPANY,
  EDIT_COMPANY,
  UPDATE_COMPANY_IMAGE,
  DEFINABLE_FEATURE,
} from "../../api/Constants";
import {
  addCompany,
  editCompany,
  updateImage,
  getDefinableFeature,
} from "../../api/Api";
import { trackScreen,trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import MultiSelectDropDown from "../../components/multi-select-dropdown/multiSelectdropDown";
import { ApiKeys, googleAutoCompleteAPI } from "../../api/GoogleServices";
import ImagePicker from "react-native-image-crop-picker";
import AsyncStorage from '@react-native-async-storage/async-storage';
import DeletePop from "../../components/toastpopup/logoutPop";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import { checkAndRequestPermission } from "../../utils/PermissionUtils";
import { PERMISSIONS } from "react-native-permissions";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";

const API_KEY = Platform.OS == "android" ? ApiKeys.android : ApiKeys.ios;

class AddCompanies extends Component {
  constructor(props) {
    super(props);
    this.state = {
      projectModal: false,
      roleModal: false,
      role: "",
      name: "",
      companyName: "",
      addressline1: "",
      addressline2: "",
      mobile: "",
      email: "",
      website: "",
      assignProject: "",
      definableList:[],
      projectlist: [],
      rolelist: [],
      edit: false,
      selectedCountry: [],
      selectedState: [],
      selectedCity: [],
      dfowList: [],
      selecteddfow: [],
      logo: "",
      imageModal: false,
      predictionList: [],
      showAutoComplete: false,
      showPopUp: false,
      showText: '',
      imageOptions: [
        {
          id: 1,
          name: "Camera",
        },
        {
          id: 2,
          name: "Gallery",
        },
      ],
      showCancel: false,
      comparison: [],
      isNetworkCheck:false,
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      }
    };
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.renderInitial();
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.renderInitial();
    }
    })
  }


  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  renderInitial = async() => {
    if (this.props.editedData.item) {
      this.setState({ showLoader: true });
    }
    await this.getDfow();

    
  };

  getDfow = () => {
    getDefinableFeature(
      DEFINABLE_FEATURE +
      this.props.projectDetails.id +
        "/" +
        this.props.projectDetails.ParentCompany.id,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.data.length !== 0) {
            let definableList = [];

            for (let item of response.data.data) {
              definableList.push({
                id: item.id,
                name: item.DFOW,
                label: item.DFOW,
                selected: false,
              });
            }
    
            this.setState({
              dfowList: definableList,
            });
           
          }
        }
        this.checkEdit()
      }
    );
  };           
  checkEdit = () => {
    this.setState({ showLoader: false });
    if (this.props.editedData.item) {
      trackScreen('Edit Company')
      let data = this.props.editedData.item;
      let selectedDefinableList = [];
      for(let item of this.state.dfowList){
        if( data.define.some(
          (person) => person.DeliverDefineWork.DFOW === item.name
        )){
        selectedDefinableList.push({
          id: item.id,
          label: item.label,
          name: item.name,
          selected: true,
        });}
        else{
          selectedDefinableList.push({
            id: item.id,
            label: item.label,
            name: item.name,
            selected: false,
          });
      }
    }

      this.setState(
        {
          edit: true,
          companyName: data.companyName,
          additionalNotes: data.scope ? data.scope : "",
          state: data.state,
          country: data.country,
          city: data.city,
          dfowList: selectedDefinableList,
          zipcode: data.zipCode,
          website: data.website!=null?data.website:"",
          id: data.id,
          addressline1: data.address,
          addressline2: data.secondAddress ? data.secondAddress : "",
          comparison: data,
        },
        () => {
          this.props.editData({});
        }
      );
    } else {
      trackScreen('Add Company')
      this.setState({ edit: false });
    }

  };

  hideToast = () =>
    setTimeout(() => this.setState({ showToaster: false }), 2000);


  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }
  }

  //HEADER COMPONENT
  renderHeader() {
    return (
      <View style={styles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={styles.title}>{this.state.edit?Strings.addCompany.edit:Strings.addCompany.add}</Text>
      </View>
    );
  }

  renderImage() {
    return (
      <View style={styles.imageContainer}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.setState({
              imageModal: true,
            });
          }}
        >
          <View style={styles.imageButton}>
            <Image
              resizeMode={"center"}
              source={
                this.state.logo ? { uri: this.state.logo } : Images.companyPlace
              }
              style={styles.imageButton}
            />
            <View style={styles.camera}>
              <Image source={Images.camRound} />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  renderMemberId() {
    return (
      <View style={styles.memberContainer}>
        <Text style={styles.idTitle}>{Strings.addMember.id}</Text>
        <Text style={styles.idText}>{this.props.lastid}</Text>
      </View>
    );
  }

  validateWebsite(input) {
    var res = input.match(
      /(http(s)?:\/\/.)?(www\.)[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g
    );

    return res == null ? false : true;
  }

  validate() {
    let selectedDefinableList = [];
    for (let item of this.state.dfowList) {
      if (item.selected == true) {
        selectedDefinableList.push(item.id);
      }
    }
    if (isEmpty(this.state.companyName.trim())) {
      this.showError("error", Strings.errors.emptyCompany);
      return false;
    } else if (
      this.state.companyName[0] == "&" ||
      isCompany(this.state.companyName)
    ) {
      this.showError("error", Strings.errors.validCompany);
    } else if (this.state.companyName.length < 3) {
      this.showError("error", "Company name " + Strings.errors.lengthError);
    }
    //  else if (isEmpty(selectedDefinableList)) {
    //   this.showError("error", Strings.errors.emptyDefinabel);
    // }
    // else if (isEmpty(this.state.addressline1.trim())) {
    //   this.showError("error", Strings.errors.addressline1);
    //   return false;
    // } else if (this.state.addressline1.length < 3) {
    //   this.showError("error", "Address " + Strings.errors.lengthError);
    //   return false;
    // } else if (isEmpty(this.state.country)) {
    //   this.showError("error", Strings.errors.emptyCountry);
    //   return false;
    // } else if (isEmpty(this.state.state)) {
    //   this.showError("error", Strings.errors.emptyState);
    //   return false;
    // } else if (isEmpty(this.state.city)) {
    //   this.showError("error", Strings.errors.emptyCity);
    //   return false;
    // } else if (isEmpty(this.state.zipcode)) {
    //   this.showError("error", Strings.errors.emptyZipcode);
    //   return false;
    // } else if (isAlphaNumeric(this.state.zipcode)) {
    //   this.showError("error", Strings.errors.validZipCode);
    //   return false;
    //}
     else if (this.state.website!="") {
       if (!this.validateWebsite(this.state.website)) {
      this.showError("error", Strings.errors.validwebsite);
      return false;
      }else{
        return true;
      }
    } else {
      return true;
    }
  }

  submit = async () => {
    let selectedDefinableList = [];
    for (let item of this.state.dfowList) {
      if (item.selected == true) {
        selectedDefinableList.push(item.id);
      }
    }
    if (this.validate()) {
      if (this.state.edit == true) {
        let param = {
          companyName: this.state.companyName,
          scope: this.state.additionalNotes,
          address: this.state.addressline1,
          secondAddress: this.state.addressline2 ? this.state.addressline2 : "",
          city: this.state.city,
          country: this.state.country,
          definableWorkId: selectedDefinableList,
          state: this.state.state,
          zipCode: this.state.zipcode,
          ProjectId: this.props.projectDetails.id,
          website: this.state.website,
          logo: this.state.logo,
          id: this.state.id,
        };

        this.setState({ showLoader: true });

        await editCompany(
          EDIT_COMPANY,
          param,
          () => null,
          (response) => {
            this.setState({ showLoader: false });
            if (response.status) {
              if (response.data.message.message) {
                this.setState(
                  {
                    showToaster: true,
                    toastMessage: response.data.message.message,
                    toastType: "error",
                  },
                  () => this.hideToast()
                );
              } else if (response.data.message) {
                if (response.data.message == "Company Updated Successfully.") {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: response.data.message,
                      toastType: "success",
                    },
                    () => {
                      setTimeout(() => {
                        this.setState({ showToaster: false });
                        // this.props.cameBack(true)
                        if (
                          this.props.route.params?.from == "search"
                        ) {
                          this.props.route.params.updateData("data");
                        }
                        this.props.updateList(false);
                        this.props.refreshPage(true);
                        this.props.refreshDashboard(
                          true,
                          "Add company submit edit"
                        );
                        this.props.navigation.goBack();
                      }, 2000);
                    }
                  );
                  trackEvent('Edited_Company')
                  mixPanelTrackEvent('Edited Company',this.state.mixpanelParam)
                } else {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: response.data.message,
                      toastType: "error",
                    },
                    () => this.hideToast()
                  );
                }
              }
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.toString(),
                  toastType: "error",
                },
                () => this.hideToast()
              );
            }
          }
        );
      } else {
        let param = {
          companyName: this.state.companyName,
          scope: this.state.additionalNotes,
          address: this.state.addressline1,
          secondAddress: this.state.addressline2 ? this.state.addressline2 : "",
          city: this.state.city,
          country: this.state.country,
          definableWorkId: selectedDefinableList,
          state: this.state.state,
          zipCode: this.state.zipcode,
          ProjectId: this.props.projectDetails.id,
          website: this.state.website,
          logo: this.state.logo,
          isParent: false,
        };

        this.setState({ showLoader: true });
        let url = `${ADD_COMPANY}`;
        await addCompany(
          url,
          param,
          () => null,
          (response) => {
            this.setState({ showLoader: false });

            if (response.status) {
              if (response.data.message.message) {
                this.setState(
                  {
                    showToaster: true,
                    toastMessage: response.data.message.message,
                    toastType: "error",
                  },
                  () => this.hideToast()
                );
              } else if (response.data.message) {
                if (response.data.message == "Company Created Successfully.") {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: response.data.message,
                      toastType: "success",
                    },
                    () => {
                      setTimeout(() => {
                        this.setState({ showToaster: false });
                        this.props.refreshPage(true);
                        this.props.refreshDashboard(
                          true,
                          "Add company Submit New"
                        );
                        let isInviteMember=this.props.route.params?.inviteMember;
                        if(isInviteMember== true){
                        this.props.updateInviteMember(true);
                        }
                        this.props.navigation.goBack();
                      }, 2000);
                    }
                  );
                  trackEvent('Added_Company')
                  mixPanelTrackEvent('Added Company',this.state.mixpanelParam)
                } else {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: response.data.message,
                      toastType: "error",
                    },
                    () => this.hideToast()
                  );
                }
              }
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.toString(),
                  toastType: "error",
                },
                () => this.hideToast()
              );
            }
          }
        );
      }
    }
  };
  onPressCancel = () => {
    if (this.state.edit) {
    let data = this.state.comparison;
    let companyName = data.companyName;
    let additionalNotes = data.scope ? data.scope : "";
    let state = data.state;
    let country = data.country;
    let city = data.city;
    let zipcode = data.zipCode;
    let website = data.website;
    let addressline1 = data.address;
    let addressline2 = data.secondAddress ? data.secondAddress : "";
      if (
        companyName == this.state.companyName &&
        addressline1 == this.state.addressline1 &&
        addressline2 == this.state.addressline2 &&
        country == this.state.country &&
        state == this.state.state &&
        city == this.state.city &&
        website == this.state.website &&
        additionalNotes == this.state.additionalNotes &&
        zipcode == this.state.zipcode
      ) {
        this.props.navigation.goBack();
      } else {
        this.setState({ showCancel: true });
      }
    } else {
      this.props.navigation.goBack();
    }
  };
  bottomContainer = () => {
    return (
      <View style={styles.bottomContainer}>
        <TouchableOpacity
          onPress={
            this.onPressCancel
            //() => this.props.navigation.goBack()
          }
        >
          <View style={styles.cancel}>
            <Text style={styles.cancelText}>{Strings.addMember.cancel}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            this.submit();
          }}
        >
          <View style={styles.submit}>
            <Text style={styles.submitText}>
              {this.state.edit == true
                ? Strings.addMember.update
                : Strings.addMember.submit}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  updateMasterState = (key, value) => {
    if (key == Strings.placeholders.companyName) {
      this.setState({ companyName: value });
    } else if (key == Strings.placeholders.addressline1) {
      this.setState({ addressline1: value });
    } else if (key == Strings.placeholders.addressline2) {
      this.setState({ addressline2: value });
    } else if (key == Strings.placeholders.city) {
      this.setState({ city: value });
    } else if (key == Strings.placeholders.state) {
      this.setState({ state: value });
    } else if (key == Strings.placeholders.country) {
      this.setState({ country: value });
    } else if (key == Strings.placeholders.zipcode) {
      this.setState({ zipcode: value });
    } else if (key == Strings.placeholders.website) {
      this.setState({ website: value });
    } else if (key == Strings.placeholders.additionalNotes) {
      this.setState({ additionalNotes: value });
    }
  };

  showError = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
      },
      () => this.hideToast()
    );
  };

  hideImageModal = (image) => {
    this.setState({
      imageModal: false,
    });
    this.uploadImage(image);
  };

  onPressOption = (item) => {
    let imagePickerStyle = { width: 300, height: 400, cropping: true };
    this.setState({imageModal: false,});
    if (item.name == "Camera") {
      checkAndRequestPermission(PERMISSIONS.ANDROID.CAMERA).then(async res => {
        if(res || Platform.OS =='ios') {
      ImagePicker.openCamera({
        ...imagePickerStyle,
      }).then((image) => this.hideImageModal(image));
    } else {
          this.setState({ showPopUp: true, showText: Strings.permissions.camera_permission });
        }
      })
    } else {
      checkAndRequestPermission(Platform.Version >= 33 ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE).then(async res => {
        if (res || Platform.OS == 'ios') {
      ImagePicker.openPicker({
        ...imagePickerStyle,
      }).then((image) => this.hideImageModal(image));
        } else {
          this.setState({ showPopUp: true, showText: Strings.permissions.files_media_permission });
        }
      })
    }
  };

  uploadImage = async (response) => {
    this.setState({
      showLoader: true,
      imageModal: false,
    });

    let headers = {
      Accept: "application/json",
      "Content-Type": "multipart/form-data",
      Authorization: `JWT ${await AsyncStorage.getItem("AccessToken")}`,
    };

    let path = response.path;
    let formData = new FormData();

    formData.append(
      "logo",
      {
        fileCopyUri: path,
        name: Platform.OS == "ios" ? response.filename : `${getuniqId()}.jpg`,
        size: response.size,
        type: "image/jpg",
        uri: path,
      },
      Platform.OS == "ios" ? response.filename : `${getuniqId()}.jpg`
    );

    updateImage(
      `${UPDATE_COMPANY_IMAGE}${this.props.projectDetails.id}/${this.props.projectDetails.ParentCompany.id}`,
      formData,
      headers,
      () => null,
      (imgresp) => {
        this.setState({
          showLoader: false,
        });
       
        if (imgresp.toString() == "Error: timeout of 15000ms exceeded") {
  
        } else if (imgresp.status) {
          if (imgresp.status == 200 || imgresp.status == 201) {
            this.setState({
              logo: imgresp.data.data[0].Location,
            });
          } else {
            
          }
        } else {
          
        }
      }
    );
  };

  onSelectPlace = (place) => {
    const { terms, structured_formatting } = place;
    let city="";
    let mainText = structured_formatting.main_text;
    let secondaryText = structured_formatting.secondary_text;
    let street = `${mainText}`;
    let country = terms[terms.length - 1].value;
    let state =
      terms[terms.length - 2].value != undefined &&
      terms[terms.length - 2].value;
      if(terms.length>2){
        city =terms[terms.length - 3].value != undefined && terms[terms.length - 3].value;
     }

    this.setState({
      addressline1: street,
      country: country,
      state: state,
      city: city,
      showAutoComplete: false,
    });
  };

  getPlacePrediction = async (keyword) => {
    let showPlaces = keyword.toString().length > 0 ? true : false;
    let placeURL = googleAutoCompleteAPI(API_KEY, keyword);
    let placeResponse = await axios.get(placeURL);

    this.setState({
      showAutoComplete: showPlaces,
      predictionList: placeResponse.data.predictions,
    });
  };
  getSelectedDefinableList = (data) => {
    this.setState({
      dfowList: data,
    });
  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <AppView>
        <View style={styles.parentContainer}>
          {this.renderHeader()}

          <KeyboardAwareScrollView extraScrollHeight={50}>
            {this.renderImage()}

            <TextField
              attrName={Strings.placeholders.companyName}
              title={Strings.placeholders.companyName}
              value={this.state.companyName}
              maxLength={150}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
            />
            <Text
              style={
                {
                  color: Colors.placeholder,
                  fontSize: wp("4%"),
                  fontFamily: Fonts.montserratMedium,
                  marginLeft: 10,
                  marginTop: hp("2%"),
                  alignSelf: "center",
                  width: wp("90%"),
                }
              }
            >
              {Strings.placeholders.definable}{" "}
            </Text>

            <MultiSelectDropDown
              dataItems={this.state.dfowList}
              title={"Select"}
              selectedDataItem={this.getSelectedDefinableList}
            />
            <TextField
              attrName={Strings.placeholders.addressline1}
              title={Strings.placeholders.addressline1}
              value={this.state.addressline1}
              maxLength={150}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
                this.getPlacePrediction(value);
              }}
              mandatory={false}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              container={{marginTop:hp('4%')}}
            />

            {this.state.showAutoComplete && (
              <AutoCompleteList
                source={this.state.predictionList}
                onSelectPlace={(place) => this.onSelectPlace(place)}
              />
            )}

            <TextField
              attrName={Strings.placeholders.addressline2}
              title={Strings.placeholders.addressline2}
              value={this.state.addressline2}
              maxLength={150}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={false}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
            />

           

            <TextField
              attrName={Strings.placeholders.city}
              title={Strings.placeholders.city}
              value={this.state.city}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={false}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              // showButton={true}
              // onPress={() => {
              // this.setState({ roleModal: true });
              // }}
              // imageSource={Images.downArr}
              //  placeholder={"Select"}
              // onPress={() => {
              //  this.onPressCityDrop();
              // }}
            />
            <TextField
              attrName={Strings.placeholders.state}
              title={Strings.placeholders.state}
              value={this.state.state}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={false}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              // showButton={true}
              // onPress={() => {
              //  this.setState({ roleModal: true });
              // }}
              //imageSource={Images.downArr}
              // placeholder={"Select"}
              //onPress={() => {
              // this.onPressStateDrop();
              // }}
            />
            <TextField
              attrName={Strings.placeholders.country}
              title={Strings.placeholders.country}
              value={this.state.country}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={false}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              // showButton={true}
              onPress={() => {
                // this.setState({ roleModal: true });
                 // this.setState({ countryVisible: true });
              }}
              // imageSource={Images.downArr}
              // placeholder={"Select"}
            />

            

           

            <TextField
              attrName={Strings.placeholders.zipcode}
              title={Strings.placeholders.zipcode}
              value={this.state.zipcode}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              maxLength={10}
              mandatory={false}
              keyboardType="number-pad"
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
            />

            <TextField
              attrName={Strings.placeholders.website}
              title={Strings.placeholders.website}
              value={this.state.website}
              autoCapitalize="none"
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={false}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
            />

            <TextField
              attrName={Strings.placeholders.additionalNotes}
              title={Strings.placeholders.additionalNotes}
              value={this.state.additionalNotes}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              container={{ height: hp("20%") }}
              mandatory={false}
              maxLength={150}
              textInputStyles={styles.txtAdditonalNotes}
              multiline={true}
            />

            {this.bottomContainer()}
          </KeyboardAwareScrollView>
        </View>

        <Dropdown
          title={Strings.addCompany.chooseCountry}
          closeBtn={() => this.setState({ countryVisible: false })}
          data={this.state.countrylist}
          value={this.state.country}
          onPress={(item) => this.onPressCountry(item)}
          visible={this.state.countryVisible}
          onbackPress={() => this.setState({ countryVisible: false })}
        />

        <Dropdown
          title={Strings.addCompany.chooseState}
          data={this.state.statelist}
          value={this.state.state}
          onPress={(item) => this.onPressState(item)}
          visible={this.state.stateVisible}
          onbackPress={() => this.setState({ stateVisible: false })}
          closeBtn={() => this.setState({ stateVisible: false })}
        />

        <Dropdown
          title={Strings.addCompany.chooseCity}
          data={this.state.citylist}
          value={this.state.city}
          onPress={(item) => this.onPressCity(item)}
          visible={this.state.cityVisible}
          onbackPress={() => this.setState({ cityVisible: false })}
          closeBtn={() => this.setState({ cityVisible: false })}
        />

        <Dropdown
          data={this.state.imageOptions}
          title={Strings.profile.ChooseOption}
          value={""}
          closeBtn={() => this.setState({ imageModal: false })}
          onPress={(item) => this.onPressOption(item)}
          visible={this.state.imageModal}
          onbackPress={() => this.setState({ imageModal: false })}
          container={{ justifyContent: "center", alignItems: "center" }}
          textContainer={{ fontSize: 14 }}
        />
        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.loginSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}
        {this.state.showCancel && this.state.edit && (
          <DeletePop
            title={Strings.popup.cancel}
            desc={Strings.popup.cancel}
            acceptTap={() => {
              this.setState({ showCancel: false });
              this.props.navigation.goBack();
            }}
            container={{ bottom: 0 }}
            declineTap={() => {
              this.setState({ showCancel: false });
            }}
          />
        )}
            {this.state.showPopUp && (
              <DeletePop
                container={styles.containerStyles}
                descStyles={styles.descStyles}
                desc={this.state.showText}
                declineText={Strings.permissions.no_thanks}
                acceptText={Strings.permissions.go_to_settings}
                declineTap={() => this.setState({ showPopUp: false })}
                acceptTap={() => {
                  Linking.openSettings()
                  this.setState({ showPopUp: false })
                }}
                declineTextStyle={styles.textStyle}
                acceptTextStyle={styles.textStyle}
              />
            )}
      </AppView>
       }
       </>
    );
  }
}

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    lastid,
    countrylist,
    projectDetails,
    editedData,
    updateList,
    refresh,
    userDetails,
    updateInviteMem
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    lastid,
    countrylist,
    projectDetails,
    editedData,
    updateList,
    refresh,
    updateInviteMem,
    userDetails,
  };
};

export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    countryList,
    cameBack,
    editData,
    updateList,
    refreshPage,
    refreshDashboard,
    updateInviteMember,
  }),
  withBackHandler
)(AddCompanies);

const styles = StyleSheet.create({
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("80%"),
    fontSize: wp("6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratBold,
  },
  imageContainer: {
    width: wp("100%"),
    height: hp("15%"),
    justifyContent: "center",
    alignItems: "center",
  },
  imageButton: {
    width: wp("35%"),
    height: wp("20%"),
    borderRadius: wp("4%"),
    backgroundColor: "#F5F5F5",
  },
  placeholder: {
    width: wp("15%"),
    alignSelf: "center",
  },
  camera: {
    position: "absolute",
    bottom: -10,
    right: -10,
    width: wp("8%"),
    height: wp("8%"),
    borderRadius: wp("4%"),
    backgroundColor: Colors.white,
    justifyContent: "center",
    alignItems: "center",
  },
  memberContainer: {
    width: wp("90%"),
    height: hp("10%"),
    alignSelf: "center",
    marginTop: hp("2%"),
    justifyContent: "center",
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  idText: {
    width: wp("90%"),
    height: hp("5%"),
    backgroundColor: "#EFEFEF",
    marginTop: wp("1%"),
    padding: hp("1%"),
    paddingLeft: 15,
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("4%"),
  },
  bottomContainer: {
    width: wp("90%"),
    flexDirection: "row",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: hp("4%"),
    marginTop: hp("8%"),
  },
  cancel: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonGray,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  txtAdditonalNotes: {
    // here you can add additional TextInput styles
    color: Colors.black,
    fontSize: 14,
    height: hp("17%"),
    marginTop: hp("2%"),
    paddingTop: hp("3%"),
  },
  descStyles: { 
    fontSize: wp("4.5%"), 
    marginRight: wp("5%"),
  },
  textStyle: { 
    fontSize: wp("4%")
  },
  containerStyles: {
    bottom: 0,
  }
});
