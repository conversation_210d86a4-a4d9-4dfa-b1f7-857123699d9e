import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  Platform,
} from "react-native";

import {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  editData,
  clickAdd,
  onTapSearch,
  updateList,
  refreshPage,
  refreshDashboard,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import {
  GET_COMPANY_LIST,
  DELETE_COMPANY,
  DEFINABLE_FEATURE,
} from "../../api/Constants";
import {
  getCompanyList,
  deleteCompany,
  CompanyGetDefinable,
} from "../../api/Api";

import {
  AppView,
  AppLoader,
  Alert,
  Toastpopup,
  TextField,
  DeletePop,
} from "../../components";

import { Images, Strings, Fonts, Colors } from "../../common";
import ModalDropdown from "react-native-modal-dropdown";
import Modal from "react-native-modal";
import { trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import Dropdown from "../../components/dropdown/dropdown";
import DeleteError from "../../components/DeleteError/DeleteError";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";

let DROPDOWNOPTIONS = [
  { id: "Edit", image: Images.editNew, name: "Edit" },
  { id: "Delete", image: Images.deleteNew, name: "Delete" },
];
let PARENTDROPDOWNOPTIONS = [
  { id: "Edit", image: Images.editNew, name: "Edit" },
];

class CompanyList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      memberslist: [],
      showLoader: false,
      showToaster: false,
      showAlert: false,
      toastMessage: "",
      toastType: "error",
      refreshing: false,
      lastId: 0,
      showNoData: false,
      showDelete: false,
      parentCompany: [],
      showFilter: false,
      definable: "",
      searchText: "",
      selecteddfow: null,
      selecteddfowId: 0,
      dfowList: [],
      totalCount: 0,
       mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      },
      dfwModalVisible:false,
      showError:false,
      errorMessage:"",
      isNetworkCheck:false,
    };

    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;
  }

  componentDidMount = () => {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.renderInital();
    }
  };
  

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.renderInital();
    }
    })
  }



  renderInital = () => {
    this.setState({ showLoader: true });
    this.page_number = 1;
    this.getCompanyList();
    this.getDefinableFeature();
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }
    if (nextProps.refresh == true) {
      this.setState({
        showLoader: true,
      });
      this.renderInital();
      this.props.refreshPage(false);
    }

    if (this.props.projectSwitched != nextProps.projectSwitched) {
      this.renderInital();
    }
  }

  getDefinableFeature = () => {
    let url = `${DEFINABLE_FEATURE}${this.props.projectDetails.id}/${this.props.projectDetails.ParentCompany.id}`;
    CompanyGetDefinable(
      url,
      {},
      () => null,
      (resp) => {
        if (resp.status) {
          if (resp.status == 200) {
            this.storeDfow(resp.data.data);
          }
        }
      }
    );
  };

  storeDfow = (data) => {
    if (data.length == 0) {
      this.setState({
        dfowList: [],
      });
    } else {
      let dfow = [];

      for (let item of data) {
        dfow.push({
          label: item.DFOW,
          value: item.DFOW,
          id: item.id,
          name:item.DFOW,
        });
      }

      this.setState({
        dfowList: dfow,
      });
    }
  };

  hideToast = () =>
    setTimeout(() => this.setState({ showToaster: false }), 2000);

  getCompanyList = async () => {
    let param = {};

    if (this.state.filter == true) {
      param = {
        dfowFilter: this.state.selecteddfowId,
        search: this.state.searchText,
        inviteMember: false,
      };
    } else {
      param = {
        companyFilter: 0,
        dfowFilter: 0,
        search: "",
        inviteMember: false,
      };
    }
    this.setState({ showNoData: false });

    let url = `${GET_COMPANY_LIST}${this.props.projectDetails.id}/20/${this.page_number}/${this.props.projectDetails.ParentCompany.id}`;

    await getCompanyList(
      url,
      param,
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.message === "Company list.") {
            let data = this.state.memberslist;
            // this.setState({
            //   parentCompany: response.data.parentCompany
            //     ? response.data.parentCompany[0]
            //     : "",
            // });

            if (response.data.data.count == 0) {
              this.setState({
                showNoData: false,
                memberslist: [],
                totalCount: 0,
                parentCompany: response.data.parentCompany
                  ? response.data.parentCompany[0]
                  : "",             
              });
            } else if (this.page_number == 1) {
              let sampleData = [];

              sampleData = response.data.data.rows;
              sampleData.unshift(
                response.data.parentCompany
                  ? response.data.parentCompany[0]
                  : ""
              );
              this.setState({
                memberslist: sampleData,
                totalCount: response.data.data.count,
              });
            } else {
              let data1 = response.data.data.rows;
              this.setState({
                memberslist: data.concat(data1),
                totalCount: response.data.data.count,
              });
            }
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };

  renderEmail = (title, name) => {
    return (
      <View style={styles.emailContainer}>
        <Text style={styles.emailTitle}>{title}</Text>
        <Text style={styles.emailText}>{name}</Text>
      </View>
    );
  };

  renderRow = (option, index, isSelected) => {
    return (
      <View
        style={{
          width: wp("40%"),
          flexDirection: "row",
          alignItems: "center",
          height: hp("7%"),
          backgroundColor: "white",
        }}
      >
        <Image
          resizeMode={"center"}
          source={option.image}
          style={{
            width: wp("4.5%"),
            height: hp("4%"),
            marginLeft: 10,
          }}
        />
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: Colors.planDesc,
            marginLeft: 15,
          }}
        >
          {option.id}
        </Text>
      </View>
    );
  };

  renderParentRow = (option, index, isSelected) => {
    return (
      <View
        style={{
          width: wp("40%"),
          flexDirection: "row",
          alignItems: "center",
          height: hp("7%"),
          backgroundColor: "white",
        }}
      >
        <Image
          resizeMode={"center"}
          source={option.image}
          style={{
            width: option.id == "Edit" ? wp("5%") : wp("6%"),
            height: option.id == "Edit" ? hp("4%") : hp("5%"),
            marginLeft: 10,
          }}
        />
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: Colors.planDesc,
            marginLeft: 10,
          }}
        >
          {option.id}
        </Text>
      </View>
    );
  };

  renderSeparator = () => {
    return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
  };

  onSelectDropdown = (option, index, item) => {
    if (option == 0) {
      this.editCompany(item, index);

    } else {
      this.setState({
        showDelete: true,
        selectedCompany: item,
        selectedIndex: index,
      });
      // this.deleteCompany(item, index)
    }
  };

  deleteCompany = async (item, index) => {
    let param = {
      id: [item.id],
      ProjectId: this.props.projectDetails.id,
      isSelectAll: false,
    };
    this.setState({ showLoader: true });

    deleteCompany(
      DELETE_COMPANY,
      param,
      () => null,
      (response) => {
        if (response.status) {
          if (response.status==500) {
            this.setState(
              {
                errorMessage:response.data.message,
                showLoader: false,
                showError:true,
              });
          } else if (response.status==200) {
            this.page_number = 1;
            this.getCompanyList();
            setTimeout(() => {
              this.props.refreshDashboard(true, "Company List delete");
            }, 2000);
            trackEvent('Deleted_Company')
            mixPanelTrackEvent('Deleted Company',this.state.mixpanelParam)
          } else {
            this.setState(
              {
                showLoader: false,
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        } else {
          this.setState(
            {
              showLoader: false,
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };

  editCompany = (item, index) => {
    this.props.editData({
      item: item,
      index: index,
    });
    this.props.clickAdd(true);
  };

  renderParent = (item) => {
    return (
      <View style={styles.flatlistContainer}>
        <View>
          <View style={{ width: wp("95%") }}>
            <View style={[styles.nameContainer]}>
              <View
                style={{
                  width: wp("2%"),
                  backgroundColor: Colors.cardBorder,
                  borderTopLeftRadius: wp("4%"),
                  borderBottomLeftRadius: wp("4%"),
                }}
              ></View>

              <View>
                <View
                  style={[
                    styles.detailContainer,
                    {
                      flexDirection: "row",
                      width: "95%",
                      marginLeft: 0,
                      marginTop: 15,
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.nameText,
                      { width: wp("72%"), marginLeft: 20 },
                    ]}
                    numberOfLines={2}
                  >
                    {item.companyName}
                  </Text>

                  <ModalDropdown
                    saveScrollPosition={false}
                    style={styles.customDropdownStyle}
                    dropdownStyle={[
                      styles.customOptionsStyle,
                      { height: hp("7%") },
                    ]}
                    dropdownTextStyle={styles.customOptionsTextStyle}
                    options={PARENTDROPDOWNOPTIONS}
                    renderRow={this.renderParentRow}
                    renderSeparator={this.renderSeparator}
                    showsVerticalScrollIndicator={false}
                    onSelect={(options) =>
                      this.onSelectDropdown(
                        options,
                        null,
                        this.state.parentCompany
                      )
                    }
                    defaultValue=""
                    dropdownListProps={{}}
                  >
                    <View style={styles.imageContainer}>
                      <Image style={styles.dotMenu} source={Images.dotmenu} />
                    </View>
                  </ModalDropdown>
                </View>

                <View style={{ flexDirection: "row" }}>
                  <View style={{ width: wp("50%") }}>
                    <View
                      style={{
                        flexDirection: "row",
                        marginLeft: 15,
                        alignItems: "center",
                        marginTop: 10,
                      }}
                    >
                      <Image source={Images.company_address} />
                      <Text
                        style={[
                          styles.companyText,
                          { marginLeft: 10, marginTop: 0 },
                        ]}
                      >
                        {item.address != null
                          ? `${item.address.trim()}, ${item.city}, ${
                              item.state
                            }, ${item.country}.`
                          : ""}
                      </Text>
                    </View>

                    <View
                      style={{
                        flexDirection: "row",
                        marginLeft: 15,
                        marginTop: 15,
                        marginBottom: hp("3%"),
                        alignItems: "center",
                      }}
                    >
                      <Image
                        source={Images.membersCount}
                        style={{ height: hp("2%"), width: wp("6%") }}
                      />
                      <Text
                        style={[
                          styles.companyText,
                          { marginLeft: 10, marginTop: 0 },
                        ]}
                      >
                        {item.Members != null ? item.Members.length : 0}
                      </Text>
                    </View> 
                  </View>

                  <View
                    style={{
                      width: wp("40%"),
                      justifyContent: "center",
                      alignItems: "flex-end",
                    }}
                  >
                    <Image
                      source={
                        item.logo ? { uri: item.logo } : Images.companyPlace
                      }
                      resizeMode={"contain"}
                      style={{
                        width: wp("35%"),
                        height: hp("5%"),
                        alignSelf: "flex-end",
                      }}
                    />
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };

  renderFlatListItem = ({ item, index }) => {
    let dfow = "";
    if (item.define != []) {
      let indexDfow = item.define.length - 1;
     
      for (let [i, dfowName] of item.define.entries()) {
        if(dfowName.DeliverDefineWork){
        if (indexDfow == i) {
          dfow += dfowName.DeliverDefineWork.DFOW;
        } else {
          dfow += dfowName.DeliverDefineWork.DFOW + ", ";
        }
      } else {
        dfow = "---";
      }
      }
    }
let address='';
      if(item.address!=null && item.city!=null &&  item.state!=null && item.country!=null){
        address =`${item.address.trim()}, ${item.city}, ${
          item.state
        }, ${item.country}.`
      }
    return (
      <View style={styles.flatlistContainer}>
        <View>
          <View style={{ width: wp("95%") }}>
            <View style={[styles.nameContainer]}>
              <View
                style={{
                  width: wp("2%"),
                  backgroundColor: Colors.cardBorder,
                  borderTopLeftRadius: wp("4%"),
                  borderBottomLeftRadius: wp("4%"),
                }}
              ></View>

              <View>
                <View
                  style={[
                    styles.detailContainer,
                    {
                      flexDirection: "row",
                      width: "95%",
                      marginLeft: 0,
                      marginTop: 15,
                    },
                  ]}
                >
                  <View style={{width:wp(20)}}>
                  <View style={styles.imgContainer}>
                   <Image
                        source={item.logo ? { uri: item.logo } : Images.companylogo}
                        style={[styles.profAvatar,{width:  item.logo ?'100%':25, height:  item.logo ?'100%':25,}]}
                      />
                      </View>
                      </View>
                  <Text
                    style={[
                      styles.nameText,styles.titleText]}
                    numberOfLines={2}
                  >
                    {item.companyName}
                  </Text>

                  <ModalDropdown
                    saveScrollPosition={false}
                    style={styles.customDropdownStyle}
                    dropdownStyle={[
                      styles.customOptionsStyle,
                      { height: index == 0 ? hp("7%") : hp("14%") },
                    ]}
                    dropdownTextStyle={styles.customOptionsTextStyle}
                    options={
                      index == 0 ? PARENTDROPDOWNOPTIONS : DROPDOWNOPTIONS
                    }
                    renderRow={this.renderRow}
                    renderSeparator={this.renderSeparator}
                    showsVerticalScrollIndicator={false}
                    onSelect={(options) =>
                      this.onSelectDropdown(options, index, item)
                    }
                    defaultValue=""
                    dropdownListProps={{}}
                  >
                    {(this.props.projectRoleId === 2 || this.props.projectRoleId === 3) && (
                      <View style={styles.imageContainer}>
                        <Image style={styles.dotMenu} source={Images.dotmenu} />
                      </View>
                    )}
                  </ModalDropdown>
                </View>

                <View style={styles.containerAddress}>
                    <View
                      style={styles.insideContainerAddress}
                    >
                      <Image source={Images.company_address} />
                      <Text
                        style={styles.companyText}
                      >
                       {address}
                      </Text>
                    </View>

                    <View
                      style={styles.memberContainer}
                    >
                      <Image
                        source={Images.membersCount}
                        style={styles.memberIcon}
                      />
                      <Text style={styles.companyText}>
                        {item.Members.length}
                      </Text>
                    </View>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };

  renderHeader() {
    let count = 0;
    if (this.state.selecteddfow !== null) {
      count = 1;
    }
    if (this.state.searchText !== "") {
      count = count + 1;
    }
    return (
      <View style={styles.headerContainer}>
        <View style={{ flexDirection: "row", width: "60%" }}>
          {/* {this.props.checkCameBack == true && (
            <View
              style={{
                width: 50,
                maxHeight: 50,
                marginBottom: hp("2%"),
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <TouchableWithoutFeedback
                onPress={() => {
                  this.props.cameBack(true);
                  this.props.navigation.goBack();
                }}
              >
                <Image source={Images.backArrow} style={{}} />
              </TouchableWithoutFeedback>
            </View>
          )} */}
          <View style={{ maxHeight: 50 }}>
            <Text style={styles.title}>{Strings.menu.company}</Text>
          </View>
        </View>

        <View style={styles.headerRowContainer}>
          <TouchableOpacity
            style={[styles.image, { marginTop: wp("1%") }]}
            onPress={() => this.setState({ showFilter: true })}
          >
            <Image source={Images.filter} />
            {this.state.filter == true && (
              <View
                style={{
                  position: "absolute",
                  marginTop: -10,
                  right: -10,
                  backgroundColor: Colors.themeColor,
                  width: 16,
                  justifyContent: "center",
                  alignItems: "center",
                  height: 16,
                  borderRadius: 8,
                }}
              >
                <Text style={{ color: "white" }}>{count}</Text>
              </View>
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.image}
            onPress={() => this.props.onTapSearch("companySearch")}
          >
            <Image source={Images.Search1} style={{ height: 21, width: 21 }} />
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  onEndReached = () => {
    if (this.state.memberslist.length < this.state.totalCount) {
      this.page_number = this.page_number + 1;
      this.getCompanyList();
      this.onEndReachedCalledDuringMomentum = true;
    }
  };

  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        memberslist: [],
        totalCount: 0,
        showLoader: true,
        //load: true,
      },
      () => {
        this.getCompanyList();
      }
    );
  };

  updateMasterState = (key, value) => {
    this.setState({
      searchText: value,
    });
  };

  onPressDefinaleDrop = () => {
    if (this.state.selectedCountry.id) {
      this.setState({ stateVisible: true });
    } else {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.selectCountry,
          toastType: "error",
        },
        () => this.hideToast()
      );
    }
  };

  applyFilter = () => {
    if (this.state.selecteddfowId !== 0 || this.state.searchText !== "") {
      this.setState(
        {
          filter: true,
          showFilter: false,
          showLoader: true,
        },
        () => {
          this.getCompanyList();
        }
      );
    }
  };

  onPressEqipType=(item)=>{
    this.setState({
      selecteddfow: item.value,
      selecteddfowId: item.id,
      dfwModalVisible:false
    })
  }

  //RENDER FILTER
  renderFilter = () => {
    return (
      <View style={modalStyles.container}>
        <View style={modalStyles.topContainer}>
          <TouchableOpacity
            onPress={() => this.setState({ showFilter: false })}
            style={{ width: 40 }}
          >
            <Image source={Images.closeBlack} />
          </TouchableOpacity>
          <View style={modalStyles.titleContainer}>
            <Text style={modalStyles.title}>{Strings.filter.title}</Text>
          </View>
          <View style={{ width: 40, height: 40 }} />
        </View>

        <TextField
          showLeft={true}
          attrName={Strings.placeholders.companyName}
          title={Strings.placeholders.companyName}
          value={this.state.searchText}
          updateMasterState={(key, value) => {
            this.updateMasterState(key, value);
          }}
          hideShow={false}
          hideImage={""}
          textInputStyles={{
            color: Colors.black,
            fontSize: 14,
            width: "75%",
            marginLeft: wp("10%"),
            fontFamily: Fonts.montserratMedium,
            paddingTop: 10,
          }}
          textTitleStyles={{
            marginLeft: wp("10%"),
            fontSize: 14,
            fontFamily: Fonts.montserratMedium,
          }}
          leftImage={Images.searchGray}
          leftButton={{ bottom: 0 }}
        />

            <TextField
              attrName={Strings.placeholders.definable}
              title={Strings.placeholders.definable}
              value={this.state.selecteddfow}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              showButton={true}
              onPress={() => {
                this.setState({ dfwModalVisible: true });
              }}
              imageSource={Images.downArr}
              // placeholder={"Select"}
            />

          <Dropdown
            data={this.state.dfowList}
            title={Strings.placeholders.definable}
            value={this.state.selecteddfow}
            closeBtn={() => this.setState({ dfwModalVisible: false })}
            onPress={(item) => this.onPressEqipType(item)}
            visible={this.state.dfwModalVisible}
            onbackPress={() => this.setState({ dfwModalVisible: false })}
            container={styles.equipmentContainer}
            customMainContainer={styles.renderEquipStyle}
            equipTextContainer={styles.equipTextStyle}
            customTextTitle={styles.textCustomStyle}
            closeBtnStyle={styles.closeButtonStyle}
          />

        {/* <DropDownPicker
          items={this.state.dfowList}
          defaultValue={this.state.selecteddfow}
          placeholder={Strings.placeholders.definable}
          placeholderStyle={modalStyles.filterPlaceholder}
          containerStyle={{ marginTop: 15,height:65 }}
          style={{
            backgroundColor: Colors.white,
            width: wp("90%"),
            borderColor: "#0000",
            borderBottomColor: Colors.placeholder,
            alignSelf: "center",
            height: hp("5%"),
          }}
         
          itemStyle={{
            justifyContent: "flex-start",
          }}
         
          dropDownStyle={{
            backgroundColor: Colors.white,
            width: "90%",
            alignSelf: "center",
          }}
          onChangeItem={(item) =>
            this.setState({
              selecteddfow: item.value,
              selecteddfowId: item.id,
            })
          }
          customArrowUp={(size) => (
            <Image
              source={Images.downArr}
              style={{ width: size, height: size, alignSelf: "flex-end" }}
            />
          )}
          customArrowDown={(size) => (
            <Image
              source={Images.downArr}
              style={{ width: size, height: size, alignSelf: "flex-end" }}
            />
          )}
          selectedLabelStyle={{ color: Colors.black }}
        /> */}

        <View style={modalStyles.buttonContainer}>
          <TouchableOpacity
            style={
              this.state.filter == true
                ? [
                    modalStyles.cancelButton,
                    { backgroundColor: Colors.themeOpacity },
                  ]
                : modalStyles.cancelButton
            }
            onPress={() =>
              this.setState(
                {
                  showFilter: false,
                  selecteddfow: null,
                  selecteddfowId: 0,
                  searchText: "",
                },
                () => {
                  if (this.state.filter == true) {
                    this.setState({ filter: false }, () => {
                      this.getCompanyList();
                    });
                  }
                }
              )
            }
          >
            <Text
              style={[
                modalStyles.cancelText,
                {
                  color:
                    this.state.filter == true
                      ? Colors.themeColor
                      : Colors.buttonBackground,
                },
              ]}
            >
              {this.state.filter == true
                ? Strings.addMember.reset
                : Strings.addMember.cancel}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={modalStyles.applyButton}
            onPress={() => this.applyFilter()}
          >
            <Text style={modalStyles.applyText}>{Strings.filter.apply}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  deletePopupAcceptTap = () => {
    this.setState({ showDelete: false });
    this.deleteCompany(this.state.selectedCompany, this.state.selectedIndex);
  };

  deletePopupDeclineTap = () => {
    this.setState({
      showDelete: false,
      selectedCompany: [],
      selectedIndex: null,
    });
  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <AppView style={styles.safeArea}>
        <View style={styles.parentContainer}>
          {this.renderHeader()}

          {this.state.parentCompany.companyName &&
            this.state.totalCount == 0 &&
            this.renderParent(this.state.parentCompany)}

          <FlatList
            data={this.state.memberslist}
            renderItem={this.renderFlatListItem}
            // ItemSeparatorComponent={this.itemSeparator}
            keyExtractor={(item, index) => index.toString()}
            onEndReached={() => this.onEndReached()}
            //onEndReachedThreshold={0}
            onMomentumScrollBegin={() => {
              this.onEndReachedCalledDuringMomentum = false;
            }}
            onRefresh={() => this._onReset()}
            refreshing={this.state.refreshing}
            extraData={this.state}
          />
        </View>

        <Modal
          isVisible={this.state.showFilter}
          style={modalStyles.filterModal}
        >
          {this.renderFilter()}
        </Modal>
        {this.state.showError &&(  <DeleteError message={this.state.errorMessage} close={()=>this.setState({showError:false})}/>)}
        {this.state.showLoader && (
          <Modal
            isVisible={true}
            backdropOpacity={0}
            style={modalStyles.loaderModal}
          >
            <AppLoader viewRef={this.state.showLoader} />
          </Modal>
        )}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.forgotSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}

        {this.state.showDelete && (
          <DeletePop
            title={Strings.popup.success}
            desc={Strings.popup.delete}
            acceptTap={this.deletePopupAcceptTap}
            declineTap={this.deletePopupDeclineTap}
            container={styles.deletePopContainer}
          />
        )}
      </AppView>
       }
       </>
    );
  }
}

const styles = StyleSheet.create({
  safeArea: {
    height: hp("50%"),
  },
  parentContainer: {
    flex: 1,
    backgroundColor: "#FCFBFC",
  },
  headerContainer: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderBottomWidth: 0.3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 6,
  },
  title: {
    color: Colors.black,
    fontSize: 22,
    fontFamily: Fonts.montserratBold,
    marginBottom: hp("2%"),
    marginLeft: wp("4%"),
  },
  headerRowContainer: {
    flex: 1,
    height: hp("15%"),
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  image: {
    marginRight: wp("6%"),
    marginBottom: hp("2%"),
  },
  flatlistContainer: {
    width: wp("90%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    alignSelf: "center",
    borderRadius: wp("2%"),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  nameContainer: {
    minHeight: hp("14%"),
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  detailContainer: {
    marginLeft: 30,
    justifyContent: "center",
     backgroundColor: Colors.white,
  },
  imagePlaceholder: {
    width: wp("14%"),
    height: wp("14%"),
    borderRadius: wp("7%"),
    marginLeft: wp("6%"),
  },
  nameText: {
    color: Colors.black,
    fontSize: 15,
    fontFamily: Fonts.montserratSemiBold,
  },
  titleText:{
    width:wp('55%'),
    alignSelf:"center"
  },
  containerAddress:{
    flexDirection: "row",
    width:"95%",
    marginBottom:20
  },
  insideContainerAddress:{
    marginLeft: 15,
    marginTop: 10,
    width:'60%',
  },
  companyText: {
    color: "#5B5B5B",
    fontSize: 13,
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  memberContainer:{
    marginLeft: 30,
    marginTop: 10,
    marginBottom: hp("3%"),
  },
  dotMenu: {
    height: 8,
    width: 25,
  },
  emailContainer: {
    flex: 1,
    marginLeft: 10,
    marginRight: 10,
  },
  emailTitle: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    margin: 5,
  },
  emailText: {
    color: "#1E1E1E",
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginTop: 0,
  },
  customDropdownStyle: {},
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: Colors.memberCardShadowCard,
    alignSelf: "center",
    borderRadius: wp("5%")
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    padding:15,
    marginRight: 0,
  },
  imgContainer: {
    flex: 1,
    maxWidth: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    borderColor: "#D8D8D8",
    borderWidth: 0.5,
    margin:5,
    backgroundColor:"#E3E3E3"
  },
 profAvatar: {borderRadius: 30 },

 deletePopContainer:{
  height:'35%'
 },

 memberIcon:{
  height: 17.5, 
  width: 25 
 },
 textCustomStyle:{
  color: Colors.black,
  fontSize: wp("4.5%"),
  fontFamily: Fonts.montserratBold,
  alignSelf: "center",
  paddingRight:20
 },
 closeButtonStyle:{
   right:5
 },
 equipmentContainer:{
  height: hp("4%"),
  paddingBottom:5
},
renderEquipStyle:{
 marginBottom:10,
},
equipTextStyle:{
  width:'100%',
  fontSize:16,
  paddingTop:2
},
});

const modalStyles = StyleSheet.create({
  container: {
    flex: 1,
    width: wp("100%"),
    height: hp("95%"),
  },
  topContainer: {
    flexDirection: "row",
    margin: 20,
    height: 40,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 20,
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  buttonContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-end",
    marginBottom: 50,
    justifyContent: "space-around",
  },
  cancelButton: {
    backgroundColor: `rgba(117,117,117,0.2)`,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonBackground,
    fontSize: wp("4.5%"),
    fontFamily: Fonts.montserratBold,
  },
  applyButton: {
    backgroundColor: Colors.themeOpacity,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  applyText: {
    color: Colors.themeColor,
    fontSize: wp("4.5%"),
    fontFamily: Fonts.montserratBold,
  },
  filterModal: {
    paddingTop: 45,
    paddingBottom: 30,
    margin: 0,
    backgroundColor: Colors.white,
  },
  loaderModal: {
    paddingTop: 45,
    paddingBottom: 30,
    margin: 0,
    backgroundColor: "#0000",
  },
  filterPlaceholder:{
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
  
  },
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    updatelist,
    refresh,
    projectRoleId,
    projectSwitched,
    userDetails,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    updatelist,
    refresh,
    projectRoleId,
    projectSwitched,
    userDetails,
  };
};

export default connect(mapStateToProps, {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  editData,
  clickAdd,
  onTapSearch,
  updateList,
  refreshPage,
  refreshDashboard,
})(CompanyList);