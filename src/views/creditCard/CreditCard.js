import React, { Component } from "react";
import {
  View,
  StyleSheet,
  Platform,
  Text,
  TouchableOpacity,
  Image,
} from "react-native";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import { storeUserid, countryList, updateData } from "../../actions/postAction";
import {
  Image as AnimatableImage,
  View as AnimatableView,
} from "react-native-animatable";

import {
  AppView,
  AppLoader,
  <PERSON>er<PERSON>ni<PERSON>,
  <PERSON>er,
  TextField,
  Alert,
  Toastpopup,
  Dropdown,
} from "../../components";

import { Colors, isEmpty, isName, Images, Strings, Fonts } from "../../common";

import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

import {
  SIGN_UP,
  CREATE_PROJECT,
  UPGRADE_PLAN,
  GET_COUNTRY,
} from "../../api/Constants";
import { signup, addProject, upgradePlan, getCountryList } from "../../api/Api";

const rotation = {
  from: {
    rotate: "0deg",
  },
  to: {
    rotate: "360deg",
  },
};

class CreditCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      amount: 49,
      cardname: "",
      cardnumber: "",
      expirydate: "",
      cvv: "",
      country: "",
      zipcode: "",
      countryVisible: false,
      showToaster: false,
      toastMessage: "",
      toastType: "error",
      upgradePlan: false,
      projectData: [],
    };
  }

  componentDidMount() {
    let data = this.props.route?.params?.step4;

    if (data.showStep == true) {
      this.setState({ showStep: true });
    } else {
      this.setState({ showStep: false });
    }

    if (this.props.route?.params?.step4?.upgradePlan == true) {
      this.setState({
        projectData: data.step4.projectItem,
        upgradePlan: true,
      });
    } else {
      this.setState({
        upgradePlan: false,
      });
    }

    if (this.props.countrylist.length !== 0) {
      this.storeCountryList(this.props.countrylist);
    } else {
      this.getCountries();
    }
    // this.setState({countrylist: this.props.countrylist})
  }

  getCountries = () => {
    getCountryList(
      GET_COUNTRY,
      {},
      () => {},
      (response) => {
        this.setState({ showLoader: false });


        if (response.status) {
          if (response.data.countryList) {
            this.storeCountryList(response.data.countryList);
            this.props.countryList(response.data.countryList);
            // this.setState({countryData: response.data.countryList})
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
  };

  storeCountryList = (data) => {
    let list = [];
    for (let i = 0; i < data.length; i++) {
      list.push({
        id: data[i].id,
        name: data[i].countryName,
      });
    }
    this.setState({ countrylist: list });
  };

  onBackPress = () => {
    this.props.navigation.goBack();
  };

  updateMasterState = (key, value) => {

    if (key == Strings.placeholders.cardName) {
      this.setState({ cardname: value });
    } else if (key == Strings.placeholders.cardNumber) {
      this.handleCardNumber(value);
    } else if (key == Strings.placeholders.expiry) {
      this.handleCardExpiry(value);
    } else if (key == Strings.placeholders.cvv) {
      this.setState({ cvv: value });
    } else if (key == Strings.placeholders.zipcode) {
      this.setState({ zipcode: value });
    }
  };

  handleCardNumber = (text) => {
    let formattedText = text.split(" ").join("");
    if (formattedText.length > 0) {
      formattedText = formattedText.match(new RegExp(".{1,4}", "g")).join(" ");
    }
    this.setState(
      { cardnumber: formattedText, oCardNumber: text.split(" ").join("") }
    );
    return formattedText;
  };

  handleCardExpiry(text) {
    if (text.length == 2 && this.state.expirydate.length == 1) {
      text += "/";
    } else if (text.length == 2 && this.state.expirydate.length == 3) {
      text = text.substring(0, text.length - 1);
    }
    this.setState({ expirydate: text });
  }

  validate = () => {
    let month = "";
    let year = "";

    if (this.state.expirydate.split("/")[0]) {
      month = this.state.expirydate.split("/")[0];
    }

    if (this.state.expirydate.split("/")[1]) {
      year = this.state.expirydate.split("/")[1];
    }
    if (isEmpty(this.state.cardname.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyCardname,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (this.state.cardname.length < 3) {
      this.setState(
        {
          showToaster: true,
          toastMessage: "Card name " + Strings.errors.lengthError,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (isName(this.state.cardname)) {
      this.setState(
        {
          showToaster: true,
          toastMessage: "Please enter valid card name",
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (isEmpty(this.state.cardnumber.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyCardNumber,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (isEmpty(this.state.expirydate.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyExpiry,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (month.length > 2 || month.length < 2) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.validMonth,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (year.length > 4 || year.length < 4) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.validYear,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (isEmpty(this.state.cvv.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyCVV,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (isEmpty(this.state.country.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyCountry,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (isEmpty(this.state.zipcode.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyZipcode,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else {
      return true;
    }
  };

  doSignup = async () => {
    let data = this.props.route?.params?.step4?.step4;
    let switchCheck = this.props.route?.params?.step4?.switch;

    // let ParamCheck = {
    //   basicDetails: {
    //     email: data.step1.email.trim(),
    //     phoneNumber: data.step1.mobile.trim(),
    //     phoneCode: data.step1.countryCode.trim(),
    //   },
    //   companyDetails: {
    //     companyName: data.step2.companyname.trim(),
    //     fullName: data.step1.fullName.trim(),
    //     scope: 'site',
    //     isParent: true,
    //     address: data.step2.street.trim(),
    //     country: data.step2.country.trim(),
    //     city: data.step2.city.trim(),
    //     state: data.step2.state.trim(),
    //     website: data.step2.website.trim(),
    //     zipCode: data.step2.zipcode.trim(),
    //   },
    //   projectDetails: {
    //     projectName: data.step3.projectName.trim(),
    //     projectLocation: data.step3.location.trim(),
    //   },
    //   planData: {
    //     id: switchCheck == false ? 2 : 3,
    //   },
    //   cardDetails: {
    //     "number": this.state.oCardNumber.trim(),
    //     "exp_month": this.state.expirydate.split('/')[0],
    //     "exp_year": this.state.expirydate.split('/')[1],
    //     "cvc": this.state.cvv.trim()
    //   }
    // };

    // ""companyDetails.fullName" is required"
    //       ""companyDetails.lastName" is required"

    let ParamCheck = {
      basicDetails: {
        email: data.step1.email,
        phoneNumber: data.step1.mobile,
        phoneCode: data.step1.countryCode,
      },
      companyDetails: {
        companyName: data.step2.companyname,
        fullName: data.step2.fullName,
        lastName: data.step2.lastName,
        scope: "site",
        isParent: true,
        address: data.step2.street,
        country: data.step2.country,
        city: data.step2.city,
        state: data.step2.state,
        website: data.step2.website,
        zipCode: data.step2.zipcode,
      },
      projectDetails: {
        projectName: data.step3.projectName,
        projectLocation: data.step3.location,
      },
      planData: {
        id: switchCheck == false ? 2 : 3,
      },
      cardDetails: {
        country: "United States",
        interval: "month",
        number: this.state.oCardNumber,
        exp_month: this.state.expirydate.split("/")[0],
        exp_year: this.state.expirydate.split("/")[1],
        cvc: this.state.cvv,
        zipCode: "624301",
        name: this.state.cardname,
      },
    };


    this.setState({ showLoader: true });

    await signup(
      SIGN_UP,
      ParamCheck,
      () => {},
      (response) => {
        this.setState({ showLoader: false });


        if (response.status) {
          if (response.data.message.message) {
            let array = Object.values(response.data.message.details[0]);
            this.setState(
              {
                showToaster: true,
                toastMessage: array.toString(),
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 3500);
              }
            );
          } else if (
            response.data.message == "Registered Successfully." ||
            response.data.message == "Email/Mobile Number already exist" ||
            response.data.message ==
              "Please contact our Sales to get your Enterprise plan on an exciting prize !"
          ) {
            this.props.navigation.navigate("SubscriptionThanks");
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
  };

  nextClick = async () => {
    if (this.validate()) {
      if (this.props.route?.params?.step4?.step4.showStep == true) {
        this.doSignup();
      } else {
        if (this.state.upgradePlan == true) {
          this.upgradeProject();
        } else {
          this.createProject();
        }
      }
    }
  };

  upgradeProject = async () => {
  
    let param = {
      ProjectId: this.state.projectData.id,
      PlanId: this.props.route?.params?.step4?.switch == true ? 3 : 2,
      existcard: false,
      cardDetails: {
        number: this.state.oCardNumber,
        exp_month: this.state.expirydate.split("/")[0],
        exp_year: this.state.expirydate.split("/")[1],
        cvc: this.state.cvv,
        interval:
          this.props.route?.params?.step4?.switch == true
            ? "year"
            : "month",
        name: this.state.cardname,
        zipCode: this.state.zipcode,
        country: this.state.country,
      },
    };


    this.setState({ showLoader: true });

    await upgradePlan(
      UPGRADE_PLAN,
      param,
      () => {},
      (resp) => {
        this.setState({ showLoader: false });

        if (resp.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              showLoader: false,
              toastMessage: Strings.errors.checkInternet,
              type: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        } else if (resp.status) {
          if (resp.status == 200 || resp.status == 201) {
            this.setState(
              {
                showToaster: true,
                showLoader: false,
                toastMessage: "Project Updated Successfully",
                type: "success",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                  this.props.updateData(true);
                  this.props.navigation.navigate("Profile");
                }, 2000);
              }
            );
          } else if (resp.status == 504) {
            this.setState(
              {
                showToaster: true,
                showLoader: false,
                toastMessage: `Server error: ${resp.status}`,
                type: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          } else if (resp.data.message.message) {
            let array = Object.values(resp.data.message.details[0]);
            this.setState(
              {
                showToaster: true,
                showLoader: false,
                toastMessage: array.toString(),
                type: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          } else {
            this.setState(
              {
                showToaster: true,
                showLoader: false,
                toastMessage: resp.data.message,
                type: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              showLoader: false,
              toastMessage: resp.toString(),
              type: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
  };

  createProject = () => {
    // "firstName": "anbu",
    // "email": "<EMAIL>",
    // "phoneNumber": "9738947873",
    // "projectName": "New Project with somu",
    // "projectLocation": "Tamilnadu",
    // "PlanId": 1,
    // "existcard": false,
    // "cardDetails": {
    //   "number": ****************,
    //   "exp_month": 5,
    //   "exp_year": 2029,
    //   "cvc": 987
    // }

    let data = this.props.route?.params?.step4?.step4;


    let ParamCheck = {
      firstName: this.props.userDetails.firstName,
      email: this.props.userDetails.email,
      phoneNumber: this.props.userDetails.phoneNumber,
      projectName: data.step3.projectName,
      projectLocation: data.step3.location,
      PlanId: this.props.route?.params?.step4?.switch == true ? 3 : 2,
      existcard: false,
      cardDetails: {
        number: this.state.oCardNumber,
        exp_month: this.state.expirydate.split("/")[0],
        exp_year: this.state.expirydate.split("/")[1],
        cvc: this.state.cvv,
      },
    };

    this.setState({ showLoader: true });

    addProject(
      CREATE_PROJECT,
      ParamCheck,
      () => {},
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.message.message) {
            let array = Object.values(response.data.message.details[0]);

            this.setState(
              {
                showToaster: true,
                toastMessage: array.toString(),
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          } else if (response.data.message == "Project Created Successfully.") {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType:
                  response.data.message == "Project Created Successfully."
                    ? "success"
                    : "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                  this.props.navigation.navigate("Menu");
                  this.props.updateData(true);
                }, 2000);
              }
            );
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: response.data.message == "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                  // this.props.navigation.navigate('Menu')
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
  };

  onPressCountry = (item) => {
    this.setState(
      { selectedCountry: item, countryVisible: false, country: item.name },
      () => {
        // this.getStateList(item.id)
      }
    );
  };

  //Main Render method
  render() {
    return (
      <AppView>
        <KeyboardAwareScrollView
          extraScrollHeight={50}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.parentContainer}>
            <HeaderAnimation />
            <View style={styles.subContainer}>
              <AnimatableView animation={"bounceInLeft"} duration={800}>
                <AnimatableImage
                  animation={rotation}
                  duration={800}
                  source={Images.path2}
                  style={styles.path}
                />
              </AnimatableView>

              <AnimatableView
                animation={"fadeInUpBig"}
                duration={500}
                style={styles.signupContainer}
              >
                <Header
                  backPress={() => this.onBackPress()}
                  title={Strings.creditCard.cardDetails}
                />

                <View style={{ flex: 1 }}>
                  <TextField
                    attrName={Strings.placeholders.cardName}
                    title={Strings.placeholders.cardName}
                    value={this.state.cardname}
                    maxLength={150}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                  />

                  <TextField
                    attrName={Strings.placeholders.cardNumber}
                    title={Strings.placeholders.cardNumber}
                    value={this.state.cardnumber}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    keyboardType={"number-pad"}
                    maxLength={19}
                  />

                  <TextField
                    attrName={Strings.placeholders.expiry}
                    title={Strings.placeholders.expiry}
                    value={this.state.expirydate}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    keyboardType={"number-pad"}
                    maxLength={7}
                  />

                  <TextField
                    attrName={Strings.placeholders.cvv}
                    title={Strings.placeholders.cvv}
                    value={this.state.cvv}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    keyboardType={"number-pad"}
                    maxLength={3}
                  />

                  <TextField
                    attrName={Strings.placeholders.country}
                    title={Strings.placeholders.country}
                    value={this.state.country}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    showButton={true}
                    imageSource={Images.downArr}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    onPress={() => this.setState({ countryVisible: true })}
                  />

                  <TextField
                    attrName={Strings.placeholders.zipcode}
                    title={Strings.placeholders.zipcode}
                    value={this.state.zipcode}
                    maxLength={10}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    keyboardType={"number-pad"}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                  />

                  <View style={styles.footer}>
                    <View style={{ flex: 2 }}>
                      <Text style={styles.payable}>
                        {Strings.creditCard.payable}
                      </Text>
                      <Text style={styles.amount}>
                        {`$${
                          this.props.route?.params?.step4?.switch == true
                            ? "999"
                            : "49"
                        }`}{" "}
                        <Text style={styles.month}>{`/${
                          this.props.route?.params?.step4?.switch == true
                            ? Strings.creditCard.year
                            : Strings.creditCard.month
                        }`}</Text>
                      </Text>
                    </View>
                    <View style={{ flex: 1 }}>
                      <TouchableOpacity onPress={() => this.nextClick()}>
                        <View style={styles.container}>
                          <Image source={Images.whiteForward} />
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>
                  {/* <NextButton 
                nextClick={()=> this.nextClick()}/> */}

                  <View style={{ flex: 1, justifyContent: "flex-end" }}>
                    <Text
                      style={styles.cancel}
                      onPress={() => this.props.navigation.goBack()}
                    >
                      {Strings.creditCard.cancel}
                    </Text>
                  </View>
                </View>
              </AnimatableView>
            </View>
          </View>
        </KeyboardAwareScrollView>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.forgotSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}

        <Dropdown
          data={this.state.countrylist}
          title={Strings.addCompany.chooseCountry}
          value={this.state.country}
          closeBtn={() => this.setState({ countryVisible: false })}
          onPress={(item) => this.onPressCountry(item)}
          visible={this.state.countryVisible}
          onbackPress={() => this.setState({ countryVisible: false })}
        />
      </AppView>
    );
  }
}

const mapStateToProps = (state) => {
  const { userid, countrylist, userDetails } = state.LoginReducer;

  return {
    userid,
    countrylist,
    userDetails,
  };
};

export default connect(mapStateToProps, {
  storeUserid,
  countryList,
  updateData,
})(CreditCard);

const styles = StyleSheet.create({
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  subContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  container: {
    width: wp("18%"),
    height: wp("18%"),
    borderRadius: hp("9%"),
    backgroundColor: Colors.themeColor,
    borderColor: Colors.themeColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0,
    shadowOffset: { width: 5, height: 7 },
    shadowColor: Colors.themeOpacity,
    shadowOpacity: 1,
    elevation: 3,
    alignSelf: "flex-end",
    marginRight: wp("5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  signupContainer: {
    width: wp("100%"),
    height: hp("100%"),
    borderTopLeftRadius: hp("6%"),
    borderTopRightRadius: hp("6%"),
    zIndex: 999,
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0,
    shadowOffset: { width: 10, height: 10 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  path: {
    width: wp("70%"),
    height: hp("43%"),
    marginBottom: -hp("33%"),
    marginRight: -wp("26%"),
    alignSelf: "flex-end",
  },
  payable: {
    color: Colors.black,
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
  },
  amount: {
    color: Colors.black,
    fontFamily: Fonts.montserratBold,
    fontSize: 20,
    marginTop: hp("2%"),
  },
  month: {
    color: Colors.black,
    fontFamily: Fonts.montserratLight,
    fontSize: 15,
  },
  cancel: {
    color: Colors.black,
    fontSize: 12,
    margin: wp("3%"),
    textDecorationLine: "underline",
    fontFamily: Fonts.montserratLight,
    marginTop: hp("4%"),
  },
  footer: {
    width: wp("90%"),
    justifyContent: "space-between",
    alignItems: "center",
    alignSelf: "center",
    flexDirection: "row",
    height: hp("12%"),
  },
});
