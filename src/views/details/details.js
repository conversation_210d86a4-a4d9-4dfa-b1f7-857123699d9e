import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  TextInput,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  Linking,
  Dimensions,
} from "react-native";
import { Avatar } from 'react-native-paper';
import {
  cameBack,
  showDeliverdetailsid,
  // showInspectiondetailsid,
  editINS,
  editData,
  tappedNotificationDetails,
  refreshPage,
  refreshDashboard,
  refreshDeliveryList,
  showCraneRequestId,
  editCraneRequest,
  refreshCalendar,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import {
  AppView,
  AppLoader,
  Toastpopup,
  Dropdown,
  DeletePop,
} from "../../components";
import { launchImageLibrary, launchCamera } from "react-native-image-picker";

import AttachDropdown from "../../components/dropdown/attachDropdown";
import { TextField } from "../../components/textinput/addMemberTextinput";
import { Strings, Fonts, Images, Colors, isEmpty, editSeriesOption1, editSeriesOption2 } from "../../common";
import { TouchableWithoutFeedback } from "react-native-gesture-handler";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

import { TabView, TabBar, SceneMap } from 'react-native-tab-view';

import moment from "moment";

import DocumentPicker from "react-native-document-picker";

import ReactNativeBlobUtil from 'react-native-blob-util'

import Share from "react-native-share";
import { PERMISSIONS } from 'react-native-permissions';
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";

import {
  GET_SINGLE_NDR,
  GET_HISTORY_NDR,
  GET_ATTACHMENT_NDR,
  GET_COMMENT_NDR,
  GET_SINGLE_INS,
  GET_HISTORY_INS,
  GET_ATTACHMENT_INS,
  GET_COMMENT_INS,
  CREATE_COMMENT_NDR,
  CREATE_COMMENT_INS,
  UPDATE_STATUS_NDR,
  UPDATE_STATUS_INS,
  ADD_ATTACHEMENT,
  ADD_ATTACHMENT_INS,
  CREATE_VOID,
  CREATE_VOID_INS,
  REMOVE_ATTACHEMENT,
  REMOVE_ATTACHMENT_INS,
  GET_PROJECT_ROLE,
  GET_SINGLE_CRANE,
  CREATE_COMMENT_CRANE,
  GET_COMMENT_CRANE,
  CRANE_HISTORY,
  GET_ATTACHMENT_CRANE,
  ADD_ATTACHEMENT_CRANE,
  ADD_VOID_CRANE,
  UPDATE_CRANE_STATUS,
  GET_EQUIP_LIST,
  GET_GATE_LIST,
  LIST_ALL_MEMBER,
} from "../../api/Constants";
import {
  getDeliveryDetails,
  getHistoryDetails,
  getAttachmentDetails,
  getCommentDetails,
  createComment,
  updateStatus,
  addAttachment,
  removeAttachement,
  createVoid,
  getprojectRole,
  getEquipList,
  getGateList,
  getAllMemberList,
} from "../../api/Api";

let HEADERDROPDOWNOPTIONS = [
  { id: "Save", image: Images.save, name: "Save" },
  { id: "Edit", image: Images.edit_blue, name: "Edit" },
  { id: "Void", image: Images.crossred, name: "Void" },
];

let HEADERDROPDOWNOPTIONSSC = [
  { id: "Edit", image: Images.edit_blue, name: "Edit" },
  { id: "Void", image: Images.crossred, name: "Void" },
];

import FileViewer from "react-native-file-viewer";
import RNFS from "react-native-fs";
import { trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent } from "../../MixPanel/MixPanel";
import ResDropdown from "../../components/dropdown/ResDropdown";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import { checkAndRequestPermission } from "../../utils/PermissionUtils";

var commentListHeight = 0.0;
var commentHeight = 0.0;
class Details extends Component {
  constructor(props) {
    super(props);

    this.state = {
      selectstatus: "",
      statusModal: false,
      statuslist: [
        { name: "Approved" },
        // { name: "Declined" },
        { name: "Delivered" },
        { name: "Pass" },
        { name: "Fail" }
      ],
      gcStatusList: [{ name: "Delivered" }],
      attachmentList: [],
      totalCount: 0,
      refreshing: false,
      lastId: 0,
      showToaster: false,
      itemdescription: "",
      deliveryId: "",
      // inspectionid:"",
      deliverydetails: "",
      responsiblecompany: "",
      gateno: "",
      gateNoID: "",
      equipementneeded: "",
      equipmentId: '',
      featureofwork: "",
      deliveryvehicledet: "",
      appliedby: "",
      notes: "",
      historylist: [],
      commentList: [],
      newValue: "",
      height: 40,
      commenttext: "",
      DeliveryRequestId: this.props.deliveryDetailsId ? this.props.deliveryDetailsId.id : "",

      // InspectionRequestId:true,
      //  lastId: 0,
      updatestatus: "",
      attachModel: false,
      addattachmentlist: [],
      showDelete: false,
      bottom: 10,
      showDropDown: false,
      gcStatusModal: false,
      showSave: false,
      projectRoleId: this.props.projectRoleId,
      projectId: this.props.projectDetails.id,
      notificationDetails: [],
      downloadoption: [{ name: "Download" }, { name: "Open" }],
      downloadModal: false,
      downloaditem: {},
      showDeliveredPop: false,
      deliveryCreatedRoleId: 0,
      isDelivery: this.props.deliveryDetailsId ? this.props.deliveryDetailsId.isDelivery : false,
      isInspection: this.props.deliveryDetailsId ? this.props.deliveryDetailsId.isInspection : false,
      // isInspection: true,
      pickFrom: "",
      pickTo: "",
      responsiblePerson: [],
      onlyId: 0,
      deliveryItem: [],
      isIosDropDown: false,
      iosDropDown: [
        { name: "Camera" },
        { name: "Photos" },
        { name: "Document" },
      ],
      isAssociatedWithCrane: false,
      associateCraneId: 0,
      isResponsiblePersonCount: false,
      responsiblePersonCount: 0,
      isModal: false,
      responsibleName: '',
      mixpanelParam: {
        ProjectName: this.props.projectDetails.projectName,
        CompanyName: this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName: this.props.userDetails.firstName,
      },
      equipTypeList: [],
      gateList: [],
      showGatePopup: false,
      gatePopupText: "",
      controlledByList: [],
      renderRespId: "",
      isRequestType: "",
      id: 0,
      isAccess: true,
      isNetworkCheck: false,
      isEditAccess: false,
      editDropDown: [],
      recurrenceType: '',
      editSeriesID: 1,
      showAlert: false,
      showPopUp: false,
      showText: '',
      location: '',
      inspectionType: " ",
      tabIndex: 0,
      tabRoutes: [
        { key: 'details', title: 'Details' },
        { key: 'attachments', title: 'Attachments' },
        { key: 'comments', title: 'Comments' },
        { key: 'history', title: 'History' },
      ],
    };
    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;
  }

  // UNSAFE_componentWillMount() {
  //   this.keyboardDidShowListener = Keyboard.addListener(
  //     "keyboardDidShow",
  //     () => null
  //   );
  //   this.keyboardDidHideListener = Keyboard.addListener(
  //     "keyboardDidHide",
  //     () => null
  //   );
  // }

  // componentWillUnmount() {
  //   this.keyboardDidShowListener?.();
  //   this.keyboardDidHideListener?.();
  // }

  onBackPress = () => {
    this.props?.navigation?.goBack();
    return true;
  };

  async componentDidMount() {
    if (Platform.OS === 'ios') {
      this.networkCheck();
    } else {

      if (this.props.deliveryDetailsId) {
        this.setState({
          DeliveryRequestId: this.props.deliveryDetailsId.id,
          isDelivery: this.props.deliveryDetailsId.isDelivery,
          projectRoleId: this.props.projectRoleId,
          showLoader: true,
        });
      }


      if (this.props.inspectionDetailsId) {
        this.setState({
          DeliveryRequestId: this.props.deliveryDetailsId.id,
          isInspection: this.props.deliveryDetailsId.id.isInspection,
          projectRoleId: this.props.projectRoleId,
          showLoader: true,
        });
      }

      this.page_number = 1;

      if (this.props.notificationDetails.ProjectId) {
        this.setState(
          {
            notificationDetails: this.props.notificationDetails,
            projectId: this.props.notificationDetails.ProjectId,
          },
          () => {
            this.props.tappedNotificationDetails({});
            this.getProjectRole(this.state.notificationDetails);
          }
        );
      } else {
        this.setState({
          projectId: this.props.projectDetails.id,
        });

        this.getProjectRole({ ProjectId: this.state.projectId });
      }

      // DO FOR LATER
      // await this.historyDetails();
      // await this.attachmentDetails();
      // await this.commentDetails();

      // Fetch details
      await this.getEquiptypes();
      await this.getGateList();
      await this.getControlledByList();

      // Add any additional inspection-related async calls here
      // if (this.state.isInspection) {
      //     await this.getInspectionHistory();
      //     await this.getInspectionAttachments();
      //     await this.getInspectionComments();
      // }
    }
  }

  networkCheck = () => {
    NetInfo.addEventListener(async state => {
      if (!state.isConnected && !state.isInternetReachable) {
        this.setState({ isNetworkCheck: true });
      } else {
        this.setState({ isNetworkCheck: false });


        if (this.props.deliveryDetailsId) {
          this.setState({
            DeliveryRequestId: this.props.deliveryDetailsId.id,
            isDelivery: this.props.deliveryDetailsId.isDelivery,
            projectRoleId: this.props.projectRoleId,
            showLoader: true,
          });
        }


        if (this.props.inspectionDetailsId) {
          this.setState({
            DeliveryRequestId: this.props.deliveryDetailsId.id,
            isInspection: this.props.deliveryDetailsId.isInspection,
            projectRoleId: this.props.projectRoleId,
            showLoader: true,
          });
        }

        this.page_number = 1;

        if (this.props.notificationDetails.ProjectId) {
          this.setState(
            {
              notificationDetails: this.props.notificationDetails,
              projectId: this.props.notificationDetails.ProjectId,
            },
            () => {
              this.props.tappedNotificationDetails({});
              this.getProjectRole(this.state.notificationDetails);
            }
          );
        } else {
          this.setState({
            projectId: this.props.projectDetails.id,
          });

          this.getProjectRole({ ProjectId: this.state.projectId });
        }

        // Fetch details
        await this.getEquiptypes();
        await this.getGateList();
        await this.getControlledByList();

        // Add any additional inspection-related async calls here
        // if (this.state.isInspection) {
        //     await this.getInspectionHistory();
        //     await this.getInspectionAttachments();
        //     await this.getInspectionComments();
        // }
      }
    });
  }

  getEquiptypes = () => {
    let param = { isFilter: true, showActivatedAlone: true }
    let url = `${GET_EQUIP_LIST}${this.state.projectId}/0/0/${this.props.projectDetails.ParentCompany.id}`;
    getEquipList(
      url,
      param,
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.data.length !== 0) {
            let equipTypelist = [];
            for (let item of response.data.data) {
              equipTypelist.push({
                id: item.id,
                value: item.equipmentName,
                name: item.equipmentName,
                label: item.equipmentName,
                selected: false,
              });
            }

            this.setState({
              equipTypeList: equipTypelist,
              storeEquipmentList: response.data.data,
            });
          }
        }
      }
    );
  };


  getGateList = () => {
    let param = { isFilter: true, showActivatedAlone: true }
    getGateList(
      GET_GATE_LIST +
      this.state.projectId +
      "/0/0/" +
      this.props.projectDetails.ParentCompany.id,
      param,
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.data.length !== 0) {
            let gateList = [];

            for (let item of response.data.data) {
              gateList.push({
                id: item.id,
                name: item.gateName,
                value: item.gateName,
                label: item.gateName,
                selected: false,
              });
            }

            this.setState({
              gateList: gateList,
            });
          }
        }
      }
    );
  };

  getControlledByList = () => {
    getAllMemberList(
      LIST_ALL_MEMBER +
      this.props.projectDetails.id +
      "/" +
      this.props.projectDetails.ParentCompany.id,
      {},
      () => { },
      (response) => {
        if (response.status) {
          if (response.data.message == Strings.popup.getMemEqui) {
            this.storeContactPerson(response.data.data);
          } else {
            this.setState({
              memberlist: [],
            });
          }
        } else {
          this.showError("error", Strings.errors.failed);
        }
      }
    );
  };

  storeContactPerson = (data) => {
    let memberList = [];

    for (let item of data) {
      if (item.User.firstName != null) {
        memberList.push({
          label: item.User.firstName + " " + item.User.lastName + " (" + item.User.email + ")",
          value: item.User.email,
          id: item.id,
          name: item.User.email,
        });
      } else {
        memberList.push({
          label: item.User.email,
          value: item.User.email,
          id: item.id,
        });
      }
    }
    this.setState({ controlledByList: memberList });
  };


  getProjectRole = (data) => {
    if (data) {
      getprojectRole(
        GET_PROJECT_ROLE +
        data.ProjectId +
        `/${this.props.projectDetails.ParentCompany.id}`,
        {},
        () => null,
        (response) => {
          if (response.data) {
            this.setState(
              {
                projectRoleId: response.data.data.RoleId,
                id: response.data.data.id,
              },
              () => {
                this.deliveryDetails();
              }
            );
          }
        }
      );
    }
  };

  deliveryDetails = () => {
    let count = 1;
    this.setState({
      showLoader: true,
    });
    let url = "";
    if (this.state.isDelivery) {
      url = `${GET_SINGLE_NDR}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}`;
    }
    else if (this.state.isInspection) {
      url = `${GET_SINGLE_INS}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}`;
    }
    else {
      url = `${GET_SINGLE_CRANE}${this.state.DeliveryRequestId}/${this.state.projectId}/${this.props.projectDetails.ParentCompany.id}/`;
    }
    if (count === 1) {
      getDeliveryDetails(
        url,
        {},
        () => null,
        (deliveryDetailsresp) => {
          if (__DEV__) {
          }
          this.setState({
            showLoader: false,
          });
          if (deliveryDetailsresp.toString() == Strings.errors.timeout) {
            this.showErrorMessage("error", Strings.errors.checkInternet);
          } else if (deliveryDetailsresp.status == 200) {
            if (deliveryDetailsresp?.data?.data?.message) {
              this.showErrorMessage(
                "error",
                deliveryDetailsresp?.data?.data?.message
              );
            } else {

              this.setState({
                recurrenceType: deliveryDetailsresp?.data?.data?.recurrence != null ? deliveryDetailsresp?.data?.data?.recurrence?.recurrence : Strings.calendarSettings.doseNotRepeat
              })
              this.processDeliveryDetailsResponse(
                deliveryDetailsresp.data.data
              );
            }
          } else {
            this.showErrorMessage("error", deliveryDetailsresp.toString());
          }
        }
      );
    }
    count = count + 1;
  };

  async getResponsibilityCompany(companyDetails) {
    return companyDetails.map((e) => e.Company.companyName).join(",");
  }

  async getResponsibilityCompanyID(companyDetails) {
    return companyDetails.map((e) => e.Company.id).join(",");
  }


  async getGateName(gateDetails) {
    return gateDetails.map((e) => e.Gate.gateName).join(",");
  }

  async getGateNameID(gateDetailsID) {
    return gateDetailsID.map((e) => e.Gate.id).join(",");
  }

  async getEquipmentNeeded(equipmentDetails) {
    return equipmentDetails.map((e) => e.Equipment.equipmentName).join(",");
  }

  async getEquipmentID(equipmentDetails) {
    return equipmentDetails.map((e) => e.Equipment.id).join(",");
  }

  async getFeatureOfWork(defineWorkDetails) {
    return defineWorkDetails.map((e) => e.DeliverDefineWork.DFOW).join(",");
  }
  async getResponsiblePerson(responsiblePersonDetails) {
    let person = [];
    for (let sampleData of responsiblePersonDetails) {
      if (sampleData.Member.User.firstName == null) {
        let nullData = "uu";
        let nullName = `${sampleData.Member.User.email}`;
        let nullId = sampleData.Member.id;
        person.push({ "label": nullData, "name": nullName, 'email': nullData, 'id': nullId })
      } else {
        let data = `${sampleData.Member.User.firstName.charAt(0)}${sampleData.Member.User.lastName.charAt(0)}`;
        let name = `${sampleData.Member.User.firstName} ${sampleData.Member.User.lastName}`;
        let email = sampleData.Member.User.email;
        let phoneNumber = sampleData.Member.User.phoneCode != null ? sampleData.Member.User.phoneCode + sampleData.Member.User.phoneNumber : null
        let respoID = sampleData.Member.id
        person.push({ "label": data, "name": name, 'email': email, 'phoneNumber': phoneNumber, 'id': respoID })
      }

    }
    let count = responsiblePersonDetails.length - 3;
    this.setState({
      isResponsiblePersonCount: count > 0 ? true : false,
      responsiblePersonCount: count
    })
    // let person = "";
    // if (responsiblePersonDetails != null) {
    //   for (let [index, item] of responsiblePersonDetails.entries()) {
    //     if(item.Member!=null){
    //     if (item.Member.User.firstName != null) {
    //       if (index == responsiblePersonDetails.length - 1) {
    //         person +=
    //           item.Member.User.firstName + " " + item.Member.User.lastName;
    //       } else {
    //         person +=
    //           item.Member.User.firstName +
    //           " " +
    //           item.Member.User.lastName +
    //           ", ";
    //       }
    //     }
    //   }
    // }
    // }
    return person;
  }

  async processDeliveryDetailsResponse(data) {
    let cranePickFrom, cranePickTo = "";
    if (data.requestType == "deliveryRequestWithCrane") {
      cranePickFrom = data.cranePickUpLocation;
      cranePickTo = data.craneDropOffLocation;
    } else if (
      data.requestType == "craneRequest"
    ) {
      cranePickTo = data.dropOffLocation;
      cranePickFrom = data.pickUpLocation;
    }

    let deliveryStatus = "";
    if (data.status == "Pending") {
      deliveryStatus = data.status;
    } else if (data.status == "Expired") {
      deliveryStatus = data.status;
    } else if (data.status == "Delivered" || data.status == "Completed") {
      deliveryStatus = data.status;
    } else if (data.status == "Approved") {
      deliveryStatus =
        data.approverDetails != null ? data.status +
          " by " +
          data.approverDetails.User.firstName +
          " " +
          data.approverDetails.User.lastName +
          " on " +
          moment(data.approved_at).format("MMM DD, YYYY") : data.status + " by " + "on"
    } else if (data.status == "Declined") {
      deliveryStatus = data.status;
    }

    let inspectionStatus = "";
    if (data.status == "Pending") {
      inspectionStatus = data.status;
    } else if (data.status == "Expired") {
      inspectionStatus = data.status;
    } else if (data.status == "Pass" || data.status == "Fail") {
      inspectionStatus = data.status;
    } else if (data.status == "Approved") {
      inspectionStatus =
        data.approverDetails != null ? data.status +
          " by " +
          data.approverDetails.User.firstName +
          " " +
          data.approverDetails.User.lastName +
          " on " +
          moment(data.approved_at).format("MMM DD, YYYY") : data.status + " by " + "on"
    } else if (data.status == "Declined") {
      inspectionStatus = data.status;
    }
    this.setState({
      deliveryItem: data,
      itemdescription: data?.description,
      inspectionType: data.inspectionType,
      vehicleType: data?.vehicleType || '',
      originationAddress: data?.OriginationAddress || '',
      deliveryId: this.state.isDelivery ? data.DeliveryId : (this.state.isInspection ? data.InspectionId : data.CraneRequestId),
      deliverydetails: moment.parseZone(
        this.state.isDelivery
          ? data.deliveryStart
          : (this.state.isInspection
            ? data.inspectionStart
            : data.craneDeliveryStart)
      ).format("hh:mm A - MM/DD/YY"),

      responsiblecompany: await this.getResponsibilityCompany(
        data?.companyDetails
      ),
      responsiblecompany: await this.getResponsibilityCompany(
        data?.companyDetails
      ),
      renderRespId: await this.getResponsibilityCompanyID(
        data?.companyDetails
      ),
      gateno: this.state.isDelivery || this.state.isInspection
        ? await this.getGateName(data?.gateDetails)
        : "--",
      gateNoID: this.state.isDelivery || this.state.isInspection
        ? await this.getGateNameID(data?.gateDetails)
        : "--",
      equipementneeded: await this.getEquipmentNeeded(data?.equipmentDetails),
      equipmentId: await this.getEquipmentID(data?.equipmentDetails),
      featureofwork: data?.defineWorkDetails ? await this.getFeatureOfWork(data?.defineWorkDetails) : "---",
      deliveryvehicledet: data?.vehicleDetails,
      appliedby: this.state.isDelivery ? deliveryStatus : inspectionStatus,
      craneStatus: data.status,
      status: data?.inspectionStatus,
      note: this.state.isDelivery || this.state.isInspection ? data?.notes : data?.additionalNotes,
      selectstatus: data?.status,
      updatestatus: data?.status,
      showSave: false,
      deliveryCreatedRoleId: data?.createdUserDetails.RoleId,
      pickFrom: this.state.isDelivery ? data.cranePickUpLocation : (this.state.isInspection ? data.cranePickUpLocation : data.pickUpLocation),
      pickTo: this.state.isDelivery ? data.craneDropOffLocation : (this.state.isInspection ? data.craneDropOffLocation : data.dropOffLocation),
      responsiblePerson: await this.getResponsiblePerson(data?.memberDetails),
      onlyId: data?.id,
      isRequestType: data?.requestType,
      isAssociatedWithCrane:
        data?.requestType == "deliveryRequestWithCrane" ? true : false,
      associateCraneId: data?.requestType == "deliveryRequestWithCrane" ? data?.CraneRequestId : 0,
      repMemberIds: data?.memberDetails,
      location: data?.location != null ? data?.location.locationPath : '---',
    })

    if (this.state.isDelivery) {
      if (!moment(data.deliveryStart).isAfter(moment())) {
        this.setState({
          editDropDown: editSeriesOption2
        })
      } else {
        this.setState({
          editDropDown: editSeriesOption1
        })
      }
    } else if (this.state.isInspection)
      if (!moment(data.inspectionStart).isAfter(moment())) {
        this.setState({
          editDropDown: editSeriesOption2
        })
      } else {
        this.setState({
          editDropDown: editSeriesOption1
        })
      }
    else {
      if (!moment(data.craneDeliveryStart).isAfter(moment())) {
        this.setState({
          editDropDown: editSeriesOption2
        })
      } else {
        this.setState({
          editDropDown: editSeriesOption1
        })
      }
    }

    if (
      (data.createdUserDetails.RoleId == 2 &&
        this.props.projectRoleId == 4) ||
      (data.createdUserDetails.RoleId == 3 && this.props.projectRoleId == 4)
    ) {
      this.setState({ isAccess: false })
    }

    if (this.props.projectRoleId === 4 && data.memberDetails != undefined) {
      const index = data.memberDetails.findIndex((i) => (i.Member.id === this.state.id));
      if (index !== -1) {
        this.setState({ isAccess: true })
      } else {
        this.setState({ isAccess: false })
      }
    }

    if (data.status == "Approved") {
      if (data.requestType == "craneRequest") {
        this.setState({ statuslist: [{ name: "Completed" }], gcStatusList: [{ name: "Completed" }] })
      } else if (data.requestType == "deliveryRequest" || data.requestType == "deliveryRequestWithCrane") {

        this.setState({ statuslist: [{ name: "Delivered" }], gcStatusList: [{ name: "Delivered" }] });
      }
      else if (data.requestType == "inspectionRequest") {
        this.setState({
          statuslist: [{ name: "Pass" }, { name: "Fail" }],
          gcStatusList: [{ name: "Pass" }, { name: "Fail" }]
        });

      }
    } else if (data.status == "Pending") {
      this.setState({
        statuslist: [{ name: "Approved" }, { name: "Declined" }],
      });
    }
    if (data.status == "Delivered" || data.status == "Completed" || data.inspectionStatus == "Pass" || data.inspectionStatus == "Fail") {
      this.setState({
        showDropDown: false,
        showGCDropDown: false,
      });
    } else {
      if (this.state.projectRoleId == 2) {
        this.setState({
          showDropDown: true,
        });
      } else if (this.state.projectRoleId == 3) {
        this.setState({
          showGCDropDown: true,
        });
      } else if (data.createdUserDetails.RoleId == 4) {
        this.setState({ showGCDropDown: true });
      }
    }
    setTimeout(() => {
      this.setState({
        showLoader: false,
      });
    }, 1000);
  }

  historyDetails = () => {
    let url = "";
    if (this.state.isDelivery) {
      url = `${GET_HISTORY_NDR}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}`;
    } else if (this.state.isInspection) {
      url = `${GET_HISTORY_INS}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}`;
    }
    else {
      url = `${CRANE_HISTORY}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}/${this.state.projectId}`;
    }
    getHistoryDetails(
      url,
      {},
      () => null,
      (historyDetailsresp) => {
        this.setState({
          showLoader: false,
        });
        if (historyDetailsresp.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (historyDetailsresp.status) {
          if (historyDetailsresp.status == 200) {
            let data = historyDetailsresp.data.data;

            this.setState({
              historylist: data,
            });
          } else if (historyDetailsresp.data.data.message) {
            this.showErrorMessage(
              "error",
              historyDetailsresp.data.data.message
            );
          } else {
            this.showErrorMessage("error", historyDetailsresp.data.message);
          }
        } else {
          this.showErrorMessage("error", historyDetailsresp.toString());
        }
      }
    );
  };

  attachmentDetails = () => {
    this.setState({
      showLoader: true,
    });
    let url = "";
    if (this.state.isDelivery) {
      url = `${GET_ATTACHMENT_NDR}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}`;
    } else if (this.state.isInspection) {
      url = `${GET_ATTACHMENT_INS}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}`;
    }
    else {
      url = `${GET_ATTACHMENT_CRANE}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}/${this.state.projectId}`;
    }
    getAttachmentDetails(
      url,
      {},
      () => null,
      (attachmentDetailsresp) => {
        this.setState({
          showLoader: false,
        });

        if (attachmentDetailsresp.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (attachmentDetailsresp.status) {
          if ((attachmentDetailsresp.status == 200)) {
            let data = attachmentDetailsresp.data.data;
            this.setState({
              attachmentList: data,
            });
          } else if (attachmentDetailsresp.data.data.message) {
            this.showErrorMessage(
              "error",
              attachmentDetailsresp.data.data.message
            );
          } else {
            this.showErrorMessage("error", attachmentDetailsresp.data.message);
          }
        } else {
          this.showErrorMessage("error", attachmentDetailsresp.toString());
        }
      }
    );
  };

  commentDetails = () => {
    this.setState({
      showLoader: true,
    });
    let url = "";
    if (this.state.isDelivery) {
      url = `${GET_COMMENT_NDR}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}/${this.props.projectDetails.id}`;
    } else if (this.state.isInspection) {
      url = `${GET_COMMENT_INS}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}/${this.props.projectDetails.id}`;
    }
    else {
      url = `${GET_COMMENT_CRANE}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}/${this.state.projectId}`;
    }
    getCommentDetails(
      url,
      {},
      () => null,
      (commentDetailsresp) => {
        this.setState({
          showLoader: false,
        });

        if (commentDetailsresp.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (commentDetailsresp.status) {
          if (commentDetailsresp.status == 200) {
            let data = this.state.commentList;

            if (this.page_number == 1) {
              if (commentDetailsresp.data.data.count != 0) {
                this.setState({
                  commentList: commentDetailsresp.data.data.rows,
                  totalCount: commentDetailsresp.data.data.count,
                });
              } else {
                this.setState({
                  showNoData: true,
                  commentList: [],
                });
              }
            } else {
              let data1 = commentDetailsresp.data.data.rows;
              this.setState({
                commentList: data.concat(data1),
                totalCount: commentDetailsresp.data.data.count,
                //    lastId: commentDetailsresp.data.lastId.id,
              });
            }
          } else if (commentDetailsresp.data.data.message) {
            this.showErrorMessage(
              "error",
              commentDetailsresp.data.data.message
            );
          } else {
            this.showErrorMessage("error", commentDetailsresp.data.message);
          }
        } else {
          this.showErrorMessage("error", commentDetailsresp.toString());
        }
      }
    );
  };

  nextCommentClick = () => {
    this.setState({ showLoader: true });
    let param = "";
    if (this.state.isDelivery) {
      param = {
        comment: this.state.commenttext,
        DeliveryRequestId: this.state.DeliveryRequestId,
        ParentCompanyId: this.props.projectDetails?.ParentCompany?.id,
      };
    } else if (this.state.isInspection) {
      param = {
        comment: this.state.commenttext,
        InspectionRequestId: this.state.DeliveryRequestId,
        ParentCompanyId: this.props.projectDetails?.ParentCompany?.id,
      };
    }
    else {
      param = {
        comment: this.state.commenttext,
        CraneRequestId: this.state.DeliveryRequestId,
        ParentCompanyId: this.props.projectDetails?.ParentCompany?.id,
        ProjectId: this.state.projectId,
      };
    }


    let url = this.state.isDelivery ? CREATE_COMMENT_NDR : (this.state.isInspection ? CREATE_COMMENT_INS : CREATE_COMMENT_CRANE)
    createComment(
      url,
      param,
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {

          if (response.data.message) {
            if (response.status == 200 || response.status == 201) {
              this.setState({ commenttext: "" });
              this.showErrorMessage("Success", response.data.message);
              this.commentDetails();
              if (this.state.isDelivery) {
                trackEvent('Comment_Added_Against_Delivery_Request')
                mixPanelTrackEvent('Comment Added Against Delivery Request', this.state.mixpanelParam)
              } else if (this.state.isInspection) {
                trackEvent('Comment_Added_Against_Inspection_Request')
                mixPanelTrackEvent('Comment Added Against Inspection Request', this.state.mixpanelParam)
              }
              else {
                trackEvent('Comment_Added_Against_Crane_Request')
                mixPanelTrackEvent('Comment Added Against Crane Request', this.state.mixpanelParam)
              }
            } else {
              this.showErrorMessage("error", response.data.message);
            }
          } else if (response.data.message.message) {
            this.showErrorMessage("error", response.data.message.message);
          } else {
            this.showErrorMessage("error", Strings.errors.failed);
          }
        } else {
          this.showErrorMessage("error", response.toString());
        }
      }
    );
  };

  statusUpdateAPI = (param) => {
    updateStatus(
      this.state.isDelivery ? UPDATE_STATUS_NDR : (this.state.isInspection ? UPDATE_STATUS_INS : UPDATE_CRANE_STATUS),
      param,
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.message) {
            if (
              response.status
            ) {
              this.showErrorMessage("success", response.data.message);
              this.setState({ showLoader: true });
              this.deliveryDetails();
              this.props.refreshDashboard(true, "Details Update");
              this.props.refreshCalendar(true);
            }
            else {
              this.showErrorMessage("error", response.data.message);
            }
          } else if (response.data.message.message) {
            this.showErrorMessage("error", response.data.message.message);
          } else {
            this.showErrorMessage("error", Strings.errors.failed);
          }
        } else {
          this.showErrorMessage("error", response.toString());
        }
      }
    );
  }

  statusUpdate = () => {

    let isResponseValue = false;

    const arr1 = this.state.controlledByList;
    const arr2 = this.state.responsiblePerson

    const result = arr1.filter(o => arr2.some(({ id }) => o.id === id));

    if (this.state.responsiblePerson.length !== result.length) {
      isResponseValue = true;
    }


    let isEquipType = false; let isGateType = false;

    let equipTypeData = this.state.equipTypeList;

    const equipmentIdsArray = this.state.equipmentId.split(',');
    isEquipType = equipTypeData.find((item) => {

      return equipmentIdsArray.includes(item.id.toString());
    });




    let gateTypeData = this.state.gateList
    isGateType = gateTypeData.find(
      (item) => {
        if (item.id == this.state.gateNoID) { return true }
      })

    if (this.state.isRequestType == "craneRequest") {
      isGateType = true
    }
    if ((!isEquipType && !isGateType && isResponseValue)) {
      this.setState({
        showDelete: false,
        showGatePopup: true,
        gatePopupText: Strings.errors.emptyEquipGateResp,
      })
    } else if (!isEquipType && !isGateType) {
      this.setState({
        showDelete: false,
        showGatePopup: true,
        gatePopupText: Strings.errors.emptyEquipGate,
      })
    } else if (!isEquipType && isResponseValue) {

      this.setState({
        showDelete: false,
        showGatePopup: true,
        gatePopupText: Strings.errors.emptyEquipResp,
      })
    } else if (!isGateType && isResponseValue) {
      this.setState({
        showDelete: false,
        showGatePopup: true,
        gatePopupText: Strings.errors.emptyGateResp,
      })
    } else if (!isEquipType) {
      this.setState({
        showGatePopup: true,
        gatePopupText: Strings.errors.emptyEquipDetails
      })
    } else if (!isGateType) {
      this.setState({
        showGatePopup: true,
        gatePopupText: Strings.errors.emptyGateDetails
      })
    } else if (isResponseValue) {
      this.setState({
        showGatePopup: true,
        gatePopupText: Strings.errors.emptyRespoDetails
      })
    }
    else if (this.state.isDelivery) {
      param = {
        status: this.state.updatestatus,
        id: this.state.DeliveryRequestId,
      };
      this.statusUpdateAPI(param)
    }
    else if (this.state.isInspection) {


      let param = {
        id: this.state.DeliveryRequestId,
        status: this.state.updatestatus
      };


      if (this.state.selectstatus === 'Approved') {
        param = {
          ...param,
          inspectionStatus: this.state.updatestatus,
          status: this.state.selectstatus
        };
      }


      this.statusUpdateAPI(param);
    }
    else {
      param = {
        status: this.state.updatestatus,
        id: this.state.onlyId,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
      };
      this.statusUpdateAPI(param)
    }

  };

  onPressrole = (item) => {
    this.setState({
      updatestatus: item.name,
      statusModal: false,
      gcStatusModal: false,
      showSave: true,
    });
  };

  onPressOption = async (items) => {
    let item = this.state.downloaditem;
    this.setState({ downloadModal: false });
    const url = item.attachement;
    const localFile = `${RNFS.DocumentDirectoryPath}/${item.filename}`;
    const androidPath = `${RNFS.DownloadDirectoryPath}/${item.filename}`;
    const options = {
      fromUrl: url,
      toFile: localFile,
    };
    if (items.name == this.state.downloadoption[1].name) {
      if (Platform.OS == "android") {
        RNFS.downloadFile(options)
          .promise.then(() =>
            FileViewer.open(localFile)
              .then((response) => { })
              .catch((error) => console.log(error))
          )
          .catch((error) => console.log(error));
      } else {
        RNFS.downloadFile(options).promise.then(() =>
          FileViewer.open(localFile)
            .then((response) => { })
            .catch((error) => console.log(error))
        );
      }
    } else {
      this.setState({ showLoader: true });
      if (Platform.OS == "android") {
        try {
          await checkAndRequestPermission(Platform.Version >= 33 ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE).then(res => {
            if (res) {
              ReactNativeBlobUtil.config({
                path: androidPath,
                fileCache: true,
                addAndroidDownloads: {
                  useDownloadManager: true,
                  path: androidPath,
                },
              })
                .fetch("GET", url)
                .then(async (resp) => {
                  if (resp) {
                    if (resp) {
                      this.setState(
                        {
                          showToaster: true,
                          toastType: "success",
                          toastMessage: `${Strings.popup.downloadStatus} at ${androidPath}`,
                        },
                        () => {
                          setTimeout(() => {
                            this.setState({
                              showToaster: false,
                              showLoader: false,
                            });
                          }, 2000);
                        }
                      );
                    }
                  }
                  this.setState({ showLoader: false });
                })
                .catch((err) => {
                  this.setState({ showLoader: false });
                });
            } else {
              this.setState({ showPopUp: true, showText: Strings.permissions.files_media_permission, showLoader: false });
            }
          })
        } catch (err) { }
      } else {
        RNFS.downloadFile(options)
          .promise.then(() => {
            Share.open({
              type: item.extension,
              urls: [localFile],
              saveToFiles: true,
            });
          })
          .then((res) => {
            this.setState({ showLoader: false });

            // success
          })
          .catch((error) => {
            this.setState({ showLoader: false });

            // error
          });
      }
    }
  };

  onremovePressrole = (index) => {
    this.state.addattachmentlist.splice(index, 1);
    this.setState({
      addattachmentlist: this.state.addattachmentlist,
    });
    if (this.state.addattachmentlist.length === 0) {
      this.setState({ attachModel: false });
    }
  };

  onattachPressrole = (index) => {
    this.setState({ attachModel: false });
    let formData = new FormData();
    this.state.addattachmentlist.forEach((element) => {
      let filename =
        element.name == undefined ? element.fileName : element.name;
      let data = element;
      formData.append("attachement", data, filename);
    });
    this.setState({
      showLoader: true,
    });
    let url = "";
    if (this.state.isDelivery) {
      url = `${ADD_ATTACHEMENT}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}`;
    } else if (this.state.isInspection) {
      url = `${ADD_ATTACHMENT_INS}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}`;
    }
    else {
      url = `${ADD_ATTACHEMENT_CRANE}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}/${this.state.projectId}`;
    }
    addAttachment(
      url,
      formData,
      () => null,
      (response) => {
        if (response.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastType: "error",
              toastMessage: Strings.errors.checkInternet,
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                  showLoader: false,
                });
              }, 2000);
            }
          );
        }
        if (response.status) {
          if (response.status == 200 || response.status == 201) {
            this.attachmentDetails();
            this.setState(
              {
                showToaster: true,
                toastType: "success",
                toastMessage: "Uploaded Successfully.",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                    showLoader: false,
                  });
                }, 2000);
              }
            );
            trackEvent('Added_Attachment')
            mixPanelTrackEvent('Add Attachment', this.state.mixpanelParam)
          } else if (response.status == 413) {
            this.setState(
              {
                showToaster: true,
                toastType: "error",
                toastMessage: "Please upload valid profile or less then 1MB",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                    showLoader: false,
                  });
                }, 2000);
              }
            );
          } else {
            this.setState(
              {
                showToaster: true,
                toastType: "error",
                toastMessage: "Please upload valid profile or less then 1MB",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                    showLoader: false,
                  });
                }, 2000);
              }
            );
          }
        }
      }
    );
  };

  updateMasterState = (key, value) => { };

  hideToast = () => {
    setTimeout(() => this.setState({ showToaster: false }), 2000);
  };

  showErrorMessage = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
      },
      () => {
        setTimeout(() => {
          this.setState({ showToaster: false });
        }, 2000);
      }
    );
  };

  //HEADER COMPONENT
  renderHeader() {
    return (
      <View style={styles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            // this.props.navigation.navigate("Plus");
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.closeBlack} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={styles.title}>
          {this.state.isDelivery ? Strings.deliverydetails.deliverydetails : (this.state.isInspection ? Strings.deliverydetails.inspectiondetails : Strings.deliverydetails.craneDetails)}
          {/* {this.state.isDelivery
            ? Strings.deliverydetails.deliverydetails
            : Strings.deliverydetails.craneDetails} */}
        </Text>
        <View style={styles.closecontainer}></View>
      </View>
    );
  }
  renderResponsible = ({ item, index }) => {
    if (index < 3) {
      return (
        <Avatar.Text size={24} label={item.label} color="white" theme="grey" style={{ backgroundColor: 'grey', marginLeft: 5 }} />
      )
    }
    return null; // Must return null for items that shouldn't be rendered
  }
  renderDetails = () => {

    return (
      <View style={{ flex: 1, margin: 20 }}>
        <Text style={styles.detailsHeadingStyle}>
          {Strings.deliverydetails.itemdescription}
        </Text>
        <Text numberOfLines={2} style={styles.detailsTextStyle}>
          {this.state.itemdescription}
        </Text>
        {!this.state.isAssociatedWithCrane && (
          <>
            <View style={{ flexDirection: "row", marginTop: 8 }}>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {this.state.isDelivery ? Strings.deliverydetails.deliveryid : (this.state.isInspection ? Strings.deliverydetails.inspectionid : Strings.deliverydetails.craneId)}

                </Text>
                <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.deliveryId}
                </Text>
              </View>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.responsiblecompany}
                </Text>
                <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
                  {this.state.responsiblecompany != null ? this.state.responsiblecompany : ""}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: "row" }}>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.featureofwork}
                </Text>
                <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.featureofwork != '' ? this.state.featureofwork : "---"}
                </Text>
              </View>
              <View style={{ width: wp("35%"), }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.responsiblePerson}
                </Text>
                <TouchableOpacity onPress={() => { this.setState({ isModal: true }) }}
                  style={styles.responsibleContainer}>
                  <FlatList
                    data={this.state.responsiblePerson || []}
                    renderItem={this.renderResponsible}
                    horizontal={true}
                    scrollEnabled={false}
                    style={styles.responsibleFlatlistContainer}
                  />
                  {this.state.isResponsiblePersonCount &&
                    <Avatar.Text size={24} label={`+${this.state.responsiblePersonCount}`} color="white" theme="grey" style={[styles.avatarContainer, { marginRight: Platform.OS == 'android' ? 20 : 25 }]} />

                  }
                </TouchableOpacity>
                {/* <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.responsiblePerson}
                </Text> */}

              </View>
            </View>
            <View style={{ flexDirection: "row" }}>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.timeanddate}
                </Text>
                <Text style={styles.detailsTextRowStyle}>
                  {this.state.deliverydetails}
                </Text>
              </View>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.equipmentneeded}
                </Text>
                <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.equipementneeded != null ? this.state.equipementneeded : ""}
                </Text>
              </View>
            </View>
            {this.state.isDelivery && (
              <>
                <View style={{ flexDirection: "row" }}>
                  <View style={{ width: wp("50%") }}>
                    <Text style={styles.detailsHeadingRowStyle}>
                      {Strings.deliverydetails.gateno}
                    </Text>
                    <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
                      {this.state.gateno != null ? this.state.gateno : ""}
                    </Text>
                  </View>
                </View>
              </>
            )}
            {!this.state.isDelivery && (
              <>
                <View style={{ flexDirection: "row" }}>
                  <View style={{ width: wp("50%") }}>
                    <Text style={styles.detailsHeadingRowStyle}>
                      {Strings.deliverydetails.pickFrom}
                    </Text>
                    <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                      {this.state.pickFrom != null ? this.state.pickFrom : "---"}
                    </Text>
                  </View>
                  <View style={{ width: wp("50%") }}>
                    <Text style={styles.detailsHeadingRowStyle}>
                      {Strings.deliverydetails.pickTo}
                    </Text>
                    <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                      {this.state.pickTo != null ? this.state.pickTo : "---"}
                    </Text>
                  </View>
                </View>
              </>
            )}
          </>
        )}

        {this.state.isAssociatedWithCrane && (
          <>
            <View style={{ flexDirection: "row", marginTop: 8 }}>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {this.state.isDelivery ? Strings.deliverydetails.deliveryid : (this.state.isInspection ? Strings.deliverydetails.inspectionid : Strings.deliverydetails.craneId)}

                </Text>
                <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.deliveryId}
                </Text>
              </View>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.craneId}
                </Text>
                <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
                  {this.state.associateCraneId}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: "row" }}>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.responsiblecompany}
                </Text>
                <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
                  {this.state.responsiblecompany}
                </Text>
              </View>

              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.featureofwork}
                </Text>
                <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.featureofwork}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: "row" }}>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.responsiblePerson}
                </Text>
                <TouchableOpacity onPress={() => { this.setState({ isModal: true }) }}
                  style={{ flexDirection: 'row', width: '65%', }}>
                  <FlatList
                    data={this.state.responsiblePerson || []}
                    renderItem={this.renderResponsible}
                    horizontal={true}
                    scrollEnabled={false}
                    style={{ marginTop: 5 }}
                  />
                  {this.state.isResponsiblePersonCount &&
                    <Avatar.Text size={24} label={`+${this.state.responsiblePersonCount}`} color="white" theme="grey" style={{ backgroundColor: 'grey', marginTop: 5 }} />

                  }
                </TouchableOpacity>
                {/* <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.responsiblePerson}
                </Text> */}
              </View>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.timeanddate}
                </Text>
                <Text style={styles.detailsTextRowStyle}>
                  {this.state.deliverydetails}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: "row" }}>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.equipmentneeded}
                </Text>
                <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.equipementneeded}
                </Text>
              </View>

              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.gateno}
                </Text>
                <Text numberOfLines={2} style={styles.detailsTextRowStyle}>
                  {this.state.gateno}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: "row" }}>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.pickFrom}
                </Text>
                <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.pickFrom}
                </Text>
              </View>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.pickTo}
                </Text>
                <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.pickTo}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: "row" }}>
              <View style={{ width: wp("50%") }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.deliverydetails.deliveryvehicledetail}
                </Text>
                <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.deliveryvehicledet}
                </Text>
              </View>

            </View>
          </>
        )}

        {this.state.isDelivery && (
          <>
            <Text style={styles.detailsHeadingStyle}>
              {Strings.deliverydetails.deliveryStatus}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextStyle}>
              {this.state.appliedby}
            </Text>
          </>
        )}

        {!this.state.isDelivery && !this.state.isInspection && (
          <>
            <Text style={styles.detailsHeadingStyle}>
              {Strings.deliverydetails.deliveryStatus}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextStyle}>
              {this.state.craneStatus}
            </Text>
          </>
        )}


        {this.state.isInspection && (
          <>
            <Text style={styles.detailsHeadingStyle}>
              Inspection Status
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextStyle}>
              {this.state.status === null ? this.state.appliedby : this.state.status}
            </Text>
          </>
        )}

        <View style={{ flexDirection: 'row', marginTop: 20 }}>
          <View style={{ flexDirection: 'column', }}>
            <Text style={styles.detailsHeadingRowStyle}>
              {Strings.concreteDetails.location}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
              {this.state.location}
            </Text>
          </View>
          {this.state.isInspection && (
            <>
              <View style={{ marginLeft: 30 }}>
                <Text style={styles.detailsHeadingRowStyle}>
                  {Strings.concreteDetails.Inspection_type}
                </Text>
                <Text numberOfLines={0} style={styles.detailsTextRowStyle}>
                  {this.state.inspectionType}
                </Text>
              </View>
            </>
          )}
        </View>
        {this.state.note != "" && (
          <>
            <Text style={styles.detailsHeadingStyle}>
              {Strings.deliverydetails.note}
            </Text>
            <Text
              numberOfLines={0}
              style={[styles.detailsTextStyle, { marginBottom: 15 }]}
            >
              {this.state.note}
            </Text>
          </>
        )}
        {this.state.isInspection && (
          <>
            <Text style={styles.detailsHeadingStyle}>
              {Strings.addDR.originTitle}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextStyle}>
              {this.state?.originationAddress ? this.state?.originationAddress : '---'}
            </Text>
          </>
        )}


        {this.state.isInspection && (
          <>
            <Text style={styles.detailsHeadingStyle}>
              {Strings.addDR.vehicleType}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextStyle}>
              {this?.state?.vehicleType ? this.state.vehicleType : '---'}
            </Text>
          </>
        )}

        {this.state.isDelivery && (
          <>
            <Text style={styles.detailsHeadingStyle}>
              {Strings.addDR.originTitle}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextStyle}>
              {this.state?.originationAddress ? this.state?.originationAddress : '---'}
            </Text>
          </>
        )}


        {this.state.isDelivery && (
          <>
            <Text style={styles.detailsHeadingStyle}>
              {Strings.addDR.vehicleType}
            </Text>
            <Text numberOfLines={0} style={styles.detailsTextStyle}>
              {this?.state?.vehicleType ? this.state.vehicleType : '---'}
            </Text>
          </>
        )}



        <View style={{ marginBottom: 50 }}>
          {this.state.showDropDown == true &&
            (this.state.selectstatus == "Approved" ||
              this.state.selectstatus == "Pending") && (
              <TextField
                attrName={Strings.placeholders.selectstatus}
                title={Strings.placeholders.selectstatus}
                value={this.state.updatestatus}
                updateMasterState={(key, value) => {
                  this.updateMasterState(key, value);
                }}
                mandatory={true}
                textInputStyles={styles.textInputStatus}
                textTitleStyles={styles.textTitleStatus}
                showButton={true}
                onPress={() => {
                  if (this.state.projectRoleId != 4) {
                    this.setState({ statusModal: true });
                  }
                }}
                imageSource={Images.downArr}
              //   placeholder={'Select'}
              />
            )}

          {this.state.showGCDropDown == true &&
            this.state.selectstatus == "Approved" && (
              <TextField
                attrName={Strings.placeholders.selectstatus}
                title={Strings.placeholders.selectstatus}
                value={this.state.updatestatus}
                updateMasterState={(key, value) => {
                  this.updateMasterState(key, value);
                }}
                mandatory={true}
                textTitleStyles={styles.textTitleStatus}
                textInputStyles={styles.textInputStatus}
                showButton={true}
                onPress={() => {
                  if (this.state.projectRoleId == 3) {
                    this.setState({ gcStatusModal: true });
                  } else {
                    if (this.state.deliveryCreatedRoleId == 4) {
                      this.setState({ gcStatusModal: true });
                    }
                  }
                }}
                imageSource={Images.downArr}
              />
            )}
        </View>
      </View>
    );
  };

  renderRow = (option, index, isSelected) => {
    return (
      <View
        style={{
          width: wp("40%"),
          flexDirection: "row",
          alignItems: "center",
          height: hp("7%"),
          backgroundColor: "white",
        }}
      >
        <Image
          resizeMode={"center"}
          source={option.image}
          style={{
            width: option.id == "Edit" ? wp("5%") : wp("6%"),
            height: option.id == "Edit" ? hp("4%") : hp("5%"),
            marginLeft: 10,
          }}
        />
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: Colors.planDesc,
            marginLeft: 10,
          }}
        >
          {option.id}
        </Text>
      </View>
    );
  };

  renderSeparator = () => {
    return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
  };

  updateData = (data) => {
    this.getProjectRole(this.state.notificationDetails);
    this.deliveryDetails();
  };

  onSelectDropdown = (options, editRequestID) => {

    if (options == "Save") {

      if (this.state.updatestatus == "Delivered" || this.state.updatestatus == "Completed" || this.state.updatestatus == "Pass" || this.state.updatestatus == "Fail") {
        this.setState({ showLoader: true });
        this.setState({ selectstatus: this.state.updatestatus });
        this.statusUpdate();
      } else {
        if (this.state.updatestatus != this.state.selectstatus) {
          this.setState({ selectstatus: this.state.updatestatus });
          this.statusUpdate();
        }
      }
    }

    if (options == "Edit") {
      if (this.state.deliveryItem) {
        if (this.state.isDelivery) {
          this.props.showDeliverdetailsid(this.state.deliveryItem.id);
          this.props.editData({
            item: this.state.deliveryItem,
            index: 0,
          });
        }
        else if (this.state.isInspection) {
          this.props.showDeliverdetailsid(this.state.deliveryItem.id);
          this.props.editData({
            item: this.state.deliveryItem,
            index: 0,
          });
        }
        else {
          this.props.showCraneRequestId(this.state.deliveryItem.id);
          this.props.editCraneRequest({
            item: this.state.deliveryItem,
            index: 0,
          });
        }
        this.props.navigation.navigate(
          // this.state.isDelivery ? "AddDR" : "AddCrane",
          this.state.isDelivery ? "AddDR" : (this.state.isInspection ? "addinspection" : "AddCrane"),
          {
            from: "detailDRPage",
            notificationDetails: this.state.notificationDetails,
            updateData: this.updateData,
            showEditRequestID: editRequestID
          }
        );
      }
    }

    if (options == "Void") {
      this.voidClick();
    }
  };

  voidClick = () => {
    this.setState({ showLoader: true });
    let param = "";
    if (this.state.isDelivery) {
      param = {
        ProjectId: this.state.projectId,
        DeliveryRequestId: this.state.DeliveryRequestId,
      };
    } else if (this.state.isInspection) {
      param = {
        ProjectId: this.state.projectId,
        InspectionRequestId: this.state.DeliveryRequestId,
      };
    }
    else {
      param = {
        CraneRequestId: this.state.onlyId,
        ProjectId: this.props.projectDetails.id,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
      };
    }
    createVoid(
      // this.state.isDelivery ? CREATE_VOID : ADD_VOID_CRANE,
      this.state.isDelivery ? CREATE_VOID : (this.state.isInspection ? CREATE_VOID_INS : ADD_VOID_CRANE),
      param,
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.status == 200 || response.status == 201) {
            this.showErrorMessage("success", response.data.message);

            setTimeout(() => {
              this.props.refreshDashboard(true, "Details Create Void ");
              this.props.cameBack(false);
              this.props.refreshCalendar(true);
              //this.props.navigation.navigate("Plus");
              this.props.navigation.goBack();
            }, 2000);
            if (this.state.isDelivery) {
              trackEvent('Delivery_Request_Voided')
              mixPanelTrackEvent('Delivery Request Voided', this.state.mixpanelParam)
            } else if (this.state.isInspection) {
              trackEvent('Inspection_Request_Voided')
              mixPanelTrackEvent('Inspection Request Voided', this.state.mixpanelParam)
            }
            else {
              trackEvent('Crane_Request_Voided')
              mixPanelTrackEvent('Crane Request Voided', this.state.mixpanelParam)
            }
          } else if (response.data.message.message) {
            let array = Object.values(response.data.message.details[0]);
            this.showErrorMessage("error", array.toString());
          } else {
            this.showErrorMessage("error", response.data.message);
          }
        } else {
          this.showErrorMessage("error", response.toString());
        }
      }
    );
  };

  deleteattachement = (id) => {
    this.setState({ showLoader: true });
    let url = "";
    if (this.state.isDelivery) {
      url = `${REMOVE_ATTACHEMENT}${id}/${this.props.projectDetails.ParentCompany.id}`;
    } else if (this.state.isInspection) {
      url = `${REMOVE_ATTACHMENT_INS}${id}/${this.props.projectDetails.ParentCompany.id}`;
    }
    else {
      url = `crane_request_attachment/remove_crane_request_attachement/${id}/${this.props.projectDetails.ParentCompany.id}/${this.state.projectId}`;
    }
    removeAttachement(
      url,
      {},
      () => null,
      (response) => {
        this.setState({
          showLoader: false,
        });
        if (response.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (response.status) {
          if (response.status == 200) {
            this.showErrorMessage("success", response.data.message);
            trackEvent('Removed_Attachment')
            mixPanelTrackEvent('Removed Attachment', this.state.mixpanelParam)
            this.attachmentDetails();
          } else if (response.data.data.message) {
            this.showErrorMessage("error", response.data.data.message);
          } else {
            this.showErrorMessage("error", response.data.message);
          }
        } else {
          this.showErrorMessage("error", response.toString());
        }
      }
    );
  };

  renderFlatListItem = ({ item, index }) => {
    let showDelete = true;
    if (this.state.projectRoleId == 4) {
      showDelete = false;
    }
    return (
      <View
        style={{
          minHeight: 40,
          marginVertical: hp("1%"),
          width: wp("95%"),
          alignSelf: "center",
          flexDirection: "row",
        }}
      >
        <TouchableOpacity
          onPress={() => {
            this.setState({ downloadModal: true, downloaditem: item });
          }}
        >
          <View style={[styles.flatlistContainer, { width: wp("95%") }]}>
            <View style={styles.nameContainer}>
              <View
                style={{
                  width: wp("18%"),
                  minHeight: hp("10%"),
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                {(item.extension === "jpg" ||
                  item.extension === "jpeg" ||
                  item.extension === "png") && (
                    <Image
                      source={{ uri: item.attachement }}
                      style={styles.imagePlaceholder}
                    />
                  )}
                {item.extension === "pdf" && (
                  <Image
                    source={Images.pdf_place}
                    resizeMode={"contain"}
                    style={styles.imagePlaceholder}
                  />
                )}
                {item.extension === "doc" && (
                  <Image
                    source={Images.doc_place}
                    resizeMode={"contain"}
                    style={styles.imagePlaceholder}
                  />
                )}
              </View>
              <View style={styles.detailContainer}>
                <Text style={styles.nameText}>{item.filename}</Text>
                <Text style={styles.companyText}>
                  {moment(item.createdAt).format("MMMM DD, YYYY, hh:mm:ss a")}
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>

        {showDelete && (
          <View
            style={[
              styles.dotMenu,
              {
                width: wp("10%"),
                height: hp("8%"),
                position: "absolute",
                right: 10,
              },
            ]}
          >
            <TouchableWithoutFeedback
              onPress={() => {
                this.setState({
                  selectedAttachmentId: item.id,
                  showDelete: true,
                });
                // this.deleteattachement(item.id)
              }}
            >
              <Image source={Images.delete} />
            </TouchableWithoutFeedback>
          </View>
        )}
      </View>
    );
  };

  onEndReached = () => {
    if (this.state.commentList.length < this.state.totalCount) {
      this.page_number = this.page_number + 1;
      this.commentDetails();
      this.onEndReachedCalledDuringMomentum = true;
    }
  };
  _onResetHistory = () => {
    this.page_number = 1;
    this.setState(
      {
        historylist: [],
        showLoader: true,
      },
      () => {
        this.historyDetails();
      }
    );
  };
  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        commentList: [],
        totalCount: 0,
        showLoader: true,
      },
      () => {
        this.commentDetails();
      }
    );
  };
  _onResetAttachment = () => {
    this.page_number = 1;
    this.setState(
      {
        attachmentList: [],
        showLoader: true,
      },
      () => {
        this.attachmentDetails();
      }
    );
  };

  renderAttachement = () => {
    return (
      <View style={styles.renderAttachementContainer}>
        <FlatList
          data={this.state.attachmentList || []}
          renderItem={this.renderFlatListItem}
          keyExtractor={(item, index) => index.toString()}
          onEndReached={() => this.onEndReached()}
          onEndReachedThreshold={0}
          onMomentumScrollBegin={() => {
            this.onEndReachedCalledDuringMomentum = false;
          }}
          onRefresh={() => this._onResetAttachment()}
          refreshing={this.state.refreshing}
          style={styles.flatlistStyleContainer}
        />
        <View style={styles.flatlistContainer}>
          <TouchableWithoutFeedback
            onPress={() => {
              if (Platform.OS == "android") {
                this.setState({
                  iosDropDown: [{ name: "Camera" }, { name: "Document" }],
                });
              }
              this.setState({ isIosDropDown: !this.state.isIosDropDown });
            }}
          >
            <View style={styles.nameWidthContainer}>
              <View style={styles.nameContainer}>
                <View
                  style={{
                    width: wp("18%"),
                    minHeight: hp("10%"),
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <View
                    style={{
                      width: wp("15%"),
                      height: wp("15%"),
                      borderRadius: wp("3%"),
                      marginLeft: wp("6%"),
                      backgroundColor: "#ECECEC",
                      justifyContent: "center",
                    }}
                  >
                    <Image
                      source={Images.plus_attach}
                      style={{
                        justifyContent: "center",
                        alignItems: "center",
                        alignSelf: "center",
                      }}
                    />
                  </View>
                </View>
                <View style={styles.detailContainer}>
                  <Text
                    style={{
                      color: "#707070",
                      fontSize: 14,
                      fontFamily: Fonts.montserratSemiBold,
                    }}
                  >
                    {Strings.deliverydetails.addattachment}
                  </Text>
                </View>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>

      </View>
    );
  };
  onPressIosDropDown = async (item) => {
    this.setState({ isIosDropDown: false })
    if (item.name == "Document") {
      this.pickupDocument();
    } else if (item.name == "Photos") {
      this.pickPhotos();
    } else if (item.name == "Camera") {
      checkAndRequestPermission(PERMISSIONS.ANDROID.CAMERA).then(async res => {
        if (res || Platform.OS === 'ios') {
          const result = await launchCamera({ mediaType: "photo" });
          let assets = [];
          for (let item of result.assets) {
            assets.push({
              size: item.fileSize,
              fileCopyUri: item.uri,
              name: item.fileName,
              uri: item.uri,
              type: item.type,
            });
          }
          this.setState({
            isIosDropDown: false,
            attachModel: true,
            addattachmentlist: assets,
          });
        } else {
          this.setState({ showPopUp: true, showText: Strings.permissions.camera_permission });
        }
      })
    }
  };
  onPressEditDropDown = (item) => {
    this.onSelectDropdown("Edit", item.id)
    this.setState({ isEditAccess: false })
  }
  pickPhotos = async () => {
    const result = await launchImageLibrary({ mediaType: "photo" });
    let assets = [];
    for (let item of result.assets) {
      assets.push({
        size: item.fileSize,
        fileCopyUri: item.uri,
        name: item.fileName,
        uri: item.uri,
        type: item.type,
      });
    }
    const filesize = Math.round(assets[0].size / 1024);
    // if (filesize > 2048) {
    //   this.setState({ isIosDropDown: false });
    //   this.showErrorMessage(
    //     "error",
    //     "Image size should not exceed greater than 2MB"
    //   );
    // } else {
    this.setState({
      isIosDropDown: false,
      attachModel: true,
      addattachmentlist: assets,
    });
    // }
  };
  pickupDocument = async () => {
    checkAndRequestPermission(Platform.Version >= 33 ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE).then(async res => {
      if (res || Platform.OS === 'ios') {
        try {
          const results = await DocumentPicker.pickMultiple({
            type: [DocumentPicker.types.allFiles],
          });
          let attach = results
            .filter((e) => {
              let type = ["pdf", "doc", "png", "jpg", "jpeg"].includes(
                e.type.split("/")[1]
              );
              if (!type) {
                //TODO FOR LATER
                // alert(
                //   "Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)"
                // );
              }
              return type;
            })
            .map((e) => e);
          if (attach.length) {
            this.setState({
              isIosDropDown: false,
              attachModel: true,
              addattachmentlist: attach,
            });
          }
        } catch (err) {
          if (DocumentPicker.isCancel(err)) {
            // User cancelled the picker, exit any dialogs or menus and move on
          } else {
            throw err;
          }
        }
      } else {
        this.setState({ showPopUp: true, showText: Strings.permissions.files_media_permission });
      }
    })
  };

  renderCommentListItem = ({ item, index }) => {
    return (
      <View style={styles.flatlistContainer}>
        <View style={{ width: wp("95%") }}>
          <View style={{ flexDirection: "row", width: wp("90%") }}>
            <Image
              source={
                item.Member.User.profilePic
                  ? { uri: item.Member.User.profilePic }
                  : Images.placeholder
              }
              style={{
                width: wp("10%"),
                height: wp("10%"),
                borderRadius: wp("5%"),
                //  backgroundColor: "gray",
                marginRight: 10,
                marginLeft: 10,
              }}
            ></Image>
            <Text
              style={
                (styles.historyCompanyText, { marginTop: 10, width: wp("70%") })
              }
            >
              {item.Member.User.firstName + " " + item.Member.User.lastName}
            </Text>
          </View>
          <View style={styles.historyDetailContainer}>
            <Text style={styles.historyNameText}>{item.comment.trim()}</Text>
            <Text style={styles.historyDateText}>
              {moment(item.createdAt).format("MMMM DD,YYYY, hh:mm:ss a")}
            </Text>
          </View>
        </View>

        <View
          style={{ marginTop: 10, height: 0.5, backgroundColor: "#A8B2B9" }}
        ></View>
      </View>
    );
  };

  renderTextField = () => {
    const { height } = this.state;

    let newStyle = {
      height,
    };

    return (
      <View
        style={{
          // marginLeft: 20,
          // marginRight: 20,
          paddingHorizontal: 20,
          // width: "100%",
          // height:"100%",
          flex: 1,
          bottom: 0,
          backgroundColor: "pink",
        }}
        onLayout={(event) => { }}
      >
        {/* <View style={{}}> */}
        <Text style={styles.commentsTextBox}>
          {Strings.deliverydetails.entercomments}
        </Text>
        <View
          style={{
            height: hp("10%"),
            borderRadius: 5,
            borderColor: "#BEBEBE",
            borderWidth: 0.5,
            marginTop: 10,
          }}
        >
          <TextInput
            placeholder="Your Placeholder"
            onChangeText={(value) => this.setState({ commenttext: value })}
            style={([newStyle], { margin: 5 })}
            editable
            multiline
            maxLength={150}
            value={this.state.commenttext}
            onContentSizeChange={(e) =>
              this.updateSize(e.nativeEvent.contentSize.height)
            }
          />
        </View>
        <TouchableOpacity
          onPress={() => {
            let comStr = this.state.commenttext.trim();
            if (isEmpty(comStr)) {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: "Please enter the comments",
                  toastType: "error",
                },
                () => this.hideToast()
              );
            } else {
              if (comStr.length < 3) {
                this.setState(
                  {
                    showToaster: true,
                    toastMessage:
                      "comment lenght must be atleast 3 characters long",
                    toastType: "error",
                  },
                  () => this.hideToast()
                );
              } else {
                this.nextCommentClick();
              }
            }
          }}
          style={{
            width: wp("30%"),

            height: hp("4%"),
            marginTop: hp("3%"),
            backgroundColor: Colors.themeOpacity,
            borderRadius: hp("3.5%"),
            justifyContent: "center",
            alignItems: "center",
            alignSelf: "flex-end",
          }}
        >
          <View
            style={{
              width: wp("50%"),
              alignSelf: "center",
              height: hp("7%"),
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                fontSize: wp("4%"),
                color: Colors.themeColor,
                fontFamily: Fonts.montserratSemiBold,
              }}
            >
              {Strings.profile.submit}
            </Text>
          </View>
        </TouchableOpacity>
        {/* </View> */}
      </View>
    );
  };

  renderComments = () => {
    return (
      // <View style={{ flex: 1, height: "100%" }}>
      // <View style={{ flex: 1, height: "60%" }}>
      <FlatList
        style={{ marginTop: 10, }}
        data={this.state.commentList || []}
        // contentContainerStyle={{paddingBottom:400}}
        renderItem={this.renderCommentListItem}
        keyExtractor={(item, index) => index.toString()}
        onEndReached={() => this.onEndReached()}
        onEndReachedThreshold={0}
        onMomentumScrollBegin={() => {
          this.onEndReachedCalledDuringMomentum = false;
        }}
        onRefresh={() => this._onReset()}
        refreshing={this.state.refreshing}
      />
      //  </View>
      // </View>
    );
  };

  renderHistoryListItem = ({ item, index }) => {
    if (item.type != "comment") {
      return (
        <View style={styles.flatlistContainer}>
          <View style={{ width: wp("95%") }}>
            {//TODO FOR LATER
            /* <Text style={styles.historyUserText}>
              {item.Member.User.firstName + " " + item.Member.User.lastName}
            </Text> */}
            <View style={styles.historyDetailContainer}>
              <Text style={styles.historyDateText}>
                {moment(item.createdAt).format("MMMM DD,YYYY, hh:mm:ss a")}
              </Text>
              <Text style={styles.historyCompanyText}>{item.description}</Text>
            </View>
            <View
              style={{ marginTop: 10, height: 0.5, backgroundColor: "#A8B2B9" }}
            ></View>
          </View>
        </View>
      );
    }
  };

  renderHistory = () => {
    return (
      <View style={{ flex: 1, marginBottom: hp("10%") }}>
        <FlatList
          data={this.state.historylist || []}
          renderItem={this.renderHistoryListItem}
          // ItemSeparatorComponent={this.itemSeparator}
          keyExtractor={(item, index) => index.toString()}
          onEndReached={() => this.onEndReached()}
          onEndReachedThreshold={0}
          onMomentumScrollBegin={() => {
            this.onEndReachedCalledDuringMomentum = false;
          }}
          onRefresh={() => this._onResetHistory()}
          refreshing={this.state.refreshing}
        />
      </View>
    );
  };

  updateSize = (height) => {
    this.setState({
      height,
    });
  };

  dropdownAccessFunction = () => {
    if (this.state.recurrenceType == Strings.calendarSettings.doseNotRepeat) {
      this.onSelectDropdown("Edit", 1)
    } else {
      this.setState({ isEditAccess: !this.state.isEditAccess })
    }
  }

  renderBottomContainer = () => {
    const { id, repMemberIds } = this.state;

    return (
      <View style={styles.bottomContainer}>
        {this.state.showSave == true && (
          <View style={styles.bottomView}>
            <TouchableOpacity
              onPress={() => this.onSelectDropdown("Save", 0)}
              style={styles.footerAction}
            >
              <Image source={HEADERDROPDOWNOPTIONS[0].image} />
              <Text style={styles.footerActionText}>
                {HEADERDROPDOWNOPTIONS[0].name}
              </Text>
            </TouchableOpacity>
          </View>
        )}
        {(this.state.isAccess) && (
          <>
            <View style={styles.bottomView}>
              <TouchableOpacity
                onPress={() => this.dropdownAccessFunction()}
                style={styles.footerAction}
              >
                <Image source={HEADERDROPDOWNOPTIONS[1].image} />
                <Text style={styles.footerActionText}>
                  {HEADERDROPDOWNOPTIONS[1].name}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.bottomView}>
              <TouchableOpacity
                onPress={() => this.onSelectDropdown("Void", 0)}
                style={styles.footerAction}
              >
                <Image source={HEADERDROPDOWNOPTIONS[2].image} />
                <Text style={styles.footerActionText}>
                  {HEADERDROPDOWNOPTIONS[2].name}
                </Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    );
  };

  cancelPopupAcceptTap = () => {
    this.setState({ showCancel: false });
    this.onCancelSubscription();
  };

  cancelPopupDeclineTap = () => {
    this.setState({ showCancel: false });
  };

  deletePopupAcceptTap = () => {
    this.setState({ showDelete: false });
    this.deleteattachement(this.state.selectedAttachmentId);
  };

  deletePopupDeclineTap = () => {
    this.setState({
      showDelete: false,
      selectedAttachmentId: null,
    });
  };

  deleteGateAcceptTap = () => {
    this.setState({ selectstatus: this.state.updatestatus });
    this.showGateUpdate();
    this.setState({ showDeliveredPop: false, showGatePopup: false });
  }

  showGateUpdate = () => {
    if (this.state.isDelivery) {
      param = {
        status: this.state.updatestatus,
        id: this.state.DeliveryRequestId,
      };
    } else if (this.state.isInspection) {
      param = {
        status: this.state.updatestatus,
        id: this.state.DeliveryRequestId,

      };
    }
    else {
      param = {
        status: this.state.updatestatus,
        id: this.state.onlyId,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
      };
    }

    updateStatus(
      this.state.isDelivery ? UPDATE_STATUS_NDR : (this.state.isInspection ? UPDATE_STATUS_INS : UPDATE_CRANE_STATUS),
      param,

      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.message) {
            if (
              response.status
            ) {
              this.showErrorMessage("success", response.data.message);
              this.setState({ showLoader: true });
              this.deliveryDetails();
              this.props.refreshDashboard(true, "Details Update");
              this.props.refreshCalendar(true);
            }
            else {
              this.showErrorMessage("error", response.data.message);
            }
          } else if (response.data.message.message) {
            this.showErrorMessage("error", response.data.message.message);
          } else {
            this.showErrorMessage("error", Strings.errors.failed);
          }
        } else {
          this.showErrorMessage("error", response.toString());
        }
      }
    );
  }

  deleteGateDeclineTap = () => {
    this.setState({ showGatePopup: false })
    this.onSelectDropdown("Edit", 0)
  }
  onChangeTab = (i) => {
    if (i == 1) {
      this.attachmentDetails();
    } else if (i == 2) {
      this.commentDetails();
    } else if (i == 3) {
      this.historyDetails();
    }
  };

  renderTabScene = ({ route }) => {
    switch (route.key) {
      case 'details':
        return (
          <KeyboardAwareScrollView extraScrollHeight={50}>
            {this.renderDetails()}
          </KeyboardAwareScrollView>
        );
      case 'attachments':
        return this.renderAttachement();
      case 'comments':
        return (
          <KeyboardAvoidingView tabLabel="Comments" style={{ flex: 1 }}
            behavior="position"
            keyboardVerticalOffset={Platform.OS === "ios" ? 100 : 0}
          >

            <FlatList
              style={{ marginTop: 10, height: hp('42%') }}
              data={this.state.commentList || []}
              contentContainerStyle={{ paddingBottom: 50 }}
              renderItem={this.renderCommentListItem}
              keyExtractor={(item, index) => index.toString()}
              onEndReached={() => this.onEndReached()}
              onEndReachedThreshold={0}
              onMomentumScrollBegin={() => {
                this.onEndReachedCalledDuringMomentum = false;
              }}
              onRefresh={() => this._onReset()}
              refreshing={this.state.refreshing}
            />
            <Text style={styles.commentsTextBox}>
              {Strings.deliverydetails.entercomments}
            </Text>
            <View
              style={{
                height: hp("10%"),
                borderRadius: 5,
                borderColor: "#BEBEBE",
                borderWidth: 0.5,
                marginTop: 10,
                marginLeft: hp("2%"),
                marginRight: hp('2%')
              }}
            >
              <TextInput
                placeholder="Your Placeholder"
                onChangeText={(value) => this.setState({ commenttext: value })}
                style={({ margin: 5 })}
                editable
                multiline
                maxLength={150}
                value={this.state.commenttext}
                onContentSizeChange={(e) =>
                  this.updateSize(e.nativeEvent.contentSize.height)
                }
              />
            </View>
            <TouchableOpacity
              onPress={() => {
                let comStr = this.state.commenttext.trim();
                if (isEmpty(comStr)) {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: "Please enter the comments",
                      toastType: "error",
                    },
                    () => this.hideToast()
                  );
                } else {
                  if (comStr.length < 3) {
                    this.setState(
                      {
                        showToaster: true,
                        toastMessage:
                          "comment lenght must be atleast 3 characters long",
                        toastType: "error",
                      },
                      () => this.hideToast()
                    );
                  } else {
                    this.nextCommentClick();
                  }
                }
              }}
              style={{
                width: wp("30%"),
                marginRight: hp("2%"),
                height: hp("4%"),
                marginTop: hp("3%"),
                backgroundColor: Colors.themeOpacity,
                borderRadius: hp("3.5%"),
                justifyContent: "center",
                alignItems: "center",
                alignSelf: "flex-end",
              }}
            >
              <View
                style={{
                  width: wp("50%"),
                  alignSelf: "center",
                  height: hp("7%"),
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text
                  style={{
                    fontSize: wp("4%"),
                    color: Colors.themeColor,
                    fontFamily: Fonts.montserratSemiBold,
                  }}
                >
                  {Strings.profile.submit}
                </Text>
              </View>
            </TouchableOpacity>
          </KeyboardAvoidingView>
        );
      case 'history':
        return this.renderHistory();
      default:
        return null;
    }
  };

  renderTabBar = props => (
    <TabBar
      {...props}
      scrollEnabled={true}
      tabStyle={{ width: 'auto', marginHorizontal: wp('2%') }}
      indicatorStyle={{ backgroundColor: Colors.themeColor }}
      style={{ backgroundColor: Colors.white }}
      activeColor={Colors.black}
      inactiveColor={'#A8B2B9'}
      labelStyle={{
        fontSize: wp('3.8%'),
        fontFamily: Fonts.montserratExtraLight,
      }}
    />
  );

  render() {
    const showBottomContainer = (this.state.showSave == true) || (this.state.isAccess == true)
    return (
      <>
        {this.state.isNetworkCheck ?
          <NoInternet
            Refresh={() => this.networkCheck()} /> :
          <AppView>
            <View style={styles.parentContainer}>
              {this.renderHeader()}

              <TabView
                navigationState={{ index: this.state.tabIndex, routes: this.state.tabRoutes }}
                renderScene={this.renderTabScene}
                style={showBottomContainer ? { marginBottom: hp("10%") } : {}}
                renderTabBar={this.renderTabBar}
                onIndexChange={tabIndex => {

                  this.setState({ tabIndex })

                  this.onChangeTab(tabIndex)
                }}
                initialLayout={{ width: Dimensions.get('window').width }}
              />

              {showBottomContainer && this.renderBottomContainer()}
            </View>
            {this.state.statusModal && (
              <Dropdown
                data={this.state.statuslist}
                title={Strings.placeholders.selectstatus}
                value={this.state.updatestatus}
                closeBtn={() => this.setState({ statusModal: false })}
                onPress={(item) => this.onPressrole(item)}
                visible={this.state.statusModal}
                onbackPress={() => this.setState({ statusModal: false })}
                textContainer={{
                  textAlign: "center",
                }}
              />
            )}
            {this.state.downloadModal && (
              <Dropdown
                data={this.state.downloadoption}
                title={Strings.profile.Choose}
                value={""}
                closeBtn={() =>
                  this.setState({ downloadModal: false, downloaditem: {} })
                }
                onPress={(item) => this.onPressOption(item)}
                visible={this.state.downloadModal}
                onbackPress={() =>
                  this.setState({ downloadModal: false, downloaditem: {} })
                }
                container={{ justifyContent: "center", alignItems: "center" }}
                textContainer={{ fontSize: 14, textAlign: "center", }}
              />
            )}
            {this.state.isModal && (
              <ResDropdown
                data={this.state.responsiblePerson}
                title={Strings.deliverydetails.responsiblePerson}
                value={""}
                closeBtn={() =>
                  this.setState({ isModal: false, downloaditem: {} })
                }
                onPress={() => null}
                visible={this.state.isModal}
                onbackPress={() =>
                  this.setState({ isModal: false })
                }
                //container={{  alignItems: "center" }}
                textContainer={{ fontSize: 16 }}
              />
            )
            }
            {this.state.gcStatusModal && (
              <Dropdown
                data={this.state.gcStatusList}
                title={Strings.placeholders.selectstatus}
                value={this.state.updatestatus}
                closeBtn={() => this.setState({ gcStatusModal: false })}
                onPress={(item) => this.onPressrole(item)}
                visible={this.state.gcStatusModal}
                onbackPress={() => this.setState({ gcStatusModal: false })}
                textContainer={{
                  textAlign: "center",
                }}
              />
            )}
            {this.state.isIosDropDown && (
              <Dropdown
                data={this.state.iosDropDown}
                title={Strings.placeholders.chooseType}
                value={this.state.iosDropDown}
                closeBtn={() => this.setState({ isIosDropDown: false })}
                onPress={(item) => this.onPressIosDropDown(item)}
                visible={this.state.isIosDropDown}
                onbackPress={() => this.setState({ isIosDropDown: false })}
                textContainer={{
                  textAlign: "center",
                }}
              />
            )}
            {this.state.attachModel && (
              <AttachDropdown
                data={this.state.addattachmentlist}
                title={Strings.deliverydetails.files}
                value={this.state.updatestatus}
                closeBtn={() => this.setState({ attachModel: false })}
                onRemove={(item) => this.onremovePressrole(item)}
                onDone={(item) => this.onattachPressrole(item)}
                visible={this.state.attachModel}
                onbackPress={() => this.setState({ attachModel: false })}
              />
            )}
            {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

            {this.state.isEditAccess && (
              <Dropdown
                data={this.state.editDropDown}
                title={Strings.placeholders.chooseType}
                value={this.state.editDropDown}
                closeBtn={() => this.setState({ isEditAccess: false })}
                onPress={(item) => this.onPressEditDropDown(item)}
                visible={this.state.isEditAccess}
                onbackPress={() => this.setState({ isEditAccess: false })}
                textContainer={styles.editDropdownStyle}
              />
            )}

            {this.state.showToaster && (
              <Toastpopup
                backPress={() => this.setState({ showToaster: false })}
                toastMessage={this.state.toastMessage}
                type={this.state.toastType}
                container={{ marginBottom: hp("12%") }}
              />
            )}

            {this.state.showAlert && (
              <Alert
                title={Strings.popup.success}
                desc={Strings.popup.changepasswordSuccess}
                okTap={() => {
                  this.okTap();
                }}
              />
            )}

            {this.state.showCancel && (
              <DeletePop
                title={Strings.popup.success}
                desc={Strings.popup.cancelSub}
                descStyles={{
                  width: "80%",
                }}
                container={{ bottom: 0 }}
                acceptTap={this.cancelPopupAcceptTap}
                declineTap={this.cancelPopupDeclineTap}
              />
            )}

            {this.state.showDelete && (
              <DeletePop
                container={{
                  bottom: 0,
                }}
                title={Strings.popup.success}
                desc={Strings.popup.delete}
                acceptTap={this.deletePopupAcceptTap}
                declineTap={this.deletePopupDeclineTap}
              />
            )}

            {this.state.showGatePopup && (
              <DeletePop
                container={{
                  bottom: 0,
                }}
                title={Strings.popup.success}
                desc={this.state.gatePopupText}
                acceptTap={this.deleteGateAcceptTap}
                declineTap={this.deleteGateDeclineTap}
              />
            )}
            {this.state.showDeliveredPop && (
              <DeletePop
                title={Strings.popup.success}
                desc={Strings.popup.beforeDeliveryPopUp}
                container={{ bottom: 0 }}
                acceptTap={() => {
                  this.setState({ selectstatus: this.state.updatestatus });
                  this.statusUpdate();
                  this.setState({ showDeliveredPop: false });
                }}
                declineTap={() => {
                  this.setState({ showDeliveredPop: false });
                }}
              />
            )}

            {this.state.showPopUp && (
              <DeletePop
                container={styles.containerStyles}
                descStyles={styles.descStyles}
                desc={this.state.showText}
                declineText={Strings.permissions.no_thanks}
                acceptText={Strings.permissions.go_to_settings}
                declineTap={() => this.setState({ showPopUp: false })}
                acceptTap={() => {
                  Linking.openSettings()
                  this.setState({ showPopUp: false })
                }}
                declineTextStyle={styles.textStyle}
                acceptTextStyle={styles.textStyle}
              />
            )}

          </AppView>
        }
      </>
    );
  }
}
const mapStateToProps = (state) => {
  const {
    projectDetails,
    deliveryDetailsId,
    inspectionDetailsId,
    projectRoleId,
    notificationDetails,
    userDetails,
  } = state.LoginReducer;

  return {
    projectDetails,
    deliveryDetailsId,
    inspectionDetailsId,
    projectRoleId,
    notificationDetails,
    userDetails,
  };
};

export default compose(
  connect(mapStateToProps, {
    cameBack,
    showDeliverdetailsid,
    editData,
    tappedNotificationDetails,
    refreshPage,
    refreshDashboard,
    refreshDeliveryList,
    showCraneRequestId,
    editCraneRequest,
    refreshCalendar,
  }),
  withBackHandler
)(Details);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("80%"),
    fontSize: wp("6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratSemiBold,
  },
  // imageContainer: {
  //   width: wp("100%"),
  //   height: hp("13%"),
  //   alignItems: "center"
  // },
  imageButton: {
    width: wp("24%"),
    height: wp("24%"),
    borderRadius: wp("12%"),
    backgroundColor: "#F5F5F5",
    borderWidth: 1,
    borderColor: "#00000029",
  },
  placeholder: {
    width: wp("15%"),
    alignSelf: "center",
  },
  closecontainer: {
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("21%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    marginRight: wp("20%"),
    //outlineProvider: 'bounds'
  },
  detailsHeadingStyle: {
    width: wp("80%"),
    color: Colors.descriptionField,
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratBold,
    marginTop: 20,
  },
  detailsTextStyle: {
    width: wp("90%"),
    color: Colors.black,
    fontSize: wp("3.5%"),
    marginLeft: 2,
    marginTop: 8,
    fontFamily: Fonts.montserratMedium,
  },
  detailsHeadingRowStyle: {
    width: wp("40%"),
    color: Colors.descriptionField,
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratBold,
    marginTop: 20,
  },
  detailsTextRowStyle: {
    width: wp("40%"),
    color: Colors.black,
    fontSize: wp("3.5%"),
    marginLeft: 2,
    marginTop: 8,
    fontFamily: Fonts.montserratMedium,
  },
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 3,
    marginTop: 5,
    alignSelf: "center",
  },
  renderAttachementContainer: {
    flex: 1,
    height: Platform.OS == "ios" ? hp('65%') : hp('73%')
  },
  nameContainer: {
    minHeight: hp("5%"),
    width: wp("95%"),
    alignSelf: "center",
    flexDirection: "row",
  },
  detailContainer: {
    width: wp("53%"),
    marginLeft: 20,
    justifyContent: "center",
  },
  imagePlaceholder: {
    width: wp("15%"),
    height: wp("15%"),
    borderRadius: wp("1%"),
    marginLeft: wp("6%"),
  },
  nameText: {
    color: "#292529",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratSemiBold,
    marginTop: 10,
  },
  companyText: {
    color: "#A8B2B9",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle1: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    marginRight: wp("10%"),
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    //outlineProvider: 'bounds'
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  dotMenu: {
    marginTop: hp("2%"),
  },
  customheaderDropdownStyle: {
    justifyContent: "flex-end",
    alignItems: "flex-end",
    height: 40,
    width: wp("30%"),
  },
  historyDetailContainer: {
    width: wp("90%"),
    marginLeft: 20,
    justifyContent: "center",
  },
  historyNameText: {
    color: "#5B5B5B",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  historyDateText: {
    color: "#A8B2B9",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  historyCompanyText: {
    color: "#5B5B5B",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratSemiBold,
    marginTop: hp("1%"),
    width: wp("90%"),
    textAlign: "left",
  },
  historyUserText: {
    marginLeft: 20,
    color: "#5B5B5B",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratSemiBold,
  },
  commentsTextBox: {
    color: "#5B5B5B",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
    marginLeft: hp("2%")
    // marginTop: 20,
  },
  bottomContainer: {
    position: "absolute",
    width: wp("100%"),
    borderTopWidth: 0.5,
    shadowOpacity: 1,
    elevation: 200,
    //shadowColor: "rgba(0,0,0,0.14)",
    backgroundColor: "#fff",
    shadowColor: Colors.placeholder,
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    height: hp("10%"),
    bottom: 0,
  },
  bottomView: {
    width: wp("30%"),
    justifyContent: "center",
    alignItems: "center",
    height: hp("8%"),
    marginHorizontal: 20,
  },
  footerAction: { justifyContent: "center", alignItems: "center" },
  footerActionText: {
    fontFamily: Fonts.montserratRegular,
    marginTop: 5,
  },
  responsibleContainer: {
    flexDirection: 'row',
    width: '110%'
  },
  responsibleFlatlistContainer: {
    marginTop: 5
  },
  avatarContainer: {
    backgroundColor: 'grey',
    marginTop: 5,
  },
  textInputStatus: {
    fontFamily: Fonts.montserratMedium,
    color: Colors.black,
    fontSize: wp("3.5%"),
  },
  textTitleStatus: {
    fontFamily: Fonts.montserratBold,
    fontSize: wp("3.5%"),
    color: Colors.descriptionField
  },
  flatlistStyleContainer: {
    height: hp("55%")
  },
  nameWidthContainer: {
    width: wp("95%")
  },
  editDropdownStyle: {
    textAlign: "center",
  },
  containerStyles: {
    bottom: 0
  },
  descStyles: {
    fontSize: wp("4.5%"),
    marginRight: wp("5%"),
  },
  textStyle: {
    fontSize: wp("4%")
  },
});