import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  Platform,
  PermissionsAndroid,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from "react-native";
import AsyncStorage from '@react-native-async-storage/async-storage';

import {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  editData,
  clickAdd,
  onTapSearch,
  updateList,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import {
  GET_DEFINABLE,
  DELETE_DFOW,
  IMPORT_DFOW,
  EXPORT_DFOW,
  BASE_URL,
  SAMPLE_DFOW,
} from "../../api/Constants";
import { getDefinableList, deleteDfow, importDfow } from "../../api/Api";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ie<PERSON>,
  <PERSON><PERSON>,
  Toastpopup,
  Dropdown,
  DeletePop,
} from "../../components";

import { Colors, Images, Strings, Fonts } from "../../common";


import ReactNativeBlobUtil from 'react-native-blob-util'

import DocumentPicker from "react-native-document-picker";

import Share from "react-native-share";
import { trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import DeleteError from "../../components/DeleteError/DeleteError";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";

var RNFS = require("react-native-fs");


var ID = function () {
  // Math.random should be unique because of its seeding algorithm.
  // Convert it to base 36 (numbers + letters), and grab the first 9 characters
  // after the decimal.
  return "_" + Math.random().toString(36).substr(2, 9);
};

class DFOW extends Component {
  constructor(props) {
    super(props);
    this.state = {
      memberslist: [],
      showLoader: false,
      showToaster: false,
      showAlert: false,
      toastMessage: "",
      toastType: "error",
      refreshing: false,
      lastId: 0,
      selectedAll: false,
      selectedGates: [],
      showNoData: false,
      sort: "ASC",
      showDelete: false,
      downloadOption: [{ name: "Download" }, { name: "Share" }],
      downloadModal: false,
      showImportModal: false,
      isNetworkCheck: false,
      importOption: [
        { name: "Import Definable Feature of Work List", id: 1 },
        { name: "Download Sample Template File", id: 2 },
      ],
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      },
      showError:false,
      errorMessage:'',
    };

    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.requestCameraPermission();
      this.renderInitial();
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.requestCameraPermission();
      this.renderInitial();
    }
    })
  }

  async fileshare(path) {
    /*   const shareOptions = {
      title: "Share file",
      failOnCancel: false,
      urls: [path],
    }; */

    try {

    } catch (error) {
     
    }
  }

  onPressImportOption(item) {
    this.setState({ showImportModal: false });
    if (item.id == 1) {
      // import DFOW File
      this.importFile();
    } else if (item.id == 2) {
      //download sample file
      this.downloadSample();
    }
  }

  downloadSample = async (items) => {
    let token = await AsyncStorage.getItem("AccessToken");

    var fileName =
      "Sample_Export_" + Math.round(new Date().getTime() / 1000) + ".xlsx";

    let url = `${BASE_URL}${SAMPLE_DFOW}${this.props.projectDetails.id}`;
    const androidpath = `${RNFS.DownloadDirectoryPath}/${fileName}`;
    const iospath = `${RNFS.DocumentDirectoryPath}/${fileName}`;
    ReactNativeBlobUtil.config({
      path: Platform.OS == "android" ? androidpath : iospath,
    })
      .fetch("POST", url, {
        Authorization: "JWT " + token,
        "Content-Type": "application/json",
      })
      .then((resp) => {

        if (Platform.OS == "android") {
          this.setState(
            {
              showToaster: true,
              toastMessage: `${Strings.popup.downloadStatus} at ${androidpath}`,
              toastType: "success",
            },
            () => {
              setTimeout(() => {
                this.setState({ showToaster: false });
              }, 2000);
            }
          );
        } else {
          Share.open({
            type: "xlsx",
            urls: [iospath],
            saveToFiles: true,
          });
        }
      })
      .catch();
  };

  onPressOption = async (items) => {
    this.setState({ downloadModal: false });

    let token = await AsyncStorage.getItem("AccessToken");

    var fileName =
      "DFOW_Export_" + Math.round(new Date().getTime() / 1000) + ".xlsx";

    let url = `${BASE_URL}${EXPORT_DFOW}${this.props.projectDetails.id}/${this.state.sort}/${this.props.projectDetails.ParentCompany.id}`;
    const androidpath = `${RNFS.DownloadDirectoryPath}/${fileName}`;
    const iospath = `${RNFS.DocumentDirectoryPath}/${fileName}`;
    ReactNativeBlobUtil.config({
      path: Platform.OS == "android" ? androidpath : iospath,
    })
      .fetch("GET", url, {
        Authorization: "JWT " + token,
        "Content-Type": "application/json",
      })
      .then((resp) => {
        //       this.setState({showLoader:false})
        if (Platform.OS == "android") {
          if (items.name == this.state.downloadOption[1].name) {
            this.fileshare("file:///" + androidpath);
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: `${Strings.popup.downloadStatus} at ${androidpath}`,
                toastType: "success",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          }
        } else {
          if (items.name == this.state.downloadOption[1].name) {
            this.fileshare(iospath);
          } else {
            Share.open({
              type: "xlsx",
              urls: [iospath],
              saveToFiles: true,
            });
          }
        }
      })
      .catch((err) => {
        //     this.setState({showLoader:false})
      });
  };

  exportDfow = async () => {
    this.setState({ downloadModal: true });
  };

  requestCameraPermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: "Cool Photo App Camera Permission",
          message:
            "Cool Photo App needs access to your camera " +
            "so you can take awesome pictures.",
          buttonNeutral: "Ask Me Later",
          buttonNegative: "Cancel",
          buttonPositive: "OK",
        }
      );
    } catch (err) {
    }
  };

  renderInitial = () => {
    this.setState({ showLoader: true });
    this.page_number = 1;
    this.getCompanyList(0);
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
    
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }

    if (nextProps.updatelist == true) {
      this.setState({
        showLoader: true,
      });
      this.page_number = 1;
      this.renderInitial();
      this.props.updateList(false);
    }
    if (this.props.projectSwitched != nextProps.projectSwitched) {
      this.renderInitial();
    }
  }

  getCompanyList = async (data) => {
    this.setState({ showNoData: false });
    let param = {
      search: "",
      ParentCompanyId: this.props.projectDetails.ParentCompany.id,
    };

    let url = `${GET_DEFINABLE}${this.props.projectDetails.id}/20/${this.page_number}/${this.state.sort}/${data}/DFOW`;
    await getDefinableList(
      url,
      param,
      () => {},
      (response) => {
        this.setState({ showLoader: false });

        if (response.status) {
          if (response.status == 200 || response.status == 201) {
            if (data == 1) {
              this.exportcheck(response.data.data.rows);
            } else {
              this.setState({ lastId: response.data.lastId.id });
              let data = this.state.memberslist;
              this.props.storeLastid(response.data.lastId.id);
              if (response.data.data.count == 0) {
                this.setState({
                  showNoData: this.page_number == 1 ? true : false,
                });
                if (this.page_number == 1) {
                  this.setState({
                    memberslist: [],
                  });
                }
              } else if (this.page_number == 1) {
                let gateresp = response.data.data.rows;
                for (let i = 0; i < response.data.data.rows.length; i++) {
                  gateresp[i].selected = false;
                }
                this.setState({
                  memberslist: response.data.data.rows,
                  totalCount: response.data.data.count,
                });
              } else {
                let data1 = response.data.data.rows;
                for (let i = 0; i < data1.length; i++) {
                  data1[i].selected = false;
                }
                this.setState({
                  memberslist: data.concat(data1),
                  totalCount: response.data.data.count,
                });
              }
            }
          } else if (response.data.message == Strings.errors.noData) {
            if (this.page_number == 1) {
              this.setState({
                memberslist: [],
              });
            }
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({ showToaster: false });
              }, 2000);
            }
          );
        }
      }
    );
  };

  renderEmail = (title, name) => {
    return (
      <View style={styles.emailContainer}>
        <Text style={styles.emailTitle}>{title}</Text>
        <Text style={styles.emailText}>{name}</Text>
      </View>
    );
  };
  onPressEdit=()=>{
    this.editGate(this.state.selectedGates[0])
  }
  editGate = (item) => {
    this.props.editData({
      item: item
    });
    this.props.clickAdd(true);
  };

  deleteApi = (param) => {
    this.setState({ showLoader: true });
    deleteDfow(
      DELETE_DFOW,
      param,
      () => {},
      (response) => {
        this.setState({ showLoader: false });
        
        this.page_number = 1;
        if (response.status) {
          if(response.status==500){
            this.setState(
              {
                errorMessage:response.data.message,
                showLoader: false,
                showError:true,
              });
          }
          else if (response.data.message.details) {
            let array = Object.values(response.data.message.details[0]);

            this.setState(
              {
                showToaster: true,
                toastMessage: array.toString(),
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          } else if (response.data.data.message == "Deleted Successfully.") {
            this.setState(
              { showLoader: false },
              this.setState(
                {
                  showToaster: true,
                  toastMessage: "Deleted Successfully.",
                  toastType: "success",
                },
                () => {
                  setTimeout(() => {
                    this.setState({ showToaster: false });
                  }, 2000);
                }
              )
            );
            this.getCompanyList(0);
            trackEvent('Deleted_Definable_Feature_Of_Work')
            mixPanelTrackEvent('Deleted Definable Feature Of Work',this.state.mixpanelParam)
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "success",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
            this.getCompanyList(0);
          }
          // if(response.data.)
          // this.setState({showToaster: true})
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({ showToaster: false });
              }, 2000);
            }
          );
        }
      }
    );

    this.setState({ selectedGates: [], selectedAll: false, showLoader: false });
  };

  selectGate = (item, index) => {

    let data = this.state.memberslist;
    let selectedGate = [];
    for (let i = 0; i < this.state.memberslist.length; i++) {
      if (index == i) {
        data[i].selected = !data[i].selected;
      } /* else {
        data[i].selected = data[i].selected;
      } */

      if (data[i].selected == true) {
        selectedGate.push(data[i]);
      }
    }
    this.setState(
      { memberslist: data, selectedGates: selectedGate, selectedAll: false }
    );
  };

  checkAll = () => {
    let data = this.state.memberslist;
    this.setState(
      {
        selectedAll: !this.state.selectedAll,
        showDeleteicon: !this.state.selectedAll,
      },
      () => {
        for (let i = 0; i < this.state.memberslist.length; i++) {
          if (this.state.selectedAll == true) {
            data[i].selected = true;
          } else {
            data[i].selected = false;
          }
        }
        this.setState({
          memberslist: data,
          selectedGates: this.state.selectedAll == true ? data : [],
        });
      }
    );
  };

  
  renderFlatListItem = ({ item, index }) => {

    return (
      <View>
        <View style={styles.flHeader}>
          <View style={styles.checkbox}>
            <TouchableWithoutFeedback
              onPress={() => {
                this.selectGate(item, index);
              }}
            >
              <Image
                resizeMode={"contain"}
                source={item.selected == true ? Images.check : Images.uncheck}
                style={{ width: wp("9%"), height: hp("4%") }}
              />
            </TouchableWithoutFeedback>
          </View>

          <View style={[styles.checkbox]}>
            <Text style={styles.flatlistTitle}>{item.autoId}</Text>
          </View>

          <View style={{ width: wp("40%") }}>
            <Text style={styles.flatlistTitle} numberOfLines={2}>
              {item.Specification}
            </Text>
          </View>

          <View style={{ width: wp("30%") }}>
            <Text style={styles.flatlistTitle} numberOfLines={3}>
              {item.DFOW}
            </Text>
          </View>

          {/*   <View
            style={[
              styles.checkbox,
              {
                width: wp("18%"),
                alignItems: "center",
                flexDirection: "row",
                justifyContent: "space-around",
              },
            ]}
          >

            <TouchableWithoutFeedback
              onPress={() => this.editGate(item, index)}
            >
              <Image
                resizeMode={"contain"}
                source={Images.edit}
                style={{ width: wp("7%") }}
              />
            </TouchableWithoutFeedback>

            <TouchableWithoutFeedback
              onPress={() =>
                this.setState({
                  showDelete: true,
                  selectedGate: item,
                  selectedIndex: index,
                })
              }
            >
              <Image
                resizeMode={"contain"}
                source={Images.delete}
                style={{ width: wp("7%") }}
              />
            </TouchableWithoutFeedback>
          </View> */}
        </View>

        <View
          style={{
            backgroundColor: "#EFEFEF",
            height: hp("0.3%"),
            width: wp("96%"),
            alignSelf: "center",
          }}
        />
      </View>
    );
  };

  onPressDelete = () => {
    let data = this.state.selectedGates;
    let id = [];
    for (let i = 0; i < data.length; i++) {
      id.push(data[i].id);
    }
    let param = {
      deleteData: id,
      ProjectId: this.props.projectDetails.id,
      isSelectAll: this.state.selectedAll,
    };
    if (this.state.selectedGates.length > 0) {
      this.deleteApi(param);
    }
    this.setState({ selectedGates: [] });
  };

  deleteGate = (item, index) => {
    // data.splice(index, 1)

    let param = {
      deleteData: [item.id],
      ProjectId: this.props.projectDetails.id,
      isSelectAll: false,
    };
    this.deleteApi(param);
  };

  saveFile = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        // RNFetchBlob.fs.dirs.DownloadDir it's getting the download folder from internal storage
        let filePath = ReactNativeBlobUtil.fs.dirs.DownloadDir + "/testPDF.pdf";

        ReactNativeBlobUtil.fs
          .writeFile(filePath, "foo", "base64")
          .then()
          .catch();
       }
       //else {
      //   let filePath = RNFetchBlob.fs.dirs.DownloadDir + "/testPDF.pdf";

      //   RNFetchBlob.fs
      //     .writeFile(filePath, "foo", "base64")
      //     .then()
      //     .catch();
      // }
    } catch (err) {
    }
  };

  exportcheck = (data) => {
    if (data.length == 0) {
      this.setState(
        {
          showToaster: true,
          toastType: "error",
          toastMessage: "No DFOW to export",
        },
        () => {
          setTimeout(() => {
            this.setState({
              showToaster: false,
            });
          }, 2000);
        }
      );
    }
  };

  importFile = async () => {
    try {
      const results = await DocumentPicker.pick({
        type: [DocumentPicker.types.allFiles],
      });


      let extCheck = results.name.split(".");

      if (
        extCheck[1] === "xls" ||
        extCheck[1] == "xlsx" ||
        extCheck[1] == "csv"
      ) {
       
        let formData = new FormData();
        formData.append("definable", results, results.name);
        this.setState({
          showLoader: true,
        });

        importDfow(
          `${IMPORT_DFOW}${this.props.projectDetails.id}/${this.props.projectDetails.ParentCompany.id}`,
          formData,
          () => {},
          (response) => {
            // show loader
            this.setState({ showLoader: false });
            if (response.toString() == Strings.errors.timeout) {
              this.setState(
                {
                  showLoader: false,
                  showToaster: true,
                  toastType: "error",
                  toastMessage: Strings.errors.checkInternet,
                },
                () => {
                  setTimeout(() => {
                    this.setState({
                      showToaster: false,
                      showLoader: false,
                    });
                  }, 2000);
                }
              );
            }

            if (response.status) {
              if (
                response.data.message ==
                "Definable Feature of Work Created Successfully."
              ) {
                this.setState(
                  {
                    showLoader: false,
                    showToaster: true,
                    toastType: "error",
                    toastMessage:
                      "Definable Feature of Work Created Successfully.",
                  },
                  () => {
                    setTimeout(() => {
                      this.setState({
                        showToaster: false,
                        showLoader: false,
                      });
                    }, 2000);
                  }
                );
                this.renderInitial();
              } else {
                this.setState(
                  {
                    showLoader: false,
                    showToaster: true,
                    toastType: "error",
                    toastMessage: response.data.message,
                  },
                  () => {
                    setTimeout(() => {
                      this.setState({
                        showToaster: false,
                        showLoader: false,
                      });
                    }, 2000);
                  }
                );
              }
            } else {
              this.setState(
                {
                  showLoader: false,
                  showToaster: true,
                  toastType: "error",
                  toastMessage: response.data.message,
                },
                () => {
                  setTimeout(() => {
                    this.setState({
                      showToaster: false,
                      showLoader: false,
                    });
                  }, 2000);
                }
              );
            }
            // this.renderInitial()
          }
        );

        // name="definable"; filename="sample_export_1604854593997.xlsx"
      } else {
        this.setState(
          {
            showLoader: false,
            showToaster: true,
            toastType: "error",
            toastMessage:
              "Please select valid document, Support file formats (.xlsx, .xls, .csv)",
          },
          () => {
            setTimeout(() => {
              this.setState({
                showToaster: false,
              });
            }, 2000);
          }
        );
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker, exit any dialogs or menus and move on
      } else {
        this.setState(
          {
            showToaster: true,
            toastType: "error",
            toastMessage: err.toString(),
          },
          () => {
            setTimeout(() => {
              this.setState({
                showToaster: false,
              });
            }, 2000);
          }
        );
      }
    }
  };

  renderHeader() {
    let showButton = false;
    let showEdit=false;
    if (this.state.selectedGates.length > 0 || this.state.selectedAll == true) {
      showButton = true;
    } 
    if(this.state.selectedGates.length==1){
      showEdit=true;
    }
    /* else {
      showButton = false;
    } */

    return (
      <View style={styles.headerContainer}>
        <View style={styles.headerview}>
          <Text style={styles.title}>{Strings.menu.dfow}</Text>
          <View style={{ flexDirection: "row" }}>
            {showButton == true && (
              <View style={{flexDirection:'row',}}>
                {showEdit== true &&
                <TouchableOpacity
                onPress={this.onPressEdit}
              >
                <Image   resizeMode={"contain"}
                source={Images.edit2}
                style={{ width: wp("7%") }} />
              </TouchableOpacity>
          }
            
              <TouchableOpacity
                onPress={() => {
                  this.setState({ showAllDelete: true });
                }}
                style={[styles.image]}
              >
                <Image source={Images.deleteBin} />
              </TouchableOpacity>
              </View>
            )}

            <TouchableOpacity
              style={styles.image}
              onPress={() =>
                this.setState(
                  {
                    sort: this.state.sort == "ASC" ? "DESC" : "ASC",
                  },
                  () => {
                    this.renderInitial();
                  }
                )
              }
            >
              <Image source={Images.sort} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.image}
              onPress={() => this.props.onTapSearch("dfowSearch")}
            >
             <Image source={Images.Search1} style={{height:16,width:16,}}/>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  onEndReached = () => {
    if (this.state.memberslist.length < this.state.totalCount) {
      this.page_number = this.page_number + 1;
      this.getCompanyList(0);
      this.onEndReachedCalledDuringMomentum = true;
    }
  };

  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        memberslist: [],
        totalCount: 0,
        showLoader: true,
        //load: true,
      },
      () => {
        this.getCompanyList(0);
      }
    );
  };

  renderFlatlistHeader = () => {
    if (this.state.showNoData == true || this.state.memberslist.length == 0) {
      return null;
    } else {
      return (
        <View style={{}}>
          <View style={styles.flHeaderTitle}>
            <View style={styles.checkbox}>
              <TouchableWithoutFeedback
                onPress={() => {
                  this.checkAll();
                }}
              >
                <Image
                  resizeMode={"contain"}
                  source={
                    this.state.selectedAll ? Images.check : Images.uncheck
                  }
                  style={{ width: wp("9%"), height: hp("4%") }}
                />
              </TouchableWithoutFeedback>
            </View>
            <View style={[styles.checkbox]}>
              <Text
                style={[styles.flatlistHeaderTitle, { fontWeight: "bold" }]}
              >
                {Strings.gates.id}
              </Text>
            </View>
            <View style={[styles.checkbox,{width: wp("40%") }]}>
              <Text
                style={[styles.flatlistHeaderTitle, { fontWeight: "bold" }]}
              >
                {Strings.menu.spec}
              </Text>
            </View>
            <View style={[styles.checkbox, { width: wp("30%") }]}>
              <Text
                style={[styles.flatlistHeaderTitle, { fontWeight: "bold" }]}
              >
                {Strings.menu.dfow}
              </Text>
            </View>
            {/*  <View
              style={[
                styles.checkbox,
                {
                  width: wp("18%"),
                  justifyContent: "center",
                  alignItems: "center",
                },
              ]}
            >
              <Text
                style={[styles.flatlistHeaderTitle, { fontWeight: "bold" }]}
              >
                {Strings.gates.action}
              </Text>
            </View> */}
          </View>

          <View
            style={{
              backgroundColor: "#EFEFEF",
              height: hp("0.3%"),
              width: wp("96%"),
              alignSelf: "center",
            }}
          />
        </View>
      );
    }
  };

  renderNoDFOW = () => {
    return (
      this.state.showNoData == true && (
        <Text style={styles.noDFOW}>No DFOW Found</Text>
      )
    );
  };

  deletePopupAcceptTap = () => {
    this.setState({ showDelete: false });
    this.deleteGate(this.state.selectedGate, this.state.selectedIndex);
  };

  deletePopupDeclineTap = () => {
    this.setState({
      showDelete: false,
      selectedGate: [],
      selectedIndex: null,
    });
  };

  deleteAllPopupAcceptTap = () => {
    this.setState({ showAllDelete: false });
    this.onPressDelete();
  };

  deleteAllPopupDeclineTap = () => {
    this.setState({
      showAllDelete: false,
      selectedGate: [],
      selectedIndex: null,
    });
  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <AppView ref={this.props.refer} style={styles.safeArea}>
        <View style={styles.parentContainer}>
          {this.renderHeader()}

          <FlatList
            data={this.state.memberslist}
            renderItem={this.renderFlatListItem}
            ListHeaderComponent={this.renderFlatlistHeader()}
            // ItemSeparatorComponent={this.itemSeparator}
            keyExtractor={(item, index) => index.toString()}
            onEndReached={() => this.onEndReached()}
            onEndReachedThreshold={0}
            onMomentumScrollBegin={() => {
              this.onEndReachedCalledDuringMomentum = false;
            }}
            onRefresh={() => this._onReset()}
            refreshing={this.state.refreshing}
          />

          {this.renderNoDFOW()}
        </View>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.forgotSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}

        <Dropdown
          data={this.state.downloadOption}
          title={Strings.profile.Choose}
          value={""}
          closeBtn={() => this.setState({ downloadModal: false })}
          onPress={(item) => this.onPressOption(item)}
          visible={this.state.downloadModal}
          onbackPress={() => this.setState({ downloadModal: false })}
          container={{ justifyContent: "center", alignItems: "center" }}
          textContainer={{ fontSize: 14 }}
        />

        <Dropdown
          data={this.state.importOption}
          title={Strings.profile.Choose}
          value={""}
          closeBtn={() => this.setState({ showImportModal: false })}
          onPress={(item) => this.onPressImportOption(item)}
          visible={this.state.showImportModal}
          onbackPress={() => this.setState({ showImportModal: false })}
          container={{ justifyContent: "center", alignItems: "center" }}
          textContainer={{ fontSize: 14 }}
        />
        {this.state.showError &&(  <DeleteError message={this.state.errorMessage} close={()=>this.setState({showError:false})}/>)}
        {this.state.showDelete && (
          <DeletePop
            container={{ bottom: hp("5%") }}
            title={Strings.popup.success}
            desc={Strings.popup.delete}
            acceptTap={this.deletePopupAcceptTap}
            declineTap={this.deletePopupDeclineTap}
          />
        )}

        {this.state.showAllDelete && (
          <DeletePop
            container={{ bottom: Platform.OS=='android'?hp('5'): hp("15%") }}
            title={Strings.popup.success}
            desc={Strings.popup.delete}
            acceptTap={this.deleteAllPopupAcceptTap}
            declineTap={this.deleteAllPopupDeclineTap}
          />
        )}
      </AppView>
      }
      </>
    );
  }
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
    height: hp("50%"),
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  headerContainer: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderBottomWidth: 0.3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  title: {
    flex: 1,
    color: Colors.black,
    fontSize: 22,
    fontFamily: Fonts.montserratBold,
  },
  headerRowContainer: {
    height: hp("15%"),
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  image: {
    marginLeft: 8,
    width: wp("5%"),
    height: hp("2%"),
  },
  flatlistContainer: {
    width: wp("85%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  nameContainer: {
    height: hp("14%"),
    width: wp("85%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  detailContainer: {
    width: wp("58%"),
    marginLeft: 20,
    justifyContent: "center",
    backgroundColor: Colors.white,
  },
  imagePlaceholder: {
    width: wp("14%"),
    height: wp("14%"),
    borderRadius: wp("7%"),
    marginLeft: wp("6%"),
  },
  nameText: {
    color: Colors.black,
    fontSize: wp("5%"),
    fontFamily: Fonts.montserratSemiBold,
  },
  companyText: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  dotMenu: {
    marginTop: hp("2%"),
  },
  emailContainer: {
    flex: 1,
    marginLeft: 10,
    marginRight: 10,
  },
  emailTitle: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    margin: 5,
  },
  emailText: {
    color: "#1E1E1E",
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginTop: 0,
  },
  flHeader: {
    flexDirection: "row",
    alignItems: "center",
    // alignSelf: "center",
   /*  marginTop: 2,
    marginBottom: 2, */
    //////modified
    marginLeft: 10,
    marginTop: 10,
    marginBottom: 10,

  },
  flHeaderTitle: {
    flexDirection: "row",
    alignItems: "center",
    // alignSelf: "center",
   /*  paddingTop: 4,
    paddingBottom: 4, */
    ////modified
    //justifyContent:'space-evenly',

    marginLeft: 10,
    marginTop: 10,
    marginBottom: 10,
    
  },
  checkbox: {
    width: wp("13%"),
    justifyContent: "center",
  },
  flatlistTitle: {
    color: Colors.planDesc,
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
  },
  flatlistHeaderTitle: {
    color: Colors.planCost,
    fontSize: 14,
    fontFamily: Fonts.montserratBold,
  },
  noDFOW: {
    alignSelf: "center",
    position: "absolute",
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("45%"),
  },
  headerview: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    alignContent: "center",
    marginLeft: 20,
    marginRight: 20,
    marginBottom: 20,
  },
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    updatelist,
    projectSwitched,
    userDetails,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    updatelist,
    projectSwitched,
    userDetails,
  };
};

export default connect(mapStateToProps, {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  editData,
  clickAdd,
  onTapSearch,
  updateList,
})(DFOW);
