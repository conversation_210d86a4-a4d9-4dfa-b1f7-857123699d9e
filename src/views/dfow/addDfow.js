import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Platform
} from "react-native";

import {
  changeTab,
  showSideMenu,
  cameBack,
  editData,
  updateList,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import { AppView, AppLoader, Toastpopup, Alert } from "../../components";

import DeletePop from "../../components/toastpopup/logoutPop";
import { TextField } from "../../components/textinput/addMemberTextinput";
import { trackScreen ,trackEvent} from "../../Google Analytics/GoogleAnalytics";
import {
  Images,
  Strings,
  Fonts,
  Colors,
  isAlphaNumeric,
  isEmpty,
} from "../../common";

import { UPDATE_DFOW } from "../../api/Constants";
import { addDfow } from "../../api/Api";

import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import NetInfo from '@react-native-community/netinfo';
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";
import NoInternet from "../../components/NoInternet/noInternet";

class AddDFOW extends Component {
  constructor(props) {
    super(props);
    this.state = {
      memberId: this.props.lastid,
      update: false,
      gateName: "",
      updateId: 0,
      showToaster: false,
      specification: "",
      showCancel: false,
      comparision: [],
      isNetworkCheck: false,
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      },
    };
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      if (this.props.editedData.item) {
        trackScreen('Edit Definable Fetaures of Work')
        this.setState(
          {
            memberId: this.props.editedData.item.autoId,
            update: true,
            gateName: this.props.editedData.item.DFOW,
            updateId: this.props.editedData.item.id,
            specification: this.props.editedData.item.Specification,
            comparision: this.props.editedData.item,
          },
          () => {
            this.props.editData({});
          }
        );
      } else {
        trackScreen('Add Definable Fetaures of Work')
        this.setState({
          memberId: this.props.lastid,
        });
      }
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      if (this.props.editedData.item) {
        trackScreen('Edit Definable Fetaures of Work')
        this.setState(
          {
            memberId: this.props.editedData.item.autoId,
            update: true,
            gateName: this.props.editedData.item.DFOW,
            updateId: this.props.editedData.item.id,
            specification: this.props.editedData.item.Specification,
            comparision: this.props.editedData.item,
          },
          () => {
            this.props.editData({});
          }
        );
      } else {
        trackScreen('Add Definable Fetaures of Work')
        this.setState({
          memberId: this.props.lastid,
        });
      }
    }
    })
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }
  }

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  onPressTab = (item, data) => {};

  //HEADER COMPONENT
  renderHeader() {
    return (
      <View style={styles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.props.cameBack(true);
            // this.props.navigation.navigate('Plus');
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={styles.title}>{this.state.update?Strings.addDFOW.edit:Strings.addDFOW.add}</Text>
      </View>
    );
  }

  renderImage() {
    return (
      <View style={styles.imageContainer}>
        <TouchableWithoutFeedback
          onPress={() => {
            // alert('coming soon')
          }}
        >
          <View style={styles.imageButton}>
            <Image
              resizeMode={"center"}
              source={Images.placeholder}
              style={styles.imageButton}
            />
            <View style={styles.camera}>
              <Image source={Images.camera} />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  submit = async () => {
    if (isEmpty(this.state.specification.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptySpecification,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({
              showToaster: false,
            });
          }, 2000);
        }
      );
    } else if (isEmpty(this.state.gateName.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyDfow,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({
              showToaster: false,
            });
          }, 2000);
        }
      );
    } else if (this.state.gateName.length < 3) {
      this.showError("error", "Definable name " + Strings.errors.lengthError);
    } else if (isAlphaNumeric(this.state.gateName)) {
      this.showError("error", Strings.errors.validDfow);
    } else {
      this.setState({ showLoader: true });
      let data = {};

      let url = "";

      if (this.state.update) {
        data = {
          editData: [
            {
              id: this.state.updateId,
              DFOW: this.state.gateName,
              Specification: this.state.specification,
            },
          ],
        };

        url = UPDATE_DFOW + this.props.projectDetails.id;
      }
      //In the backend add_definable is not updated.
      else {
        data = {
          editData: [
            {
              DFOW: this.state.gateName,
              Specification: this.state.specification,
            },
          ],
          //ProjectId: this.props.projectDetails.id,
        };
        url = UPDATE_DFOW + this.props.projectDetails.id;
      }

      await addDfow(
        url,
        data,
        () => {},
        (resp) => {
          this.setState({ showLoader: false });

          if (resp.status) {
            if (resp.status == 200) {
              if (this.state.update) {
                this.setState(
                  {
                    showToaster: true,
                    toastMessage: "DFOW Updated Successfully",
                    toastType: "success",
                    gateName: "",
                  },
                  () => {
                    setTimeout(() => {
                      this.setState({ showToaster: false });
                      this.props.cameBack(true);
                                              if (this.props.route.params?.from == "search") {
                        this.props.route.params.updateData("data");
                      }
                      this.props.updateList(true);
                      this.props.navigation.goBack();
                    }, 500);
                  }
                );
                trackEvent('Edited_Definable_Feature_Of_Work')
                mixPanelTrackEvent('Edited Definable Feature Of Work',this.state.mixpanelParam)
              } else {
                this.setState(
                  {
                    showToaster: true,
                    toastMessage: "DFOW Added Successfully",
                    toastType: "success",
                    gateName: "",
                  },
                  () => {
                    setTimeout(() => {
                      this.setState({ showToaster: false });
                      this.props.cameBack(true);
                      this.props.updateList(true);
                      this.props.navigation.goBack();
                    }, 500);
                  }
                );
                trackEvent('Added_Definable_Feature_Of_Work')
                mixPanelTrackEvent('Added Definable Feature Of Work',this.state.mixpanelParam)
              }
            } else if (resp.data.message.message) {
              let array = Object.values(resp.data.message.details[0]);

              this.setState(
                {
                  showToaster: true,
                  toastMessage: array.toString(),
                  toastType: "error",
                },
                () => {
                  setTimeout(() => {
                    this.setState({
                      showToaster: false,
                    });
                  }, 2000);
                }
              );
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: resp.data.message,
                  toastType: "error",
                },
                () => {
                  setTimeout(() => {
                    this.setState({ showToaster: false });
                  }, 2000);
                }
              );
            }
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: resp.toString(),
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
        }
      );
    }
  };

  showError = (type, message) => {
    this.setState(
      { showToaster: true, toastType: type, toastMessage: message },
      () => {
        setTimeout(() => {
          this.setState({ showToaster: false });
        }, 2000);
      }
    );
  };

  onPressCancel = () => {
    if (this.state.update) {
    let data = this.state.comparision;
    let gateName = data.DFOW;
    let specification = data.Specification;
      if (
        gateName == this.state.gateName &&
        specification == this.state.specification
      ) {
        this.props.cameBack(true);
        this.props.navigation.goBack();
      } else {
        this.setState({ showCancel: true });
      }
    } else {
      this.props.cameBack(true);
      this.props.navigation.goBack();
    }
  };
  bottomContainer = () => {
    return (
      <View style={styles.bottomContainer}>
        <TouchableOpacity onPress={this.onPressCancel}>
          <View style={styles.cancel}>
            <Text style={styles.cancelText}>{Strings.addMember.cancel}</Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            this.submit();
          }}
        >
          <View style={styles.submit}>
            <Text style={styles.submitText}>
              {this.state.update
                ? Strings.addMember.update
                : Strings.addMember.submit}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  renderMemberId() {
    return (
      <View style={styles.memberContainer}>
        <Text style={styles.idTitle}>{Strings.gates.id}</Text>
        <Text style={styles.idText}>{this.state.memberId}</Text>
      </View>
    );
  }

  updateMasterState = (key, value) => {
    this.setState({ gateName: value });
  };
  updateMasterStateSpecification = (key, value) => {
    this.setState({ specification: value });
  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <AppView>
        <View style={styles.parentContainer}>
          {this.renderHeader()}
          {this.renderMemberId()}
          <TextField
            attrName={Strings.menu.specification}
            title={Strings.menu.specification}
            value={this.state.specification}
            maxLength={150}
            updateMasterState={(key, value) => {
              this.updateMasterStateSpecification(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
          />
          <TextField
            attrName={Strings.menu.dfow}
            title={Strings.menu.dfow}
            value={this.state.gateName}
            maxLength={150}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
          />

          {this.bottomContainer()}
        </View>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.loginSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}
        {this.state.showCancel && this.state.update && (
          <DeletePop
            title={Strings.popup.cancel}
            desc={Strings.popup.cancel}
            acceptTap={() => {
              this.setState({ showCancel: false });
              this.props.cameBack(true);
              this.props.navigation.goBack();
            }}
            container={{ bottom: 0 }}
            declineTap={() => {
              this.setState({ showCancel: false });
            }}
          />
        )}
      </AppView>
       }
       </>
    );
  }
}

const styles = StyleSheet.create({
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("80%"),
    fontSize: wp("6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratBold,
  },
  imageContainer: {
    width: wp("100%"),
    height: hp("20%"),
    justifyContent: "center",
    alignItems: "center",
  },
  imageButton: {
    width: wp("24%"),
    height: wp("24%"),
    borderRadius: wp("12%"),
    backgroundColor: "#F5F5F5",
    borderWidth: 1,
    borderColor: "#00000029",
  },
  placeholder: {
    width: wp("15%"),
    alignSelf: "center",
  },
  camera: {
    position: "absolute",
    bottom: 10,
    right: -10,
    width: wp("8%"),
    height: wp("8%"),
    borderRadius: wp("4%"),
    backgroundColor: Colors.white,
    justifyContent: "center",
    alignItems: "center",
  },
  memberContainer: {
    width: wp("90%"),
    height: hp("10%"),
    alignSelf: "center",
    marginTop: hp("2%"),
    justifyContent: "center",
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  idText: {
    width: wp("90%"),
    height: hp("5%"),
    backgroundColor: "#EFEFEF",
    marginTop: wp("1%"),
    padding: hp("1%"),
    paddingLeft: 15,
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("4%"),
  },
  bottomContainer: {
    flex: 1,
    width: wp("90%"),
    flexDirection: "row",
    marginBottom: hp("5%"),
    marginTop: hp("8%"),
    alignSelf: "center",
    alignItems: "flex-end",
    justifyContent: "center",
  },
  cancel: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
    bottom: 0,
  },
  submit: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
    bottom: 0,
  },
  cancelText: {
    color: "#757575",
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    lastid,
    projectDetails,
    editedData,
    updateList,
    userDetails,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    lastid,
    projectDetails,
    editedData,
    updateList,
    userDetails,
  };
};

export default compose(
  connect(mapStateToProps, {
  changeTab,
  showSideMenu,
  cameBack,
  editData,
  updateList,
}),
withBackHandler
)(AddDFOW);
