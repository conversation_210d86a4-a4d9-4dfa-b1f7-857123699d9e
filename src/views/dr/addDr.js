import React, { Component } from "react";
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
  Switch,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  TextInput,
  Button,
} from "react-native";
import {
  changeTab,
  showSideMenu,
  cameBack,
  editData,
  updateList,
  refreshDashboard,
  refreshDeliveryList,
  refreshCalendar,
} from "../../actions/postAction";

import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";
import { TextField } from "../../components/textinput/addMemberTextinput";
import { isEmpty } from "../../common/validators";
import AsyncStorage from "@react-native-async-storage/async-storage";

import {
  GET_NEW_COMPANIES,
  GET_EQUIP_LIST,
  GET_GATE_LIST,
  DEFINABLE_FEATURE,
  SEARCH_MEMBER,
  ADD_DR,
  EDIT_DR,
  GET_SINGLE_NDR,
  GET_LAST_CRANE_ID,
  GET_PROJECT_ROLE,
  LIST_ALL_MEMBER,
  GET_SINGLE_PROJECT,
  GET_TIMEZONE,
  GET_LOCATION_DETAILS,
  GET_PROJECT_SETTING_DETAILS,
  NO_EQUIPMENT_NEEDED,
} from "../../api/Constants";
import {
  getNewCompanyList,
  getEquipList,
  getGateList,
  getDefinableFeature,
  searchMember,
  addNDR,
  getDeliveryDetails,
  getLastCraneRequestId,
  _getData,
  getAllMemberList,
  getTimeZone,
  getProjectSettings,
  getLocationSettings,
} from "../../api/Api";
import Modal from "react-native-modal";
import { Selectize, Chip } from "react-native-material-selectize";
import DateTimePicker from "@react-native-community/datetimepicker";
import moment from "moment";
import MultiSelectDropDown from "../../components/multi-select-dropdown/multiSelectdropDown";
import DeletePop from "../../components/toastpopup/logoutPop";
import AppLoader from "../../components/apploader/AppLoader";
import {
  trackScreen,
  trackEvent,
} from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent } from "../../MixPanel/MixPanel";
import Dropdown from "../../components/dropdown/dropdown";
import { listOfRecurrence, Images, Strings, Fonts, Colors } from "../../common";
import RecurrenceComponent from "../../components/Recurrence/RecurrenceComponent";
import DropDownPicker from "../../components/dropdown/DropDownPicker";
import NetInfo from "@react-native-community/netinfo";
import NoInternet from "../../components/NoInternet/noInternet";
import * as RNLocalize from "react-native-localize";
const API_KEY = "AIzaSyB2EHXZ7yQZ3KItOpshW2F4DJ5ewmagOWU";
import axios from "axios";
import {
  googleAutoCompleteAPI,
  geoCodeAdrressAPI,
} from "../../api/GoogleServices";

let selectedDeliveryDate = new Date();
let selectedStartDate = "";
let selectedEndDate = "";

//Default calender date will be 7th day from current date
let initialCalDate = new Date(
  moment(new Date()).add(7, "days").format("YYYY-MM-DD"),
);

const vehicle_Type = [
  { type: "Medium and Heavy Duty Truck" },
  { type: "Passenger Car" },
  { type: "Light Duty Truck" },
];

const vehicleTypeOptions = vehicle_Type.map((item) => ({
  label: item.type,
  value: item.type,
  name: item.type,
}));

class AddNewDR extends Component {
  handleNavigation = () => {
    const {
      selectedLocationNew,
      selectedLocationId, // Add this to your state if not already there
      selectedEquipmentList,
      equipTypeList,
      selectedGate,
      selectedGateId, // Add this to your state if not already there
      selectedTimeZone, // Add this to your state if not already there
      selectedTimeZoneId, // Add this to your state if not already there
      selectedDate,
      selectedStartTime,
      selectedEndTime,
      projectId,
    } = this.state;

    let errorMessage = "";

    if (!selectedLocationNew) {
      errorMessage = "Please select a location";
    } 
    // else if (!selectedGate) {
    //   errorMessage = "Please select a gate";
    // } else if (
    //   (!selectedEquipmentList || selectedEquipmentList.length === 0) &&
    //   (!equipTypeList || equipTypeList.length === 0)
    // ) {
    //   errorMessage = "Please select at least one equipment";
    // }

    if (errorMessage) {
      Alert.alert("Validation Error", errorMessage);
      return;
    }
    const equipmentSource =
      selectedEquipmentList && selectedEquipmentList.length > 0
        ? selectedEquipmentList
        : equipTypeList;

    const equipmentToSend = Array.isArray(equipmentSource)
      ? equipmentSource.filter((item) => item && item.selected === true)
      : [];

    // if (equipmentToSend.length === 0) {
    //   Alert.alert("Validation Error", "Please select at least one equipment");
    //   return;
    // }

    const navParams = {
      location: selectedLocationNew,
      locationId: selectedLocationId, // Add location ID
      equipment: equipmentToSend,
      gate: selectedGate,
      gateId: selectedGateId, // Add gate ID
      timeZone: selectedTimeZone, // Add time zone
      timeZoneId: selectedTimeZoneId, // Add time zone ID
      bookingId: this.state.bookingId || null,
      projectId: projectId,
      prevParams: this.props.route.params,
      ...(selectedDate && { date: selectedDate.toString() }),
      // Only include times if they exist
      ...(selectedStartTime && { startTime: selectedStartTime.toString() }),
      ...(selectedEndTime && { endTime: selectedEndTime.toString() }),
    };
    this.props.navigation.navigate("timeSlot", navParams);
  };
  constructor(props) {
    super(props);
    this.state = {
      showMultipleSec: false,
      showInfo: false,
      dfowList: [],
      selecteddfow: [],
      selectedItems: [],
      responsiblePersonData: [],
      editDR: false,
      selectedItem: [
        {
          id: `${this.props.responsiblePersonData.name}`,
          email: this.props.userDetails.email,
          userId: this.props.responsiblePersonData.id,
        },
      ],
      selectedItemIndex: 0,
      selectedItemList: [],
      equipTypeList: [],
      selectedEquipName: null,
      selectedResponsibleCompany: null,
      responisbleCompanyList: [],
      gateList: [],
      selectedGate: null,
      vehicleType: null,
      vehicleTypeOptions: vehicleTypeOptions,

      definableList: [],
      selectedDefinableList: null,
      deliveryId: 0,
      description: "",
      escortNeeded: false,
      delVehicleDetails: "",
      additionalNotes: "",
      disableSubmit: false,
      showDateModal: false,
      showStartTimeModal: false,
      showEndTimeModal: false,
      selectedDate: "",
      selectedStartTime: "",
      selectedEndTime: "",
      calSelectedDate: new Date(),
      calSelectedStartTime: new Date(),
      calSelectedEndTime: new Date(),
      minimumDate: new Date(),
      minimumStartTime: new Date(),
      minimumEndTime: new Date(),
      projectId: this.props.projectDetails.id,
      showCancel: false,
      comparision: [],
      bookingId: null,
      isAssociatedWithCraneRequest: false,
      pickId: 0,
      pickFrom: "",
      pickTo: "",
      storeEquipmentList: [],
      mixpanelParam: {
        ProjectName: this.props.projectDetails.projectName,
        CompanyName:
          this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName: this.props.userDetails.firstName,
      },
      status: "",
      roleId: 2,
      isEditDate: true,
      vehicleModelVisible: false,
      eqipModalVisible: false,
      gateModalVisible: false,
      deactiveDateChecker: new Date(),
      isFutureEquipment: false,
      controlledByList: [],
      recurrence: Strings.calendarSettings.doseNotRepeat,
      isRecurrence: false,
      daysList: [
        { key: 1, name: "Sunday", label: "S", selected: true },
        { key: 2, name: "Monday", label: "M", selected: true },
        { key: 3, name: "Tuesday", label: "T", selected: true },
        { key: 4, name: "Wednesday", label: "W", selected: true },
        { key: 5, name: "Thursday", label: "T", selected: true },
        { key: 6, name: "Friday", label: "F", selected: true },
        { key: 7, name: "Saturday", label: "S", selected: true },
      ],
      times: "1",
      isMonthFirstCheck: true,
      isMonthSecondCheck: false,
      isMonthThirdCheck: false,
      isYearFirstCheck: true,
      isYearSecondCheck: false,
      isYearThirdCheck: false,
      monthlyDay: "",
      monthlyLastDay: "",
      endDateRecurrence: moment(new Date()).format("MM/DD/YYYY"),
      selectedDaysOccurs: "",
      selectedDayArray: "",
      listSetName: "",
      yearListSetName: "",
      selectedEndDateYear: new Date(),
      timeZoneList: [],
      selectedTimeZone: "",
      selectedTimeZoneId: 0,
      editedZoneID: "",
      isFromDate: false,
      isNetworkCheck: false,
      deliveryWindowTime: "",
      deliveryWindowTimeUnit: "",
      editCurrentDate: "",
      timeZoneParam: RNLocalize.getTimeZone(),
      editRequestID: "",
      recurrenceSeriesID: "",
      recurrenceEndDateSeries: "",
      recurrenceDeliverStartDate: "",
      recurrenceDeliverEndDate: "",
      recurrenceType: Strings.calendarSettings.doseNotRepeat,
      recurrenceEndDateRes: "",
      deliveryStatus: "",
      selectedLocationId: 0,
      locationDropdownList: [],
      selectedLocationNew: "",
      textFieldValue: "",
      predictionList: [],
      showAutoComplete: false,
      isModalVisible: false,
      selectedAddress: "",
      selectedDay: [],
      showEquipmentGuidance: false,
      editIN: false,
      selectedEquipTypeId: null,
      deliveryStartDate: null,
      deliveryEndDate: null,
      timeSlotExplicitlyChanged: false, // Flag to track if user explicitly changed time via time slot picker
    };
    this.searchMember = this.searchMember.bind(this);
    this.onPressVehicleType = this.onPressVehicleType.bind(this);
  }

  onPressVehicleType(item) {
    this.setState({
      vehicleType: item.value,
      vehicleModelVisible: false,
    });
  }

  componentDidUpdate(prevProps) {
    // Removed automatic loadData call to prevent unnecessary checks on every state update
    // Data loading is now handled by the focus listener
  }

  componentWillUnmount() {
    // Remove the focus listener when component unmounts
    if (this.focusListener) {
      this.focusListener();
    }
  }

  loadData = async () => {
    const back = await AsyncStorage.getItem("Isback");

    try {
      if (back === "true") {
        const savedData = await AsyncStorage.getItem("DRDateTime");

        if (savedData) {
          const { date, fromTime, toTime } = JSON.parse(savedData);

          // Convert loaded strings to Date objects
          const loadedDate = moment(date, "MM/DD/YYYY").toDate();
          const loadedStartTime = this.parseTimeString(fromTime, loadedDate);
          const loadedEndTime = this.parseTimeString(toTime, loadedDate);

          this.setState(
            {
              selectedDate: date,
              calSelectedDate: loadedDate,
              selectedStartTime: fromTime,
              calSelectedStartTime: loadedStartTime,
              selectedEndTime: toTime,
              calSelectedEndTime: loadedEndTime,
            },
            () => { },
          );

          // Update global variables
          selectedDeliveryDate = loadedDate;
          selectedStartDate = loadedStartTime;
          selectedEndDate = loadedEndTime;

          // Set flag to indicate user explicitly changed time via time slot picker
          this.setState({ timeSlotExplicitlyChanged: true });
        }
        await AsyncStorage.setItem("Isback", "false");
      }
    } catch (error) {
      console.error("[LOAD] Error:", error);
    }
  };
  parseTimeString = (timeString, baseDate) => {
    if (!timeString) return new Date(baseDate);

    const timeParts = timeString.split(/:| /);
    let hours = parseInt(timeParts[0]);
    const minutes = parseInt(timeParts[1]);
    const period = timeParts[2].toLowerCase();

    // Convert to 24-hour format
    if (period === "pm" && hours < 12) hours += 12;
    if (period === "am" && hours === 12) hours = 0;

    return new Date(
      baseDate.getFullYear(),
      baseDate.getMonth(),
      baseDate.getDate(),
      hours,
      minutes,
    );
  };

  // Helper function to extract timezone offset from selectedTimeZone string
  getTimezoneOffset = () => {
    const timeZoneString = this.state.selectedTimeZone;
    if (!timeZoneString) return "+00:00";

    // Extract offset from strings like "(UTC-10:00) Hawaii" or "(UTC+05:30) India"
    const offsetMatch = timeZoneString.match(/\(UTC([+-]\d{1,2}:\d{2})\)/);
    if (offsetMatch) {
      return offsetMatch[1];
    }
    return "+00:00";
  };

  // Helper function to format date with timezone offset
  formatDateWithTimezone = (date) => {
    if (!date) return null;

    const offset = this.getTimezoneOffset();

    // Use local date components to avoid timezone conversion issues
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${offset}`;
  };

  getPlacePrediction = async (keyword) => {
    const showPlaces = keyword.toString().length > 0;
    const placeURL = googleAutoCompleteAPI(API_KEY, keyword);
    const placeResponse = await axios.get(placeURL);
    this.setState({
      showAutoComplete: showPlaces,
      predictionList: placeResponse.data.predictions,
    });
  };

  // Method to handle place selection
  onSelectPlace = async (place) => {
    Keyboard.dismiss();
    this.setState({
      showAutoComplete: false,
      textFieldValue: place.description,
      isModalVisible: false, // Close the modal on selection
      selectedAddress: place.description, // Update selected address to display outside modal
    });

    const URL = geoCodeAdrressAPI(place.description, API_KEY);
    const addressAPI = await axios.get(URL);
    const addressCoordinate = {
      latitude: addressAPI.data.results[0].geometry.location.lat,
      longitude: addressAPI.data.results[0].geometry.location.lng,
    };
  };

  // Toggle modal visibility
  toggleModal = () => {
    this.setState({ isModalVisible: !this.state.isModalVisible });
  };

  UNSAFE_componentWillMount() {
    let date = new Date();
    // old code == > date.setDate(date.getDate() + 1);
    date.setDate(date.getDate());
    date.setHours(0);
    date.setMinutes(0);
    date.setSeconds(0);
    selectedDeliveryDate = date;

    //initial calselectedStartDate should be 7 am
    let initialStartTime = this.state.calSelectedStartTime;
    initialStartTime.setHours(0);
    initialStartTime.setMinutes(0);
    initialStartTime.setSeconds(0);

    //initially end time would be 1 hr ahead then the startTime
    let initialEndTime = this.state.calSelectedEndTime;
    initialEndTime.setHours(initialStartTime.getHours() + 1);
    initialEndTime.setMinutes(0);
    initialEndTime.setSeconds(0);

    this.setState({
      selectedDate: "",
      minimumDate: date,
      calSelectedStartTime: "",
      calSelectedEndTime: "",
      selectedStartTime: "",
      selectedEndTime: "",
    });
    selectedStartDate = initialStartTime;
    selectedEndDate = initialEndTime;
  }

  componentDidMount() {
    // Add focus listener to handle data refresh when navigating back
    this.focusListener = this.props.navigation.addListener("focus", () => {
      this.loadData();
    });

    if (Platform.OS === "ios") {
      this.networkCheck();
    } else {
      this.getInitialTimeZone();
      this.getProjectSettingTime();
      this.getLocationDetail();
      this.setState({
        deliveryId: this.props.lastid,
      });

      this.checkAndUpdateSelectedDate();

      this.setState({
        editRequestID: this.props.route.params
          ? this.props.route.params != undefined &&
          this.props.route.params.showEditRequestID
          : 1,
      });
      if (
        this.props.route.params?.notificationDetails &&
        this.props.route.params?.notificationDetails.Project
      ) {
        this.setState(
          {
            projectId: this.props.route.params?.notificationDetails.Project.id,
          },
          () => {
            this.loadInitial();
          },
        );
      } else {
        this.setState(
          {
            projectId: this.props.projectDetails.id,
          },
          () => {
            this.loadInitial();
          },
        );
      }
    }
  }

  checkAndUpdateSelectedDate = () => {
    // Set selectedDeliveryDate from navigation params if passed
    if (this.props.route.params?.selectedDate) {
      const dateStr = this.props.route.params.selectedDate;
      let parsedMoment = moment(dateStr, "MM/DD/YYYY", true);
      if (!parsedMoment.isValid()) {
        parsedMoment = moment(dateStr);
      }

      if (parsedMoment.isValid()) {
        const parsedDate = parsedMoment.toDate();
        selectedDeliveryDate = parsedDate;
        this.setState({
          selectedDate: parsedMoment.format("MM/DD/YYYY"),
          calSelectedDate: parsedDate,
        });
      }
    }
  };

  networkCheck = () => {
    NetInfo.addEventListener((state) => {
      if (!state.isConnected && !state.isInternetReachable) {
        this.setState({ isNetworkCheck: true });
      } else {
        this.setState({ isNetworkCheck: false });
        this.getInitialTimeZone();
        this.getLocationDetail();
        this.setState({
          deliveryId: this.props.lastid,
        });

        this.checkAndUpdateSelectedDate();

        this.setState({
          editRequestID: this.props.route.params
            ? this.props.route.params != undefined &&
            this.props.route.params.showEditRequestID
            : 1,
        });
        if (
          this.props.route.params?.notificationDetails &&
          this.props.route.params?.notificationDetails.Project
        ) {
          this.setState(
            {
              projectId:
                this.props.route.params?.notificationDetails.Project.id,
            },
            () => {
              this.loadInitial();
            },
          );
        } else {
          this.setState(
            {
              projectId: this.props.projectDetails.id,
            },
            () => {
              this.loadInitial();
            },
          );
        }
      }
    });
  };

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  loadInitial = () => {
    this.getResponsibleCompanies();
    this.getdefinableList();
    // this.getEquiptypes();
    // this.getGateList();
    this.getControlledByList();
    this.searchMember(true, this.props.userDetails.firstName);
  };

  getInitialTimeZone = () => {
    this.setState({ showLoader: true });
    getTimeZone(
      `${GET_SINGLE_PROJECT}/${this.props.projectDetails.id}`,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.status == 200) {
            let temp =
              response.data.data != null ? response.data.data.TimeZoneId : "";
            this.timeZone(temp);
          }
        }
      },
    );
  };

  getProjectSettingTime = () => {
    let URL = `${GET_PROJECT_SETTING_DETAILS}?ProjectId=${this.props.projectDetails.id}`;
    getProjectSettings(
      URL,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.status == 200) {
            this.setState({
              deliveryWindowTime: response.data.data.deliveryWindowTime,
              deliveryWindowTimeUnit: response.data.data.deliveryWindowTimeUnit,
            });
          }
        }
        this.setState({
          editCurrentDate: moment()
            .clone()
            .add(
              this.state.deliveryWindowTime,
              this.state.deliveryWindowTimeUnit,
            ),
        });
      },
    );
  };

  // getLocationDetail = () => {
  //   let URL = `${GET_LOCATION_DETAILS}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`;
  //   getLocationSettings(
  //     URL,
  //     {},
  //     () => null,
  //     (response) => {
  //       console.log("location------>",response.data)
  //       let defaultLocation = {};
  //       response.data.data.map((item) => {
  //         if (item.isDefault)
  //           defaultLocation = {
  //             id: item.id,
  //             label: item.locationPath,
  //             value: item.locationPath,
  //           };
  //         this.state.locationDropdownList.push({
  //           id: item.id,
  //           label: item.locationPath,
  //           value: item.locationPath,
  //         });
  //       });
  //       this.setState({
  //         selectedLocationNew:
  //           defaultLocation != null ? defaultLocation.value : null,
  //           selectedLocationId:
  //           defaultLocation != null ? defaultLocation?.id : null,
  //       });
  //     }
  //   );
  // };

  getLocationDetail = () => {
    let URL = `${GET_LOCATION_DETAILS}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`;
    getLocationSettings(
      URL,
      {},
      () => null,
      (response) => {
        // Process location data
        let defaultLocation = {};
        let locationDropdownList = [];

        let fullLocationData = []; // Store full location data including equipment and gates

        response.data.data.forEach((item) => {
          // Build location dropdown
          locationDropdownList.push({
            id: item.id,
            label: item.locationPath,
            value: item.locationPath,
          });
          fullLocationData.push(item);

          // Set default location
          if (item.isDefault) {
            defaultLocation = {
              id: item.id,
              label: item.locationPath,
              value: item.locationPath,
            };
          }
        });

        // Update state with all data
        this.setState(
          {
            locationDropdownList,
            selectedLocationNew: defaultLocation?.value || null,
            selectedLocationId: defaultLocation?.id || null,
            fullLocationData, // Store full data for equipment and gate filtering
          },
          () => {
            // Now filter equipment and gates based on the selected location ID
            this.filterEquipmentAndGatesByLocation();
          },
        );
      },
    );
  };

  filterEquipmentAndGatesByLocation = () => {
    const {
      selectedLocationId,
      fullLocationData,
      editIN,
      selectedEquipTypeId,
      selectedGateId,
    } = this.state;

    if (!selectedLocationId || !fullLocationData || !fullLocationData.length) {
      return;
    }

    let equipTypeList = [];
    let storeEquipmentList = [];
    let gateList = [];

    // Find the selected location from stored data
    const selectedLocation = fullLocationData.find(
      (item) => item.id === selectedLocationId,
    );

    // Check if "No Equipment Needed" was selected
    let isNoEquipmentSelected = false;
    if (editIN && selectedEquipTypeId) {
      const selectedIds =
        typeof selectedEquipTypeId === "string"
          ? selectedEquipTypeId.split(",").map((id) => id.trim())
          : [String(selectedEquipTypeId)];
      isNoEquipmentSelected = selectedIds.some(
        (id) => String(id) === "0" || Number(id) === 0,
      );
    }

    const NoEquipmentOption = {
      ...NO_EQUIPMENT_NEEDED,
      visible: true, // Initially visible
      disabled: false, // Initially enabled
      selected: isNoEquipmentSelected,
    };
    equipTypeList.push(NoEquipmentOption);

    // Filter equipment based on selected location
    if (
      selectedLocation &&
      selectedLocation.EquipmentId &&
      Array.isArray(selectedLocation.EquipmentId)
    ) {
      selectedLocation.EquipmentId.forEach((equip) => {
        // Check if this equipment was previously selected (for edit mode)
        let isSelected = false;
        if (editIN && selectedEquipTypeId) {
          const selectedIds =
            typeof selectedEquipTypeId === "string"
              ? selectedEquipTypeId.split(",").map((id) => id.trim())
              : [String(selectedEquipTypeId)];
          isSelected = selectedIds.some(
            (id) => String(id) === String(equip.id),
          );
        }

        equipTypeList.push({
          id: equip.id,
          value: equip.equipmentName,
          name: equip.equipmentName,
          label: equip.equipmentName,
          selected: isSelected,
          isCrane: equip.PresetEquipmentType?.isCraneType || false,
          visible: true, // Always visible
          disabled: false,
        });
        storeEquipmentList.push(equip);
      });
    }

    // Filter gates based on selected location
    if (
      selectedLocation &&
      selectedLocation.gateDetails &&
      Array.isArray(selectedLocation.gateDetails)
    ) {
      selectedLocation.gateDetails.forEach((gate) => {
        // Check if this gate was previously selected (for edit mode)
        let isSelected = false;
        if (editIN && selectedGateId) {
          const selectedIds =
            typeof selectedGateId === "string"
              ? selectedGateId.split(",").map((id) => id.trim())
              : [String(selectedGateId)];
          isSelected = selectedIds.some((id) => String(id) === String(gate.id));
          console.log(
            `Gate ${gate.id} (${gate.name}) - wasSelected: ${isSelected}, selectedIds:`,
            selectedIds,
          );
        }

        gateList.push({
          id: gate.id,
          name: gate.gateName,
          value: gate.gateName,
          label: gate.gateName,
          selected: isSelected,
        });
      });
    }

    // Update equipment and gate lists
    this.setState(
      {
        equipTypeList: [
          ...new Map(equipTypeList.map((item) => [item.id, item])).values(),
        ],
        storeEquipmentList,
        gateList: [
          ...new Map(gateList.map((item) => [item.id, item])).values(),
        ],
      },
      () => {
        if (this.state.editIN) {
          if (equipTypeList.length === 0) {
            this.setState({
              selectedEquipName: null,
              selectedEquipTypeId: null,
              selectedEquipmentList: [],
              isAssociatedWithCraneRequest: false,
            });
          }
          if (gateList.length === 0) {
            this.setState({
              selectedGate: null,
              selectedGateId: null,
            });
          }
          // Trigger the callback to update the selected equipment display
          const selectedEquipment = this.state.equipTypeList.filter(
            (item) => item && item.selected === true,
          );
          if (selectedEquipment.length > 0) {
            this.getSelectedEquipmentList(this.state.equipTypeList);
          }
        }
      },
    );
  };

  timeZone = (timeZone) => {
    getTimeZone(
      GET_TIMEZONE,
      {},
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.status == 200) {
            let list = [];
            response.data.data.forEach((e) => {
              let temp = {};
              temp = {
                id: e.id,
                value: e.location,
                label: e.location,
                selected: false,
                name: e.location,
              };
              list.push(temp);
            });
            let item = null;
            if (timeZone != null) {
              item = list.filter((element) => {
                return element.id === timeZone;
              });
            }

            this.setState(
              {
                showLoader: false,
                timeZoneList: list,
                selectedTimeZone: item != null ? item[0].value : null,
                selectedTimeZoneId: item != null ? item[0].id : "",
              },
              () => this.checkEdit(),
            );
          }
        }
      },
    );
  };

  checkEdit = () => {
    if (this.props.editedData.item) {
      let data = this.props.editedData.item;
      this.setState(
        {
          deliveryId: data.DeliveryId,
          DeliveryRequestId: this.props.deliveryDetailsId,
          editDR: true,
        },
        async () => {
          await this.getRole();
          await this.deliveryDetails();
        },
      );
      trackScreen("Edit Delivery Request");
      this.props.editData({});
    } else {
      trackScreen("New Delivery Request");
    }
  };

  getControlledByList = () => {
    getAllMemberList(
      LIST_ALL_MEMBER +
      this.props.projectDetails.id +
      "/" +
      this.props.projectDetails.ParentCompany.id,
      {},
      () => { },
      (response) => {
        if (response.status) {
          if (response.data.message == Strings.popup.getMemEqui) {
            this.storeContactPerson(response.data.data);
          } else {
            this.setState({
              memberlist: [],
            });
          }
        } else {
          this.showError("error", Strings.errors.failed);
        }
      },
    );
  };

  storeContactPerson = (data) => {
    let memberList = [];

    for (let item of data) {
      if (item.User.firstName != null) {
        memberList.push({
          label:
            item.User.firstName +
            " " +
            item.User.lastName +
            " (" +
            item.User.email +
            ")",
          value: item.User.email,
          id: item.id,
          name: item.User.email,
        });
      } else {
        memberList.push({
          label: item.User.email,
          value: item.User.email,
          id: item.id,
        });
      }
    }
    this.setState({ controlledByList: memberList });
  };

  getRole = async () => {
    let url =
      GET_PROJECT_ROLE +
      this.state.projectId +
      "/" +
      this.props.projectDetails.ParentCompany.id;
    this.setState({ showLoader: true });
    let response = await _getData(url);
    this.setState({ showLoader: false });
    if (response.data) {
      this.setState({
        roleId: response.data.data.RoleId,
        editedZoneID: response.data.data.TimeZoneId,
      });
    }
  };
  getdefinableList = () => {
    getDefinableFeature(
      DEFINABLE_FEATURE +
      this.state.projectId +
      "/" +
      this.props.projectDetails.ParentCompany.id,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.data.length !== 0) {
            let definableList = [];

            for (let item of response.data.data) {
              definableList.push({
                id: item.id,
                name: item.DFOW,
                label: item.DFOW,
                selected: false,
              });
            }

            this.setState({
              definableList: definableList,
            });
          }
        }
      },
    );
  };

  // getGateList = () => {
  //   let param = { isFilter: true, showActivatedAlone: true };
  //   getGateList(
  //     GET_GATE_LIST +
  //       this.state.projectId +
  //       "/0/0/" +
  //       this.props.projectDetails.ParentCompany.id,
  //     param,
  //     () => null,
  //     (response) => {
  //       if (response.status) {
  //         if (response.data.data.length !== 0) {
  //           let gateList = [];

  //           for (let item of response.data.data) {
  //             gateList.push({
  //               id: item.id,
  //               name: item.gateName,
  //               value: item.gateName,
  //               label: item.gateName,
  //               selected: false,
  //             });
  //           }

  //           this.setState({
  //             gateList: gateList,
  //           });
  //         }
  //       }
  //     }
  //   );
  // };

  // getEquiptypes = () => {
  //   let param = { isFilter: true, showActivatedAlone: true };
  //   let url = `${GET_EQUIP_LIST}${this.state.projectId}/0/0/${this.props.projectDetails.ParentCompany.id}`;
  //   getEquipList(
  //     url,
  //     param,
  //     () => null,
  //     (response) => {
  //       if (response.status) {
  //         if (response.data.data.length !== 0) {
  //           let equipTypelist = [];
  //           for (let item of response.data.data) {
  //             equipTypelist.push({
  //               id: item.id,
  //               value: item.equipmentName,
  //               name: item.equipmentName,
  //               label: item.equipmentName,
  //               selected: false,
  //               isCrane: item.PresetEquipmentType.isCraneType,
  //             });
  //           }

  //           this.setState({
  //             equipTypeList: equipTypelist,
  //             storeEquipmentList: response.data.data,
  //           });
  //         }
  //       }
  //     }
  //   );
  // };

  getResponsibleCompanies = () => {
    const { selected_Company } = this.props;
    //#fff;
    getNewCompanyList(
      GET_NEW_COMPANIES +
      this.state.projectId +
      `/${this.props.projectDetails.ParentCompany.id}`,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.data.length !== 0) {
            let responsibleCompany = [];

            for (let item of response.data.data) {
              responsibleCompany.push({
                id: item.id,
                name: item.companyName,
                label: item.companyName,
                selected:
                  selected_Company.companyName === item.companyName
                    ? true
                    : false,
              });
            }

            this.setState({
              responisbleCompanyList: responsibleCompany,
            });
          }
        }
      },
    );
  };

  onChangeperson = (text) => {
    if (text != "") {
      this.searchMember(false, text);
    } else {
      this.setState({ responsiblePersonData: [] });
    }
  };

  searchMember = (initial, text) => {
    searchMember(
      SEARCH_MEMBER +
      this.state.projectId +
      `/${text}` +
      `/${this.props.projectDetails.ParentCompany.id}`,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.length !== 0) {
            let searchList = [];
            let selectedItem = [];
            let userId = -1;

            for (let item of response.data) {
              if (
                this.state.selectedItem.filter(
                  (data) => data.email == item.emails,
                ) == true ||
                (item.emails == this.props.userDetails.email) == true
              ) {
              } else {
                searchList.push({
                  id: `${item.email}`,
                  email: item.email,
                  userId: item.id,
                });
              }

              if (
                (item.emails == this.props.userDetails.email) == true &&
                initial == true
              ) {
                selectedItem.push({
                  id: item.email,
                  email: item.email,
                  userId: item.id,
                });
                userId = item.id;
              } else {
              }
            }
            if (userId == -1) {
              this.setState({
                responsiblePersonData: searchList,
                selectedItem: selectedItem,
              });
            } else {
              this.setState({
                responsiblePersonData: searchList,
                selectedItem: selectedItem,
                selectedItemIndex: userId,
              });
            }
          }
        }
      },
    );
  };

  deliveryDetails = () => {
    this.setState({
      showLoader: true,
    });
    let url = `${GET_SINGLE_NDR}${this.state.DeliveryRequestId}/${this.props.projectDetails.ParentCompany.id}`;
    getDeliveryDetails(
      url,
      {},
      () => null,
      (deliveryDetailsresp) => {
        this.setState({
          showLoader: false,
        });

        const item = this.state.timeZoneList.filter((element) => {
          // 👇️ using AND (&&) operator
          return element.id === this.state.editedZoneID;
        });

        if (deliveryDetailsresp.toString() == Strings.errors.timeout) {
          this.showToaster("error", Strings.errors.checkInternet);
        } else if (deliveryDetailsresp.status) {
          this.setState({
            recurrenceType:
              deliveryDetailsresp.data.data.recurrence != null
                ? deliveryDetailsresp.data.data.recurrence.recurrence
                : Strings.calendarSettings.doseNotRepeat,
            deliveryStatus:
              deliveryDetailsresp.data.data.status != null
                ? deliveryDetailsresp.data.data.status
                : null,
          });
          if (deliveryDetailsresp.status == 200) {
            let data = deliveryDetailsresp.data.data;
            let isAssociate = data.isAssociatedWithCraneRequest;
            let selectedCompanyList = [];
            let selectedDefinableList = [];
            let selectedEquipmentList = [];

            for (let item of this.state.responisbleCompanyList) {
              if (
                data.companyDetails.some(
                  (person) => person.Company.companyName === item.name,
                )
              ) {
                selectedCompanyList.push({
                  id: item.id,
                  label: item.name,
                  name: item.name,
                  selected: true,
                });
              } else {
                selectedCompanyList.push({
                  id: item.id,
                  label: item.name,
                  name: item.name,
                  selected: false,
                });
              }
            }
            for (let item of this.state.equipTypeList) {
              let isSelected = false;

              // Check for regular equipment match
              if (
                data.equipmentDetails.some(
                  (person) => person.Equipment.equipmentName === item.name,
                )
              ) {
                isSelected = true;
              }

              // Special handling for "No Equipment Needed" case
              // Check if API returned "No Equipment Needed" and current item is the no-equipment option
              if (
                data.equipmentDetails.some(
                  (person) =>
                    person.Equipment.equipmentName === "No Equipment Needed" &&
                    person.Equipment.id === 0,
                ) &&
                item.id === 0
              ) {
                isSelected = true;
              }

              selectedEquipmentList.push({
                id: item.id,
                label: item.name,
                name: item.name,
                selected: isSelected,
                isCrane: item.isCrane,
              });
            }

            for (let item of this.state?.definableList) {
              if (
                data.defineWorkDetails.some(
                  (person) => person.DeliverDefineWork.DFOW === item.name,
                )
              ) {
                selectedDefinableList.push({
                  id: item.id,
                  label: item.name,
                  name: item.name,
                  selected: true,
                });
              } else {
                selectedDefinableList.push({
                  id: item.id,
                  label: item.name,
                  name: item.name,
                  selected: false,
                });
              }
            }

            let selectedPersons = [];
            for (let item of data.memberDetails) {
              if (item.Member != null) {
                if (item.Member.User.firstName != null) {
                  selectedPersons.push({
                    id: `${item.Member.User.firstName} ${item.Member.User.lastName}(${item.Member.User.email})`,
                    email: item.Member.User.email,
                    userId: item.Member.id,
                  });
                } else {
                  selectedPersons.push({
                    id: item.Member.User.email,
                    email: item.Member.User.email,
                    userId: item.Member.id,
                  });
                }
              }
            }

            const startMoment = moment.parseZone(data.deliveryStart);
            const endMoment = moment.parseZone(data.deliveryEnd);

            // Extract values
            const startHours = startMoment.format("hh"); // 12-hour format
            const startMinutes = startMoment.format("mm");
            const ampm = startMoment.format("a"); // am/pm

            const endHours = endMoment.format("hh");
            const endMinutes = endMoment.format("mm");
            const endampm = endMoment.format("a");

            // Year, month (0-based for Date), date
            const fullYear = startMoment.year();
            const startMonthIndex = startMoment.month(); // 0-based
            const endMonthIndex = endMoment.month(); // 0-based
            const startDate = startMoment.date();
            const endDate = endMoment.date();

            // Formatted strings
            const strTime = `${startHours}:${startMinutes} ${ampm}`;
            const endTime = `${endHours}:${endMinutes} ${endampm}`;

            // Use moment's hour() and minute() to avoid timezone conversion
            const delStartTime = new Date(
              fullYear,
              startMonthIndex,
              startDate,
              startMoment.hour(), // Use moment's 24-hour format hour
              startMoment.minute(),
            );

            const delEndTime = new Date(
              fullYear,
              endMonthIndex,
              endDate,
              endMoment.hour(), // Use moment's 24-hour format hour
              endMoment.minute(),
            );

            let selectedDateRequest = `${startMonthIndex + 1
              }/${startDate}/${fullYear}`;

            selectedEndDate = delEndTime;
            selectedStartDate = delStartTime;
            // Parse the delivery date to preserve local date components
            // Use the parsed moment date to avoid timezone conversion issues
            selectedDeliveryDate = new Date(
              fullYear,
              startMonthIndex,
              startDate,
            );
            if (isAssociate) {
              this.setState({
                pickId: data.CraneRequestId,
                pickFrom: data.cranePickUpLocation,
                pickTo: data.craneDropOffLocation,
                isAssociatedWithCraneRequest: data.isAssociatedWithCraneRequest,
              });
            }
            if (data.status == "Delivered") {
              if (this.state.roleId == 3 || this.state.roleId == 4) {
                if (this.state.editDR) {
                  this.setState({ isEditDate: false });
                }
              }
            }
            this.setState(
              {
                selectedDayArray: data.recurrence.days,
                selectedDay: data.recurrence.days,
                description: data.description,
                DeliveryId: data.DeliveryId,
                vehicleType: data.vehicleType,
                textFieldValue: data.OriginationAddress,
                deliveryStartDate: data.deliveryStart,
                deliveryEndDate: data.deliveryEnd,
                deliverydetails:
                  moment(data.deliveryStart).format(
                    "MMMM DD,YYYY, hh:mm:ss a",
                  ) +
                  " " +
                  "-" +
                  " " +
                  moment(data.deliveryEnd).format("MMMM DD,YYYY, hh:mm:ss a"),
                responsiblecompany: data.companyDetails
                  .map((e) => e.Company.companyName)
                  .join(",")
                  .toString(),
                selectedGateId:
                  data.equipmentDetails.length > 0
                    ? data.gateDetails.map((e) => e.Gate.id).join(",")
                    : 0,
                selectedGate:
                  data.gateDetails.length > 0
                    ? data.gateDetails
                      .map((e) => e.Gate.gateName)
                      .join(",")
                      .toString()
                    : null,
                selectedEquipTypeId:
                  data.equipmentDetails.length > 0
                    ? data.equipmentDetails.map((e) => e.Equipment.id).join(",")
                    : 0,
                selectedEquipName:
                  data.equipmentDetails.length > 0
                    ? data.equipmentDetails
                      .map((e) => e.Equipment.equipmentName)
                      .join(",")
                      .toString()
                    : null,
                // selectedResponsibleCompany: data.companyDetails.map(e => e.Company.companyName).join(',').toString(),
                selectedResponsibleCompanyId: data.companyDetails
                  .map((e) => e.Company.id)
                  .join(","),
                selectedDefinableList: data.defineWorkDetails
                  .map((e) => e.DeliverDefineWork.DFOW)
                  .join(","),
                selectedDefinableId: data.defineWorkDetails
                  .map((e) => e.DeliverDefineWork.id)
                  .join(","),
                delVehicleDetails:
                  data.vehicleDetails != null ? data.vehicleDetails : "",
                appliedby: data.createdUserDetails.User.firstName,
                additionalNotes: data.notes,
                status: data.status,
                updatestatus: data.status,
                escortNeeded: data.escort,
                definableList: selectedDefinableList,
                responisbleCompanyList: selectedCompanyList,
                // equipTypeList will be populated by filterEquipmentAndGatesByLocation
                selectedEndTime: endTime,
                calSelectedEndTime: new Date(data.deliveryEnd),
                calSelectedStartTime: new Date(data.deliveryStart),
                selectedStartTime: strTime,
                selectedDate: moment(selectedDateRequest).format("MM/DD/YYYY"),
                calSelectedDate: selectedDeliveryDate,
                selectedItem: selectedPersons,
                showMultipleSec: true,
                comparision: data,
                bookingId: data.id,
                deactiveDateChecker: selectedDeliveryDate,
                selectedTimeZoneId: item != null ? item[0].id : "",
                recurrenceSeriesID:
                  deliveryDetailsresp.data.data.recurrence != null
                    ? deliveryDetailsresp.data.data.recurrence.id
                    : null,
                recurrenceEndDateSeries:
                  deliveryDetailsresp.data.data.recurrence != null
                    ? deliveryDetailsresp.data.data.recurrence.recurrenceEndDate
                    : null,
                recurrenceDeliverStartDate:
                  deliveryDetailsresp.data.data.deliveryStart != null
                    ? deliveryDetailsresp.data.data.deliveryStart
                    : null,
                recurrenceDeliverEndDate:
                  deliveryDetailsresp.data.data.deliveryEnd != null
                    ? deliveryDetailsresp.data.data.deliveryEnd
                    : null,
                recurrenceEndDateRes:
                  deliveryDetailsresp.data.data.recurrence != null
                    ? deliveryDetailsresp.data.data.recurrence
                    : null,
                endDateRecurrence:
                  deliveryDetailsresp.data.data.recurrence != null
                    ? moment(
                      deliveryDetailsresp.data.data.recurrence
                        .recurrenceEndDate,
                    ).format("MM/DD/YYYY")
                    : null,
                selectedEndDateYear:
                  this.state.editDR == false
                    ? deliveryDetailsresp.data.data.recurrence != null
                      ? deliveryDetailsresp.data.data.recurrence
                        .recurrenceEndDate
                      : null
                    : new Date(),
                recurrence:
                  deliveryDetailsresp.data.data.recurrence != null
                    ? deliveryDetailsresp.data.data.recurrence.recurrence
                    : this.state.recurrence,
                selectedLocationNew:
                  data.location?.locationPath != null
                    ? data.location?.locationPath
                    : null,
                editIN: true,
              },
              () => {
                // After setting the state, filter equipment and gates based on the loaded location
                if (this.state.editDR && data.location?.id) {
                  this.setState(
                    {
                      selectedLocationId: data.location.id,
                      editIN: true,
                    },
                    () => {
                      this.filterEquipmentAndGatesByLocation();
                    },
                  );
                }
              },
            );
          } else if (deliveryDetailsresp.data.data.message) {
            this.showToaster("error", deliveryDetailsresp.data.data.message);
          } else {
            this.showToaster("error", deliveryDetailsresp.data.message);
          }
        } else {
          this.showToaster("error", deliveryDetailsresp.toString());
        }
      },
    );
  };

  submit = () => {
    const {
      recurrence,
      times,
      isMonthFirstCheck,
      isMonthSecondCheck,
      isYearFirstCheck,
      isYearSecondCheck,
      endDateRecurrence,
      selectedDayArray,
      editDR,
      listSetName,
      yearListSetName,
      selectedEndDateYear,
    } = this.state;
    let responsibleData = [];
    let selectedArrayItem = [];
    selectedArrayItem.push({
      id: `${this.props.responsiblePersonData.name}`,
      email: this.props.userDetails.email,
      userId: this.props.responsiblePersonData.id,
    });
    let responsibleValue = true;
    if (this.state.selectedItemList.length == 0) {
      for (let item of this.state.selectedItem) {
        responsibleData.push({ userId: item.userId });
      }
    } else if (
      this.state.selectedItemList != undefined ||
      this.state.selectedItemList != null
    ) {
      let entities = this.state.selectedItemList.entities;
      let results = this.state.selectedItemList.result;
      for (let item of results) {
        if (item == this.props.userDetails.email) {
          responsibleData.push({ userId: this.state.selectedItemIndex });
        } else {
          if (entities.item[item].userId == undefined) {
            responsibleValue = false;
          } else {
            responsibleData.push({
              userId: entities.item[item].userId,
            });
          }
        }
      }
    }
    this.getProjectSettingTime();

    let isResponseValue = false;

    let isEquipType = false;
    let isGateType = false;
    if (
      this.state.calSelectedDate > new Date() &&
      this.state.calSelectedDate > this.state.deactiveDateChecker
    ) {
      let equipTypeData = this.state.equipTypeList;
      const selectedEquipIds = this.state.selectedEquipTypeId
        ? typeof this.state.selectedEquipTypeId === "string"
          ? this.state.selectedEquipTypeId.split(",").map((id) => id.trim())
          : [String(this.state.selectedEquipTypeId)]
        : [];
      isEquipType = !!equipTypeData.find((item) => {
        return selectedEquipIds.some(
          (selectedId) =>
            String(item.id) === String(selectedId) ||
            Number(item.id) === Number(selectedId),
        );
      });

      let gateTypeData = this.state.gateList;
      const selectedGateIds = this.state.selectedGateId
        ? typeof this.state.selectedGateId === "string"
          ? this.state.selectedGateId.split(",").map((id) => id.trim())
          : [String(this.state.selectedGateId)]
        : [];
      isGateType = !!gateTypeData.find((item) => {
        return selectedGateIds.some(
          (selectedId) =>
            String(item.id) === String(selectedId) ||
            Number(item.id) === Number(selectedId),
        );
      });
    }

    if (
      this.state.calSelectedDate > new Date() &&
      this.state.calSelectedDate > this.state.deactiveDateChecker
    ) {
      const arr1 = this.state.controlledByList;
      const arr2 = responsibleData;
      const result = arr1.filter((o) =>
        arr2.some(({ userId }) => o.id === userId),
      );
      if (responsibleData.length !== result.length) {
        isResponseValue = true;
      }
    }
    //TODO FOR LATER
    // function check() {
    //     var now = moment();
    //     var hourToCheck = (now.day() !== 0)?17:15;
    //     var dateToCheck = now.hour(hourToCheck).minute(30);
    //       return moment().isAfter(dateToCheck);
    //   }
    //   console.log(check())

    Keyboard.dismiss();
    let selectedCompanyList = [];
    let selectedDefinableList = [];
    let selectedEquipmentList = [];

    for (let item of this.state.responisbleCompanyList) {
      if (item.selected == true) {
        selectedCompanyList.push(item.id);
      }
    }

    for (let item of this.state.definableList) {
      if (item.selected == true) {
        selectedDefinableList.push(item.id);
      }
    }

    for (let item of this.state.equipTypeList) {
      if (item.selected == true) {
        selectedEquipmentList.push(item.id);
      }
    }

    if (
      isEmpty(this.state.description) ||
      this.state.description.trim() == ""
    ) {
      this.showToaster("error", Strings.errors.emptyDescription);
    } else if (this.state.description.length < 3) {
      this.showToaster("error", "Description " + Strings.errors.lengthError);
    } else if (isEmpty(selectedCompanyList)) {
      this.showToaster("error", Strings.errors.emptyRespCompany);
    } else if (
      isEmpty(this.state.selectedItem) &&
      isEmpty(this.state.selectedItemList)
    ) {
      this.showToaster("error", Strings.errors.emptyResponsiblePerson);
    } else if (!responsibleValue) {
      this.showToaster("error", Strings.errors.validResponsiblePerson);
    // } else if (isEmpty(selectedEquipmentList)) {
    //   this.showToaster("error", Strings.errors.emptyEquip);
    // } else if (isEmpty(this.state.selectedGate)) {
    //   this.showToaster("error", Strings.errors.emptyGate);
    } else if (this.state.selectedDate === "") {
      this.showToaster("error", "Please select date and time");
    } else if (
      isEmpty(this.state.selectedStartTime) ||
      this.state.selectedStartTime === ""
    ) {
      this.showToaster("error", Strings.errors.emptyStartTime);
    } else if (
      isEmpty(this.state.selectedEndTime) ||
      this.state.selectedEndTime === ""
    ) {
      this.showToaster("error", Strings.errors.emptyEndTime);
    } else if (
      !this.state.editDR &&
      this.state.calSelectedDate < new Date().setHours(0, 0, 0, 0)
    ) {
      // Check if selected date is in the past (only for new entries, not for edits)
      this.showToaster("error", Strings.errors.futureDate);
    } else {
      // Check if selected time is in the past when date is today (only for new entries)
      if (
        !this.state.editDR &&
        moment(this.state.calSelectedDate).format("MM/DD/YYYY") ===
        moment(new Date()).format("MM/DD/YYYY")
      ) {
        // Create a date object with selected date and time
        const selectedDateTime = new Date(
          this.state.calSelectedDate.getFullYear(),
          this.state.calSelectedDate.getMonth(),
          this.state.calSelectedDate.getDate(),
          this.state.calSelectedStartTime.getHours(),
          this.state.calSelectedStartTime.getMinutes(),
          0,
          0
        );

        // Get current time with same precision
        const currentDateTime = new Date();
        currentDateTime.setSeconds(0, 0); // Reset seconds and milliseconds for fair comparison

        // Check if selected time is in the past
        if (selectedDateTime < currentDateTime) {
          this.showToaster("error", Strings.errors.futureTime);
          return;
        }
      }

      if (
        this.state.isAssociatedWithCraneRequest &&
        isEmpty(this.state.pickFrom)
      ) {
        this.showToaster("error", Strings.errors.emptyPickFrom);
      } else if (
        this.state.isAssociatedWithCraneRequest &&
        isEmpty(this.state.pickTo)
      ) {
        this.showToaster("error", Strings.errors.emptyPickTo);
      } else if (
        this.state.editDR == true &&
        this.state.calSelectedDate > new Date() &&
        this.state.calSelectedDate > this.state.deactiveDateChecker &&
        (!isEquipType || !isGateType || isResponseValue)
      ) {
        if (!isEquipType) {
          this.showToaster("error", Strings.errors.emptyEquipValue);
          return;
        } else if (!isGateType) {
          this.showToaster("error", Strings.errors.emptyGateValue);
          return;
        } else if (isResponseValue) {
          this.showToaster("error", Strings.errors.emptyResponseValue);
          return;
        }
      } else {
        let values = [];
        if (this.state.selectedItemList.length == 0) {
          for (let item of this.state.selectedItem) {
            values.push(item.userId);
          }
        } else if (
          this.state.selectedItemList != undefined ||
          this.state.selectedItemList != null
        ) {
          let entities = this.state.selectedItemList.entities;
          let results = this.state.selectedItemList.result;
          for (let item of results) {
            if (item == this.props.userDetails.email) {
              values.push(this.state.selectedItemIndex);
            } else {
              if (entities.item[item].userId != undefined) {
                values.push(entities.item[item].userId);
              }
            }
          }
        }
        // For edit mode, preserve original delivery dates if no time changes were made
        let startTimeValue, endTimeValue;

        if (
          this.state.editDR &&
          this.state.deliveryStartDate &&
          this.state.deliveryEndDate
        ) {
          // FOOLPROOF APPROACH: Check if user explicitly changed times via time slot picker
          // If no explicit time change flag is set, always preserve original dates in edit mode

          const userExplicitlyChangedTime =
            this.state.timeSlotExplicitlyChanged === true;

          // Multiple comparison methods to ensure we catch when no changes were made
          const originalStartTime = moment(this.state.deliveryStartDate)
            .local()
            .format("HH:mm");
          const originalEndTime = moment(this.state.deliveryEndDate)
            .local()
            .format("HH:mm");
          const currentStartTime = moment(selectedStartDate).format("HH:mm");
          const currentEndTime = moment(selectedEndDate).format("HH:mm");

          // Compare dates with multiple approaches
          const originalDateLocal = moment(this.state.deliveryStartDate)
            .local()
            .format("YYYY-MM-DD");
          const originalDateUTC = moment(this.state.deliveryStartDate)
            .utc()
            .format("YYYY-MM-DD");
          const originalDateRaw = moment(this.state.deliveryStartDate).format(
            "YYYY-MM-DD",
          );
          const currentDate = moment(selectedDeliveryDate).format("YYYY-MM-DD");

          // Check if user came back from time slot selection without changes
          const cameFromTimeSlot =
            this.state.selectedDate &&
            this.state.selectedStartTime &&
            this.state.selectedEndTime;
          const timeSlotMatches =
            cameFromTimeSlot &&
            this.state.selectedStartTime === originalStartTime &&
            this.state.selectedEndTime === originalEndTime;

          // AGGRESSIVE PRESERVATION: If user didn't explicitly change time, always preserve
          let shouldPreserve;
          if (!userExplicitlyChangedTime) {
            shouldPreserve = true;
          } else {
            // Only use comparison logic if user explicitly changed time
            const timesMatch =
              originalStartTime === currentStartTime &&
              originalEndTime === currentEndTime;
            const datesMatch =
              originalDateLocal === currentDate ||
              originalDateUTC === currentDate ||
              originalDateRaw === currentDate;
            shouldPreserve = timesMatch && (datesMatch || timeSlotMatches);
          }

          if (shouldPreserve) {
            startTimeValue = new Date(this.state.deliveryStartDate);
            endTimeValue = new Date(this.state.deliveryEndDate);
          } else {
            // Times or date changed, reconstruct with new values
            startTimeValue = new Date(
              selectedDeliveryDate.getFullYear(),
              selectedDeliveryDate.getMonth(),
              selectedDeliveryDate.getDate(),
              new Date(selectedStartDate).getHours(),
              new Date(selectedStartDate).getMinutes(),
              0,
            );
            endTimeValue = new Date(
              selectedDeliveryDate.getFullYear(),
              selectedDeliveryDate.getMonth(),
              selectedDeliveryDate.getDate(),
              new Date(selectedEndDate).getHours(),
              new Date(selectedEndDate).getMinutes(),
              0,
            );
          }
        } else {
          // New delivery request, construct times normally
          startTimeValue = new Date(
            selectedDeliveryDate.getFullYear(),
            selectedDeliveryDate.getMonth(),
            selectedDeliveryDate.getDate(),
            new Date(selectedStartDate).getHours(),
            new Date(selectedStartDate).getMinutes(),
            0,
          );
          endTimeValue = new Date(
            selectedDeliveryDate.getFullYear(),
            selectedDeliveryDate.getMonth(),
            selectedDeliveryDate.getDate(),
            new Date(selectedEndDate).getHours(),
            new Date(selectedEndDate).getMinutes(),
            0,
          );
        }

        // Keep these for compatibility with existing code
        let startTime = new Date(
          selectedDeliveryDate.getFullYear(),
          selectedDeliveryDate.getMonth(),
          selectedDeliveryDate.getDate(),
        );
        let endTime = new Date(
          selectedDeliveryDate.getFullYear(),
          selectedDeliveryDate.getMonth(),
          selectedDeliveryDate.getDate(),
        );

        let endRecurrenceTime = new Date(
          selectedEndDateYear.getFullYear(),
          selectedEndDateYear.getMonth(),
          selectedEndDateYear.getDate(),
        );

        // Use direct Date methods to avoid timezone conversion issues
        // This ensures 12 AM (midnight) is formatted as "00:00" not "12:00"
        const formatTimeToHHMM = (date) => {
          if (!date) return "00:00";
          const hours = date.getHours();
          const minutes = date.getMinutes();
          return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
        };

        // Use preserved times for picker values if in edit mode and no changes were made
        let startPickerValue, endPickerValue;
        if (
          this.state.editDR &&
          startTimeValue &&
          endTimeValue &&
          startTimeValue.getTime() ===
          new Date(this.state.deliveryStartDate).getTime()
        ) {
          // Using preserved times - extract picker values from original dates
          startPickerValue = moment(this.state.deliveryStartDate)
            .local()
            .format("HH:mm");
          endPickerValue = moment(this.state.deliveryEndDate)
            .local()
            .format("HH:mm");
        } else {
          // Using new/changed times - extract from selected dates
          startPickerValue = formatTimeToHHMM(selectedStartDate);
          endPickerValue = formatTimeToHHMM(selectedEndDate);
        }
        let days = selectedDayArray;

        const editStartDate = moment(startTimeValue);
        const editEndDate = moment(endTimeValue);

        // Use preserved times for delivery time values if in edit mode and no changes were made
        let deliveryStartTime, deliveryEndTime;
        if (
          this.state.editDR &&
          startTimeValue &&
          endTimeValue &&
          startTimeValue.getTime() ===
          new Date(this.state.deliveryStartDate).getTime()
        ) {
          // Using preserved times - extract delivery times from original dates
          deliveryStartTime = moment(this.state.deliveryStartDate)
            .local()
            .format("HH:mm");
          deliveryEndTime = moment(this.state.deliveryEndDate)
            .local()
            .format("HH:mm");
        } else {
          // Using new/changed times - extract from calendar selected times
          deliveryStartTime = formatTimeToHHMM(this.state.calSelectedStartTime);
          deliveryEndTime = formatTimeToHHMM(this.state.calSelectedEndTime);
        }

        if (endTimeValue.valueOf() < startTimeValue.valueOf()) {
          ``;
          this.showToaster("error", Strings.errors.notValidTime);
        } else if (endTimeValue.valueOf() == startTimeValue.valueOf()) {
          this.showToaster("error", Strings.errors.notValidSameTime);
        } else if (
          editDR == false &&
          recurrence != Strings.calendarSettings.doseNotRepeat &&
          endRecurrenceTime.valueOf() < startTime.valueOf()
        ) {
          this.showToaster("error", Strings.errors.errorEnddate);
        } else if (
          (this.state.roleId == 3 || this.state.roleId == 4) &&
          editDR == true &&
          !editStartDate.isAfter(this.state.editCurrentDate) &&
          !editEndDate.isAfter(this.state.editCurrentDate)
        ) {
          // this.showToaster("error", Strings.errors.projectSetting);
        } else if (
          (this.state.roleId == 3 || this.state.roleId == 4) &&
          editDR == true &&
          !editStartDate.isAfter(this.state.editCurrentDate) &&
          editEndDate.isAfter(this.state.editCurrentDate)
        ) {
          this.showToaster("error", "hello");
        } else {
          let param;
          let commonParam;
          if (this.state.isAssociatedWithCraneRequest) {
            commonParam = {
              ParentCompanyId: this.props.projectDetails.ParentCompany.id,
              EquipmentId: selectedEquipmentList,
              GateId: this.state.selectedGateId,
              ProjectId: this.state.projectId,
              companies: selectedCompanyList,
              define: selectedDefinableList,
              deliveryStart: this.formatDateWithTimezone(startTimeValue),
              deliveryEnd: this.formatDateWithTimezone(endTimeValue),
              description: this.state.description,
              escort: this.state.escortNeeded,
              notes: this.state.additionalNotes,
              persons: isResponseValue == false ? values : responsibleData,
              id: this.state.DeliveryRequestId,
              isAssociatedWithCraneRequest:
                this.state.isAssociatedWithCraneRequest,
              requestType: "deliveryRequestWithCrane",
              cranePickUpLocation: this.state.pickFrom,
              craneDropOffLocation: this.state.pickTo,
              CraneRequestId: this.state.pickId,
              LocationId: this.state.selectedLocationId,
              vehicleType: this.state.vehicleType,
              originationAddress: this.state.textFieldValue,
            };
          } else {
            commonParam = {
              ParentCompanyId: this.props.projectDetails.ParentCompany.id,
              EquipmentId: selectedEquipmentList,
              GateId: this.state.selectedGateId,
              ProjectId: this.state.projectId,
              companies: selectedCompanyList,
              define: selectedDefinableList,
              deliveryStart: this.formatDateWithTimezone(startTimeValue),
              deliveryEnd: this.formatDateWithTimezone(endTimeValue),
              description: this.state.description,
              escort: this.state.escortNeeded,
              notes: this.state.additionalNotes,
              persons: isResponseValue == false ? values : responsibleData,
              id: this.state.DeliveryRequestId,
              isAssociatedWithCraneRequest: false,
              requestType: "deliveryRequest",
              LocationId: this.state.selectedLocationId,
              vehicleType: this.state.vehicleType,
              originationAddress: this.state.textFieldValue,
            };
          }
          if (
            editDR == false &&
            recurrence === Strings.calendarSettings.doseNotRepeat
          ) {
            param = {
              ...commonParam,
              deliveryStart: moment(startTime).format("YYYY MM DD 00:00:00"),
              deliveryEnd: moment(startTime).format("YYYY MM DD 00:00:00"),
              recurrence: "Does Not Repeat",
              repeatEveryCount: null,
              repeatEveryType: null,
              chosenDateOfMonth: false,
              dateOfMonth: null,
              monthlyRepeatType: "",
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
            };
          } else if (
            editDR == false &&
            recurrence === Strings.calendarSettings.daily
          ) {
            let dailyDays = [];
            this.state.daysList.forEach((e) => {
              if (e.selected) {
                dailyDays.push(e.name);
              }
            });
            param = {
              ...commonParam,
              deliveryStart: moment(startTime).format("YYYY MM DD 00:00:00"),
              deliveryEnd: moment(endRecurrenceTime).format(
                "YYYY MM DD 00:00:00",
              ),
              recurrence: "Daily",
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Days" : "Day",
              chosenDateOfMonth: false,
              dateOfMonth: null,
              monthlyRepeatType: "",
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
              days: dailyDays,
            };
          } else if (
            editDR == false &&
            recurrence === Strings.calendarSettings.monthly
          ) {
            let repeat = null;
            let choseDate = false;
            if (isMonthFirstCheck == true) {
              repeat = null;
              choseDate = true;
            } else if (isMonthSecondCheck == true) {
              repeat = listSetName;
              choseDate = false;
            } else {
              repeat = listSetName;
              choseDate = false;
            }
            param = {
              ...commonParam,
              recurrence: "Monthly",
              deliveryStart: moment(startTime).format("YYYY MM DD 00:00:00"),
              deliveryEnd: moment(endRecurrenceTime).format(
                "YYYY MM DD 00:00:00",
              ),
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Months" : "Month",
              chosenDateOfMonth: choseDate,
              dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
              monthlyRepeatType: repeat,
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
            };
          } else if (
            editDR == false &&
            recurrence === Strings.calendarSettings.yearly
          ) {
            let repeat = "";
            let choseDate = false;
            if (isYearFirstCheck == true) {
              repeat = null;
              choseDate = true;
            } else if (isYearSecondCheck == true) {
              repeat = yearListSetName;
              choseDate = false;
            } else {
              repeat = yearListSetName;
              choseDate = false;
            }
            param = {
              ...commonParam,
              recurrence: "Yearly",
              deliveryStart: moment(startTime).format("YYYY MM DD 00:00:00"),
              deliveryEnd: moment(endRecurrenceTime).format(
                "YYYY MM DD 00:00:00",
              ),
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Years" : "Year",
              chosenDateOfMonth: choseDate,
              dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
              monthlyRepeatType: repeat,
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
            };
          } else if (
            editDR == false &&
            recurrence === Strings.calendarSettings.weekly
          ) {
            param = {
              ...commonParam,
              deliveryStart: moment(startTime).format("YYYY MM DD 00:00:00"),
              deliveryEnd: moment(endRecurrenceTime).format(
                "YYYY MM DD 00:00:00",
              ),
              recurrence: "Weekly",
              repeatEveryCount: times.toString(),
              repeatEveryType: times > 1 ? "Weeks" : "Week",
              chosenDateOfMonth: false,
              dateOfMonth: "",
              monthlyRepeatType: "",
              days: days,
              TimeZoneId: this.state.selectedTimeZoneId,
              endPicker: endPickerValue,
              startPicker: startPickerValue,
            };
          } else if (editDR === true) {
            if (recurrence === Strings.calendarSettings.doseNotRepeat) {
              // Handle "Does Not Repeat" case first
              commonParam = {
                ...commonParam,
                CraneRequestId: null,
                DeliveryId: this.state.deliveryId,
                chosenDateOfMonthValue: null,
                craneDropOffLocation: this.state.pickTo,
                cranePickUpLocation: this.state.pickFrom,
                chosenDateOfMonth: true,
                chosenDateOfMonthValue: 1,
                recurrence: "Does Not Repeat",
                recurrenceEdited: true,
                dateOfMonth: "",
                days: [],
                define: [],
                monthlyRepeatType: "",
                repeatEveryCount: null,
                repeatEveryType: null,
                deliveryStart: this.formatDateWithTimezone(startTimeValue),
                deliveryEnd: this.formatDateWithTimezone(endTimeValue),
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.daily) {
              // Handle "Daily" recurrence
              let dailyDays = [];
              this.state.daysList.forEach((e) => {
                if (e.selected) {
                  dailyDays.push(e.name);
                }
              });
              commonParam = {
                ...commonParam,
                deliveryStart: this.formatDateWithTimezone(startTimeValue),
                deliveryEnd: this.formatDateWithTimezone(endTimeValue),
                recurrence: "Daily",
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Days" : "Day",
                chosenDateOfMonth: false,
                recurrenceEdited: true,
                dateOfMonth: null,
                monthlyRepeatType: "",
                DeliveryId: this.state.deliveryId,
                CraneRequestId: null,
                chosenDateOfMonthValue: null,
                craneDropOffLocation: this.state.pickTo,
                cranePickUpLocation: this.state.pickFrom,
                days: dailyDays,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.weekly) {
              // Handle "Weekly" recurrence
              commonParam = {
                ...commonParam,
                deliveryStart: this.formatDateWithTimezone(startTimeValue),
                deliveryEnd: this.formatDateWithTimezone(endTimeValue),
                recurrence: "Weekly",
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Weeks" : "Week",
                chosenDateOfMonth: false,
                DeliveryId: this.state.deliveryId,
                dateOfMonth: null,
                monthlyRepeatType: "",
                days: days,
                recurrenceEdited: true,
                CraneRequestId: null,
                chosenDateOfMonthValue: null,
                craneDropOffLocation: this.state.pickTo,
                cranePickUpLocation: this.state.pickFrom,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.monthly) {
              // Handle "Monthly" recurrence
              let repeat = null;
              let choseDate = false;
              if (isMonthFirstCheck === true) {
                repeat = null;
                choseDate = true;
              } else if (isMonthSecondCheck === true) {
                repeat = listSetName;
                choseDate = false;
              }
              commonParam = {
                ...commonParam,
                recurrence: "Monthly",
                deliveryStart: this.formatDateWithTimezone(startTimeValue),
                deliveryEnd: this.formatDateWithTimezone(endTimeValue),
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Months" : "Month",
                chosenDateOfMonth: choseDate,
                DeliveryId: this.state.deliveryId,
                recurrenceEdited: true,
                CraneRequestId: null,
                chosenDateOfMonthValue: null,
                craneDropOffLocation: this.state.pickTo,
                cranePickUpLocation: this.state.pickFrom,
                dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
                monthlyRepeatType: repeat,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            } else if (recurrence === Strings.calendarSettings.yearly) {
              // Handle "Yearly" recurrence
              let repeat = "";
              let choseDate = false;
              if (isYearFirstCheck === true) {
                repeat = null;
                choseDate = true;
              } else if (isYearSecondCheck === true) {
                repeat = yearListSetName;
                choseDate = false;
              }
              commonParam = {
                ...commonParam,
                recurrence: "Yearly",
                deliveryStart: this.formatDateWithTimezone(startTimeValue),
                deliveryEnd: this.formatDateWithTimezone(endTimeValue),
                repeatEveryCount: times.toString(),
                repeatEveryType: times > 1 ? "Years" : "Year",
                chosenDateOfMonth: choseDate,
                DeliveryId: this.state.deliveryId,
                recurrenceEdited: true,
                CraneRequestId: null,
                chosenDateOfMonthValue: null,
                craneDropOffLocation: this.state.pickTo,
                cranePickUpLocation: this.state.pickFrom,
                dateOfMonth: moment(this.state.calSelectedDate).format("DD"),
                monthlyRepeatType: repeat,
                seriesOption: this.state.editRequestID,
                deliveryStartTime: deliveryStartTime,
                deliveryEndTime: deliveryEndTime,
                timezone: this.state.timeZoneParam,
                recurrenceId: this.state.recurrenceSeriesID,
                recurrenceEndDate:
                  this.state.recurrenceEndDateSeries != null
                    ? moment(endDateRecurrence).format("YYYY-MM-DD")
                    : null,
                recurrenceSeriesStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                ).format("YYYY-MM-DD"),
                recurrenceSeriesEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                ).format("YYYY-MM-DD"),
                previousSeriesRecurrenceEndDate: moment(
                  this.state.recurrenceDeliverEndDate,
                )
                  .add(-1, "days")
                  .format("YYYY-MM-DD"),
                nextSeriesRecurrenceStartDate: moment(
                  this.state.recurrenceDeliverStartDate,
                )
                  .add(1, "days")
                  .format("YYYY-MM-DD"),
              };
            }
          }

          this.setState({
            showLoader: true,
            disableSubmit: true,
          });
          try {
            // console.log("Parameter set while editing:", param );

            addNDR(
              this.state.editDR == true ? EDIT_DR : ADD_DR,
              this.state.editDR == true ? commonParam : param,
              () => null,
              (addDRResp) => {
                this.setState({
                  showLoader: false,
                  disableSubmit: false,
                });
                if (addDRResp.toString() == Strings.errors.timeout) {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: Strings.errors.checkInternet,
                      toastType: "error",
                    },
                    () => {
                      setTimeout(() => {
                        this.setState({
                          showToaster: false,
                        });
                      }, 2000);
                    },
                  );
                } else if (addDRResp.status) {
                  if (addDRResp.status == 200 || addDRResp.status == 201) {
                    this.setState(
                      {
                        showToaster: true,
                        toastMessage:
                          this.state.editDR == true
                            ? "Delivery Request Updated Successfully."
                            : "Delivery Request Created Successfully.",
                        toastType: "success",
                      },
                      () => {
                        setTimeout(() => {
                          this.props.cameBack(false);

                          if (
                            this.props.route.params?.from == "search" ||
                            this.props.route.params?.from == "detailDRPage"
                          ) {
                            this.props.route.params.updateData("data");
                          }

                          this.props.refreshDashboard(true, "Add Dr Submit");
                          this.props.updateList(false);
                          this.props.refreshCalendar(true);
                          this.props.navigation.goBack();
                          this.setState({ showToaster: false });
                        }, 200);
                      },
                    );
                    if (this.state.editDR) {
                      trackEvent("Edited_Delivery_Request");
                      mixPanelTrackEvent(
                        "Edited Delivery Request",
                        this.state.mixpanelParam,
                      );
                    } else {
                      trackEvent("Created_New_Delivery_Request");
                      mixPanelTrackEvent(
                        "Created New Delivery Request",
                        this.state.mixpanelParam,
                      );
                    }
                  } else if (addDRResp.status == 500) {
                    // Handle 500 errors specifically
                    let errorMessage =
                      "An error occurred while processing your request.";
                    if (addDRResp.data && addDRResp.data.message) {
                      errorMessage = addDRResp.data.message;
                    } else if (addDRResp.message) {
                      errorMessage = addDRResp.message;
                    }
                    this.showToaster("error", errorMessage);
                  } else if (addDRResp.data && addDRResp.data.message) {
                    // Handle other error statuses
                    if (
                      typeof addDRResp.data.message === "object" &&
                      addDRResp.data.message.message
                    ) {
                      this.showToaster("error", addDRResp.data.message.message);
                    } else {
                      this.showToaster("error", addDRResp.data.message);
                    }
                  } else if (addDRResp.message) {
                    this.showToaster("error", addDRResp.message);
                  } else {
                    this.showToaster("error", "An unexpected error occurred.");
                  }
                } else {
                  this.showToaster("error", addDRResp.toString());
                }
              },
            );
          } catch (e) { }
        }
      }
    }
  };

  showToaster = (type, message) => {
    Keyboard.dismiss();
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
        disableSubmit: false,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showToaster: false,
          });
        }, 2000);
      },
    );
  };
  onPressCancel = () => {
    if (this.state.editDR) {
      let data = this.state.comparision;

      let selectedCompanyList = [];
      let selectedDefinableList = [];

      for (let item of this.state.responisbleCompanyList) {
        if (
          data.companyDetails.some(
            (person) => person.Company.companyName === item.name,
          )
        ) {
          selectedCompanyList.push({
            id: item.id,
            label: item.name,
            name: item.name,
            selected: true,
          });
        } else {
          selectedCompanyList.push({
            id: item.id,
            label: item.name,
            name: item.name,
            selected: false,
          });
        }
      }

      for (let item of this.state.definableList) {
        if (
          data.defineWorkDetails.some(
            (person) => person.DeliverDefineWork.DFOW === item.name,
          )
        ) {
          selectedDefinableList.push({
            id: item.id,
            label: item.name,
            name: item.name,
            selected: true,
          });
        } else {
          selectedDefinableList.push({
            id: item.id,
            label: item.name,
            name: item.name,
            selected: false,
          });
        }
      }

      let selectedPersons = [];

      for (let item of data.memberDetails) {
        selectedPersons.push({
          id: item.Member.User.email,
          email: item.Member.User.email,
          userId: item.Member.id,
        });
      }

      let startHours = new Date(data.deliveryStart).getHours();
      let startMinutes = new Date(data.deliveryStart).getMinutes();

      let endHours = new Date(data.deliveryEnd).getHours();
      let endMinutes = new Date(data.deliveryEnd).getMinutes();

      const fullYear = new Date(data.deliveryStart).getFullYear();
      const fullMonth = new Date(data.deliveryStart).getMonth();
      const startDate = new Date(data.deliveryStart).getDate();
      const endDate = new Date(data.deliveryEnd).getDate();

      const ampm = startHours >= 12 ? "pm" : "am";

      startHours %= 12;
      startHours = (startHours < 10 ? `0${startHours}` : startHours) || 12;
      startMinutes = startMinutes < 10 ? `0${startMinutes}` : startMinutes;

      const endampm = endHours >= 12 ? "pm" : "am";

      endHours %= 12;
      endHours = (endHours < 10 ? `0${endHours}` : endHours) || 12;
      endMinutes = endMinutes < 10 ? `0${endMinutes}` : endMinutes;

      const strTime = `${startHours}:${startMinutes} ${ampm}`;

      const endTime = `${endHours}:${endMinutes} ${endampm}`;

      const delStartTime = new Date(
        fullYear,
        fullMonth,
        startDate,
        new Date(data.deliveryStart).getHours(),
        new Date(data.deliveryStart).getMinutes(),
      );

      const delEndTime = new Date(
        fullYear,
        fullMonth,
        endDate,
        new Date(data.deliveryEnd).getHours(),
        new Date(data.deliveryEnd).getMinutes(),
      );

      selectedEndDate = delEndTime;
      selectedStartDate = delStartTime;
      selectedDeliveryDate = new Date(data.deliveryStart);

      let responisbleCompanyList = selectedCompanyList;
      let description = data.description;
      let definableList = selectedDefinableList;
      let selectedGate = data.gateDetails
        .map((e) => e.Gate.gateName)
        .join(",")
        .toString();
      let selectedEquipName = data.equipmentDetails
        .map((e) => e.Equipment.equipmentName)
        .join(",")
        .toString();
      let additionalNotes = data.notes;
      let selectedStartTime = strTime;
      let selectedEndTime = endTime;
      let selectedDate = `${fullMonth + 1}/${startDate}/${fullYear}`;
      let delVehicleDetails = data.vehicleDetails;
      let selectedItem = selectedPersons;

      if (
        description == this.state.description &&
        JSON.stringify(responisbleCompanyList) ===
        JSON.stringify(this.state.responisbleCompanyList) &&
        JSON.stringify(definableList) ===
        JSON.stringify(this.state.definableList) &&
        JSON.stringify(selectedItem) ===
        JSON.stringify(this.state.selectedItem) &&
        selectedDate == this.state.selectedDate &&
        selectedEndTime == this.state.selectedEndTime &&
        selectedStartTime == this.state.selectedStartTime &&
        selectedEquipName == this.state.selectedEquipName &&
        selectedGate == this.state.selectedGate &&
        delVehicleDetails == this.state.delVehicleDetails &&
        additionalNotes == this.state.additionalNotes
      ) {
        this.props.cameBack(false);
        this.props.navigation.goBack();
      } else {
        this.setState({ showCancel: true });
      }
    } else {
      this.setState({ showCancel: true });
    }
  };

  bottomContainer = () => {
    return (
      <View style={drStyles.bottomContainer}>
        <TouchableOpacity onPress={this.onPressCancel}>
          <View style={drStyles.cancel}>
            <Text style={drStyles.cancelText}>{Strings.addMember.cancel}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            if (this.state.disableSubmit == false) {
              this.submit();
            }
          }}
        >
          <View style={drStyles.submit}>
            <Text style={drStyles.submitText}>
              {this.state.editDR == true
                ? Strings.addMember.update
                : Strings.addMember.submit}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  updateMasterState = (key, value) => {
    if (key == Strings.placeholders.description) {
      this.setState({
        description: value,
      });
    } else if (key == Strings.placeholders.deliveryVehical) {
      this.setState({
        delVehicleDetails: value,
      });
    } else if (key == Strings.addCraneRequest.pickingFrom) {
      this.setState({
        pickFrom: value,
      });
    } else if (key == Strings.addCraneRequest.pickingTo) {
      this.setState({
        pickTo: value,
      });
    } else if (key == Strings.placeholders.additional) {
      this.setState({
        additionalNotes: value,
      });
    }
  };

  onPressDelDateTF = () => {
    this.setState({
      showDateModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedDate) {
        this.onchngeDate("", this.state.calSelectedDate);
      }
    }
  };

  onchngeDate = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showDateModal: false,
          isFromDate: true,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      const fullYear = selectedValue.getFullYear();
      const fullMonth = selectedValue.getMonth();
      const date = selectedValue.getDate();

      let sampleSelectDate = `${fullMonth + 1}/${date}/${fullYear}`;
      const formattedDate = moment(sampleSelectDate).format("MM/DD/YYYY");
      this.setState(
        {
          selectedDate: moment(sampleSelectDate).format("MM/DD/YYYY"),
          calSelectedDate: selectedValue,
          isFromDate: true,
        },
        () => { },
      );
      selectedDeliveryDate = selectedValue;
    }
  };

  onPressStartDateTF = () => {
    this.setState({
      showStartTimeModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedStartTime) {
        this.onChangeStart("", this.state.calSelectedStartTime);
      }
    }
  };

  onChangeStart = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showStartTimeModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      let deliveryDate = selectedDeliveryDate;
      const fullYear = deliveryDate.getFullYear();
      const fullMonth = deliveryDate.getMonth();
      const date = deliveryDate.getDate();
      let hours = selectedValue.getHours();
      let minutes = selectedValue.getMinutes();

      const delStartTime = new Date(fullYear, fullMonth, date, hours, minutes);

      // Determine AM/PM
      const ampm = hours >= 12 ? "pm" : "am";

      // Convert to 12-hour format correctly
      let hours12 = hours % 12;
      if (hours12 === 0) {
        hours12 = 12;
      }
      hours12 = hours12 < 10 ? `0${hours12}` : hours12;
      minutes = minutes < 10 ? `0${minutes}` : minutes;

      const strTime = `${hours12}:${minutes} ${ampm}`;

      // Update end time: 1 hour ahead of the selected start time
      const delEndTime = moment(delStartTime, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .toDate();
      let updatedEndTimes = moment(selectedValue, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .toDate();
      let updatedEndTimeStrTime = moment(selectedValue, "yyyy-MM-ddTHH:mm:ssZ")
        .add(1, "hours")
        .format("hh:mm a");

      this.setState(
        {
          selectedStartTime: strTime,
          calSelectedStartTime: selectedValue,
          selectedEndTime: updatedEndTimeStrTime,
          calSelectedEndTime: updatedEndTimes,
        },
        () => { },
      );

      selectedStartDate = delStartTime;
      selectedEndDate = delEndTime;
    }
  };

  onPressEndDateTF = () => {
    this.setState({
      showEndTimeModal: true,
    });

    if (Platform.OS == "ios") {
      if (!this.state.selectedEndTime) {
        this.onChangeEndTime("", this.state.calSelectedEndTime);
      }
    }
  };

  onChangeEndTime = (tevent, selectedValue) => {
    if (Platform.OS == "android") {
      if (tevent.type == "set" || tevent.type == "dismissed") {
        this.setState({
          showEndTimeModal: false,
        });
      }
    }

    if (Platform.OS == "ios" || tevent.type == "set") {
      let deliveryDate = selectedDeliveryDate;
      const fullYear = deliveryDate.getFullYear();
      const fullMonth = deliveryDate.getMonth();
      const date = deliveryDate.getDate();
      let hours = selectedValue.getHours();
      let minutes = selectedValue.getMinutes();

      const delEndTime = new Date(fullYear, fullMonth, date, hours, minutes);

      const ampm = hours >= 12 ? "pm" : "am";

      hours %= 12;
      hours = hours || 12;
      minutes = minutes < 10 ? `0${minutes}` : minutes;

      const endTime = `${hours}:${minutes} ${ampm}`;

      this.setState(
        {
          selectedEndTime: endTime,
          calSelectedEndTime: selectedValue,
        },
        () => { },
      );

      selectedEndDate = delEndTime;
    }
  };

  renderHeader = () => {
    return (
      <View style={drStyles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.props.cameBack(false);
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={drStyles.title}>
          {this.state.editDR ? Strings.addDR.edit : Strings.addDR.ndr}
        </Text>
        <TouchableOpacity
          style={{
            width: 25,
            height: 25,
            borderRadius: 25 / 2,
            marginRight: 15,
            justifyContent: "center",
            alignItems: "center",
            borderWidth: 2,
            borderColor: Colors.black,
          }}
          onPress={() => {
            this.setState({ showInfo: true });
          }}
        >
          <Text style={{ color: Colors.black, fontSize: wp("5%") }}>i</Text>
        </TouchableOpacity>
      </View>
    );
  };

  renderSeleRow = (id, onPress, item, style) => {
    return (
      <TouchableOpacity
        activeOpacity={0.6}
        key={id}
        onPress={onPress}
        style={{
          width: wp("90%"),
          //height: 30,
          alignSelf: "center",
          justifyContent: "center",
        }}
      >
        <Text
          style={{
            color: "rgba(0, 0, 0, 0.87)",
            width: wp("90%"),
            marginLeft: 10,
            marginTop: 2,
            padding: 5,
          }}
        >
          {item.email}
        </Text>
      </TouchableOpacity>
    );
  };

  renderChip = (id, onClose, item, style, iconStyle) => (
    <Chip
      key={id}
      iconStyle={iconStyle}
      onClose={() => handleChipClose(onClose)}
      text={id}
      style={style}
    />
  );

  getSelectedCompanyList = (data) => {
    this.setState({
      responisbleCompanyList: data,
    });
  };

  getSelectedDefinableList = (data) => {
    this.setState({
      definableList: data,
    });
  };

  getSelectedEquipmentList = (data) => {
    console.log("data", data);
    console.log("this.state.equipTypeList", this.state.equipTypeList);
    if (data && data.length > 0) {
      // Create a new array with the updated selection state
      let updatedList = data.map((item) => {
        if (item && typeof item.selected !== "undefined") {
          return {
            ...item,
            selected: item.selected,
            disabled: false, // Initialize disabled state
          };
        }
        return item;
      });

      // Check if "No Equipment Needed" is selected
      const noEquipmentNeeded = updatedList.find(
        (item) => item && item.id === 0,
      ); // NO_EQUIPMENT_NEEDED has id: 0
      const otherEquipmentSelected = updatedList.some(
        (item) => item && item.id !== 0 && item.selected === true,
      );

      // Check if "Select All" scenario (all non-"No Equipment" items selected)
      const nonNoEquipmentItems = updatedList.filter(
        (item) => item && item.id !== 0,
      );
      const allOtherEquipmentSelected =
        nonNoEquipmentItems.length > 0 &&
        nonNoEquipmentItems.every((item) => item && item.selected === true);

      // Apply mutual exclusion logic
      if (noEquipmentNeeded && noEquipmentNeeded.selected === true) {
        // RULE 3: When "No Equipment Needed" is selected
        // - Deselect all other equipment options
        // - Disable all other equipment options
        // - Hide user guidance (no longer needed with new UX)
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            if (item.id === 0) {
              return {
                ...item,
                selected: true,
                disabled: false,
                visible: true,
              };
            } else {
              return {
                ...item,
                selected: false, // Automatically deselect
                disabled: true, // Disable other options when "No Equipment" is selected
                visible: true,
              };
            }
          }
          return item;
        });

        // Logic updated for DR equipment selection
      } else if (allOtherEquipmentSelected && !noEquipmentNeeded?.selected) {
        // RULE 1: When "Select All" is chosen
        // - Select all equipment EXCEPT "No Equipment Needed"
        // - Hide "No Equipment Needed" from visible options
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            if (item.id === 0) {
              return {
                ...item,
                selected: false,
                disabled: false,
                visible: false, // Hide from visible options
              };
            } else {
              return {
                ...item,
                selected: true,
                disabled: false,
                visible: true,
              };
            }
          }
          return item;
        });
      } else if (otherEquipmentSelected) {
        // RULE 4: When any individual equipment is selected
        // - Automatically unselect "No Equipment Needed"
        // - Keep "No Equipment Needed" visible but unselected
        // - Allow multiple selection of other equipment
        // - Hide guidance since user made a selection
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            if (item.id === 0) {
              return {
                ...item,
                selected: false, // Automatically unselect
                disabled: false,
                visible: true, // Keep visible but unselected
              };
            } else {
              return {
                ...item,
                disabled: false,
                visible: true,
              };
            }
          }
          return item;
        });

        // Logic updated for DR equipment selection
      } else {
        // RULE 2: When "Select All" is unselected or no selections
        // - Show all equipment options including "No Equipment Needed"
        // - Enable all options
        updatedList = updatedList.map((item) => {
          if (item && typeof item.id !== "undefined") {
            return {
              ...item,
              disabled: false,
              visible: true,
            };
          }
          return item;
        });
      }

      const selectedEquipment = updatedList.filter(
        (item) => item && item.selected === true,
      );

      // Update the state with the new data and clear time slots
      this.setState(
        {
          equipTypeList: updatedList,
          selectedEquipmentList: selectedEquipment,
          isAssociatedWithCraneRequest: selectedEquipment.some(
            (item) => item && item.isCrane === true && item.selected === true,
          )
            ? true
            : false,
          // Clear existing time slots when equipment selection changes
          selectedStartTime: '',
          selectedEndTime: '',
        },
        () => {
          // Force a re-render of the MultiSelectDropDown component
          if (this.multiSelectRef) {
            this.multiSelectRef.setState({
              dataItems: updatedList,
              isAllChecked:
                allOtherEquipmentSelected && !noEquipmentNeeded?.selected,
            });
          }
        },
      );
    } else {
      // Handle case where no data is provided
      this.setState({
        equipTypeList: [],
        selectedEquipmentList: [],
        isAssociatedWithCraneRequest: false,
      });
    }
  };

  getLastCraneId = () => {
    let url = `${GET_LAST_CRANE_ID}${this.state.projectId}/${this.props.projectDetails.ParentCompany.id}`;
    getLastCraneRequestId(
      url,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.status == 200) {
            if (response.data) {
              this.setState({ pickId: response.data.lastId.CraneRequestId });
            }
          } else if (response.status == 400) {
            this.showToaster("error", response.data.message);
          } else {
            this.showToaster("error", Strings.errors.something);
          }
        }
      },
    );
  };

  //on press done in date picker
  onDatePickerDonePressed() {
    this.setState({ showDateModal: false });
  }

  onPressEqipType = (item) => {
    if (item.isCrane) {
      this.getLastCraneId();
    }
    this.setState({
      selectedEquipName: item.value,
      selectedEquipTypeId: item.id,
      eqipModalVisible: false,
      isAssociatedWithCraneRequest: item.isCrane,
    });
    Keyboard.dismiss();
  };

  onPressGateType = (item) => {
    this.setState({
      selectedGate: item.value,
      selectedGateId: item.id,
      gateModalVisible: false,
    });
  };

  render() {
    const {
      selectedDate,
      calSelectedDate,
      recurrence,
      times,
      isMonthFirstCheck,
      isMonthSecondCheck,
      isMonthThirdCheck,
      monthlyDay,
      monthlyLastDay,
      isYearFirstCheck,
      isYearSecondCheck,
      isYearThirdCheck,
      endDateRecurrence,
      selectedDaysOccurs,
      listSetName,
      yearListSetName,
      selectedEndDateYear,
      editDR,
      recurrenceType,
      deliveryStatus,
      editRequestID,
    } = this.state;
    return (
      <>
        {this.state.isNetworkCheck ? (
          <NoInternet Refresh={() => this.networkCheck()} />
        ) : (
          <SafeAreaView style={drStyles.safeArea}>
            <View style={drStyles.parentContainer}>
              {this.renderHeader()}

              <KeyboardAwareScrollView
                keyboardShouldPersistTaps={true}
                enableResetScrollToCoords={false}
                scrollsToTop={false}
                extraScrollHeight={60}
              >
                <TextField
                  attrName={Strings.placeholders.description}
                  title={Strings.placeholders.description}
                  value={this.state.description}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  maxLength={150}
                  textInputStyles={{
                    // here you can add additional TextInput drStyles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                />

                <View style={drStyles.memberContainer}>
                  <Text style={drStyles.idTitle}>
                    {Strings.placeholders.deliveryId}
                  </Text>

                  <Text style={drStyles.idText}>{this.state.deliveryId}</Text>
                </View>

                {this.state.isAssociatedWithCraneRequest && (
                  <View style={drStyles.memberContainer}>
                    <Text style={drStyles.idTitle}>
                      {Strings.addCraneRequest.pickId}
                    </Text>

                    <Text style={drStyles.idText}>{this.state.pickId}</Text>
                  </View>
                )}

                <Text
                  style={[
                    styles.timeZoneText,
                    { fontFamily: Fonts.montserratMedium, fontSize: wp("4%") },
                  ]}
                >
                  {Strings.placeholders.location}
                  <Text style={styles.mandatory}>
                    {Strings.addNewEvent.mandatory}
                  </Text>
                </Text>
                <DropDownPicker
                  searchable={true}
                  items={this.state.locationDropdownList}
                  defaultValue={this.state.selectedLocationNew}
                  searchablePlaceholder={Strings.addNewEvent.timeZoneSearch}
                  placeholder={Strings.placeholders.location}
                  placeholderStyle={styles.dropDownPlaceHolderStyle}
                  containerStyle={styles.dropDownContainer}
                  style={styles.dropDownPickerStyle}
                  itemStyle={styles.dropDownItemStyle}
                  customArrowUp={(size) => (
                    <Image
                      source={Images.downArr}
                      style={[
                        styles.arrowDownStyle,
                        { width: size, height: size },
                      ]}
                    />
                  )}
                  customArrowDown={(size) => (
                    <Image
                      source={Images.downArr}
                      style={[
                        styles.arrowDownStyle,
                        { width: size, height: size },
                      ]}
                    />
                  )}
                  dropDownStyle={styles.dropDownListStyle}
                  selectedLabelStyle={styles.selectedDropDown}
                  onChangeItem={(item) => {
                    this.setState(
                      {
                        selectedLocationNew: item.value,
                        selectedLocationId: item.id,
                        // Reset selections when location changes
                        selectedGate: null,
                        selectedGateId: null,
                        selectedEquipName: null,
                        selectedEquipTypeId: null,
                        selectedEquipmentList: [],
                        // Also reset the equipment and gate lists to empty arrays
                        equipTypeList: [],
                        gateList: [],
                        storeEquipmentList: [],
                      },
                      () => {
                        // Filter equipment and gates based on the newly selected location
                        this.filterEquipmentAndGatesByLocation();
                      },
                    );
                  }}
                  zIndex={5000}
                />

                <Text
                  style={[
                    drStyles.idTitle,
                    {
                      fontSize: wp("4%"),
                      marginLeft: 10,
                      alignSelf: "center",
                      width: wp("90%"),
                    },
                  ]}
                >
                  {Strings.placeholders.responsibleCompany}{" "}
                  <Text style={{ color: Colors.red }}>*</Text>
                </Text>

                <MultiSelectDropDown
                  dataItems={this.state.responisbleCompanyList}
                  title={"Select"}
                  selectedDataItem={this.getSelectedCompanyList}
                />

                <Text
                  style={[
                    drStyles.idTitle,
                    {
                      fontSize: wp("4%"),
                      marginLeft: 10,
                      marginTop: hp("4%"),
                      alignSelf: "center",
                      width: wp("90%"),
                    },
                  ]}
                >
                  {Strings.placeholders.definable}{" "}
                </Text>

                <MultiSelectDropDown
                  dataItems={this.state.definableList}
                  title={"Select"}
                  selectedDataItem={this.getSelectedDefinableList}
                />

                <View style={drStyles.escortContainer}>
                  <Text style={drStyles.escortText}>
                    {Strings.addDR.escortNeeded}{" "}
                  </Text>

                  <Switch
                    style={{ transform: [{ scaleX: 0.9 }, { scaleY: 0.8 }] }}
                    trackColor={{ false: Colors.placeholder, true: "#11FF00" }}
                    thumbColor={"#fff"}
                    ios_backgroundColor="#3e3e3e"
                    onValueChange={() =>
                      this.setState(
                        { escortNeeded: !this.state.escortNeeded },
                        () => {
                          if (this.state.escortNeeded == false) {
                            this.setState({
                              responsiblePersonData: [],
                            });
                          }
                        },
                      )
                    }
                    value={this.state.escortNeeded}
                  />
                </View>

                <View
                  style={[
                    drStyles.escortContainer,
                    { flexDirection: "column", marginTop: hp("2%") },
                  ]}
                >
                  <Text style={drStyles.escortText}>
                    {Strings.addDR.responsible}{" "}
                    <Text style={{ color: Colors.red }}>*</Text>
                  </Text>

                  {this.state.showMultipleSec === false && (
                    <Selectize
                      tintColor={Colors.themeColor}
                      items={this.state.responsiblePersonData}
                      selectedItems={this.state.selectedItem}
                      containerStyle={{
                        zIndex: 1,
                      }}
                      // listStyle={{
                      //   position: "absolute",
                      // }}
                      renderRow={(id, onPress, item, style) =>
                        this.renderSeleRow(id, onPress, item, style)
                      }
                      renderChip={(id, onClose, item, style, iconStyle) => (
                        <View style={[styles.root, style]}>
                          <View style={styles.container}>
                            <Text style={styles.text} numberOfLines={1}>
                              {id}
                            </Text>
                            {id != this.props.responsiblePersonData.name && (
                              <TouchableOpacity
                                style={[styles.iconWrapper, iconStyle]}
                                onPress={onClose}
                              >
                                <Text
                                  style={[
                                    styles.icon,
                                    this.isIOS
                                      ? styles.iconIOS
                                      : styles.iconAndroid,
                                  ]}
                                >
                                  x
                                </Text>
                              </TouchableOpacity>
                            )}
                          </View>
                        </View>
                      )}
                      onChangeSelectedItems={(text) => {
                        this.setState({
                          selectedItemList: text,
                          responsiblePersonData: [],
                        });
                      }}
                      textInputProps={{
                        onChangeText: this.onChangeperson,
                      }}
                    />
                  )}

                  {this.state.showMultipleSec === true && (
                    <Selectize
                      tintColor={Colors.themeColor}
                      items={this.state.responsiblePersonData}
                      selectedItems={this.state.selectedItem}
                      containerStyle={{
                        zIndex: 1,
                      }}
                      renderRow={(id, onPress, item, style) =>
                        this.renderSeleRow(id, onPress, item, style)
                      }
                      renderChip={(id, onClose, item, style, iconStyle) => (
                        <View style={[styles.root, style]}>
                          <View style={styles.container}>
                            <Text style={styles.text} numberOfLines={1}>
                              {id}
                            </Text>

                            {id != this.props.responsiblePersonData.name && (
                              <TouchableOpacity
                                style={[styles.iconWrapper, iconStyle]}
                                onPress={onClose}
                              >
                                <Text
                                  style={[
                                    styles.icon,
                                    this.isIOS
                                      ? styles.iconIOS
                                      : styles.iconAndroid,
                                  ]}
                                >
                                  x
                                </Text>
                              </TouchableOpacity>
                            )}
                          </View>
                        </View>
                      )}
                      onChangeSelectedItems={(text) => {
                        this.setState({
                          selectedItemList: text,
                          responsiblePersonData: [],
                        });
                      }}
                      textInputProps={{
                        onChangeText: this.onChangeperson,
                      }}
                    />
                  )}
                </View>

                {/* <Button 
          title="Force Reload" 
          onPress={this.loadData} 
        /> */}

                {editDR == false ? (
                  <>
                    {/* <Text style={styles.timeZoneText}>ss
                      {Strings.placeholders.timeZone}
                      <Text style={styles.mandatory}>
                        {Strings.addNewEvent.mandatory}
                      </Text>
                    </Text>
                    <Text style={styles.timeZoneValue}>{this.state.selectedTimeZone}</Text> */}
                    {/* <DropDownPicker
                      searchable={true}
                      items={this.state.timeZoneList}
                      defaultValue={this.state.selectedTimeZone}
                      searchablePlaceholder={Strings.addNewEvent.timeZoneSearch}
                      placeholder={Strings.placeholders.chooseTimezone}
                      placeholderStyle={styles.dropDownPlaceHolderStyle}
                      containerStyle={styles.dropDownContainer}
                      style={styles.dropDownPickerStyle}
                      itemStyle={styles.dropDownItemStyle}
                      customArrowUp={(size) => (
                        <Image
                          source={Images.downArr}
                          style={[
                            styles.arrowDownStyle,
                            { width: size, height: size },
                          ]}
                        />
                      )}
                      customArrowDown={(size) => (
                        <Image
                          source={Images.downArr}
                          style={[
                            styles.arrowDownStyle,
                            { width: size, height: size },
                          ]}
                        />
                      )}
                      dropDownStyle={styles.dropDownListStyle}
                      selectedLabelStyle={styles.selectedDropDown}
                      onChangeItem={(item) =>
                        this.setState({
                          selectedTimeZone: item.value,
                          selectedTimeZoneId: item.id,
                        })
                      }
                      zIndex={5000}
                    /> */}
                  </>
                ) : null}

                <Text
                  style={[
                    drStyles.idTitle,
                    {
                      fontSize: wp("4%"),
                      marginLeft: 10,
                      marginTop: hp("3%"),
                      alignSelf: "center",
                      width: wp("90%"),
                    },
                  ]}
                >
                  {Strings.placeholders.equip}{" "}
<<<<<<< HEAD
                  {/* <Text style={{ color: Colors.red }}>*</Text> */}
=======
                  <Text style={{ color: Colors.red }}>*</Text> 
>>>>>>> 51425c15084f924a9004cef28665cf22a22a30c6
                </Text>
                <MultiSelectDropDown
                  ref={(ref) => (this.multiSelectRef = ref)}
                  dataItems={this.state.equipTypeList}
                  title={Strings.placeholders.equip}
                  selectedDataItem={this.getSelectedEquipmentList}
                />

                <TextField
                  attrName={Strings.placeholders.gate}
                  title={Strings.placeholders.gate}
                  value={this.state.selectedGate}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  // mandatory={true}
                  showButton={true}
                  onPress={() => {
                    Keyboard.dismiss();
                    this.setState({ 
                      gateModalVisible: true,
                      // Clear existing time slots when gate selection changes
                      selectedStartTime: '',
                      selectedEndTime: '',
                    });
                  }}
                  imageSource={Images.downArr}
                  placeholder={"Select"}
                  textInputStyles={{
                    // here you can add additional TextInput styles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                />

                {this.state.equipTypeList.some(
                  (item) => item.isCrane && item.selected,
                ) ? (
                  <>
                    <TextField
                      attrName={Strings.addCraneRequest.pickingFrom}
                      title={Strings.addCraneRequest.pickingFrom}
                      value={this.state.pickFrom}
                      updateMasterState={(key, value) => {
                        this.updateMasterState(key, value);
                      }}
                      mandatory={true}
                      textInputStyles={{
                        // here you can add additional TextInput drStyles
                        color: Colors.black,
                        fontSize: 14,
                      }}
                      placeholder={Strings.addCraneRequest.pickingFrom}
                    />
                    <TextField
                      attrName={Strings.addCraneRequest.pickingTo}
                      title={Strings.addCraneRequest.pickingTo}
                      value={this.state.pickTo}
                      updateMasterState={(key, value) => {
                        this.updateMasterState(key, value);
                      }}
                      textInputStyles={{
                        // here you can add additional TextInput drStyles
                        color: Colors.black,
                        fontSize: 14,
                      }}
                      mandatory={true}
                      showButton={false}
                      imageSource={Images.calGray}
                      placeholder={Strings.addCraneRequest.pickingTo}
                    />
                  </>
                ) : null}

                <View
                  style={{ alignItems: "center", justifyContent: "center" }}
                >
                  <TouchableOpacity
                    style={[drStyles.submitButton]}
                    onPress={this.handleNavigation}
                  >
                    <Text style={drStyles.submitButtonText}>
                      Select Date and Time
                    </Text>
                  </TouchableOpacity>
                </View>
                {console.log(
                  "this.state.selectedDate",
                  this.state.selectedDate,
                )}
                {this.state.selectedDate !== "" && (
                  <TextField
                    attrName={Strings.placeholders.deliveryDate}
                    title={Strings.placeholders.deliveryDate}
                    value={this.state.selectedDate.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={true}
                    textInputStyles={styles.textStyle}
                    editable={false}
                    showButton={false}
                    container={{
                      marginTop: hp("3%"),
                    }}
                    placeholder={"Select"}
                  />
                )}
                {(this.state.selectedStartTime !== "" ||
                  this.state.selectedEndTime !== "") && (
                    <View
                      style={{
                        width: wp("90%"),
                        alignSelf: "center",
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      <TextField
                        attrName={Strings.placeholders.startTime}
                        title="Start Time Slot"
                        value={this.state.selectedStartTime.toString()}
                        updateMasterState={(key, value) => {
                          this.updateMasterState(key, value);
                        }}
                        mandatory={false}
                        textInputStyles={{
                          color: Colors.black,
                          fontSize: 14,
                        }}
                        editable={false}
                        showButton={false}
                        container={{
                          width: wp("42%"),
                          alignSelf: "flex-start",
                        }}
                        placeholder={"Select"}
                      />

                      <TextField
                        attrName={Strings.placeholders.endTime}
                        title="End Time Slot"
                        value={this.state.selectedEndTime.toString()}
                        updateMasterState={(key, value) => {
                          this.updateMasterState(key, value);
                        }}
                        mandatory={false}
                        textInputStyles={{
                          color: Colors.black,
                          fontSize: 14,
                        }}
                        editable={false}
                        showButton={false}
                        container={{
                          width: wp("42%"),
                          alignSelf: "flex-start",
                        }}
                        placeholder={"Select"}
                      />
                    </View>
                  )}

                {/* <TextField
                  attrName={Strings.placeholders.deliveryDate}
                  title={Strings.placeholders.deliveryDate}
                  value={this.state.selectedDate.toString()}
                  updateMasterState={(key, value) => {
                    console.log('TextField updateMasterState called - Delivery Date:', {key, value});
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  textInputStyles={
                    (editDR == true &&
                      deliveryStatus == Strings.deliveryStatusName.expired) ||
                    (editDR == true && editRequestID == 1)
                      ? styles.textStyle
                      : editDR == true
                      ? styles.textStyleEdited
                      : styles.textStyle
                  }
                  onPress={() => {
                    console.log('Delivery Date field pressed');
                    Keyboard.dismiss();
                    if (
                      (editDR == true &&
                        recurrenceType ==
                          Strings.calendarSettings.doseNotRepeat) ||
                      (editDR == true && editRequestID == 1)
                    ) {
                      this.onPressDelDateTF();
                    } else if (
                      editDR == true &&
                      deliveryStatus == Strings.deliveryStatusName.expired
                    ) {
                      this.onPressDelDateTF();
                    } else if (editDR == true) {
                      this.setState({ showDateModal: false });
                    } else {
                      this.onPressDelDateTF();
                    }
                  }}
                  showButton={this.state.isEditDate}
                  editable={this.state.isEditDate}
                  container={{
                    marginTop: hp("3%"),
                  }}
                  imageSource={Images.calGray}
                  placeholder={"Select"}
                />  */}

                {/* <View
                  style={{
                    width: wp("90%"),
                    alignSelf: "center",
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
               
                   <TextField
                    attrName={Strings.placeholders.}
                    title="Start Time Slot"
                    value={this.state.selectedStartTime.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={false}
                    textInputStyles={{
                     
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    showButton={this.state.isEditDate}
                    editable={this.state.isEditDate}
                    onPress={() => {
                      Keyboard.dismiss();
                      this.onPressStartDateTF();
                    }}
                    container={{
                      width: wp("42%"),
                      alignSelf: "flex-start",
                    }}
                    imageSource={Images.clock}
                    placeholder={"Select"}
                    progressWidth={wp("42%")}
                    buttonContainer={{
                      width: wp("42%"),
                    }}
                  /> 
                

                
                   <TextField
                    attrName={Strings.placeholders.endTime}
                    title="End Time Slot"
                    value={this.state.selectedEndTime.toString()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    mandatory={false}
                    textInputStyles={{
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    showButton={this.state.isEditDate}
                    editable={this.state.isEditDate}
                    onPress={() => {
                      Keyboard.dismiss();
                      this.onPressEndDateTF();
                    }}
                    container={{
                      width: wp("42%"),
                      alignSelf: "flex-start",
                    }}
                    imageSource={Images.clock}
                    placeholder={"Select"}
                    progressWidth={wp("42%")}
                    buttonContainer={{
                      width: wp("42%"),
                    }}
                  />
                 
                </View>   */}

                <TextField
                  attrName={Strings.placeholders.additional}
                  title={Strings.placeholders.additional}
                  value={this.state.additionalNotes}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  container={{ height: hp("14%") }}
                  mandatory={false}
                  maxLength={150}
                  textInputStyles={{
                    // here you can add additional TextInput drStyles
                    color: Colors.black,
                    fontSize: 14,
                    height: hp("9%"),
                    marginTop: hp("2%"),
                    // paddingTop: hp("2%")
                  }}
                  multiline={true}
                  showButton={false}
                  imageSource={Images.calGray}
                />

                <Text
                  style={{
                    color: Colors.placeholder,
                    fontSize: wp("4%"),
                    margin: 10,
                  }}
                >
                  Carbon Tracking
                </Text>

                <View
                  style={{
                    borderWidth: 1,
                    marginStart: 10,
                    marginEnd: 10,
                    paddingLeft: 10,
                    borderLeftWidth: 10,
                    borderLeftColor: "#00BBA1",
                    borderRightColor: Colors.lightGray,
                    borderTopColor: Colors.lightGray,
                    borderBottomColor: Colors.lightGray,
                  }}
                >
                  <View style={{ marginTop: 10 }}>
                    <Text
                      style={{
                        fontSize: wp("4%"),
                        color: Colors.expiredColorObject,
                      }}
                    >
                      {Strings.addDR.origin}
                    </Text>

                    <TouchableOpacity onPress={this.toggleModal}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          borderColor: Colors.mediumGray,
                          paddingTop: 20,
                          paddingBottom: 20,
                        }}
                      >
                        <Text
                          style={{
                            paddingLeft: 10,
                            fontSize: wp("4%"),
                            color: this.state.textFieldValue
                              ? "#000"
                              : Colors.expiredColorObject,
                          }}
                        >
                          {this.state.textFieldValue || "Origination Address"}
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <Modal
                      animationType="slide"
                      transparent={true}
                      visible={this.state.isModalVisible}
                      onRequestClose={this.toggleModal}
                      style={{
                        margin: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.3)",
                        alignItems: "center",
                      }}
                    >
                      <View
                        style={{
                          width: "90%",
                          height: "70%",
                          backgroundColor: "#fff",
                          borderRadius: 10,
                          padding: 20,
                        }}
                      >
                        <View style={{ flexDirection: "row" }}>
                          <Text
                            style={{
                              fontSize: wp("4%"),
                              color: Colors.expiredColorObject,
                            }}
                          >
                            {Strings.addDR.origin}
                          </Text>
                          <View>
                            <TouchableOpacity onPress={this.toggleModal}>
                              <Image
                                source={Images.close_icon}
                                style={{
                                  width: 30,
                                  height: 30,

                                  marginLeft: "60%",
                                }}
                              />
                            </TouchableOpacity>
                          </View>
                        </View>

                        <View style={{ marginTop: 10 }}>
                          <TextInput
                            value={this.state.textFieldValue}
                            placeholder="Enter Address"
                            style={{
                              fontSize: 14,
                              height: 60,
                              borderBottomWidth: 1,
                              borderBottomColor: Colors.expiredColorObject,
                            }}
                            multiline={true}
                            scrollEnabled={true}
                            numberOfLines={4}
                            onChangeText={(text) => {
                              this.setState({ textFieldValue: text });
                              this.getPlacePrediction(text);
                            }}
                          />
                        </View>

                        {this.state.showAutoComplete && (
                          <ScrollView
                            bounces={true}
                            onScroll={({ nativeEvent }) => {
                              const isCloseToBottom =
                                nativeEvent.layoutMeasurement.height +
                                nativeEvent.contentOffset.y >=
                                nativeEvent.contentSize.height;
                              if (isCloseToBottom) {
                                Keyboard.dismiss();
                              }
                            }}
                            scrollEventThrottle={400}
                          >
                            {this.state.predictionList.map((item) => (
                              <View key={item.place_id}>
                                <TouchableOpacity
                                  onPress={() => this.onSelectPlace(item)}
                                >
                                  <View
                                    style={{
                                      paddingTop: 5,
                                      paddingBottom: 5,
                                      marginStart: 20,
                                      marginEnd: 20,
                                    }}
                                  >
                                    <Text style={{ fontSize: 14 }}>
                                      {item.description}
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                                <View
                                  style={{
                                    height: 1,
                                    backgroundColor: Colors.mediumGray,
                                    marginVertical: 10,
                                    marginStart: 20,
                                    marginEnd: 20,
                                  }}
                                />
                              </View>
                            ))}
                          </ScrollView>
                        )}
                      </View>
                    </Modal>
                  </View>

                  <TextField
                    styles={{ paddingRight: 20 }}
                    attrName={Strings.addDR.vehicleType}
                    title={Strings.addDR.vehicleType}
                    value={this.state.vehicleType}
                    updateMasterState={this.updateMasterState}
                    showButton={true}
                    onPress={() => {
                      Keyboard.dismiss();
                      this.setState({ vehicleModelVisible: true });
                    }}
                    imageSource={Images.downArr}
                    placeholder={"Choose vehicle type"}
                    textInputStyles={{
                      color: Colors.black,
                      fontSize: 14,
                    }}
                  />
                </View>

                {editDR == false ? (
                  <RecurrenceComponent
                    fromDate={calSelectedDate}
                    recurrenceName={recurrence}
                    onChangeRecrrenceName={(item) =>
                      this.setState({ recurrence: item })
                    }
                    timeValue={times}
                    onChangeTimeValue={(item) => this.setState({ times: item })}
                    monthFirstCheck={isMonthFirstCheck}
                    onChangeMonthFirstCheck={(item) =>
                      this.setState({ isMonthFirstCheck: item })
                    }
                    monthSecondCheck={isMonthSecondCheck}
                    onChangeMonthSecondCheck={(item) =>
                      this.setState({ isMonthSecondCheck: item })
                    }
                    monthThirdCheck={isMonthThirdCheck}
                    onChangeMonthThirdCheck={(item) =>
                      this.setState({ isMonthThirdCheck: item })
                    }
                    onMonthDay={monthlyDay}
                    onChangeMonthDay={(item) =>
                      this.setState({ monthlyDay: item })
                    }
                    onMonthLastDay={monthlyLastDay}
                    onChangeMonthLastDay={(item) =>
                      this.setState({ monthlyLastDay: item })
                    }
                    yearlyFirstCheck={isYearFirstCheck}
                    onChangeYearlyFirstCheck={(item) =>
                      this.setState({ isYearFirstCheck: item })
                    }
                    yearlySecondCheck={isYearSecondCheck}
                    onChangeYearlySecondCheck={(item) =>
                      this.setState({ isYearSecondCheck: item })
                    }
                    yearlyThirdCheck={isYearThirdCheck}
                    onChangeYearlyThirdCheck={(item) =>
                      this.setState({ isYearThirdCheck: item })
                    }
                    endDateCheck={endDateRecurrence}
                    onChangeEndDate={(item) =>
                      this.setState({ endDateRecurrence: item })
                    }
                    selectedDayCheck={selectedDaysOccurs}
                    onChangeSelectedDay={(item) =>
                      this.setState({ selectedDaysOccurs: item })
                    }
                    onChangeSelectedDayArray={(item) =>
                      this.setState({ selectedDayArray: item })
                    }
                    listName={listSetName}
                    onChangeListName={(item) =>
                      this.setState({ listSetName: item })
                    }
                    yearListName={yearListSetName}
                    onChangeYearListName={(item) =>
                      this.setState({ yearListSetName: item })
                    }
                    selectedEndDate={selectedEndDateYear}
                    onChangeSelectedEndDate={(item) =>
                      this.setState({ selectedEndDateYear: item })
                    }
                    onFromDateChanged={() =>
                      this.setState({ isFromDate: false })
                    }
                    isFromDateChanged={this.state.isFromDate}
                    recurrenceTypeData={this.state.editDR}
                  />
                ) : editDR == true && this.state.editRequestID != 1 ? (
                  <RecurrenceComponent
                    fromDate={calSelectedDate}
                    recurrenceName={recurrence}
                    onChangeRecrrenceName={(item) =>
                      this.setState({ recurrence: item })
                    }
                    timeValue={times}
                    onChangeTimeValue={(item) => this.setState({ times: item })}
                    monthFirstCheck={isMonthFirstCheck}
                    onChangeMonthFirstCheck={(item) =>
                      this.setState({ isMonthFirstCheck: item })
                    }
                    monthSecondCheck={isMonthSecondCheck}
                    onChangeMonthSecondCheck={(item) =>
                      this.setState({ isMonthSecondCheck: item })
                    }
                    monthThirdCheck={isMonthThirdCheck}
                    onChangeMonthThirdCheck={(item) =>
                      this.setState({ isMonthThirdCheck: item })
                    }
                    onMonthDay={monthlyDay}
                    onChangeMonthDay={(item) =>
                      this.setState({ monthlyDay: item })
                    }
                    onMonthLastDay={monthlyLastDay}
                    onChangeMonthLastDay={(item) =>
                      this.setState({ monthlyLastDay: item })
                    }
                    yearlyFirstCheck={isYearFirstCheck}
                    onChangeYearlyFirstCheck={(item) =>
                      this.setState({ isYearFirstCheck: item })
                    }
                    yearlySecondCheck={isYearSecondCheck}
                    onChangeYearlySecondCheck={(item) =>
                      this.setState({ isYearSecondCheck: item })
                    }
                    yearlyThirdCheck={isYearThirdCheck}
                    onChangeYearlyThirdCheck={(item) =>
                      this.setState({ isYearThirdCheck: item })
                    }
                    endDateCheck={endDateRecurrence}
                    onChangeEndDate={(item) =>
                      this.setState({ endDateRecurrence: item })
                    }
                    selectedDayCheck={selectedDaysOccurs}
                    onChangeSelectedDay={(item) =>
                      this.setState({ selectedDaysOccurs: item })
                    }
                    onChangeSelectedDayArray={(item) =>
                      this.setState({ selectedDayArray: item })
                    }
                    listName={listSetName}
                    onChangeListName={(item) =>
                      this.setState({ listSetName: item })
                    }
                    yearListName={yearListSetName}
                    onChangeYearListName={(item) =>
                      this.setState({ yearListSetName: item })
                    }
                    selectedEndDate={selectedEndDateYear}
                    onChangeSelectedEndDate={(item) =>
                      this.setState({ selectedEndDateYear: item })
                    }
                    onFromDateChanged={() =>
                      this.setState({ isFromDate: false })
                    }
                    isFromDateChanged={this.state.isFromDate}
                    recurrenceTypeData={false}
                    selectedDay={this.state.selectedDay}
                  />
                ) : null}
                {this.bottomContainer()}
              </KeyboardAwareScrollView>
            </View>

            <Dropdown
              data={this.state.equipTypeList}
              title={Strings.addEquipment.type}
              value={this.state.selectedEquipName}
              closeBtn={() => this.setState({ eqipModalVisible: false })}
              onPress={(item) => this.onPressEqipType(item)}
              visible={this.state.eqipModalVisible}
              onbackPress={() => this.setState({ eqipModalVisible: false })}
              container={drStyles.equipmentContainer}
              customMainContainer={drStyles.renderEquipStyle}
              equipTextContainer={drStyles.equipTextStyle}
            />

            <Dropdown
              data={this.state.gateList}
              title={Strings.placeholders.gate}
              value={this.state.selectedGate}
              closeBtn={() => this.setState({ gateModalVisible: false })}
              onPress={(item) => this.onPressGateType(item)}
              visible={this.state.gateModalVisible}
              onbackPress={() => this.setState({ gateModalVisible: false })}
              container={drStyles.equipmentContainer}
              customMainContainer={drStyles.renderEquipStyle}
              equipTextContainer={drStyles.equipTextStyle}
            />

            <Dropdown
              data={this.state.vehicleTypeOptions}
              title={"Vehicle Type"}
              value={this.state.vehicleType}
              closeBtn={() => this.setState({ vehicleModelVisible: false })}
              onPress={(item) => this.onPressVehicleType(item)}
              visible={this.state.vehicleModelVisible}
              onbackPress={() => this.setState({ vehicleModelVisible: false })}
              container={drStyles.equipmentContainer}
              customMainContainer={drStyles.renderEquipStyle}
              equipTextContainer={drStyles.equipTextStyle}
            />

            <Modal
              isVisible={this.state.showInfo}
              onBackdropPress={() => {
                this.setState({ showInfo: false });
              }}
              style={{
                paddingTop: 45,
                paddingBottom: 30,
                margin: 0,
                backgroundColor: "rgba(0, 0, 0, 0.3)",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <View
                style={{
                  backgroundColor: "white",
                  width: wp("90%"),
                  borderRadius: 10,
                }}
              >
                <Text
                  style={{
                    color: Colors.textGray,
                    fontSize: wp("4%"),
                    fontFamily: Fonts.montserratMedium,
                    marginVertical: 15,
                    marginHorizontal: 10,
                  }}
                >
                  {Strings.addDR.info}
                </Text>
              </View>
            </Modal>

            {/* Calender iOS */}

            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showDateModal}
                animationInTiming={500}
                onBackdropPress={() => {
                  this.setState({
                    showDateModal: false,
                  });
                }}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  // minimumDate={this.state.editDR?new Date(1950, 0, 1):this.state.minimumDate}
                  value={this.state.calSelectedDate}
                  //value={}
                  style={{
                    backgroundColor: Colors.white,
                    width: "100%",
                  }}
                  display={"inline"}
                  themeVariant="light"
                  accentColor={Colors.themeColor}
                  onChange={(time, date) => {
                    this.onchngeDate(time, date);
                  }}
                />
                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={styles.datePickerOkContainer}
                    onPress={() => {
                      this.onDatePickerDonePressed();
                    }}
                  >
                    <Text style={styles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Calender Android */}

            {Platform.OS == "android" && this.state.showDateModal && (
              <DateTimePicker
                testID="datePicker"
                // timeZoneOffsetInMinutes={0}
                // minuteInterval={interval}
                // minimumDate={this.state.editDR?new Date(1950, 0, 1):this.state.minimumDate}
                value={this.state.calSelectedDate}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                //mode={mode}
                onChange={(time, date) => {
                  this.onchngeDate(time, date);
                }}
              />
            )}

            {/* Time picker iOS - start time */}

            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showStartTimeModal}
                onBackdropPress={() => {
                  this.setState({
                    showStartTimeModal: false,
                  });
                }}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  mode={"time"}
                  // timeZoneOffsetInMinutes={0}
                  // minuteInterval={interval}
                  value={this.state.calSelectedStartTime}
                  textColor="black"
                  style={{
                    backgroundColor: "#fff",
                    width: "100%",
                  }}
                  //mode={mode}
                  display={"spinner"}
                  onChange={(time, date) => {
                    this.onChangeStart(time, date);
                  }}
                />

                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={styles.datePickerOkContainer}
                    onPress={() => {
                      this.setState({
                        showStartTimeModal: false,
                      });
                    }}
                  >
                    <Text style={styles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Timepicker android - start Time */}

            {Platform.OS == "android" && this.state.showStartTimeModal && (
              <DateTimePicker
                testID="datePicker"
                mode={"time"}
                // timeZoneOffsetInMinutes={0}
                // minuteInterval={interval}
                value={this.state.calSelectedStartTime}
                minimumDate={this.state.minimumDate}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                // mode={mode}
                onChange={(time, date) => {
                  this.onChangeStart(time, date);
                }}
              />
            )}

            {/* Timepicker - ios End Time */}

            {Platform.OS == "ios" && (
              <Modal
                isVisible={this.state.showEndTimeModal}
                onBackdropPress={() => {
                  this.setState({
                    showEndTimeModal: false,
                  });
                }}
                style={{
                  paddingTop: 45,
                  margin: 0,
                  justifyContent: "flex-end",
                }}
              >
                <DateTimePicker
                  testID="datePicker"
                  mode={"time"}
                  // timeZoneOffsetInMinutes={0}
                  // minuteInterval={interval}
                  // minimumDate={this.state.minimumDate}
                  value={this.state.calSelectedEndTime}
                  textColor="black"
                  style={{
                    backgroundColor: "#fff",
                    width: "100%",
                  }}
                  display={"spinner"}
                  // mode={mode}
                  onChange={(time, date) => {
                    this.onChangeEndTime(time, date);
                  }}
                />
                <View>
                  <TouchableOpacity
                    activeOpacity={0.5}
                    style={styles.datePickerOkContainer}
                    onPress={() => {
                      this.setState({
                        showEndTimeModal: false,
                      });
                    }}
                  >
                    <Text style={styles.datePickerOkLabel}>Done</Text>
                  </TouchableOpacity>
                </View>
              </Modal>
            )}

            {/* Timepicker android - End Time */}

            {Platform.OS == "android" && this.state.showEndTimeModal && (
              <DateTimePicker
                testID="datePicker"
                mode={"time"}
                // timeZoneOffsetInMinutes={0}
                // minuteInterval={interval}
                // minimumDate={this.state.minimumDate}
                value={this.state.calSelectedEndTime}
                style={{
                  backgroundColor: "#fff",
                  width: "100%",
                }}
                // mode={mode}
                onChange={(time, date) => {
                  this.onChangeEndTime(time, date);
                }}
              />
            )}

            {this.state.showLoader && (
              <AppLoader viewRef={this.state.showLoader} />
            )}

            {this.state.showToaster && this.state.toastMessage && (
              <Toastpopup
                backPress={() => this.setState({ showToaster: false })}
                toastMessage={this.state.toastMessage}
                type={this.state.toastType}
                container={{ marginBottom: hp("12%") }}
              />
            )}
            {this.state.showCancel && (
              <DeletePop
                title={Strings.popup.cancel}
                desc={Strings.popup.cancel}
                acceptTap={() => {
                  this.setState({ showCancel: false });
                  this.props.cameBack(false);
                  this.props.navigation.goBack();
                }}
                container={{ bottom: 0 }}
                declineTap={() => {
                  this.setState({ showCancel: false });
                }}
              />
            )}
            {this.state.isRecurrence && (
              <Dropdown
                data={listOfRecurrence}
                title={Strings.addNewEvent.recurrence}
                value={""}
                closeBtn={() => this.setState({ isRecurrence: false })}
                onPress={(item) => {
                  this.setState({
                    recurrence: item.name,
                    isRecurrence: false,
                  });
                }}
                visible={this.state.isRecurrence}
                onbackPress={() => this.setState({ isRecurrence: false })}
                container={{ alignItems: "center" }}
                textContainer={{
                  textAlign: "center",
                  marginRight: 20,
                  fontSize: 14,
                }}
              />
            )}
          </SafeAreaView>
        )}
      </>
    );
  }
}

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    deliveryDetailsId,
    updateList,
    responsiblePersonData,
    selected_Company,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    deliveryDetailsId,
    updateList,
    responsiblePersonData,
    selected_Company,
  };
};

const styles = StyleSheet.create({
  root: {
    justifyContent: "center",
    borderRadius: 50,
    backgroundColor: Colors.borderGray,
    paddingHorizontal: 10,
    paddingVertical: 4,
    height: 28,
    marginBottom: 4,
    marginRight: 4,
  },
  container: {
    flexDirection: "row",
    overflow: "hidden",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  text: {
    color: "rgba(0, 0, 0, 0.87)",
  },
  iconWrapper: {
    borderRadius: 50,
    backgroundColor: Colors.lightBorderGray,
    height: 16,
    width: 16,
    overflow: "hidden",
    marginLeft: 4,
    justifyContent: "center",
    alignItems: "center",
  },
  icon: {
    textAlign: "center",
    color: Colors.borderGray,
  },
  iconIOS: {
    fontSize: 14,
  },
  iconAndroid: {
    fontSize: 13,
    lineHeight: 15,
  },
  datePickerOkLabel: {
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 6,
    paddingBottom: 6,
    fontFamily: Fonts.montserratBold,
  },
  datePickerOkContainer: {
    width: "100%",
    alignItems: "center",
    backgroundColor: Colors.white,
    paddingBottom: hp("2%"),
  },
  timeZoneText: {
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    marginLeft: 20,
  },
  mandatory: {
    color: Colors.themeColor,
  },
  dropDownPlaceHolderStyle: {
    color: Colors.placeholder,
    fontSize: 14,
    marginLeft: -5,
  },
  dropDownPickerStyle: {
    backgroundColor: Colors.white,
    width: wp("90%"),
    borderColor: Colors.borderTransparent,
    borderBottomColor: Colors.placeholder,
    alignSelf: "center",
    height: hp("5%"),
  },
  dropDownContainer: {
    height: hp("6%"),
    marginTop: 6,
    marginBottom: 20,
  },
  dropDownItemStyle: {
    justifyContent: "flex-start",
  },
  arrowDownStyle: {
    alignSelf: "flex-end",
  },
  dropDownListStyle: {
    backgroundColor: Colors.white,
    width: "90%",
    alignSelf: "center",
  },
  selectedDropDown: {
    color: Colors.black,
    marginLeft: -5,
  },
  textStyleEdited: {
    color: Colors.inlineGrey,
    fontSize: 14,
  },
  textStyle: {
    color: Colors.black,
    fontSize: 14,
  },
  timeZoneValue: {
    color: Colors.black,
    fontSize: 14,
    marginLeft: 25,
    paddingTop: 10,
  },
});

export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    cameBack,
    editData,
    updateList,
    refreshDashboard,
    refreshDeliveryList,
    refreshCalendar,
  }),
  withBackHandler,
)(AddNewDR);

const drStyles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("78%"),
    fontSize: wp("5.6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratSemiBold,
  },
  memberContainer: {
    width: wp("90%"),
    height: hp("10%"),
    alignSelf: "center",
    marginTop: hp("1%"),
    justifyContent: "center",
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  idText: {
    width: wp("90%"),
    height: hp("5%"),
    backgroundColor: Colors.greyContainer,
    marginTop: wp("1%"),
    padding: hp("1%"),
    paddingLeft: 15,
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("4%"),
  },
  escortContainer: {
    width: "90%",
    alignSelf: "center",
    marginTop: hp("4%"),
    justifyContent: "space-between",
    flexDirection: "row",
  },
  escortText: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
  },
  bottomContainer: {
    width: wp("90%"),
    flexDirection: "row",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: hp("4%"),
    marginTop: hp("8%"),
  },
  cancel: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonGray,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  root: {
    justifyContent: "center",
    borderRadius: 50,
    backgroundColor: Colors.borderGray,
    paddingHorizontal: 10,
    paddingVertical: 4,
    height: 28,
    marginBottom: 4,
    marginRight: 4,
  },
  equipmentContainer: {
    height: hp("4%"),
    paddingBottom: 5,
  },
  renderEquipStyle: {
    marginBottom: 10,
  },
  equipTextStyle: {
    width: "100%",
    fontSize: 16,
    paddingTop: 2,
  },
  submitButton: {
    flex: 1,
    backgroundColor: Colors.pendingEventColor,
    padding: 10,
    borderRadius: 25,
    // marginLeft: 10,
    alignItems: "center",
  },
  submitButtonText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: 16,
    paddingLeft: 70,
    paddingRight: 70,
  },
});
