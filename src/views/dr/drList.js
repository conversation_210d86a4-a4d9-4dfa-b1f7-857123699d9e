import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  Keyboard,
  ActivityIndicator,
  TouchableOpacity,
  Platform,
} from "react-native";

import {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  clickAdd,
  editData,
  onTapSearch,
  storeRole,
  goToVoid,
  onTapDetail,
  showDeliverdetailsid,
  updateList,
  refreshDeliveryList,
  refreshDashboard,
  updateDrList,
  toggleAddAllRequest,
  lastCraneId,
  showCraneRequestId,
  editCraneRequest,
  toggleAssociatedWithCrane,
  onPressConcreteDetail,
  concreteDetailsID,
  lastConcreteId,
  editConcreteRequest,
  showConcreteRequestId,
  selectedConcreteLocationsData,
  selectedConcreteMixDesignsData,
  selectedConcretePlacementsData,
  selectedConcretePumpSizesData,
  getDrPage,
} from "../../actions/postAction";
import { connect } from "react-redux";

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import { Images, Strings, Fonts, Colors } from "../../common";

import {
  GET_DR_LIST,
  GET_IN_LIST,
  VOID_DR,
  CREATE_VOID_INS,
  GET_NEW_COMPANIES,
  GET_GATE_LIST,
  LIST_ALL_MEMBER,
  CRANE_DELIVERY_LIST,
  GET_EQUIP_LIST,
  ADD_VOID_CRANE,
  GET_CONCRETE_REQUEST,
  ADD_VOID_CONCRETE,
  DELETE_CONCRETE,
  CONCRETE_DROPDOWN_DETAILS,
  GET_PROJECT_ROLE,
} from "../../api/Constants";
import {
  getEquipList,
  addVoid,
  getNewCompanyList,
  getGateList,
  getAllMemberList,
  getCraneDeliveryList,
  getConcreteList,
  addVoidConcrete,
  deleteConcreteList,
  getConcreteDropDownDetails,
  getprojectRole,
} from "../../api/Api";

import {
  AppView,
  AppLoader,
  Alert,
  Toastpopup,
  DeletePop,
  TextField,
  DRCard,
} from "../../components";
import ConcreteCard from "../../components/cards/ConcreteCard";
import Modal from "react-native-modal";
import _ from "lodash";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { trackScreen, trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent } from "../../MixPanel/MixPanel";
import DateTimePicker from "@react-native-community/datetimepicker";
import moment from "moment";
import Dropdown from "../../components/dropdown/dropdown";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import { TabView, TabBar } from 'react-native-tab-view';
import { Dimensions } from 'react-native';

let DROPDOWNOPTIONS = [
  { id: "Edit", image: Images.edit_blue, name: "Edit" },
  { id: "Void", image: Images.void, name: "Void" },
];
let selectedDate = new Date();

const inspection_type = [
  { type: "Material" },
  { type: "Quality Control" },
  { type: "Special Inspection" },
  { type: "Equipment" },
  { type: "Safety" },
  { type: "Other" },
];

const inspection_Status = [
  { status: "Pass" },
  { status: "Fail" },
]

const inspectionTypeOptions = inspection_type.map(item => ({
  label: item.type,
  value: item.type,
  name: item.type,
}));

const inspectionStatusOptions = inspection_Status.map(item => ({
  label: item.status,
  value: item.status,
  name: item.status,
}));

class DrList extends Component {
  constructor(props) {
    super(props);
    this.searchList = _.debounce(this.searchList, 1000);
    this.state = {
      showLoader: false,
      showToaster: false,
      showAlert: false,
      toastMessage: "",
      toastType: "error",
      refreshing: false,
      lastId: 0,
      showFilter: false,
      rolelist: [],
      searchText: "",
      selectedRole: null,
      selectedCompany: null,
      selectedCompanyId: 0,
      selectedRoleId: 0,
      drList: [],
      inspectionList: [],
      searchbarShow: false,
      selectedResponsibleNameId: 0,
      selectedGateNameId: 0,
      selectedEquipNameId: 0,
      selectedStatusId: 0,
      descriptionFilter: "",
      companyFilterList: [],
      selectedCompanyName: null,
      inspectionType: null,
      inspectionTypeOptions: inspectionTypeOptions,
      inspectionStatus: null,
      inspectionStatusOptions: inspectionStatusOptions,
      responiblePersonList: [],
      selectedResponsibleName: null,
      gateList: [],
      selectedGateName: null,
      equipmentList: [],
      selectedEquipmentName: null,
      statusList: [
        {
          label: "Approved",
          value: "Approved",
          id: "1",
          name: "Approved",
        },
        {
          label: "Declined",
          value: "Declined",
          id: "2",
          name: "Declined",
        },
        {
          label: "Delivered",
          value: "Delivered",
          id: "3",
          name: "Delivered",
        },
        {
          label: "Pending",
          value: "Pending",
          id: "4",
          name: "Pending",
        },
      ],

      // InspectionStatusList: [
      //   {
      //     label: "Approved",
      //     value: "Approved",
      //     id: "1",
      //     name:"Approved",
      //   },
      //   {
      //     label: "Declined",
      //     value: "Declined",
      //     id: "2",
      //     name:"Declined",
      //   },
      //   {
      //     label: "Completed",
      //     value: "Completed",
      //     id: "3",
      //     name:"Completed",
      //   },
      //   {
      //     label: "Pending",
      //     value: "Pending",
      //     id: "4",
      //     name:"Pending",
      //   },
      // ],
      selectedStatusName: null,
      selectedEquipName: null,
      totalCount: 0,
      craneList: [],
      totalCrane: 0,
      lastCraneId: 0,
      showCraneNoData: false,
      isDeliveryRequest: true,
      pickFrom: "",
      pickTo: "",
      concreteList: [],
      showConcreteNoData: false,
      currentScreen: 'Delivery',
      locationsList: [],
      selectedLocationName: null,
      selectedLocationId: 0,
      selectedMixDesignName: null,
      selectedMixDesignId: 0,
      concreteSuppliersList: [],
      mixDesignList: [],
      selectedConcreteSupplier: null,
      selectedSupplierId: 0,
      orderNumbersList: [],
      selectedOrderId: 0,
      orderNumber: 0,
      mixpanelParam: {
        ProjectName: this.props.projectDetails.projectName,
        CompanyName: this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName: this.props.userDetails.firstName,
      },
      locationModalVisible: false,
      supplierModalVisible: false,
      mixDesignDropdown: false,
      selectStatusDropdown: false,
      companyModalVisible: false,
      responisblePersonModal: false,
      gateModalVisible: false,
      equipModalVisible: false,
      statusModalVisible: false,
      isDeliveryDate: false,
      dateFilter: '',
      isNetworkCheck: false,
      id: 0,
      isAccess: true,
      colorData: [],
      isDefaultColor: false,
      tabIndex: 0,
      tabRoutes: [
        { key: 'deliveries', title: 'Deliveries' },
        { key: 'crane', title: 'Crane' },
        { key: 'concrete', title: 'Concrete' },
        { key: 'inspection', title: 'Inspection' },
      ],
    };
    this.onPressInspectionType = this.onPressInspectionType.bind(this);
    this.onPressInspectionStatus = this.onPressInspectionStatus.bind(this);
    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;
  }

  onPressInspectionType(item) {
    this.setState({
      inspectionType: item.value,
      inspectionModelVisible: false,
    });
  }

  onPressInspectionStatus(item) {
    this.setState({
      inspectionStatus: item.value,
      inspectionStatusModelVisible: false,
    });
  }

  componentDidMount() {
    if (Platform.OS === 'ios') {
      this.networkCheck();
    } else {
      this.props.toggleAddAllRequest("Delivery");
      this.renderInitial();
    }
    setTimeout(() => this?.scrollableTabView?.goToPage(this?.props?.isCurrentDRPage ? this?.props?.isCurrentDRPage : 0), 300);
  }
  componentWillUnmount() {
    this.props.getDrPage(0)
  }

  networkCheck = () => {
    NetInfo.addEventListener(state => {
      if (!state.isConnected && !state.isInternetReachable) {
        this.setState({ isNetworkCheck: true })
      } else {
        this.setState({ isNetworkCheck: false })
        this.props.toggleAddAllRequest("Delivery");
        this.renderInitial();
      }
    })
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }

    if (nextProps.refreshDelivery === true) {
      this.setState({
        showLoader: true,
      });
      this.page_number = 1;
      this.renderInitial();
    }
    if (nextProps.needToRefreshDrList) {
      this.renderInitial();
    }
  }

  renderInitial = async () => {
    this.props.refreshDeliveryList(false, "drList_renderInitial");
    this.setState({ showLoader: true });
    this.page_number = 1;
    await this.getProjectRole(this.props.projectDetails.id);
    await this.getDropdownDetails();
    await this.getdrlist();
    await this.getCraneList();
    await this.getConcreteRequest();
    await this.getCompanyList();
    await this.getResponsibleList();
    await this.getGateList();
    await this.getEquipmentList();
    await this.getInspectionList();
  };


  getProjectRole = (data) => {
    if (data) {
      getprojectRole(
        GET_PROJECT_ROLE +
        data +
        `/${this.props.projectDetails.ParentCompany.id}`,
        {},
        () => null,
        (response) => {
          if (response.data) {
            this.setState(
              {
                id: response.data.data.id,
              },
            );
          }
        }
      );
    }
  }

  getEquipmentList = () => {
    let param = { isFilter: true, showActivatedAlone: true }
    let url = `${GET_EQUIP_LIST}${this.props.projectDetails.id}/20/${this.page_number}/${this.props.projectDetails.ParentCompany.id}`;
    getEquipList(
      url,
      param,
      () => null,
      (equipResp) => {


        if (equipResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (equipResp.status) {
          if (equipResp.data.data.length !== 0) {
            let equipTypelist = [];
            for (let item of equipResp.data.data.rows) {
              if (item.id) {
                equipTypelist.push({
                  id: item.id,
                  value: item.equipmentName,
                  label: item.equipmentName,
                  name: item.equipmentName,
                });
              }
            }
            const newArray = equipTypelist.reduce((arr, item) => {
              const exists = !!arr.find((x) => x.value === item.value);
              if (!exists) {
                arr.push(item);
              }
              return arr;
            }, []);

            this.setState({
              equipmentList: newArray,
            });
          }
        }
      }
    );
  };

  getGateList = () => {
    let param = { isFilter: true, showActivatedAlone: true, }
    getGateList(
      GET_GATE_LIST +
      this.props.projectDetails.id +
      "/0/0/" +
      this.props.projectDetails.ParentCompany.id,
      param,
      () => null,
      (gateResp) => {
        if (gateResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (gateResp.status) {
          if (gateResp.data.data.length !== 0) {
            let gateList = [];

            for (let item of gateResp.data.data) {
              gateList.push({
                id: item.id,
                value: item.gateName,
                label: item.gateName,
                name: item.gateName,
              });
            }

            this.setState({
              gateList: gateList,
            });
          }
        }
      }
    );
  };

  getResponsibleList = () => {
    getAllMemberList(
      `${LIST_ALL_MEMBER}${this.props.projectDetails.id}/${this.props.projectDetails.ParentCompany.id}`,
      {},
      () => null,
      (memResp) => {
        if (memResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (memResp.status) {
          if (memResp.data.message == "Member listed Successfully.") {
            this.storeMemberList(memResp.data.data);
          }
        }
      }
    );
  };

  getCompanyList = () => {
    getNewCompanyList(

      `${GET_NEW_COMPANIES}${this.props.projectDetails.id}/${this.props.projectDetails.ParentCompany.id}`,
      {},
      () => null,
      (compResp) => {
        if (compResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (compResp.status) {
          if (compResp.data.message == "Company list.") {
            this.storeCompanyList(compResp.data.data);
          }
        }
      }
    );
  };

  storeMemberList = (data) => {
    let memberList = [];
    for (let item of data) {
      if (item.User.firstName != null) {
        memberList.push({
          label: item.User.firstName + " " + item.User.lastName + " (" + item.User.email + ")",
          value: item.User.email,
          id: item.id,
          name: item.User.email,
        });
      } else {
        memberList.push({
          label: item.User.email,
          value: item.User.email,
          id: item.id,
          name: item.User.email,
        });
      }
    }
    this.setState({ responiblePersonList: memberList });
  };

  storeCompanyList = (data) => {
    let companyList = [];
    for (let item of data) {
      companyList.push({
        label: item.companyName,
        value: item.companyName,
        id: item.id,
        name: item.companyName,
      });
    }
    this.setState({ companyFilterList: companyList });
  };

  getDropdownDetails = () => {
    let url = `${CONCRETE_DROPDOWN_DETAILS}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`;
    try {
      getConcreteDropDownDetails(
        url,
        {},
        () => null,
        (response) => {
          this.setState({
            showLoader: false,
          });
          if (response.toString() == Strings.errors.timeout) {
            this.setState(
              {
                showToaster: true,
                toastMessage: Strings.errors.checkInternet,
                type: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                    disableSubmit: false,
                  });
                }, 2000);
              }
            );
          } else if (response.status) {
            if (response.status == 200 || response.status == 201) {
              this.storeDropDownList(response.data.data);
            } else if (response.data.message.message) {
              this.showToaster("error", response.data.message.message);
            } else {
              this.showToaster("error", response.data.message);
            }
          } else {
            this.showToaster("error", response.toString());
          }
        }
      );
    } catch (e) {
    }
  };

  storeDropDownList = (data) => {
    let locList = [];
    if (data.locationDropdown != null && data.locationDropdown != undefined && data.locationDropdown.length > 0) {
      for (let item of data.locationDropdown) {
        locList.push({
          label: item.location,
          value: item.location,
          id: item.id,
          name: item.location,
        });
      }
      this.setState({ locationsList: locList });
    }

    let supplierList = [];
    if (data.concreteSupplierDropdown != null && data.concreteSupplierDropdown != undefined && data.concreteSupplierDropdown.length > 0) {
      for (let item of data.concreteSupplierDropdown) {
        supplierList.push({
          label: item.companyName,
          value: item.companyName,
          id: item.id,
          name: item.companyName,
        });
      }
      this.setState({ concreteSuppliersList: supplierList });
    }

    let mixDesignList = [];
    if (data.mixDesignDropdown != null && data.mixDesignDropdown != undefined && data.mixDesignDropdown.length > 0) {
      for (let item of data.mixDesignDropdown) {
        mixDesignList.push({
          label: item.mixDesign,
          value: item.mixDesign,
          id: item.id,
          name: item.mixDesign,
        });
      }
      this.setState({ mixDesignList: mixDesignList });
    }
  };

  clearSearch = () => {
    this.setState({
      clearSearch: false,
      searchText: "",
      showIndicator: true,
    });
    this.searchList();
  };

  hideToast = () => {
    setTimeout(() => {
      this.setState({ showToaster: false });
    }, 2000);
  };
  getConcreteRequest = () => {
    this.setState({
      showLoader: true,
    });
    let param = {};
    if (this.state.filter == true) {
      param = {
        locationFilter: this.state.selectedLocationName,
        descriptionFilter: this.state.descriptionFilter,
        statusFilter: this.state.selectedStatusName,
        orderNumberFilter: this.state.orderNumber,
        mixDesignFilter: this.state.selectedMixDesignName,
        concreteSupplierFilter: this.state.selectedConcreteSupplier,
        search: this.state.searchText,
        sort: "DESC",
        sortByField: "",
        ParentCompanyId: this.props.projectDetails.ParentCompany.id
      };
    } else {
      param = {
        search: this.state.searchText,
        sort: "DESC",
      };
    }
    let url = `${GET_CONCRETE_REQUEST}/${this.props.projectDetails.id}/20/${this.page_number}/0`;
    getConcreteList(url, param, () => null, (response) => {
      this.setState({
        showIndicator: false,
        clearSearch: this.state.searchText ? true : false,
        showLoader: false,
        showConcreteNoData: false,
      });
      if (response.toString() == Strings.errors.timeout) {
        this.setState(
          {
            showToaster: true,
            toastMessage: Strings.errors.checkInternet,
            type: "error",
          },
          () => this.hideToast()
        );
      } else if (response.status) {
        if (response.status == 200) {
          const colorData = JSON.parse(response.data.statusData.statusColorCode)
          this.setState({ showLoader: false });
          let concreteData = this.state.concreteList;
          if (this.page_number == 1) {
            if (response.data.data.count == 0) {
              this.setState({ showConcreteNoData: true, concreteList: [] });
            } else {
              this.setState({
                concreteList: response.data.data.rows,
                totalConcrete: response.data.data.count,
                colorData: colorData,
                isDefaultColor: response.data.statusData.isDefaultColor,
              });
              let modifiedConcreteList = []

              for (let index = 0; index < this.state.concreteList.length; index++) {
                const element = this.state.concreteList[index].memberDetails;
                var memberArray = element.map((e) => e.Member.id)
                var isMemberConcreteID = memberArray.includes(this.state.id)
                if (this.props.projectRoleId == 3 || this.props.projectRoleId == 2) {
                  var memberDRCard = { ...this.state.concreteList[index], isMemberAccess: true }
                } else {
                  var memberDRCard = { ...this.state.concreteList[index], isMemberAccess: isMemberConcreteID }
                }
                modifiedConcreteList.push(memberDRCard)
              }
              this.setState({ concreteList: modifiedConcreteList })
            }
          } else {
            let data1 = response.data.data.rows;
            this.setState({
              concreteList: concreteData.concat(data1),
              totalConcrete: response.data.data.count,
              colorData: colorData,
              isDefaultColor: response.data.statusData.isDefaultColor,
            });
            let modifiedConcreteList = []
            for (let index = 0; index < this.state.concreteList.length; index++) {
              const element = this.state.concreteList[index].memberDetails;
              var membercraneArray = element.map((e) => e.Member.id)
              var isMemberConcreteIDPagination = membercraneArray.includes(this.state.id)
              if (this.props.projectRoleId == 3 || this.props.projectRoleId == 2) {
                var memberDRCard1 = { ...this.state.concreteList[index], isMemberAccess: true }
              } else {
                var memberDRCard1 = { ...this.state.concreteList[index], isMemberAccess: isMemberConcreteIDPagination }
              }
              modifiedConcreteList.push(memberDRCard1)
            }
            this.setState({ concreteList: modifiedConcreteList })
          }
        } else if (response.status == 400) {
        } else if (response.status == 401) {
        }
      }
    })
  }

  getCraneList = () => {
    this.setState({
      showLoader: true,
    });
    let param = {};

    if (this.state.filter == true) {
      param = {
        companyFilter: this.state.selectedCompanyId,
        descriptionFilter: this.state.descriptionFilter,
        memberFilter: this.state.selectedResponsibleNameId,
        equipmentFilter: this.state.selectedEquipName,
        statusFilter: this.state.selectedStatusName,
        search: this.state.searchText,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
        pickFrom: this.state.pickFrom,
        pickTo: this.state.pickTo,
        dateFilter: this.state.dateFilter == '' ? '' : moment(this.state.dateFilter).format('YYYY-MM-DD'),
      };
    } else {
      param = {
        search: this.state.searchText,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
      };
    }
    let url = `${CRANE_DELIVERY_LIST}${this.props.projectDetails.id}/20/${this.page_number}/0`;
    getCraneDeliveryList(
      url,
      param,
      () => null,
      (response) => {
        this.setState({
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          showLoader: false,
          showCraneNoData: false,
        });
        if (response.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastMessage: Strings.errors.checkInternet,
              type: "error",
            },
            () => this.hideToast()
          );
        } else if (response.status) {
          if (response.status == 200) {
            const colorData = JSON.parse(response.data.statusData.statusColorCode)
            this.setState({ showLoader: false });
            let craneData = this.state.craneList;
            this.props.lastCraneId(response.data.lastId.CraneRequestId);
            if (this.page_number == 1) {
              if (response.data.data.count == 0) {
                this.setState({ showCraneNoData: true, craneList: [] });
              } else {
                this.setState({
                  craneList: response.data.data.rows,
                  totalCrane: response.data.data.count,
                  lastCraneId: response.data.lastId.CraneRequestId,
                  colorData: colorData,
                  isDefaultColor: response.data.statusData.isDefaultColor,
                });
                let modifiedCraneList = []
                for (let index = 0; index < this.state.craneList.length; index++) {
                  const element = this.state.craneList[index].memberDetails;
                  var craneDetailsArray = element.map((e) => e.Member.id)
                  var isCraneID = craneDetailsArray.includes(this.state.id)
                  if (this.props.projectRoleId == 3 || this.props.projectRoleId == 2) {
                    var memberDRCard = { ...this.state.craneList[index], isMemberAccess: true }
                  } else {
                    var memberDRCard = { ...this.state.craneList[index], isMemberAccess: isCraneID }
                  }
                  modifiedCraneList.push(memberDRCard)
                }

                this.setState({ craneList: modifiedCraneList })
              }
            } else {
              let data1 = response.data.data.rows;
              this.setState({
                craneList: craneData.concat(data1),
                totalCrane: response.data.data.count,
                lastCraneId: response.data.lastId.CraneRequestId,
                colorData: colorData,
                isDefaultColor: response.data.statusData.isDefaultColor,
              });
              let modifiedCraneList = []
              for (let index = 0; index < this.state.craneList.length; index++) {
                const element = this.state.craneList[index].memberDetails;
                var craneDetailsArray = element.map((e) => e.Member.id)
                var isCraneID1 = craneDetailsArray.includes(this.state.id)
                if (this.props.projectRoleId == 3 || this.props.projectRoleId == 2) {
                  var memberDRCard = { ...this.state.craneList[index], isMemberAccess: true }
                } else {
                  var memberDRCard = { ...this.state.craneList[index], isMemberAccess: isCraneID1 }
                }
                modifiedCraneList.push(memberDRCard)
              }

              this.setState({ craneList: modifiedCraneList })
            }
          } else if (response.status == 400) {
          } else if (response.status == 401) {
          }
        }
      }
    );
  };
  async getdrlist(from) {
    let param = {};

    if (this.state.filter == true) {
      param = {
        companyFilter: this.state.selectedCompanyId,
        descriptionFilter: this.state.descriptionFilter,
        memberFilter: this.state.selectedResponsibleNameId,
        gateFilter: this.state.selectedGateNameId,
        equipmentFilter: this.state.selectedEquipNameId,
        statusFilter: this.state.selectedStatusName,
        search: this.state.searchText,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
        pickFrom: this.state.pickFrom,
        pickTo: this.state.pickTo,
        queuedNdr: false,
        dateFilter: this.state.dateFilter == '' ? '' : moment(this.state.dateFilter).format('YYYY-MM-DD'),
      };
    } else {
      param = {
        search: this.state.searchText,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,

        queuedNdr: false,
      };
    }
    let url = `${GET_DR_LIST}${this.props.projectDetails.id}/20/${this.page_number}/0`;

    await getEquipList(
      url,
      param,
      () => {
      },
      (response) => {

        this.setState(
          {
            showIndicator: false,
            clearSearch: this.state.searchText ? true : false,
            showLoader: false,
            showNoData: false,
          },
          () => {
            if (from == "void") {
              this.showToaster(
                "success",
                "Delivery request marked as void Successfully"
              );
            }
          }
        );

        if (response.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastMessage: Strings.errors.checkInternet,
              type: "error",
            },
            () => this.hideToast()
          );
        } else if (response.status) {
          if (response.status == 200 || response.status == 201) {
            this.setState({ showLoader: false });
            let data = this.state.drList;
            this.props.storeLastid(response.data.lastId.DeliveryId);
            const colorData = JSON.parse(response.data.statusData.statusColorCode)

            if (this.page_number == 1) {
              if (response.data.data.count == 0) {
                this.setState({ showNoData: true, drList: [] });
              } else {
                this.setState({
                  drList: response.data.data.rows,
                  totalCount: response.data.data.count,
                  lastId: response.data.lastId.DeliveryId,
                  colorData: colorData,
                  isDefaultColor: response.data.statusData.isDefaultColor,
                });

                let modifiedDRList = []
                for (let index = 0; index < this.state.drList.length; index++) {
                  const element = this.state.drList[index].memberDetails;
                  var memberDRArray = element.map((e) => e.Member.id)
                  var isMemberID = memberDRArray.includes(this.state.id)
                  if (this.props.projectRoleId == 3 || this.props.projectRoleId == 2) {
                    var memberDRCard = { ...this.state.drList[index], isMemberAccess: true }
                  } else {
                    var memberDRCard = { ...this.state.drList[index], isMemberAccess: isMemberID }
                  }
                  modifiedDRList.push(memberDRCard)
                }

                this.setState({ drList: modifiedDRList })
              }
            } else {
              let data1 = response.data.data.rows;
              this.setState({
                drList: data.concat(data1),
                totalCount: response.data.data.count,
                lastId: response.data.lastId.DeliveryId,
                colorData: colorData,
                isDefaultColor: response.data.statusData.isDefaultColor,
              });
              let modifiedDRList = []
              for (let index = 0; index < this.state.drList.length; index++) {
                const element = this.state.drList[index].memberDetails;
                var memberArrayPagination = element.map((e) => e.Member.id)
                var isMemberIDPagination = memberArrayPagination.includes(this.state.id)
                if (this.props.projectRoleId == 3 || this.props.projectRoleId == 2) {
                  var memberDRCard = { ...this.state.drList[index], isMemberAccess: true }
                } else {
                  var memberDRCard = { ...this.state.drList[index], isMemberAccess: isMemberIDPagination }
                }
                modifiedDRList.push(memberDRCard)
              }

              this.setState({ drList: modifiedDRList })
            }
          } else if (response.data.message.message) {
            let array = Object.values(response.data.message.details[0]);
            this.showError("error", array.toString());
          } else {
            this.showError("error", response.data.message);
          }
        } else {
          this.showError("error", "Loading... Please wait");
        }
        this.props.updateDrList(false);
      }
    );
  }

  async getInspectionList(from) {
    let param = {};

    if (this.state.filter == true) {
      param = {
        companyFilter: this.state.selectedCompanyId,
        descriptionFilter: this.state.descriptionFilter,
        memberFilter: this.state.selectedResponsibleNameId,
        gateFilter: this.state.selectedGateNameId,
        equipmentFilter: this.state.selectedEquipNameId,
        statusFilter: this.state.selectedStatusName,
        search: this.state.searchText,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
        pickFrom: this.state.pickFrom,
        pickTo: this.state.pickTo,
        inspectionTypeFilter: this.state.inspectionType,
        inspectionStatusFilter: this.state.inspectionStatus,
        queuedNdr: false,
        dateFilter: this.state.dateFilter == '' ? '' : moment(this.state.dateFilter).format('YYYY-MM-DD'),
      };
    }
    else {
      param = {
        search: this.state.searchText,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
        queuedNdr: false,
      };
    }
    let url = `${GET_IN_LIST}${this.props.projectDetails.id}/20/${this.page_number}/0`;



    await getEquipList(
      url,
      param,
      () => {

      },
      (response) => {


        this.setState(
          {
            showIndicator: false,
            clearSearch: this.state.searchText ? true : false,
            showLoader: false,
            showNoData: false,
          },
          () => {
            if (from == "void") {
              this.showToaster("success", "Inspection request marked as void successfully");
            }
          }
        );
        if (response.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastMessage: Strings.errors.checkInternet,
              type: "error",
            },
            () => this.hideToast()
          );
        } else if (response.status) {
          if (response.status == 200 || response.status == 201) {
            this.setState({ showLoader: false });
            let data = this.state.inspectionList;
            this.props.storeLastid(response.data.lastId.InspectionId);
            const colorData = JSON.parse(response.data.statusData.statusColorCode);


            if (this.page_number == 1) {
              if (response.data.data.count == 0) {
                this.setState({ showNoData: true, inspectionList: [] });
              } else {


                this.setState({


                  inspectionList: response.data.data.rows,
                  totalCount: response.data.data.count,
                  lastId: response.data.lastId.InspectionId,
                  colorData: colorData,
                  isDefaultColor: response.data.statusData.isDefaultColor,
                });

                let modifiedInspectionList = [];
                for (let index = 0; index < this.state.inspectionList.length; index++) {
                  const element = this.state.inspectionList[index].memberDetails;
                  var memberArray = element.map((e) => e.Member.id);
                  var isMemberID = memberArray.includes(this.state.id);
                  if (this.props.projectRoleId == 3 || this.props.projectRoleId == 2) {
                    var memberInspectionCard = { ...this.state.inspectionList[index], isMemberAccess: true };
                  } else {
                    var memberInspectionCard = { ...this.state.inspectionList[index], isMemberAccess: isMemberID };
                  }
                  modifiedInspectionList.push(memberInspectionCard);
                }

                this.setState({ inspectionList: modifiedInspectionList });
              }
            } else {
              let data1 = response.data.data.rows;
              this.setState({
                inspectionList: data.concat(data1),
                totalCount: response.data.data.count,
                lastId: response.data.lastId.InspectionId,
                colorData: colorData,
                isDefaultColor: response.data.statusData.isDefaultColor,
              });
              let modifiedInspectionList = [];
              for (let index = 0; index < this.state.inspectionList.length; index++) {
                const element = this.state.inspectionList[index].memberDetails;
                var memberArrayPagination = element.map((e) => e.Member.id);
                var isMemberIDPagination = memberArrayPagination.includes(this.state.id);
                if (this.props.projectRoleId == 3 || this.props.projectRoleId == 2) {
                  var memberInspectionCard = { ...this.state.inspectionList[index], isMemberAccess: true };
                } else {
                  var memberInspectionCard = { ...this.state.inspectionList[index], isMemberAccess: isMemberIDPagination };
                }
                modifiedInspectionList.push(memberInspectionCard);
              }

              this.setState({ inspectionList: modifiedInspectionList });
            }
          } else if (response.data.message.message) {
            let array = Object.values(response.data.message.details[0]);
            this.showError("error", array.toString());
          } else {
            this.showError("error", response.data.message);
          }
        } else {
          this.showError("error", "Loading... Please wait");
        }
        this.props.updateInspectionList(false);
      }
    );
  }



  deleteConcrete = (item, index) => {
    this.setState({
      showLoader: true,
    });
    let param = {
      id: item.id,
      ProjectId: this.props.projectDetails.id,
      ParentCompanyId: this.props.projectDetails.ParentCompany.id,
    };

    deleteConcreteList(`${DELETE_CONCRETE}`, param, () => null, async (response) => {
      this.setState({
        showLoader: false,
      });
      if (response.status) {
        if (response.status == 200 || response.status == 201) {
          await this.getConcreteRequest();
          await this.props.refreshDashboard(true);
          this.showToaster(
            "success",
            "Concrete request deleted Successfully"
          );

        } else if (resposne.status == 400) {

          this.showToaster("error", "ValidationError");
        } else {
          this.showToaster("error", "ValidationError");
        }
      } else {
        this.showToaster("error", response.toString());
      }
    })
  }
  showError = (type, message) => {
    if (message) {
      this.setState(
        {
          showToaster: true,
          toastType: type,
          toastMessage: message,
          showLoader: false,
        },
        () => this.hideToast()
      );
    }

  };

  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        drList: [],
        totalCount: 0,
        showLoader: true,
        //load: true,
      },
      () => {
        this.getdrlist();
      }
    );
  };
  _onResetInspection = () => {
    this.page_number = 1;
    this.setState(
      {
        inspectionList: [],
        totalCount: 0,
        showLoader: true,
        //load: true,
      },
      () => {
        this.getInspectionList();
      }
    );
  };
  _onResetCrane = () => {
    this.page_number = 1;
    this.setState(
      {
        craneList: [],
        totalCrane: 0,
        showLoader: true,
        //load: true,
      },
      () => {
        this.getCraneList();
      }
    );
  };
  _onResetConcrete = () => {
    this.page_number = 1;
    this.setState(
      {
        concreteList: [],
        totalConcrete: 0,
        showLoader: true,
        //load: true,
      },
      () => {
        this.getConcreteRequest();
      }
    );
  };
  onEndReached = () => {
    if (this.state.drList.length < this.state.totalCount) {
      this.page_number = this.page_number + 1;
      this.getdrlist();
      this.onEndReachedCalledDuringMomentum = true;
    }
  };
  onEndReachedInspection = () => {
    if (this.state.inspectionList.length < this.state.totalCount) {
      this.page_number = this.page_number + 1;
      this.getInspectionList();
      this.onEndReachedCalledDuringMomentum = true;
    }
  };
  onEndReachedCrane = () => {
    if (this.state.craneList.length < this.state.totalCrane) {
      this.page_number = this.page_number + 1;
      this.getCraneList();
      this.onEndReachedCalledDuringMomentum = true;
    }
  };
  onEndReachedConcrete = () => {
    if (this.state.concreteList.length < this.state.totalConcrete) {
      this.page_number = this.page_number + 1;
      this.getConcreteRequest();
      this.onEndReachedCalledDuringMomentum = true;
    }
  };
  renderSearchBar = () => {
    if (this.state.searchbarShow == true) {
      return this.searchBar();
    } else {
      return this.renderHeader();
    }
  };

  searchBar = () => {
    return (
      <View style={searchStyles.searchHeader}>
        <View style={searchStyles.mainContainer}>
          <TouchableOpacity
            style={searchStyles.closeBtn}
            onPress={() => {
              this.setState(
                {
                  searchbarShow: false,
                  searchText: "",
                },
                () => this.renderInitial()
              );
            }}
          >
            <Image
              resizeMode={"contain"}
              source={Images.closeBlack}
              style={searchStyles.closeImg}
            />
          </TouchableOpacity>

          <View
            style={{ flex: 1, justifyContent: "center", alignItems: "center" }}
          >
            <Text style={searchStyles.titleText}>{Strings.search.title}</Text>
          </View>

          <TouchableOpacity
            style={searchStyles.closeBtn}
            onPress={() => {
              if (this.state.showright) {
                this.setState({ showAllDelete: true });
              }
            }}
          >
            {this.state.showright == true && (
              <Image
                resizeMode={"contain"}
                source={Images.delete1}
                style={searchStyles.closeImg}
              />
            )}
          </TouchableOpacity>
        </View>
        <View style={{ flexDirection: "row", justifyContent: "center" }}>
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.SearchHere}
            title={Strings.placeholders.SearchHere}
            value={this.state.searchText}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: wp("4%"),
              fontFamily: Fonts.montserratMedium,
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />
          <View
            style={{
              position: "absolute",
              right: wp("5%"),
              width: wp("10%"),
              height: hp("5%"),
              marginTop: hp("3%"),
              justifyContent: "flex-end",
              alignItems: "center",
            }}
          >
            {this.state.showIndicator == true && (
              <ActivityIndicator style={{ marginBottom: 5 }} />
            )}
            {this.state.clearSearch == true && (
              <TouchableOpacity onPress={() => this.clearSearch()}>
                <Image source={Images.closeBlack} style={{ marginBottom: 5 }} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    );
  };

  updateMasterState = (key, value) => {
    this.setState(
      {
        searchText: value,
      },
      () => {
        this.searchList();
      }
    );
  };

  searchList = () => {
    this.setState({ showIndicator: true, clearSearch: false }, async () => {
      if (this.state.isDeliveryRequest) {
        this.props.refreshDeliveryList(false, "drList_renderInitial");
        this.setState({ showLoader: true });
        this.page_number = 1;
        await this.getdrlist();
        await this.getInspectionList();
        await this.getCompanyList();
        await this.getResponsibleList();
        await this.getGateList();
        await this.getEquipmentList();
        await this.getConcreteRequest();
      } else {
        this.props.refreshDeliveryList(false, "drList_renderInitial");
        this.setState({ showLoader: true });
        this.page_number = 1;
        await this.getCraneList();
        await this.getCompanyList();
        await this.getResponsibleList();
        await this.getGateList();
        await this.getEquipmentList();
        await this.getConcreteRequest();
      }
    });
  };

  renderHeader() {
    let count = 0;

    if (this.state.descriptionFilter !== "") {
      count = count + 1;
    }

    if (this.state.selectedCompanyId !== 0) {
      count = count + 1;
    }

    if (this.state.selectedResponsibleNameId !== 0) {
      count = count + 1;
    }

    if (this.state.selectedGateNameId !== 0) {
      count = count + 1;
    }



    if (this.state.selectedEquipNameId !== 0) {
      count = count + 1;
    }
    if (this.state.selectedLocationId !== 0) {
      count = count + 1;
    }
    if (this.state.selectedSupplierId !== 0) {
      count = count + 1;
    }
    if (this.state.selectedMixDesignId !== 0) {
      count = count + 1;
    }
    if (this.state.orderNumber !== 0) {
      count = count + 1;
    }
    if (
      this.state.selectedStatusName !== "" &&
      this.state.selectedStatusName !== null
    ) {
      count = count + 1;
    }
    if (this.state.pickFrom !== "" && this.state.pickFrom !== null) {
      count = count + 1;
    }
    if (this.state.pickTo !== "" && this.state.pickTo !== null) {
      count = count + 1;
    }

    if (this.state.inspectionType !== null) {
      count = count + 1;
    }
    if (this.state.inspectionStatus !== null) {
      count = count + 1;
    }
    if (this.state.dateFilter !== '') {
      count = count + 1;
    }

    return (
      <View style={styles.headerContainer}>
        <View style={{ flex: 1, flexDirection: "row" }}>
          <View style={styles.titleView}>
            <Text style={styles.title}>{Strings.menu.allRequest}</Text>
          </View>
        </View>

        <View style={styles.headerRowContainer}>
          <TouchableOpacity
            style={[styles.image, { marginTop: wp("1%") }]}
            onPress={() => this.setState({ showFilter: true })}
          >
            <Image source={Images.filter} />
            {this.state.filter == true && (
              <View
                style={{
                  position: "absolute",
                  marginTop: -10,
                  right: -10,
                  backgroundColor: Colors.themeColor,
                  width: 16,
                  justifyContent: "center",
                  alignItems: "center",
                  height: 16,
                  borderRadius: 8,
                }}
              >
                <Text style={{ color: "white" }}>{count}</Text>
              </View>
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.image}
            onPress={() => {
              this.setState({
                searchbarShow: true,
                drList: [],
                // inspectionList[],
              });
            }}
          >
            <Image source={Images.Search1} style={{ height: 21, width: 21, }} />
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  renderRow = (option, index, isSelected) => {
    return (
      <View
        style={{
          width: wp("40%"),
          flexDirection: "row",
          alignItems: "center",
          height: hp("7%"),
          backgroundColor: "white",
        }}
      >
        <Image
          source={option.image}
          style={{
            // width: option.id == "Edit" ? wp("5%") : wp("5%"),
            // height: option.id == "Edit" ? hp("4%") : hp("4%"),
            marginLeft: 10,
          }}
        />
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: Colors.planDesc,
            marginLeft: 10,
          }}
        >
          {option.id}
        </Text>
      </View>
    );
  };

  renderSeparator = () => {
    return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
  };

  onSelectDropdown = (option, index, item) => {
    if (option == 0) {
      // alert('Coming Soon')
      this.editDR(item, index);
    } else if (option == 1) {
      // this.setState({showDelete: true, selectedMember: item, selectedIndex: index})
      this.voidDR(item, index);
      trackEvent('Delivery_Request_Voided')
      mixPanelTrackEvent('Delivery Request Voided', this.state.mixpanelParam)
    }
  };
  onSelectCraneDropDown = async (option, index, item) => {
    if (option == 0) {
      // alert('Coming Soon')
      if (item.isAssociatedWithCraneRequest == true) {
        await this.props.toggleAssociatedWithCrane(true);
        await this.editDR(item, index);
      } else {
        this.editCraneRequest(item, index);
      }
    } else if (option == 1) {
      // this.setState({showDelete: true, selectedMember: item, selectedIndex: index})
      if (item.isAssociatedWithCraneRequest == true) {
        this.voidDR(item, index);
        trackEvent('Delivery_Request_Voided')
        mixPanelTrackEvent('Delivery Request Voided', this.state.mixpanelParam)
      } else {
        this.voidCrane(item, index);
        trackEvent('Crane_Request_Voided')
        mixPanelTrackEvent('Crane Request Voided', this.state.mixpanelParam)
      }
    }
  };
  onSelectConcreteDropDown = async (option, index, item) => {
    if (option == 0) {
      this.editConcreteRequest(item, index);
    } else if (option == 1) {
      this.voidConcrete(item, index);
      trackEvent('Concrete_Request_Voided')
      mixPanelTrackEvent('Concrete Request Voided', this.state.mixpanelParam)
    }
    else if (option == 2) {
      this.deleteConcrete(item, index);
      trackEvent('Concrete_Request_Deleted')
      mixPanelTrackEvent('Concrete Request Deleted', this.state.mixpanelParam)
    }
  }

  editConcreteRequest = (item, index) => {
    this.props.showConcreteRequestId(item.id);
    this.props.editConcreteRequest({
      item: item,
      index: index,
    });
    let selectedLocationsData = [];
    if (item && item != null && item != undefined && item.locationDetails) {
      item.locationDetails.forEach(element => {
        if (element && element.ConcreteLocation) {
          let data = element.ConcreteLocation;
          if (data && data != undefined) {
            selectedLocationsData.push({ id: data.location, uniqueId: data.id, chosenFromDropdown: true })
          }
        }
      });
    }
    let selectedPlacementsData = [];
    if (item && item != null && item != undefined && item.placementDetails) {
      item.placementDetails.forEach(element => {
        if (element && element.ConcretePlacement) {
          let data = element.ConcretePlacement;
          if (data && data != undefined) {
            selectedPlacementsData.push({ id: data.placement, uniqueId: data.id, chosenFromDropdown: true })
          }
        }
      });
    }

    let selectedMixDesignsData = [];
    if (item && item != null && item != undefined && item.mixDesignDetails) {
      item.mixDesignDetails.forEach(element => {
        if (element && element.ConcreteMixDesign) {
          let data = element.ConcreteMixDesign;
          if (data && data != undefined) {
            selectedMixDesignsData.push({ id: data.mixDesign, uniqueId: data.id, chosenFromDropdown: true })
          }
        }
      });
    }

    let selectedPumpSizesData = [];
    if (item && item != null && item != undefined && item.pumpSizeDetails) {
      item.pumpSizeDetails.forEach(element => {
        if (element && element.ConcretePumpSize) {
          let data = element.ConcretePumpSize;
          if (data && data != undefined) {
            selectedPumpSizesData.push({ id: data.pumpSize, uniqueId: data.id, chosenFromDropdown: true })
          }
        }
      });
    }
    this.props.selectedConcreteLocationsData(selectedLocationsData);
    this.props.selectedConcretePlacementsData(selectedPlacementsData);
    this.props.selectedConcreteMixDesignsData(selectedMixDesignsData);
    this.props.selectedConcretePumpSizesData(selectedPumpSizesData);
    this.props.clickAdd(true);
  };

  editCraneRequest = (item, index) => {
    this.props.showCraneRequestId(item.id);
    this.props.editCraneRequest({
      item: item,
      index: index,
    });
    this.props.clickAdd(true);
  };



  voidConcrete = (item, index) => {

    this.setState({
      showLoader: true,
    });
    let param = {
      ConcreteRequestId: item.id,
      ProjectId: this.props.projectDetails.id,
      ParentCompanyId: this.props.projectDetails.ParentCompany.id,
    };
    addVoidConcrete(`${ADD_VOID_CONCRETE}`, param, () => null, async (response) => {
      this.setState(
        {
          showLoader: false,
        })
      if (response.toString() == Strings.errors.timeout) {
        this.showToaster("error", Strings.errors.timeout);
      } else if (response.status) {
        if (response.status == 200 || response.status == 201) {
          await this.getConcreteRequest();
          await this.props.refreshDashboard(true);
          this.showToaster(
            "success",
            "Concrete request marked as void Successfully"
          );

        } else if (response.status == 400) {

          this.showToaster("error", "ValidationError");
        } else {
          this.showToaster("error", "ValidationError");
        }
      } else {
        this.showToaster("error", response.toString());
      }
    })
  }

  voidCrane = (item, index) => {
    this.setState({
      showLoader: true,
    });
    let param = {
      CraneRequestId: item.id,
      ProjectId: this.props.projectDetails.id,
      ParentCompanyId: this.props.projectDetails.ParentCompany.id,
    };
    addVoid(`${ADD_VOID_CRANE}`, param, () => null, async (voidResponse) => {
      this.setState(
        {
          showLoader: false,
        })
      if (voidResponse.toString() == Strings.errors.timeout) {
        this.showToaster("error", Strings.errors.timeout);
      } else if (voidResponse.status) {
        if (voidResponse.status == 200 || voidResponse.status == 201) {
          await this.getCraneList();
          await this.props.refreshDashboard(true);
          this.showToaster(
            "success",
            "Crane request marked as void Successfully"
          );

        } else if (voidResposne.data.message) {
          let array = Object.values(voidResponse.data.message);
          this.showToaster("error", array.toString());
        } else {
          this.showToaster("error", voidResponse.data.message);
        }
      } else {
        this.showToaster("error", voidResp.toString());
      }

    })
  }
  voidDR = (item, index) => {
    this.setState({
      showLoader: true,
    });
    let param = {
      DeliveryRequestId: item.id,
      ProjectId: this.props.projectDetails.id,
    };

    addVoid(
      `${VOID_DR}`,
      param,
      () => null,
      (voidResp) => {
        this.setState(
          {
            showLoader: false,
          },
          () => {
            if (voidResp.toString() == Strings.errors.timeout) {
              this.showToaster("error", Strings.errors.timeout);
            } else if (voidResp.status) {
              if (voidResp.status == 200 || voidResp.status == 201) {
                this.page_number = 1;
                this.getdrlist("void");
                // this.getInspectionList("void");
                this.getCompanyList();
                this.getResponsibleList();
                this.getGateList();
                this.getEquipmentList();
                this.props.refreshDashboard(true);
              } else if (voidResp.data.message.message) {
                let array = Object.values(voidResp.data.message.details[0]);
                this.showToaster("error", array.toString());
              } else {
                this.showToaster("error", voidResp.data.message);
              }
            } else {
              this.showToaster("error", voidResp.toString());
            }
          }
        );
      }
    );
  };

  voidINS = (item, index) => {
    this.setState({
      showLoader: true,
    });
    let param = {
      InspectionRequestId: item.id,
      ProjectId: this.props.projectDetails.id,
    };

    addVoid(
      `${CREATE_VOID_INS}`,
      param,
      () => null,
      (voidResp) => {
        this.setState(
          {
            showLoader: false,
          },
          () => {
            if (voidResp.toString() == Strings.errors.timeout) {
              this.showToaster("error", Strings.errors.timeout);
            } else if (voidResp.status) {
              if (voidResp.status == 200 || voidResp.status == 201) {
                this.page_number = 1;
                // this.getdrlist("void");
                this.getInspectionList("void");
                this.getCompanyList();
                this.getResponsibleList();
                this.getGateList();
                this.getEquipmentList();
                this.props.refreshDashboard(true);
              } else if (voidResp.data.message.message) {
                let array = Object.values(voidResp.data.message.details[0]);
                this.showToaster("error", array.toString());
              } else {
                this.showToaster("error", voidResp.data.message);
              }
            } else {
              this.showToaster("error", voidResp.toString());
            }
          }
        );
      }
    );
  };

  showToaster = (type, message) => {
    if (message) {
      this.setState(
        {
          showToaster: true,
          toastType: type,
          toastMessage: message,
        },
        () => this.hideToast()
      );
    }
  };

  editDR = (item, index) => {
    this.props.showDeliverdetailsid(item.id);
    this.props.editData({
      item: item,
      index: index,
    });
    this.props.clickAdd(true);
  };

  applyFilter = () => {
    if (this.state.currentScreen == 'Concrete') {
      if (
        this.state.descriptionFilter !== "" ||
        this.state.selectedLocationName !== null ||
        this.state.orderNumber !== 0 ||
        this.state.selectedConcreteSupplier !== null ||
        this.state.selectedMixDesignName !== null ||
        this.state.selectedStatusName !== null
      ) {
        this.setState({
          showFilter: false,
        },
          () => {
            this.setState({ showLoader: true, filter: true }, async () => {
              await this.getConcreteRequest();
            });
            // this.page_number = 3;
          });
      } else {
        this.setState({
          showFilter: false,
        });
      }
    } else if (this.state.currentScreen == "Inspection") {
      if (
        this.state.descriptionFilter !== "" ||
        this.state.selectedCompanyName !== null ||
        this.state.selectedResponsibleName !== null ||
        this.state.selectedGateName !== null ||
        this.state.selectedEquipName !== null ||
        this.state.selectedStatusName !== null ||
        this.state.pickFrom != "" ||
        this.state.pickTo != "" ||
        this.state.inspectionType !== null ||
        this.state.inspectionStatus !== null ||
        this.state.dateFilter != ""
      ) {
        this.setState(
          {
            filter: true,
            showFilter: false,
          },
          async () => {
            if (this.state.isDeliveryRequest) {
              this.props.refreshDeliveryList(false, "inList_renderInitial");
              this.setState({ showLoader: true });
              this.page_number = 1;
              // await this.getdrlist();
              await this.getInspectionList();
              await this.getCompanyList();
              await this.getResponsibleList();
              await this.getGateList();
              await this.getEquipmentList();
            } else {
              this.props.refreshDeliveryList(false, "inList_renderInitial");
              this.setState({ showLoader: true });
              this.page_number = 1;
              await this.getCraneList();
              await this.getCompanyList();
              await this.getResponsibleList();
              await this.getGateList();
              await this.getEquipmentList();
            }
          }
        );
      } else {
        this.setState({
          showFilter: false,
        });
      }
    }
  };
  updateMasterStateFilter = (key, value) => {
    if (key == Strings.filter.pickFrom) {
      this.setState({
        pickFrom: value,
      });
    } else if (key == Strings.filter.pickTO) {
      this.setState({
        pickTo: value,
      });
    }


    else {
      if (
        this.state.descriptionFilter !== "" ||
        this.state.selectedCompanyName !== null ||
        this.state.selectedResponsibleName !== null ||
        this.state.selectedGateName !== null ||
        this.state.selectedEquipName !== null ||
        this.state.selectedStatusName !== null ||
        this.state.pickFrom != "" ||
        this.state.pickTo != "" ||
        this.state.inspectionType !== null ||
        this.state.inspectionStatus !== null ||
        this.state.dateFilter != ""
      ) {
        this.setState(
          {
            filter: true,
            showFilter: false,
          },
          async () => {
            if (this.state.isDeliveryRequest) {
              this.props.refreshDeliveryList(false, "drList_renderInitial");
              this.setState({ showLoader: true });
              this.page_number = 1;
              await this.getdrlist();
              await this.getInspectionList();
              await this.getCompanyList();
              await this.getResponsibleList();
              await this.getGateList();
              await this.getEquipmentList();
            } else {
              this.props.refreshDeliveryList(false, "drList_renderInitial");
              this.setState({ showLoader: true });
              this.page_number = 1;
              await this.getCraneList();
              await this.getCompanyList();
              await this.getResponsibleList();
              await this.getGateList();
              await this.getEquipmentList();
            }
          }
        );
      } else {
        this.setState({
          showFilter: false,
        });
      }
    }
  };
  updateMasterStateFilter = (key, value) => {
    if (key == Strings.filter.pickFrom) {
      this.setState({
        pickFrom: value,
      });
    } else if (key == Strings.filter.pickTO) {
      this.setState({
        pickTo: value,
      });
    }
  };

  onPressLocationType = (item) => {
    this.setState({
      selectedLocationName: item.value,
      selectedLocationId: item.id,
      locationModalVisible: false
    })
  }

  onPressSupplierType = (item) => {
    this.setState({
      selectedConcreteSupplier: item.value,
      selectedSupplierId: item.id,
      supplierModalVisible: false,
    })
  }

  onPressMixDesignType = (item) => {
    this.setState({
      selectedMixDesignName: item.value,
      selectedMixDesignId: item.id,
      mixDesignDropdown: false
    })
  }

  onPressSelectStatusType = (item) => {
    this.setState({
      selectedStatusName: item.value,
      selectedStatusId: item.id,
      selectStatusDropdown: false
    })
  }

  renderConcreteFilter = () => {
    return (
      <View style={modalStyles.container}>
        <View style={modalStyles.topContainer}>
          <TouchableOpacity
            onPress={() => this.setState({ showFilter: false })}
            style={{ width: 40 }}>
            <Image source={Images.closeBlack} />
          </TouchableOpacity>
          <View style={modalStyles.titleContainer}>
            <Text style={modalStyles.title}>{Strings.filter.title}</Text>
          </View>
          <View style={{ width: 40, height: 40 }} />
        </View>

        <KeyboardAwareScrollView>
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.description}
            title={Strings.placeholders.description}
            value={this.state.descriptionFilter}
            updateMasterState={(key, value) => {
              this.setState({
                descriptionFilter: value,
              });
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />
          <TextField
            attrName={Strings.calender.selectLocation}
            title={Strings.calender.selectLocation}
            value={this.state.selectedLocationName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ locationModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          {/* <DropDownPicker
            items={this.state.locationsList}
            defaultValue={this.state.selectedLocationName}
            placeholder={Strings.placeholders.location}
            placeholderStyle={modalStyles.filterPlaceholder}
            containerStyle={{ height: hp("6%"), marginTop: 8 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedLocationName: item.value,
                selectedLocationId: item.id,
              })
            }
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={5000}
          /> */}

          <TextField
            attrName={Strings.placeholders.supplier}
            title={Strings.placeholders.supplier}
            value={this.state.selectedConcreteSupplier}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ supplierModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          <TextField
            attrName={Strings.calender.selectMixPanel}
            title={Strings.calender.selectMixPanel}
            value={this.state.selectedMixDesignName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ mixDesignDropdown: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          <TextField
            showLeft={true}
            attrName={Strings.placeholders.orderNumber}
            title={Strings.placeholders.orderNumber}
            value={this.state.orderNumber}
            updateMasterState={(key, value) => {
              this.setState({
                orderNumber: value,
              });
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />

          <TextField
            attrName={Strings.placeholders.selectstatus}
            title={Strings.placeholders.selectstatus}
            value={this.state.selectedStatusName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ selectStatusDropdown: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          <View style={[modalStyles.buttonContainer, { marginTop: 50 }]}>
            <TouchableOpacity
              style={
                this.state.filter == true
                  ? [
                    modalStyles.cancelButton,
                    { backgroundColor: Colors.themeOpacity },
                  ]
                  : modalStyles.cancelButton
              }
              onPress={() =>
                this.setState(
                  {
                    showFilter: false,
                    orderNumber: 0,
                    searchId: "",
                    searchText: "",
                  },
                  () => {
                    if (this.state.filter == true) {
                      this.setState(
                        {
                          filter: false,
                          descriptionFilter: "",
                          selectedMixDesignName: null,
                          selectedMixDesignId: 0,
                          selectedConcreteSupplier: null,
                          selectedSupplierId: 0,
                          selectedLocationName: null,
                          selectedLocationId: 0,
                          selectedStatusName: null,
                          selectedStatusId: 0,

                        },
                        () => {
                          this.renderInitial();
                        }
                      );
                    }
                  }
                )
              }
            >
              <Text
                style={[
                  modalStyles.cancelText,
                  {
                    color:
                      this.state.filter == true
                        ? Colors.themeColor
                        : Colors.buttonBackground,
                  },
                ]}
              >
                {this.state.filter == true
                  ? Strings.addMember.reset
                  : Strings.addMember.cancel}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={modalStyles.applyButton}
              onPress={() => { this.applyFilter() }}
            >
              <Text style={modalStyles.applyText}>{Strings.filter.apply}</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAwareScrollView>

        <Dropdown
          data={this.state.locationsList}
          title={Strings.placeholders.location}
          value={this.state.selectedLocationName}
          closeBtn={() => this.setState({ locationModalVisible: false })}
          onPress={(item) => this.onPressLocationType(item)}
          visible={this.state.locationModalVisible}
          onbackPress={() => this.setState({ locationModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.concreteSuppliersList}
          title={Strings.placeholders.supplier}
          value={this.state.selectedConcreteSupplier}
          closeBtn={() => this.setState({ supplierModalVisible: false })}
          onPress={(item) => this.onPressSupplierType(item)}
          visible={this.state.supplierModalVisible}
          onbackPress={() => this.setState({ supplierModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />
        <Dropdown
          data={this.state.mixDesignList}
          title={Strings.placeholders.mixDesign}
          value={this.state.selectedMixDesignName}
          closeBtn={() => this.setState({ mixDesignDropdown: false })}
          onPress={(item) => this.onPressMixDesignType(item)}
          visible={this.state.mixDesignDropdown}
          onbackPress={() => this.setState({ mixDesignDropdown: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.statusList}
          title={Strings.placeholders.selectstatus}
          value={this.state.selectedStatusName}
          closeBtn={() => this.setState({ selectStatusDropdown: false })}
          onPress={(item) => this.onPressSelectStatusType(item)}
          visible={this.state.selectStatusDropdown}
          onbackPress={() => this.setState({ selectStatusDropdown: false, })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

      </View>
    );
  };

  onPressCompanyType = (item) => {
    this.setState({
      selectedCompanyName: item.value,
      selectedCompanyId: item.id,
      companyModalVisible: false
    })
  }

  onPressResPersonType = (item) => {
    this.setState({
      selectedResponsibleName: item.value,
      selectedResponsibleNameId: item.id,
      responisblePersonModal: false
    })
  }

  onPressGateModalType = (item) => {
    this.setState({
      selectedGateName: item.value,
      selectedGateNameId: item.id,
      gateModalVisible: false
    })
  }

  onPressEquipModalType = (item) => {
    this.setState({
      selectedEquipName: item.value,
      selectedEquipNameId: item.id,
      equipModalVisible: false,
    })
  }

  onPressStatusModalType = (item) => {
    this.setState({
      selectedStatusName: item.value,
      selectedStatusId: item.id,
      statusModalVisible: false
    })
  }
  onchangeDate = (tevent, date1) => {
    if (Platform.OS == "android") {
      if (tevent.type == Strings.datePicker.set || tevent.type == Strings.datePicker.dismissed) {
        this.setState({
          isDeliveryDate: false,
        });
      }
    }
    if (Platform.OS == Strings.platforms.ios || tevent.type == Strings.datePicker.set) {
      this.setState({
        dateFilter: moment(date1).format("MM/DD/YYYY")
      });
      selectedDate = date1;

    }
  };

  renderFilter = () => {
    return (
      <View style={modalStyles.container}>
        <View style={modalStyles.topContainer}>
          <TouchableOpacity
            onPress={() => this.setState({ showFilter: false })}
            style={{ width: 40 }}
          >
            <Image source={Images.closeBlack} />
          </TouchableOpacity>
          <View style={modalStyles.titleContainer}>
            <Text style={modalStyles.title}>{Strings.filter.title}</Text>
          </View>
          <View style={{ width: 40, height: 40 }} />
        </View>

        <KeyboardAwareScrollView>
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.description}
            title={Strings.placeholders.description}
            value={this.state.descriptionFilter}
            updateMasterState={(key, value) => {
              this.setState({
                descriptionFilter: value,
              });
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />
          <TextField
            attrName={Strings.placeholders.deliveryDate}
            title={Strings.placeholders.deliveryDate}
            value={this.state.dateFilter}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={modalStyles.textFieldInput}
            textTitleStyles={modalStyles.textFieldTittle}
            showButton={true}
            onPress={() => {
              this.setState({
                isDeliveryDate: true,
              });
            }}
            container={modalStyles.dateFilterContainer}
            imageSource={Images.calGray}
          />

          <TextField
            attrName={Strings.placeholders.company}
            title={Strings.placeholders.company}
            value={this.state.selectedCompanyName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ companyModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          <TextField
            attrName={Strings.placeholders.responisblePerson}
            title={Strings.placeholders.responisblePerson}
            value={this.state.selectedResponsibleName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ responisblePersonModal: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          <TextField
            attrName={Strings.placeholders.gate}
            title={Strings.placeholders.gate}
            value={this.state.selectedGateName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ gateModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          <TextField
            attrName={Strings.placeholders.equip}
            title={Strings.placeholders.equip}
            value={this.state.selectedEquipName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ equipModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          <TextField
            attrName={Strings.placeholders.status}
            title={Strings.placeholders.status}
            value={this.state.selectedStatusName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ statusModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />
          <TextField
            attrName={Strings.filter.pickFrom}
            title={Strings.filter.pickFrom}
            value={this.state.pickFrom}
            updateMasterState={(key, value) => {
              this.updateMasterStateFilter(key, value);
            }}
            mandatory={true}
            textTitleStyles={{
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            textInputStyles={{
              // here you can add additional TextInput drStyles
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: 10,
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
          />
          <TextField
            attrName={Strings.filter.pickTO}
            title={Strings.filter.pickTO}
            value={this.state.pickTo}
            updateMasterState={(key, value) => {
              this.updateMasterStateFilter(key, value);
            }}
            textTitleStyles={{
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            textInputStyles={{
              // here you can add additional TextInput drStyles
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: 10,
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            mandatory={true}
            showButton={false}
            imageSource={Images.calGray}
          />

          {Platform.OS == Strings.platforms.ios && (
            <Modal
              isVisible={this.state.isDeliveryDate}
              onBackdropPress={() => {
                this.setState({ isDeliveryDate: false, });
              }}
              animationInTiming={500}
              style={modalStyles.iosDateModal}
            >
              <DateTimePicker
                value={selectedDate}
                style={modalStyles.iosDatePicker}
                display={Strings.datePicker.inline}
                themeVariant={Strings.datePicker.light}
                accentColor={Colors.themeColor}
                onChange={(time, date) => {
                  this.onchangeDate(time, date);
                }}

              />
              <View>
                <TouchableOpacity
                  activeOpacity={0.5}
                  style={styles.datePickerOkContainer}
                  onPress={() => {
                    this.setState({ isDeliveryDate: false, });
                  }}
                >
                  <Text style={styles.datePickerOkLabel}>Done</Text>
                </TouchableOpacity>
              </View>
            </Modal>)}
          {Platform.OS == Strings.platforms.android && this.state.isDeliveryDate && (
            <DateTimePicker
              value={selectedDate}
              style={modalStyles.androidDatePicker}
              display={Strings.datePicker.default}
              onChange={(time, date) => {
                this.onchangeDate(time, date);
              }}

            />
          )}
          <View style={[modalStyles.buttonContainer, { marginTop: 50 }]}>
            <TouchableOpacity
              style={
                this.state.filter == true
                  ? [
                    modalStyles.cancelButton,
                    { backgroundColor: Colors.themeOpacity },
                  ]
                  : modalStyles.cancelButton
              }
              onPress={() => {
                selectedDate = new Date()
                this.setState(
                  {
                    showFilter: false,
                    selectedControlledBy: null,
                    selectedControlledById: 0,
                    selectedEquipName: null,
                    selectedEquipId: 0,
                    searchId: "",
                    searchText: "",
                    dateFilter: "",
                  },
                  () => {
                    if (this.state.filter == true) {
                      this.setState(
                        {
                          filter: false,
                          descriptionFilter: "",
                          selectedCompanyName: null,
                          selectedCompanyId: 0,
                          selectedResponsibleName: null,
                          selectedResponsibleNameId: 0,
                          selectedGateName: null,
                          selectedGateNameId: 0,
                          selectedEquipName: null,
                          selectedEquipNameId: 0,
                          selectedStatusName: null,
                          selectedStatusId: 0,
                          pickFrom: "",
                          pickTo: "",
                          dateFilter: "",
                        },
                        () => {
                          this.renderInitial();
                        }
                      );
                    }
                  }
                )
              }
              }
            >
              <Text
                style={[
                  modalStyles.cancelText,
                  {
                    color:
                      this.state.filter == true
                        ? Colors.themeColor
                        : Colors.buttonBackground,
                  },
                ]}
              >
                {this.state.filter == true
                  ? Strings.addMember.reset
                  : Strings.addMember.cancel}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={modalStyles.applyButton}
              onPress={() => this.applyFilter()}
            >
              <Text style={modalStyles.applyText}>{Strings.filter.apply}</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAwareScrollView>
        <Dropdown
          data={this.state.companyFilterList}
          title={Strings.placeholders.company}
          value={this.state.selectedCompanyName}
          closeBtn={() => this.setState({ companyModalVisible: false })}
          onPress={(item) => this.onPressCompanyType(item)}
          visible={this.state.companyModalVisible}
          onbackPress={() => this.setState({ companyModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.responiblePersonList}
          title={Strings.placeholders.responisblePerson}
          value={this.state.selectedResponsibleName}
          closeBtn={() => this.setState({ responisblePersonModal: false })}
          onPress={(item) => this.onPressResPersonType(item)}
          visible={this.state.responisblePersonModal}
          onbackPress={() => this.setState({ responisblePersonModal: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.gateList}
          title={Strings.placeholders.gate}
          value={this.state.selectedGateName}
          closeBtn={() => this.setState({ gateModalVisible: false })}
          onPress={(item) => this.onPressGateModalType(item)}
          visible={this.state.gateModalVisible}
          onbackPress={() => this.setState({ gateModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.equipmentList}
          title={Strings.placeholders.equip}
          value={this.state.selectedEquipName}
          closeBtn={() => this.setState({ equipModalVisible: false })}
          onPress={(item) => this.onPressEquipModalType(item)}
          visible={this.state.equipModalVisible}
          onbackPress={() => this.setState({ equipModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.statusList}
          title={Strings.placeholders.status}
          value={this.state.selectedStatusName}
          closeBtn={() => this.setState({ statusModalVisible: false })}
          onPress={(item) => this.onPressStatusModalType(item)}
          visible={this.state.statusModalVisible}
          onbackPress={() => this.setState({ statusModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />
      </View>
    );
  };





  renderInspectionFilter = () => {
    return (
      <View style={modalStyles.container}>
        <View style={modalStyles.topContainer}>
          <TouchableOpacity
            onPress={() => this.setState({ showFilter: false })}
            style={{ width: 40 }}
          >
            <Image source={Images.closeBlack} />
          </TouchableOpacity>
          <View style={modalStyles.titleContainer}>
            <Text style={modalStyles.title}>{Strings.filter.title}</Text>
          </View>
          <View style={{ width: 40, height: 40 }} />
        </View>

        <KeyboardAwareScrollView>
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.description}
            title={Strings.placeholders.description}
            value={this.state.descriptionFilter}
            updateMasterState={(key, value) => {
              this.setState({
                descriptionFilter: value,
              });
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />
          <TextField
            attrName={"Inspection Date"}
            title={"Inspection Date"}
            value={this.state.dateFilter}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={modalStyles.textFieldInput}
            textTitleStyles={modalStyles.textFieldTittle}
            showButton={true}
            onPress={() => {
              this.setState({
                isDeliveryDate: true,
              });
            }}
            container={modalStyles.dateFilterContainer}
            imageSource={Images.calGray}
          />

          <TextField
            attrName={Strings.placeholders.company}
            title={Strings.placeholders.company}
            value={this.state.selectedCompanyName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ companyModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          <TextField
            attrName={Strings.placeholders.responisblePerson}
            title={Strings.placeholders.responisblePerson}
            value={this.state.selectedResponsibleName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ responisblePersonModal: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          <TextField
            attrName={Strings.placeholders.gate}
            title={Strings.placeholders.gate}
            value={this.state.selectedGateName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ gateModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          <TextField
            attrName={Strings.placeholders.equip}
            title={Strings.placeholders.equip}
            value={this.state.selectedEquipName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ equipModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />

          <TextField
            attrName={Strings.placeholders.status}
            title={Strings.placeholders.status}
            value={this.state.selectedStatusName}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
            showButton={true}
            onPress={() => {
              this.setState({ statusModalVisible: true });
            }}
            imageSource={Images.downArr}
          // placeholder={"Select"}
          />
          <TextField
            attrName={Strings.filter.pickFrom}
            title={Strings.filter.pickFrom}
            value={this.state.pickFrom}
            updateMasterState={(key, value) => {
              this.updateMasterStateFilter(key, value);
            }}
            mandatory={true}
            textTitleStyles={{
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            textInputStyles={{
              // here you can add additional TextInput drStyles
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: 10,
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
          />
          <TextField
            // attrName={Strings.filter.pickTO}
            title={Strings.filter.pickTO}
            value={this.state.pickTo}
            updateMasterState={(key, value) => {
              this.updateMasterStateFilter(key, value);
            }}
            textTitleStyles={{
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            textInputStyles={{
              // here you can add additional TextInput drStyles
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: 10,
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            mandatory={true}
            showButton={false}
            imageSource={Images.calGray}
          />

          <TextField
            attrName={Strings.addDR.inspectionType}
            title={Strings.addDR.inspectionType}
            value={this.state.inspectionType}
            updateMasterState={this.updateMasterState}
            mandatory={true}
            showButton={true}
            onPress={() => {
              Keyboard.dismiss();
              this.setState({ inspectionModelVisible: true });
            }}
            imageSource={Images.downArr}
            // placeholder={"Select"}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
          />

          <TextField
            attrName={Strings.addDR.inspectionStatus}
            title={Strings.addDR.inspectionStatus}
            value={this.state.inspectionStatus}
            updateMasterState={this.updateMasterState}
            mandatory={true}
            showButton={true}
            onPress={() => {
              Keyboard.dismiss();
              this.setState({ inspectionStatusModelVisible: true });
            }}
            imageSource={Images.downArr}
            // placeholder={"Select"}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: 14,
            }}
          />

          {Platform.OS == Strings.platforms.ios && (
            <Modal
              isVisible={this.state.isDeliveryDate}
              onBackdropPress={() => {
                this.setState({ isDeliveryDate: false, });
              }}
              animationInTiming={500}
              style={modalStyles.iosDateModal}
            >
              <DateTimePicker
                value={selectedDate}
                style={modalStyles.iosDatePicker}
                display={Strings.datePicker.inline}
                themeVariant={Strings.datePicker.light}
                accentColor={Colors.themeColor}
                onChange={(time, date) => {
                  this.onchangeDate(time, date);
                }}

              />
              <View>
                <TouchableOpacity
                  activeOpacity={0.5}
                  style={styles.datePickerOkContainer}
                  onPress={() => {
                    this.setState({ isDeliveryDate: false, });
                  }}
                >
                  <Text style={styles.datePickerOkLabel}>Done</Text>
                </TouchableOpacity>
              </View>
            </Modal>)}
          {Platform.OS == Strings.platforms.android && this.state.isDeliveryDate && (
            <DateTimePicker
              value={selectedDate}
              style={modalStyles.androidDatePicker}
              display={Strings.datePicker.default}
              onChange={(time, date) => {
                this.onchangeDate(time, date);
              }}

            />
          )}
          <View style={[modalStyles.buttonContainer, { marginTop: 50 }]}>
            <TouchableOpacity
              style={
                this.state.filter == true
                  ? [
                    modalStyles.cancelButton,
                    { backgroundColor: Colors.themeOpacity },
                  ]
                  : modalStyles.cancelButton
              }
              onPress={() => {
                selectedDate = new Date()
                this.setState(
                  {
                    showFilter: false,
                    selectedControlledBy: null,
                    selectedControlledById: 0,
                    selectedEquipName: null,
                    selectedEquipId: 0,
                    searchId: "",
                    searchText: "",
                    dateFilter: "",
                  },
                  () => {
                    if (this.state.filter == true) {
                      this.setState(
                        {
                          filter: false,
                          descriptionFilter: "",
                          selectedCompanyName: null,
                          selectedCompanyId: 0,
                          selectedResponsibleName: null,
                          selectedResponsibleNameId: 0,
                          selectedGateName: null,
                          selectedGateNameId: 0,
                          selectedEquipName: null,
                          selectedEquipNameId: 0,
                          selectedStatusName: null,
                          selectedStatusId: 0,
                          pickFrom: "",
                          pickTo: "",
                          inspectionType: null,
                          inspectionStatus: null,
                          dateFilter: "",
                        },
                        () => {
                          this.renderInitial();
                        }
                      );
                    }
                  }
                )
              }
              }
            >
              <Text
                style={[
                  modalStyles.cancelText,
                  {
                    color:
                      this.state.filter == true
                        ? Colors.themeColor
                        : Colors.buttonBackground,
                  },
                ]}
              >
                {this.state.filter == true
                  ? Strings.addMember.reset
                  : Strings.addMember.cancel}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={modalStyles.applyButton}
              onPress={() => this.applyFilter()}
            >
              <Text style={modalStyles.applyText}>{Strings.filter.apply}</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAwareScrollView>
        <Dropdown
          data={this.state.companyFilterList}
          title={Strings.placeholders.company}
          value={this.state.selectedCompanyName}
          closeBtn={() => this.setState({ companyModalVisible: false })}
          onPress={(item) => this.onPressCompanyType(item)}
          visible={this.state.companyModalVisible}
          onbackPress={() => this.setState({ companyModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.responiblePersonList}
          title={Strings.placeholders.responisblePerson}
          value={this.state.selectedResponsibleName}
          closeBtn={() => this.setState({ responisblePersonModal: false })}
          onPress={(item) => this.onPressResPersonType(item)}
          visible={this.state.responisblePersonModal}
          onbackPress={() => this.setState({ responisblePersonModal: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.gateList}
          title={Strings.placeholders.gate}
          value={this.state.selectedGateName}
          closeBtn={() => this.setState({ gateModalVisible: false })}
          onPress={(item) => this.onPressGateModalType(item)}
          visible={this.state.gateModalVisible}
          onbackPress={() => this.setState({ gateModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.equipmentList}
          title={Strings.placeholders.equip}
          value={this.state.selectedEquipName}
          closeBtn={() => this.setState({ equipModalVisible: false })}
          onPress={(item) => this.onPressEquipModalType(item)}
          visible={this.state.equipModalVisible}
          onbackPress={() => this.setState({ equipModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.statusList}
          title={Strings.placeholders.status}
          value={this.state.selectedStatusName}
          closeBtn={() => this.setState({ statusModalVisible: false })}
          onPress={(item) => this.onPressStatusModalType(item)}
          visible={this.state.statusModalVisible}
          onbackPress={() => this.setState({ statusModalVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.inspectionTypeOptions}
          title={"Inspection Type"}
          value={this.state.inspectionType}
          closeBtn={() => this.setState({ inspectionModelVisible: false })}
          onPress={(item) => this.onPressInspectionType(item)}
          visible={this.state.inspectionModelVisible}
          onbackPress={() => this.setState({ inspectionModelVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />

        <Dropdown
          data={this.state.inspectionStatusOptions}
          title={"Inspection Status"}
          value={this.state.inspectionStatus}
          closeBtn={() => this.setState({ inspectionStatusModelVisible: false })}
          onPress={(item) => this.onPressInspectionStatus(item)}
          visible={this.state.inspectionStatusModelVisible}
          onbackPress={() => this.setState({ inspectionStatusModelVisible: false })}
          container={styles.equipmentContainer}
          customMainContainer={styles.renderEquipStyle}
          equipTextContainer={styles.equipTextStyle}
        />
      </View>
    );
  };

  renderFlatListItem = ({ item, index }) => {
    return (
      <DRCard
        item={item}
        indexs={index}
        textColor={this.state.colorData}
        defaultColor={this.state.isDefaultColor}
        roleId={this.props.projectRoleId}
        onPress={() => {
          let decidingData = {
            id: item.id,
            isDelivery: true,
          }

          this.props.showDeliverdetailsid(decidingData);
          this.props.onTapDetail("drdetailspage");
          trackScreen('Delivery Details')
        }}
        onSelect={(options) => {
          if ((item.createdUserDetails.RoleId == 2 && this.props.projectRoleId == 4) || (item.createdUserDetails.RoleId == 3 && this.props.projectRoleId == 4)) {
            this.onSelectDropdown(1, index, item);
          }
          else {
            this.onSelectDropdown(options, index, item);
          }
        }}
        isAccessData={item.isMemberAccess}
      />
    );
  };
  renderInspectionFlatListItem = ({ item, index }) => {

    return (
      <DRCard
        item={item}
        indexs={index}
        textColor={this.state.colorData}
        defaultColor={this.state.isDefaultColor}
        roleId={this.props.projectRoleId}
        inspectionStatus={item.inspectionStatus}
        onPress={() => {
          let decidingData = {
            id: item.id,
            isInspection: true,
          }

          this.props.showDeliverdetailsid(decidingData);
          this.props.onTapDetail("drdetailspage");
          trackScreen('Inspection Details')
        }}
        onSelect={(options) => {
          if ((item.createdUserDetails.RoleId == 2 && this.props.projectRoleId == 4) || (item.createdUserDetails.RoleId == 3 && this.props.projectRoleId == 4)) {
            this.onSelectDropdown(1, index, item);
          }
          else {
            this.onSelectDropdown(options, index, item);
          }
        }}
        isAccessData={item.isMemberAccess}
      />
    );
  };
  renderCraneFlatListItem = ({ item, index }) => {
    return (
      <DRCard
        item={item}
        key={index}
        textColor={this.state.colorData}
        roleId={this.props.projectRoleId}
        defaultColor={this.state.isDefaultColor}
        onPress={() => {
          let decidingData = {
            id: item.requestType == "craneRequest" ? item.CraneRequestId : item.id,
            isDelivery: item.requestType == 'craneRequest' ? false : true,
          }
          this.props.showDeliverdetailsid(decidingData);
          this.props.onTapDetail("drdetailspage");
          trackScreen('Crane Details')
        }}
        onSelect={(options) => {
          if ((item.createdUserDetails.RoleId == 2 && this.props.projectRoleId == 4) || (item.createdUserDetails.RoleId == 3 && this.props.projectRoleId == 4)) {
            this.onSelectCraneDropDown(1, index, item);
          }
          else {
            this.onSelectCraneDropDown(options, index, item);
          }
        }}
        isAccessData={item.isMemberAccess}
      />
    );
  };

  renderConcreteFlatListItem = ({ item, index }) => {
    return (
      <ConcreteCard
        item={item}
        key={index}
        textColor={this.state.colorData}
        defaultColor={this.state.isDefaultColor}
        roleId={this.props.projectRoleId}
        onPress={() => {
          this.props.concreteDetailsID(item.ConcreteRequestId)
          this.props.onPressConcreteDetail("ConcreteDetails")
          trackScreen('Concrete Details')
        }}
        onSelect={(options) => {
          if ((item.createdUserDetails.RoleId == 2 && this.props.projectRoleId == 4) || (item.createdUserDetails.RoleId == 3 && this.props.projectRoleId == 4)) {
            this.onSelectConcreteDropDown(1, index, item);
          }
          else {
            this.onSelectConcreteDropDown(options, index, item);
          }
        }}
        isAccessData={item.isMemberAccess}
      />)
  }

  voidListLink = () => {
    return (
      this.state.searchbarShow == false && (
        <Text
          style={[styles.void, { marginBottom: 0 }]}
          onPress={() => {
            trackScreen('Void List')
            this.props.goToVoid(true);
          }}
        >
          {Strings.addDR.void}
        </Text>
      )
    );
  };

  renderNoDeliveryRequest = () => {
    return (
      this.state.showNoData == true && (
        <Text style={styles.showNoData}>No Delivery Requests Found</Text>
      ))
  };
  renderNoInspectionRequest = () => {
    return (
      this.state.showNoData == true && (
        <Text style={styles.showNoData}>No Inspection Requests Found</Text>
      ))
  };


  renderNoCraneRequest = () => {
    if (this.state.showCraneNoData == true) {
      return <Text style={styles.showNoData}>No Crane Requests Found</Text>;
    }
  };
  renderNoConcreteRequest = () => {
    if (this.state.showConcreteNoData == true) {
      return <Text style={styles.showNoData}>No Concrete Requests Found</Text>;
    }
  };
  deletePopupAcceptTap = () => {
    this.setState({ showDelete: false });
    this.deleteMember(this.state.selectedMember, this.state.selectedIndex);
  };

  deletePopupDeclineTap = () => {
    this.setState({
      showDelete: false,
      selectedMember: [],
      selectedIndex: null,
    });
  };
  onSwitchRequest = ({ i }) => {
    if (i == 1) {
      this.setState({
        statusList: [
          {
            label: "Approved",
            value: "Approved",
            id: "1",
            name: "Approved",
          },
          {
            label: "Completed",
            value: "Completed",
            id: "2",
            name: "Completed",
          },
          {
            label: "Declined",
            value: "Declined",
            id: "3",
            name: "Declined",
          },
          {
            label: "Delivered",
            value: "Delivered",
            id: "4",
            name: "Delivered",
          },
          {
            label: "Pending",
            value: "Pending",
            id: "5",
            name: "Pending",
          },
        ],
      })
    }
    else if (i == 2) {
      this.setState({
        statusList: [
          {
            label: "Approved",
            value: "Approved",
            id: "1",
            name: "Approved",
          },
          {
            label: "Completed",
            value: "Completed",
            id: "2",
            name: "Completed",
          },
          {
            label: "Expired",
            value: "Expired",
            id: "3",
            name: "Expired",
          },
          {
            label: "Tentative",
            value: "Tentative",
            id: "4",
            name: "Tentative",
          },
        ],
      })
    }
    else if (i == 3) {
      this.setState({
        statusList: [
          {
            label: "Approved",
            value: "Approved",
            id: "1",
            name: "Approved",
          },
          {
            label: "Declined",
            value: "Declined",
            id: "2",
            name: "Declined",
          },
          {
            label: "Completed",
            value: "Completed",
            id: "3",
            name: "Completed",
          },
          {
            label: "Pending",
            value: "Pending",
            id: "4",
            name: "Pending",
          },
        ],
      })
    }
    else {
      this.setState({
        statusList: [
          {
            label: "Approved",
            value: "Approved",
            id: "1",
            name: "Approved",
          },
          {
            label: "Declined",
            value: "Declined",
            id: "2",
            name: "Declined",
          },
          {
            label: "Delivered",
            value: "Delivered",
            id: "3",
            name: "Delivered",
          },
          {
            label: "Pending",
            value: "Pending",
            id: "4",
            name: "Pending",
          },
        ],
      })
    }

    if (this.state.searchbarShow == true || this.state.searchText != "") {
      this.setState(
        {
          searchbarShow: false,
          searchText: "",
        },
        () => {
          this.page_number = 1;
          if (i == 1) {
            this.setState({
              statusList: [
                {
                  label: "Approved",
                  value: "Approved",
                  id: "1",
                  name: "Approved",
                },
                {
                  label: "Completed",
                  value: "Completed",
                  id: "2",
                  name: "Completed",
                },
                {
                  label: "Declined",
                  value: "Declined",
                  id: "3",
                  name: "Declined",
                },
                {
                  label: "Delivered",
                  value: "Delivered",
                  id: "4",
                  name: "Delivered",
                },
                {
                  label: "Pending",
                  value: "Pending",
                  id: "5",
                  name: "Pending",
                },
              ],
            })
            this.getCraneList();
          }
          else if (i == 2) {
            this.setState({
              statusList: [
                {
                  label: "Approved",
                  value: "Approved",
                  id: "1",
                  name: "Approved",
                },
                {
                  label: "Completed",
                  value: "Completed",
                  id: "2",
                  name: "Completed",
                },
                {
                  label: "Expired",
                  value: "Expired",
                  id: "3",
                  name: "Expired",
                },
                {
                  label: "Pump Confirmed",
                  value: "Pump Confirmed",
                  id: "4",
                  name: "Pump Confirmed",
                },
                {
                  label: "Tentative",
                  value: "Tentative",
                  id: "5",
                  name: "Tentative",
                },
              ],
            })
            this.getConcreteRequest();
          }
          else if (i == 3) {
            this.setState({
              statusList: [
                {
                  label: "Approved",
                  value: "Approved",
                  id: "1",
                  name: "Approved",
                },
                {
                  label: "Declined",
                  value: "Declined",
                  id: "2",
                  name: "Declined",
                },
                {
                  label: "Completed",
                  value: "Completed",
                  id: "3",
                  name: "Completed",
                },
                {
                  label: "Pending",
                  value: "Pending",
                  id: "4",
                  name: "Pending",
                },
              ],
            })
            this.getInspectionList();
          }
          else {
            this.setState({
              statusList: [
                {
                  label: "Approved",
                  value: "Approved",
                  id: "1",
                  name: "Approved",
                },
                {
                  label: "Declined",
                  value: "Declined",
                  id: "2",
                  name: "Declined",
                },
                {
                  label: "Delivered",
                  value: "Delivered",
                  id: "3",
                  name: "Delivered",
                },
                {
                  label: "Pending",
                  value: "Pending",
                  id: "4",
                  name: "Pending",
                },
              ],
            })
            this.getdrlist();
          }
        }
      );
    }
    if (this.state.filter) {
      selectedDate = new Date()
      this.setState(
        {
          filter: false,
          descriptionFilter: "",
          selectedCompanyName: null,
          selectedCompanyId: 0,
          selectedResponsibleName: null,
          selectedResponsibleNameId: 0,
          selectedGateName: null,
          selectedGateNameId: 0,
          selectedEquipName: null,
          selectedEquipNameId: 0,
          selectedStatusName: null,
          selectedStatusId: 0,
          pickFrom: "",
          pickTo: "",
          inspectionType: null,
          inspectionStatus: null,
          selectedMixDesignName: null,
          selectedMixDesignId: 0,
          selectedConcreteSupplier: null,
          selectedSupplierId: 0,
          selectedLocationName: null,
          selectedLocationId: 0,
          orderNumber: 0,
          dateFilter: "",
        },
        () => {
          this.renderInitial();
        }
      );
    }
    this.setState({ showLoader: true });
    if (i == 1) {
      trackScreen('Crane')
      this.setState({ isDeliveryRequest: false, currentScreen: 'Crane' });
      if (this.state.craneList == "") {
        this.getCraneList();
      }
      this.props.toggleAddAllRequest("Crane");
    } else if (i == 2) {
      trackScreen('Concrete')
      this.setState({ currentScreen: 'Concrete' });
      this.getConcreteRequest();
      this.props.toggleAddAllRequest("Concrete");
    }
    else if (i == 3) {
      if (this.state.inspectionList == "") {
        this.getInspectionList();
      }
      trackScreen('Inspection')
      this.setState({ isDeliveryRequest: true, currentScreen: 'Inspection' });
      this.props.toggleAddAllRequest("Inspection");
    }
    else {
      if (this.state.drList == "") {
        this.getdrlist();
      }
      trackScreen('Deliveries')
      this.setState({ isDeliveryRequest: true, currentScreen: 'Delivery' });
      this.props.toggleAddAllRequest("Delivery");
    }
    setTimeout(() => {
      this.setState({ showLoader: false });
    }, 500);
  };
  slideScroll = () => {
    this.setState({ showLoader: true });
    setTimeout(() => {
      this.setState({ showLoader: false });
    }, 500);

  };
  renderTabScene = ({ route }) => {
    switch (route.key) {
      case 'deliveries':
        return (
          <View style={styles.bottomStyle}>
            {this.voidListLink()}
            {this.renderNoDeliveryRequest()}
            <FlatList
              data={this.state.drList}
              renderItem={this.renderFlatListItem}
              keyExtractor={(item, index) => index.toString()}
              onEndReached={() => this.onEndReached()}
              onEndReachedThreshold={0}
              onMomentumScrollBegin={() => {
                this.onEndReachedCalledDuringMomentum = false;
              }}
              onRefresh={() => this._onReset()}
              refreshing={this.state.refreshing}
            />
          </View>
        );
      case 'crane':
        return (
          <View style={styles.bottomStyle}>
            {this.voidListLink()}
            {this.renderNoCraneRequest()}
            <FlatList
              data={this.state.craneList}
              renderItem={this.renderCraneFlatListItem}
              keyExtractor={(item, index) => index.toString()}
              onEndReached={() => this.onEndReachedCrane()}
              onEndReachedThreshold={0}
              onMomentumScrollBegin={() => {
                this.onEndReachedCalledDuringMomentum = false;
              }}
              onRefresh={() => this._onResetCrane()}
              refreshing={this.state.refreshing}
            />
          </View>
        );
      case 'concrete':
        return (
          <View style={styles.bottomStyle}>
            {this.voidListLink()}
            {this.renderNoConcreteRequest()}
            <FlatList
              data={this.state.concreteList}
              renderItem={this.renderConcreteFlatListItem}
              navigation={this.props.navigation}
              keyExtractor={(item, index) => index.toString()}
              onEndReached={() => this.onEndReachedConcrete()}
              onEndReachedThreshold={0}
              onMomentumScrollBegin={() => {
                this.onEndReachedCalledDuringMomentum = false;
              }}
              onRefresh={() => this._onResetConcrete()}
              refreshing={this.state.refreshing}
            />
          </View>
        );
      case 'inspection':
        return (
          <View style={styles.bottomStyle}>
            {this.voidListLink()}
            {this.renderNoInspectionRequest()}
            <FlatList
              data={this.state.inspectionList}
              renderItem={this.renderInspectionFlatListItem}
              keyExtractor={(item, index) => index.toString()}
              onEndReached={() => this.onEndReachedInspection()}
              onEndReachedThreshold={0}
              onMomentumScrollBegin={() => {
                this.onEndReachedCalledDuringMomentum = false;
              }}
              onRefresh={() => this._onResetInspection()}
              refreshing={this.state.refreshing}
            />
          </View>
        );
      default:
        return null;
    }
  };

  renderTabBar = props => (
    <TabBar
      {...props}
      indicatorStyle={{ backgroundColor: Colors.themeColor }}
      style={{ backgroundColor: Colors.white, marginTop: 20 }}
      activeColor={Colors.themeColor}
      inactiveColor="#A8B2B9"
      labelStyle={{
        fontSize: wp("3%"),
        fontFamily: Fonts.montserratSemiBold,
        textTransform: 'capitalize',
      }}
    />
  );

  render() {
    return (
      <>
        {this.state.isNetworkCheck ?
          <NoInternet
            Refresh={() => this.networkCheck()} /> :
          <AppView>
            <View
              style={[
                styles.parentContainer,
                {
                  backgroundColor:
                    this.state.searchbarShow == true ? Colors.white : Colors.searchBarColor,
                },
              ]}
            >
              {this.renderSearchBar()}

              <TabView
                navigationState={{ index: this.state.tabIndex, routes: this.state.tabRoutes }}
                renderScene={this.renderTabScene}
                renderTabBar={this.renderTabBar}
                onIndexChange={tabIndex => {
                  this.setState({ tabIndex });
                  this.onSwitchRequest({ i: tabIndex });
                }}
                initialLayout={{ width: Dimensions.get('window').width }}
              />
            </View>

            {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

            {this.state.showToaster && (
              <Toastpopup
                backPress={() => this.setState({ showToaster: false })}
                toastMessage={this.state.toastMessage}
                type={this.state.toastType}
              />
            )}

            {this.state.showAlert && (
              <Alert
                title={Strings.popup.success}
                desc={Strings.popup.forgotSuccess}
                okTap={() => {
                  this.okTap();
                }}
              />
            )}

            {this.state.showDelete && (
              <DeletePop
                title={Strings.popup.success}
                desc={Strings.popup.delete}
                acceptTap={this.deletePopupAcceptTap}
                declineTap={this.deletePopupDeclineTap}
              />
            )}

            <Modal
              isVisible={this.state.showFilter}
              style={modalStyles.filterModal}
            >
              {
                this.state.currentScreen == 'Concrete'
                  ? this.renderConcreteFilter()
                  : this.state.currentScreen == 'Inspection'
                    ? this.renderInspectionFilter()
                    : this.renderFilter()
              }

            </Modal>
          </AppView>
        }
      </>
    );
  }
}

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    userDetails,
    updatelist,
    refreshDelivery,
    projectSwitched,
    needToRefreshDrList,
    projectRoleId,
    isAddDelivery,
    editedConcrete,
    isCurrentDRPage,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    userDetails,
    updatelist,
    refreshDelivery,
    projectSwitched,
    needToRefreshDrList,
    projectRoleId,
    isAddDelivery,
    editedConcrete,
    isCurrentDRPage,
  };
};

export default connect(mapStateToProps, {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  clickAdd,
  editData,
  onTapSearch,
  storeRole,
  goToVoid,
  onTapDetail,
  showDeliverdetailsid,
  updateList,
  refreshDeliveryList,
  refreshDashboard,
  updateDrList,
  toggleAddAllRequest,
  lastCraneId,
  editCraneRequest,
  showCraneRequestId,
  toggleAssociatedWithCrane,
  onPressConcreteDetail,
  concreteDetailsID,
  lastConcreteId,
  editConcreteRequest,
  showConcreteRequestId,
  selectedConcreteLocationsData,
  selectedConcreteMixDesignsData,
  selectedConcretePlacementsData,
  selectedConcretePumpSizesData,
  getDrPage,
})(DrList);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
    // height: hp("50%"),
  },
  parentContainer: {
    flex: 1,
    backgroundColor: "#FCFBFC",
  },
  headerContainer: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderBottomWidth: 0.3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  void: {
    alignSelf: "flex-end",
    color: "#FF3939",
    margin: 15,
    fontSize: 14,
    textDecorationLine: "underline",
    fontFamily: Fonts.montserratMedium,
  },
  title: {
    color: Colors.black,
    fontSize: 22,
    fontFamily: Fonts.montserratBold,
    //marginBottom: hp("2%"),
    //marginLeft: wp("2%"),
  },
  headerRowContainer: {
    flex: 1,
    height: hp("15%"),
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  image: {
    marginRight: wp("6%"),
    marginBottom: hp("2%"),
  },
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    alignSelf: "center",
    borderRadius: wp("2%"),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 6,
  },
  nameContainer: {
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  detailContainer: {
    width: wp("85%"),
    marginLeft: 20,
    justifyContent: "center",
  },
  imagePlaceholder: {
    width: wp("14%"),
    height: wp("14%"),
    borderRadius: wp("7%"),
    marginLeft: wp("6%"),
  },
  nameText: {
    color: Colors.black,
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
    marginTop: 10,
    marginLeft: 10,
    width: "85%",
  },
  companyText: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  dotMenu: {
    marginTop: hp("2%"),
  },
  emailContainer: {
    flex: 1,
    marginLeft: 10,
    marginRight: 10,
  },
  emailTitle: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    margin: 5,
  },
  emailText: {
    color: "#1E1E1E",
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginTop: 0,
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    marginRight: wp("20%"),
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    //outlineProvider: 'bounds'
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: wp("20%"),
  },
  subContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 5,
  },
  subtext: {
    width: "45%",
    color: "#5B5B5B",
    fontSize: 14,
    marginLeft: 10,
    fontFamily: Fonts.montserratRegular,
  },
  titleView: {
    flex: 1,
    minHeight: 50,
    minWidth: "60%",
    justifyContent: "center",
    marginLeft: wp("4%"),
  },
  backIconView: {
    width: 50,
    minHeight: 50,
    justifyContent: "center",
    alignItems: "center",
    //backgroundColor:"red"
  },
  showNoData: {
    alignSelf: "center",
    position: "absolute",
    justifyContent: "center",
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("30%"),
  },
  equipmentContainer: {
    height: hp("4%"),
    paddingBottom: 5
  },
  renderEquipStyle: {
    marginBottom: 10,
  },
  equipTextStyle: {
    width: '100%',
    fontSize: 16,
    paddingTop: 2
  },
  bottomStyle: {
    marginBottom: 40
  },
  datePickerOkLabel: {
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 6,
    paddingBottom: 6,
    fontFamily: Fonts.montserratBold,
  },
  datePickerOkContainer: {
    width: "100%",
    alignItems: "center",
    backgroundColor: Colors.white,
    paddingBottom: hp("2%"),
  },
});

const searchStyles = StyleSheet.create({
  searchHeader: {
    marginTop: hp("2%"),
    height: hp("18%"),
    width: wp("95%"),
    alignSelf: "center",
  },
  mainContainer: {
    width: "100%",
    height: hp("6%"),
    flexDirection: "row",
    alignItems: "center",
  },
  closeBtn: {
    width: wp("15%"),
    height: hp("8%"),
    marginLeft: wp("2%"),
    justifyContent: "center",
    alignItems: "center",
  },
  closeImg: {
    width: wp("5%"),
    height: hp("5%"),
  },
  titleText: {
    color: Colors.black,
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratSemiBold,
  },
});

const modalStyles = StyleSheet.create({
  container: {
    flex: 1,
    width: wp("100%"),
    height: hp("95%"),
  },
  topContainer: {
    flexDirection: "row",
    margin: 20,
    height: 40,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 20,
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  buttonContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-end",
    marginBottom: hp("10%"),
    justifyContent: "space-around",
  },
  cancelButton: {
    backgroundColor: `rgba(117,117,117,0.2)`,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonBackground,
    fontSize: 14,
    fontFamily: Fonts.montserratBold,
  },
  applyButton: {
    backgroundColor: Colors.themeOpacity,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  applyText: {
    color: Colors.themeColor,
    fontSize: 14,
    fontFamily: Fonts.montserratBold,
  },
  filterModal: {
    paddingTop: 45,
    paddingBottom: 30,
    margin: 0,
    backgroundColor: Colors.white,
  },
  filterPlaceholder: {
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,

  },
  concreteSupplierFilterStyle: {
    height: hp("6%"),
    marginTop: 30
  },
  textFieldInput: {
    color: Colors.black,
    fontSize: 14,
  },
  textFieldTittle: {
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
  },
  dateFilterContainer: {
    marginTop: 5,
  },
  iosDateModal: {
    paddingTop: 45,
    margin: 0,
    justifyContent: "flex-end",
  },
  iosDatePicker: {
    backgroundColor: Colors.white,
    width: '100%',
  },
  androidDatePicker: {
    backgroundColor: Colors.white,
    width: '100%',
  },

});