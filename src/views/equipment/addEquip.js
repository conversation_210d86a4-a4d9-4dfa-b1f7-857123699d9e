import React, { Component } from "react";
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Platform
} from "react-native";
import {
  changeTab,
  showSideMenu,
  cameBack,
  editData,
  updateList,
  refreshPage,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import Alert from "../../components/toastpopup/alert";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { TextField } from "../../components/textinput/addMemberTextinput";
import { LIST_ALL_MEMBER, ADD_EQUIP, UPDATE_EQUIP,GET_PRESET_EQUIPMENT_TYPE_LIST } from "../../api/Constants";
import { getAllMemberList, addEquip, updateEquip ,presetEquipmentType} from "../../api/Api";
import Dropdown from "../../components/dropdown/dropdown";
import { isEmpty } from "../../common/validators";
import DeletePop from "../../components/toastpopup/logoutPop";
import AppLoader from '../../components/apploader/AppLoader'
import { trackScreen,trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";

class AddEquipment extends Component {
  constructor(props) {
    super(props);
    this.state = {
      memberlist: [],
      equipName: "",
      type: "",
      contactPerson: "",
      modalVisible: false,
      eqipModalVisible:false,
      edit: false,
      showCancel: false,
      comparision: [],
      equipmentTypeList:[],
      selectedEquipName:"",
      selectedEquipId:"",
      isNetworkCheck: false,
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      },
    };
  }

  componentDidMount= async ()=> {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.setState({ showLoader: true });
      await  this.getAllList();
       await this.getPresetEquipment();
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(async state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.setState({ showLoader: true });
      await  this.getAllList();
       await this.getPresetEquipment();
    }
    })
  }

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  checkEdit = () => {
    if (this.props.editedData.item) {
      trackScreen('Edit Equipment')
      let data = this.props.editedData.item;
      //  "id": 0,
      // "ProjectId": 0,
      // "controlledBy": 0,
      // "equipmentName": "string",
      // "equipmentType": "string"
      this.setState(
        {
          equipName: data.equipmentName,
          type: data.equipmentType,
          selectedid: data.controlledBy,
          contactPerson:data.controllUserDetails? data.controllUserDetails.User.firstName+" "+data.controllUserDetails.User.lastName+" ("+data.controllUserDetails.User.email+")":'',
          id: data.id,
          edit: true,
          comparision: data,
          selectedEquipName:data.PresetEquipmentType.equipmentType,
          selectedEquipId:data.PresetEquipmentType.id,
        },
        () => {
          this.props.editData({});
        }
      );
    }else{
      trackScreen('New Equipment')
    }
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }
  }
  getPresetEquipment=()=>{
    presetEquipmentType(
      GET_PRESET_EQUIPMENT_TYPE_LIST,
      {},
      ()=>null,
      (presetResponse)=>{
        if(presetResponse.status){
          if(presetResponse.status==200){
            if(presetResponse.data.data){
              let equipTypelist = [];
              for (let item of presetResponse.data.data) {    
                equipTypelist.push({
                  id: item.id,
                  value: item.equipmentType,
                  label: item.equipmentType,
                  selected: false,
                  name:item.equipmentType,
                });
              }
              this.setState({equipmentTypeList:equipTypelist})
              this.checkEdit();
            }
          }
          else if(presetResponse.status==400){
            this.showError("error", presetResponse.data.message); 
          }else{
            this.showError("error", Strings.errors.failed);
          }
        }
        else {
          this.showError("error", Strings.errors.failed);
        }
      })
  }
  getAllList = () => {
    getAllMemberList(
      LIST_ALL_MEMBER +
        this.props.projectDetails.id +
        "/" +
        this.props.projectDetails.ParentCompany.id,
      {},
      () => {},
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.message == Strings.popup.getMemEqui) {
            this.storeContactPerson(response.data.data);
            // this.setState({
            //   memberlist: response.data.data
            // })
          } else {
            this.setState({
              memberlist: [],
            });
          }
        } else {
          this.showError("error", Strings.errors.failed);
        }
      }
    );
  };

  storeContactPerson = (data) => {
    let memList = [];

    for (let i = 0; i < data.length; i++) {
      if(data[i].User!=null){
      if (data[i].User.firstName != null) {
        memList.push({
          id: data[i].id,
          name:
            data[i].User.firstName +" "+
            data[i].User.lastName +
            " (" +
            data[i].User.email +
            ")",
        });
      }
    }
    }

    this.setState({ memberlist: memList });
  };

  showError = (type, message) => {
    this.setState(
      { showToaster: true, toastType: type, toastMessage: message },
      () => {
        setTimeout(() => {
          this.setState({ showToaster: false });
        }, 2000);
      }
    );
  };

  onPressTab = (item, data) => {};

  //HEADER COMPONENT
  renderHeader() {
    return (
      <View style={styles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.props.cameBack(true);
            this.props.refreshPage(true);
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={styles.title}>{this.state.edit?Strings.addEquipment.edit:Strings.addEquipment.new}</Text>
      </View>
    );
  }

  renderMemberId() {
    return (
      <View style={styles.memberContainer}>
        <Text style={styles.idTitle}>{Strings.addEquipment.id}</Text>
        <Text style={styles.idText}>{this.props.lastid}</Text>
      </View>
    );
  }

  submit = () => {
    if (isEmpty(this.state.equipName.trim())) {
      this.showError("error", Strings.errors.emptyEquipName);
    } else if (this.state.equipName.length < 3) {
      this.showError("error", Strings.errors.lengthEquipName);
    } else if (isEmpty(this.state.selectedEquipName.trim())) {
      this.showError("error", Strings.errors.emptyEquipType);
    } else if (this.state.selectedEquipName.length < 3) {
      this.showError("error", Strings.errors.lengthEquiptype);
    } else if (isEmpty(this.state.contactPerson)) {
      this.showError("error", Strings.errors.emptyEquipContactPerson);
    } else {
      if (this.state.edit == true) {
        let data = {
          ProjectId: this.props.projectDetails.id,
          controlledBy: this.state.selectedid,
          equipmentName: this.state.equipName,
          PresetEquipmentTypeId:this.state.selectedEquipId,
          id: this.state.id,
          ParentCompanyId: this.props.projectDetails.ParentCompany.id,
        };
        this.updateEquip(data);

        // ProjectId: 7
        // controlledBy: 39
        // equipmentName: "New check"
        // equipmentType: "Monster"
        // id: 13
      } else {
        let data = {
          ProjectId: this.props.projectDetails.id,
          controlledBy: this.state.selectedid,
          equipmentName: this.state.equipName,
          PresetEquipmentTypeId:this.state.selectedEquipId,
          ParentCompanyId: this.props.projectDetails.ParentCompany.id,

        };
        this.setState({ showLoader: true });
        this.addEquip(data);
      }
    }
  };

  addEquip = (param) => {
    addEquip(
      ADD_EQUIP,
      param,
      () => {},
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.message) {
            if (response.data.message == Strings.popup.equipCreated) {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.data.message,
                  toastType: "success",
                },
                () => {
                  setTimeout(() => {
                    this.props.cameBack(true);
                    this.props.refreshPage(true);
                    this.props.navigation.goBack();
                  }, 1000);
                }
              );
              if(this.state.edit){
                trackEvent('Edited_Equipment')
                mixPanelTrackEvent('Edited Equipment',this.state.mixpanelParam)
              }else{
                trackEvent('Added_Equipment')
                mixPanelTrackEvent('Added Equipment',this.state.mixpanelParam)
              }
            } else {
              this.showError("error", response.data.message);
            }
          } else if (response.data.message.message) {
            this.showError("error", response.data.message.message);
          } else {
            this.showError("error", Strings.errors.failed);
          }
        } else {
          this.showError("error", response.toString());
        }
      }
    );
  };

  updateEquip = (param) => {
    this.setState({ showLoader: true });
    updateEquip(
      UPDATE_EQUIP,
      param,
      () => {},
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.message.message) {
            this.showError("error", response.data.message.message);
          } else if (response.data.message) {
            if (response.status==200) {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.data.message,
                  toastType: "success",
                },
                () => {
                  setTimeout(() => {
                    this.setState({ showToaster: false });
                    if (this.props.route.params?.from == "search") {
                      this.props.route.params.updateData("data");
                    }

                    // this.props.cameBack(false)
                    this.props.refreshPage(true);
                    this.props.navigation.goBack();
                  }, 2000);
                }
              );
            } else {
              this.showError("error", response.data.message.message);
            }
          }
        } else {
          this.showError("error", response.toString());
        }
      }
    );
  };
  onPressCancel = () => {
    if (this.state.edit) {
      let data = this.state.comparision;
      let equipName = data.equipmentName;
      let type = data.equipmentType;
      let contactPerson = data.controllUserDetails.firstName;
      if (
        equipName == this.state.equipName &&
        type == this.state.type &&
        contactPerson == this.state.contactPerson
      ) {
        this.props.navigation.goBack();
      } else {
        this.setState({ showCancel: true });
      }
    } else {
      this.props.navigation.goBack();
    }
  };
  bottomContainer = () => {
    return (
      <View style={styles.bottomContainer}>
        <TouchableOpacity onPress={this.onPressCancel}>
          <View style={styles.cancel}>
            <Text style={styles.cancelText}>{Strings.addMember.cancel}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            this.submit();
          }}
        >
          <View style={styles.submit}>
            <Text style={styles.submitText}>
              {this.state.edit == true
                ? Strings.addMember.update
                : Strings.addMember.submit}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  updateMasterState = (key, value) => {
    if (key == Strings.addEquipment.name) {
      this.setState({ equipName: value });
    } else if (key == Strings.addEquipment.type) {
      this.setState({ type: value });
    }
  };

  onPressPerson = (item) => {
    this.setState({
      selectedid: item.id,
      contactPerson: item.name,
      modalVisible: false,
    });
  };

  onPressEqipType=(item)=>{
    this.setState({
      selectedEquipName: item.value,
      selectedEquipId: item.id,
      eqipModalVisible:false,
    })
  }

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.parentContainer}>
          {this.renderHeader()}
          <KeyboardAwareScrollView extraScrollHeight={50}>
            {this.renderMemberId()}
            <TextField
              attrName={Strings.addEquipment.name}
              title={Strings.addEquipment.name}
              value={this.state.equipName}
              maxLength={150}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
            />
             {/* <Text
              style={[
                styles.idTitle,
                {
                  fontSize: wp("4%"),
                  marginLeft: 10,
                  marginTop: hp("1%"),
                  alignSelf: "center",
                  width: wp("90%"),
                },
              ]}
            >
              {Strings.addEquipment.type}{" "}
              <Text style={{ color: Colors.red }}>*</Text>
            </Text> */}

            {/* <DropDownPicker
              items={this.state.equipmentTypeList}
              defaultValue={this.state.selectedEquipName}
              placeholder={'select'}
              placeholderStyle={{ color: Colors.placeholder, fontSize: 14,margin:0 }}
              containerStyle={{ height: hp("6%"), marginTop: 0 }}
              style={{
                backgroundColor: Colors.white,
                width: wp("90%"),
                borderColor: "#0000",
                borderBottomColor: Colors.placeholder,
                alignSelf: "center",
                height: hp("5%"),
              }}
              itemStyle={{
                justifyContent: "flex-start",
              }}
              customArrowUp={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              customArrowDown={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              dropDownStyle={{
                backgroundColor: Colors.white,
                width: "90%",
                alignSelf: "center",
                height:Platform.OS=='android'?hp('10%'):'auto',
              }}
              onChangeItem={(item) =>
                this.setState({
                  selectedEquipName: item.value,
                  selectedEquipId: item.id,
                })
              }
              selectedLabelStyle={{ color: Colors.black }}
              zIndex={4000}
            /> */}

            <TextField
              attrName={Strings.addEquipment.type}
              title={Strings.addEquipment.type}
              value={this.state.selectedEquipName}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              showButton={true}
              onPress={() => {
                this.setState({ eqipModalVisible: true });
              }}
              imageSource={Images.downArr}
              placeholder={"Select"}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
            />

            <TextField
              attrName={Strings.placeholders.controlledBy}
              title={Strings.placeholders.contactPerson}
              value={this.state.contactPerson}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              showButton={true}
              onPress={() => {
                this.setState({ modalVisible: true });
              }}
              imageSource={Images.downArr}
              placeholder={"Select"}
            />
          </KeyboardAwareScrollView>
          {this.bottomContainer()}
        </View>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.loginSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}
        {this.state.edit && this.state.showCancel && (
          <DeletePop
            title={Strings.popup.cancel}
            desc={Strings.popup.cancel}
            acceptTap={() => {
              this.setState({ showCancel: false });
              this.props.navigation.goBack();
            }}
            container={{ bottom: 0 }}
            declineTap={() => {
              this.setState({ showCancel: false });
            }}
          />
        )}
        {/* <DropDownPicker
              items={this.state.equipmentTypeList}
              defaultValue={this.state.selectedEquipName}
              placeholder={'select'}
              placeholderStyle={{ color: Colors.placeholder, fontSize: 14,margin:0 }}
              containerStyle={{ height: hp("6%"), marginTop: 0 }}
              style={{
                backgroundColor: Colors.white,
                width: wp("90%"),
                borderColor: "#0000",
                borderBottomColor: Colors.placeholder,
                alignSelf: "center",
                height: hp("5%"),
              }}
              itemStyle={{
                justifyContent: "flex-start",
              }}
              customArrowUp={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              customArrowDown={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              dropDownStyle={{
                backgroundColor: Colors.white,
                width: "90%",
                alignSelf: "center",
                height:Platform.OS=='android'?hp('10%'):'auto',
              }}
              onChangeItem={(item) =>
                this.setState({
                  selectedEquipName: item.value,
                  selectedEquipId: item.id,
                })
              }
              selectedLabelStyle={{ color: Colors.black }}
              zIndex={4000}
            /> */}
            <Dropdown
          data={this.state.equipmentTypeList}
          title={Strings.addEquipment.type}
          value={this.state.selectedEquipName}
          closeBtn={() => this.setState({ eqipModalVisible: false })}
          onPress={(item) => this.onPressEqipType(item)}
          visible={this.state.eqipModalVisible}
          onbackPress={() => this.setState({ eqipModalVisible: false })}
           container={styles.equipmentContainer}
           customMainContainer={styles.renderEquipStyle}
           equipTextContainer={styles.equipTextStyle}
        />
        <Dropdown
          data={this.state.memberlist}
          title={Strings.addEquipment.contact}
          value={this.state.contactPerson}
          closeBtn={() => this.setState({ modalVisible: false })}
          onPress={(item) => this.onPressPerson(item)}
          visible={this.state.modalVisible}
          onbackPress={() => this.setState({ modalVisible: false })}
        />
      </SafeAreaView>
      }
      </>
    );
  }
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,

  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("80%"),
    fontSize: wp("6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratBold,
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  imageContainer: {
    width: wp("100%"),
    height: hp("20%"),
    justifyContent: "center",
    alignItems: "center",
  },
  imageButton: {
    width: wp("24%"),
    height: wp("24%"),
    borderRadius: wp("12%"),
    backgroundColor: "#F5F5F5",
    borderWidth: 1,
    borderColor: "#00000029",
  },
  placeholder: {
    width: wp("15%"),
    alignSelf: "center",
  },
  camera: {
    position: "absolute",
    bottom: 10,
    right: -10,
    width: wp("8%"),
    height: wp("8%"),
    borderRadius: wp("4%"),
    backgroundColor: Colors.white,
    justifyContent: "center",
    alignItems: "center",
  },
  memberContainer: {
    width: wp("90%"),
    height: hp("10%"),
    alignSelf: "center",
    marginTop: hp("2%"),
    justifyContent: "center",
  },
  idText: {
    width: wp("90%"),
    height: hp("5%"),
    backgroundColor: "#EFEFEF",
    marginTop: wp("1%"),
    padding: hp("1%"),
    paddingLeft: 15,
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("4%"),
  },
  bottomContainer: {
    flex: 1,
    width: wp("90%"),
    alignItems: "flex-end",
    justifyContent: "center",
    flexDirection: "row",
    alignSelf: "center",
    marginBottom: hp("4%"),
    bottom: 0,
  },
  cancel: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonGray,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  equipmentContainer:{
    height: hp("4%"),
    paddingBottom:5
  },
  renderEquipStyle:{
   marginBottom:10,
  },
  equipTextStyle:{
    width:'100%',
    fontSize:16
  },
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    lastid,
    projectDetails,
    editedData,
    userDetails,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    lastid,
    projectDetails,
    editedData,
    userDetails,
  };
};

export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    cameBack,
    editData,
    updateList,
    refreshPage,
  }),
  withBackHandler
)(AddEquipment);
