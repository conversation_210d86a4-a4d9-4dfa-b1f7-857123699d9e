import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Image,
  FlatList,
  TouchableOpacity,
  Platform,
  
} from "react-native";
import {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  editData,
  clickAdd,
  onTapSearch,
  storeRole,
  updateList,
  refreshPage,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import {
  GET_EQUIP_LIST,
  DELETE_EQUIP,
  GET_ROLE,
  GET_EQUIPMENT_TYPE_LIST,
  LIST_ALL_MEMBER,
  GET_PRESET_EQUIPMENT_TYPE_LIST,
} from "../../api/Constants";
import {
  getEquipList,
  deleteEquipment,
  getRole,
  getEquipmentTypeList,
  getAllMemberList,
  presetEquipmentType,
} from "../../api/Api";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import ModalDropdown from "react-native-modal-dropdown";
import ResDropdown from "../../components/dropdown/ResDropdown";
import DeletePop from "../../components/toastpopup/logoutPop";
import Modal from "react-native-modal";
import { TextField } from "../../components/textinput/Textinput";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import AppLoader from '../../components/apploader/AppLoader'
import { trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import { Avatar } from 'react-native-paper';
import Dropdown from "../../components/dropdown/dropdown";
import DeleteError from "../../components/DeleteError/DeleteError";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";

let DROPDOWNOPTIONS = [
  { id: "Edit", image: Images.editNew, name: "Edit" },
  { id: "Delete", image: Images.deleteNew, name: "Delete" },
];

class EquipmentList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      memberslist: [],
      showLoader: false,
      showToaster: false,
      showAlert: false,
      toastMessage: "",
      toastType: "error",
      refreshing: false,
      lastId: 0,
      apiLoad: false,
      showNoData: false,
      showDelete: false,
      searchText: "",
      searchCompany:"",
      searchId: "",
      selectedroleid: 0,
      selectedrole: null,
      selectedCompany: "",
      equipTypeList: [],
      selectedEquipName: null,
      selectedEquipId: "",
      selectedControlledBy: null,
      selectedControlledById: 0,
      controlledByList: [],
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      },
      typeModalVisible:false,
      controllByModalVisible:false,
      showError: false,
      errorMessage: '',
      isNetworkCheck: false,
      deviceOrientation: false,
    };

    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.renderIntial();
    } 
  }



  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.renderIntial();
    }
    })
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }

    if (nextProps.refresh == true) {
      this.refreshPage();
    }

    if (this.props.projectSwitched != nextProps.projectSwitched) {
      this.renderIntial();
    }
  }

  refreshPage = () => {
    this.setState({ showLoader: true });
    this.renderIntial();
    this.props.updateList(false);
    this.props.refreshPage(false);
  };

  renderIntial = () => {
    this.page_number = 1;
     this.getRoles();
    this.getPresetEquipment();
     this.getControlledByList();
     this.getMemeberList();
  };

  
  getEquipmentTypeList = () => {
    getEquipmentTypeList(
      GET_EQUIPMENT_TYPE_LIST + this.props.projectDetails.id,
      {},
      () => {},
      (resp) => {
        if (resp.status) {
          if (resp.data.message == "Equipment Type Listed successfully.") {
           this.storeEquipType(resp.data.data);
          }
        }
      }
    );
  };
  getPresetEquipment=()=>{
    presetEquipmentType(
      GET_PRESET_EQUIPMENT_TYPE_LIST,
      {},
      ()=>null,
      (presetResponse)=>{
        if(presetResponse.status){
          if(presetResponse.status==200){
            if(presetResponse.data.data){
              this.storeEquipType(presetResponse.data.data);
              // let equipTypelist = [];

              // for (let item of presetResponse.data.data) {
              //   equipTypelist.push({
              //     id: item.id,
              //     value: item.equipmentType,
              //     label: item.equipmentType,
              //     selected: false,
              //   });
              // }
              // this.setState({equipmentTypeList:equipTypelist})
              // this.checkEdit();
            }
          }
          else if(presetResponse.status==400){
            this.showError("error", presetResponse.data.message); 
          }else{
            this.showError("error", Strings.errors.failed);
          }
        }
        else {
          this.showError("error", Strings.errors.failed);
        }
      })
  }

  getControlledByList = () => {
    getAllMemberList(
      LIST_ALL_MEMBER +
        this.props.projectDetails.id +
        "/" +
        this.props.projectDetails.ParentCompany.id,
      {},
      () => {},
      (response) => {
        if (response.status) {
          if (response.data.message == Strings.popup.getMemEqui) {
            this.storeContactPerson(response.data.data);
            // this.setState({
            //   memberlist: response.data.data
            // })
          } else {
            this.setState({
              memberlist: [],
            });
          }
        } else {
          this.showError("error", Strings.errors.failed);
        }
      }
    );
  };

  storeContactPerson = (data) => {
    let memberList = [];

    for (let item of data) {
      if(item.User.firstName!=null){
      memberList.push({
        label: item.User.firstName+" "+item.User.lastName+" ("+item.User.email+")",
        value: item.User.email,
        id: item.id,
        name:item.User.email,
      });
    }else{
      memberList.push({
        label: item.User.email,
        value: item.User.email,
        id: item.id,
      });
    }
    }
    this.setState({ controlledByList: memberList });
  };

  storeEquipType = (data) => {
    let equipTypelist = [];
    for (let item of data) {
      equipTypelist.push({
        id: item.id,
        value: item.equipmentType,
        label: item.equipmentType,
        selected: false,
        name:item.equipmentType,
      });
    }
    this.setState({equipTypeList:equipTypelist})
  };

  getRoles = () => {
    getRole(
      GET_ROLE,
      {},
      () => {},
      (response) => {
        if (response.status) {
          if (response.data.message == "Role list.") {
            this.props.storeRole(response.data.data);
            this.storeRole(response.data.data);
            // this.setState({rolelist: response.data.data})
          }
        }
      }
    );
  };

  storeRole = (data) => {
    let roles = [];
    for (let i = 0; i < data.length; i++) {
      roles.push({
        label: data[i].roleName,
        value: data[i].roleName,
        id: data[i].id,
      });
    }
    this.setState({ rolelist: roles });
  };

  getMemeberList = async () => {
    let url = `${GET_EQUIP_LIST}${this.props.projectDetails.id}/20/${this.page_number}/${this.props.projectDetails.ParentCompany.id}`;

    this.setState({
      showNoData: false,
      showLoader:true,  
    });

    let param = {};

    if (this.state.filter == true) {
      param = {
        idFilter: this.state.searchId,
        companyNameFilter:this.state.searchCompany,
        typeFilter: this.state.selectedEquipName,
        memberFilter: this.state.selectedControlledById,
        nameFilter: this.state.searchText,
        isFilter: true,
        showActivatedAlone: false,
      };
    } else {
      param = {
        isFilter: false,
        showActivatedAlone: false,
      };
    }

    await getEquipList(
      url,
      param,
      () => {},
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.message === "Equipment Listed successfully.") {
            let data = this.state.memberslist;
            this.props.storeLastid(response.data.lastId.id);

            if (this.page_number == 1) {
              if (response.data.data.count != 0) {
                this.setState({
                  memberslist: response.data.data.rows,
                  totalCount: response.data.data.count,
                  lastId: response.data.lastId.id,
                });
              } else {
                this.setState({
                  showNoData: true,
                  memberslist: [],
                });
              }
            } else {
              let data1 = response.data.data.rows;
              this.setState({
                memberslist: data.concat(data1),
                totalCount: response.data.data.count,
                lastId: response.data.lastId.id,
              });
            }
            // this.setState({showToaster: true, toastMessage: response.data.message, toastType: 'error'}, ()=>{
            //   setTimeout(()=>{
            //     this.setState({showToaster: false})
            //   }, 2000)
            // })
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({ showToaster: false });
              }, 2000);
            }
          );
        }
      }
    );
  };

  renderEmail = (title, name) => {
    return (
      <View style={styles.emailContainer}>
        <Text style={styles.emailTitle}>{title}</Text>
        <Text style={styles.emailText}>{name}</Text>
      </View>
    );
  };

  renderRow = (option, index, isSelected) => {
    return (
      <View
        style={{
          width: wp("40%"),
          flexDirection: "row",
          alignItems: "center",
          height: hp("7%"),
          backgroundColor: "white",
        }}
      >
        <Image
          resizeMode={"center"}
          source={option.image}
          style={{
            width: option.id == "Edit" ? wp("5%") : wp("6%"),
            height: option.id == "Edit" ? hp("4%") : hp("5%"),
            marginLeft: 10,
          }}
        />
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: Colors.planDesc,
            marginLeft: 10,
          }}
        >
          {option.id}
        </Text>
      </View>
    );
  };

  renderSeparator = () => {
    return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
  };

  onSelectDropdown = (option, index, item) => {
    if (option == 0) {
      this.editEquipment(item, index);
    } else {
      this.setState({
        showDelete: true,
        selectedEquip: item,
        selectedIndex: index,
      });
    }
  };

  deleteEqui = (item, index) => {
    let params = {
      id: [item.id],
      ProjectId: this.props.projectDetails.id,
      isSelectAll: false,
    };

    this.setState({ showLoader: true });
    deleteEquipment(
      DELETE_EQUIP,
      params,
      () => {},
      (response) => {
        if (response.status) {
          if (response.data.message.message) {
            let array = Object.values(response.data.message.details[0]);

            this.setState(
              {
                showToaster: true,
                toastMessage: array.toString(),
                toastType: "error",
                showLoader: false,
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          } else {
          if (response.status == 200) {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "success",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
            this.page_number = 1;
            // this.state.memberslist.splice(index, 1)
            this.getMemeberList();
            trackEvent('Deleted_Equiment')
            mixPanelTrackEvent('Deleted Equiment',this.state.mixpanelParam)
          } else if(response.status == 500) {
            this.setState(
              {
                errorMessage:response.data.message,
                showLoader: false,
                showError:true,
              });
          }
          else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
                showLoader: false,
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
        }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
              showLoader: false,
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
    // {
    //   "id": [
    //     1
    //   ],
    //   "ProjectId": 1,
    //   "isSelectAll": true
    // }
  };

  editEquipment = (item, index) => {
    this.props.storeLastid(item.equipmentAutoId);
    this.props.editData({
      item: item,
      index: index,
    });
    this.props.clickAdd(true);
  };
      renderItem=({item,index})=>{
    if(index<3){
   return(
  <Avatar.Text size={24} label={item.label} color="white" theme="grey" style={{backgroundColor:'grey', marginLeft:5}}/>)
  }
  };

  renderFlatListItem = ({ item, index }) => {
let responsiblePersons=[];
let isAdditionalAvatar=false; 
let company='---';
if(item.controllUserDetails){
  let name= item.controllUserDetails.User.firstName!=null?`${item.controllUserDetails.User.firstName} ${item.controllUserDetails.User.lastName}`:item.controllUserDetails.User.email;
  let email= item.controllUserDetails.User.email;
  let phoneNumber= item.controllUserDetails.phoneCode!=null? item.controllUserDetails.phoneCode+ item.controllUserDetails.phoneNumber:null;
let data= item.controllUserDetails.firstName!=null?`${item.controllUserDetails.User.firstName.charAt(0)}${item.controllUserDetails.User.lastName.charAt(0)}`:"uu";
 responsiblePersons.push({"label":data,"name":name,'email':email,'phoneNumber':phoneNumber})
 company=item.controllUserDetails.Company!=null||item.controllUserDetails.Company ?item.controllUserDetails.Company.companyName:"---";
}
    return (
      <View style={styles.flatlistContainer}>
        <View style={{ width: wp("90%") }}>
          <View style={styles.nameContainer}>
            <Text
              numberOfLines={1}
              style={{
                width: wp("70%"),
                color: "#1E1E1E",
                fontSize: 15,
                margin: 10,
                fontFamily: Fonts.montserratSemiBold,
              }}
            >
              {item.equipmentName}
            </Text>
            <View style={styles.dotMenu}>        
              <ModalDropdown
                saveScrollPosition={false}
                style={styles.customDropdownStyle}
                dropdownStyle={styles.customOptionsStyle}
                dropdownTextStyle={styles.customOptionsTextStyle}
                 options={DROPDOWNOPTIONS}
                renderRow={this.renderRow}
                renderSeparator={this.renderSeparator}
                showsVerticalScrollIndicator={false}
                onSelect={(options) =>
                  this.onSelectDropdown(options, index, item)
                }
                defaultValue=""
                dropdownListProps={{}}
              >
                <View style={styles.imageContainer}>
                  <Image
                    style={{ height: 10, width: 30 }}
                    source={Images.dotmenu}
                  />
                </View>
              </ModalDropdown>
            </View>
          </View>

          <View style={styles.equipContainer}>
            <View style={{ flexDirection: "row" }}>
              <Text style={[styles.titleEquip]}>{Strings.equip.company}</Text>
              <Text style={[styles.titleEquip, { marginLeft: 15 }]}>
                {Strings.equip.contact}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <Text style={[styles.valueEquip]}>
                {company}
              </Text>

              <TouchableOpacity style={{flexDirection:'row',marginLeft:10,}} onPress={()=>this.setState({responsiblePerson:responsiblePersons,isModal:true})}>
                <FlatList
                data={responsiblePersons}
                renderItem={this.renderItem}
                horizontal={true}
                scrollEnabled={false}
               />
                { isAdditionalAvatar&&
                  <Avatar.Text size={24} label={`+${count}`} color="white" theme="grey" style={{backgroundColor:'grey', marginLeft:5}}/>

                }
              </TouchableOpacity>

            </View>

            <View style={{ flexDirection: "row" }}>
              <Text style={styles.titleEquip}>{Strings.equip.type}</Text>

              <Text style={[styles.titleEquip, { marginLeft: 15 }]}>
                {Strings.equip.id}
              </Text>
            </View>

            <View style={{ flexDirection: "row", marginBottom: 15 }}>
              <Text style={styles.valueEquip}>{item.PresetEquipmentType.equipmentType}</Text>

              <Text style={[styles.valueEquip, { marginLeft: 15 }]}>
                {item.equipmentAutoId}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  renderHeader() {
    
    let count = 0;
    if (this.state.selectedEquipName !== null) {
      count = count+1;
    }

    if (this.state.searchId !=="") {
      count = count + 1;
    }

    if (this.state.selectedControlledById !== 0) {
      count = count + 1;
    }
    if(this.state.searchCompany!==""){
      count=count+1;
    }
    if (this.state.searchText !== "") {
      count = count + 1;
    }

    return (
      <View style={styles.headerContainer}>
        <View style={{ flex: 1, flexDirection: "row" }}>
        {/* {this.props.checkCameBack == true && (
            <View style={styles.backIconView}>
              <TouchableWithoutFeedback
                onPress={() => {
                  //this.props.cameBack(true);
                  //newly added
                  //this.props.refreshDashboard(true);
                  this.props.navigation.goBack();
                }}
              >
                <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
              </TouchableWithoutFeedback>
            </View>
          )} */}
          <View style={styles.titleView}>
        <Text style={styles.title}>{Strings.menu.equip}</Text>
        </View>
        </View>
        <View style={styles.headerRowContainer}>
          <TouchableOpacity
            style={[styles.image, { marginTop: wp("1%") }]}
            onPress={() => this.setState({ showFilter: true })}
          >
            <Image source={Images.filter} />
            {this.state.filter == true && (
              <View
                style={{
                  position: "absolute",
                  marginTop: -10,
                  right: -10,
                  backgroundColor: Colors.themeColor,
                  width: 16,
                  justifyContent: "center",
                  alignItems: "center",
                  height: 16,
                  borderRadius: 8,
                }}
              >
                <Text style={{ color: "white" }}>{count}</Text>
              </View>
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.image}
            onPress={() => this.props.onTapSearch("equipSearch")}
          >
           <Image source={Images.Search1} style={{height:21,width:21,}}/>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  onEndReached = () => {
    if (
      this.state.memberslist.length < this.state.totalCount &&
      this.state.apiLoad == false
    ) {
      this.page_number = this.page_number + 1;
      this.getMemeberList();
      this.onEndReachedCalledDuringMomentum = true;
    }
  };

  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        memberslist: [],
        totalCount: 0,
        showLoader: true,
        //load: true,
      },
      () => {
        this.getMemeberList();
      }
    );
  };

  applyFilter = () => {
    if (
      this.state.searchText !== "" ||
      this.state.searchId !== "" ||
      this.state.searchCompany!==""||
      this.state.selectedControlledBy !== null ||
      this.state.selectedEquipName !== null
    ) {
      this.setState(
        {
          filter: true,
          showFilter: false,
          showLoader: true,
        },
        () => {
          this.getMemeberList();
        }
      );
    }
  };

  onPressEqipType=(item)=>{
    this.setState({
      selectedEquipName: item.value,
      selectedEquipId: item.id,
      typeModalVisible:false,
    })
  }

  onPressControllType=(item)=>{
    this.setState({
      selectedControlledBy: item.value,
      selectedControlledById: item.id,
      controllByModalVisible:false,
    })
  }

  renderFilter = () => {
    return (
      <SafeAreaView>
        <KeyboardAwareScrollView>
          <View style={modalStyles.container}>
            <View style={modalStyles.topContainer}>
              <TouchableOpacity
                onPress={() => this.setState({ showFilter: false })}
                style={{ width: 40 }}
              >
                <Image source={Images.closeBlack} />
              </TouchableOpacity>
              <View style={modalStyles.titleContainer}>
                <Text style={modalStyles.title}>{Strings.filter.title}</Text>
              </View>
              <View style={{ width: 40, height: 40 }} />
            </View>

            <TextField
              showLeft={true}
              attrName={Strings.placeholders.equipmentName}
              title={Strings.placeholders.equipmentName}
              value={this.state.searchText}
              updateMasterState={(key, value) => {
                this.setState({
                  searchText: value,
                });
              }}
              hideShow={false}
              hideImage={""}
              textInputStyles={{
                color: Colors.black,
                fontSize: 14,
                width: "75%",
                marginLeft: wp("10%"),
                fontFamily: Fonts.montserratMedium,
                paddingTop: 10,
              }}
              textTitleStyles={{
                marginLeft: wp("10%"),
                fontSize: 14,
                fontFamily: Fonts.montserratMedium,
              }}
              leftImage={Images.searchGray}
              leftButton={{ bottom: 0 }}
            />
            <TextField
              showLeft={true}
              attrName={Strings.placeholders.companyName}
              title={Strings.placeholders.companyName}
              value={this.state.searchCompany}
              updateMasterState={(key, value) => {
                this.setState({
                  searchCompany: value,
                });
              }}
              hideShow={false}
              hideImage={""}
              textInputStyles={{
                color: Colors.black,
                fontSize: 14,
                width: "75%",
                marginLeft: wp("10%"),
                fontFamily: Fonts.montserratMedium,
                paddingTop: 10,
              }}
              textTitleStyles={{
                marginLeft: wp("10%"),
                fontSize: 14,
                fontFamily: Fonts.montserratMedium,
              }}
              leftImage={Images.searchGray}
              leftButton={{ bottom: 0 }}
            />


            <TextField
              container={{ marginTop: 0 }}
              showLeft={true}
              attrName={Strings.placeholders.id}
              title={Strings.placeholders.id}
              value={this.state.searchId}
              updateMasterState={(key, value) => {
                this.setState({
                  searchId: value,
                });
              }}
              
            />

              <TextField
                  attrName={Strings.placeholders.type}
                  title={Strings.placeholders.type}
                  value={this.state.selectedEquipName}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  textInputStyles={{
                    // here you can add additional TextInput styles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                  showButton={true}
                  onPress={() => {
                    this.setState({ typeModalVisible : true });
                  }}
                  imageSource={Images.downArr}
                // placeholder={"Select"}
              />

               <Dropdown
                  data={this.state.equipTypeList}
                  title={Strings.placeholders.type}
                  value={this.state.selectedEquipName}
                  closeBtn={() => this.setState({ typeModalVisible: false })}
                  onPress={(item) => this.onPressEqipType(item)}
                  visible={this.state.typeModalVisible}
                  onbackPress={() => this.setState({ typeModalVisible: false })}
                  container={styles.equipmentContainer}
                  customMainContainer={styles.renderEquipStyle}
                  equipTextContainer={styles.equipTextStyle}
                  // customTextTitle={styles.textCustomStyle}
                  // closeBtnStyle={styles.closeButtonStyle}
               />

            {/* <DropDownPicker
              items={this.state.equipTypeList}
              defaultValue={this.state.selectedEquipName}
              placeholder={Strings.placeholders.type}
              placeholderStyle={modalStyles.filterPlaceholder}
              containerStyle={{ height: hp("6%"), marginTop: 10 }}
              style={{
                backgroundColor: Colors.white,
                width: wp("90%"),
                borderColor: "#0000",
                borderBottomColor: Colors.placeholder,
                alignSelf: "center",
                height: hp("5%"),
              }}
              itemStyle={{
                justifyContent: "flex-start",
              }}
              customArrowUp={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              customArrowDown={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              dropDownStyle={{
                backgroundColor: Colors.white,
                width: "90%",
                alignSelf: "center",
              }}
              onChangeItem={(item) =>
                this.setState({
                  selectedEquipName: item.value,
                  selectedEquipId: item.id,
                })
              }
              selectedLabelStyle={{ color: Colors.black }}
              zIndex={4000}
            /> */}

            <TextField
                  attrName={Strings.placeholders.contactPerson}
                  title={Strings.placeholders.contactPerson}
                  value={this.state.selectedControlledBy}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  textInputStyles={{
                    // here you can add additional TextInput styles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                  showButton={true}
                  onPress={() => {
                    this.setState({ controllByModalVisible: true });
                  }}
                  imageSource={Images.downArr}
                // placeholder={"Select"}
              />

               <Dropdown
                  data={this.state.controlledByList}
                  title={Strings.placeholders.controlledBy}
                  value={this.state.selectedControlledBy}
                  closeBtn={() => this.setState({ controllByModalVisible: false })}
                  onPress={(item) => this.onPressControllType(item)}
                  visible={this.state.controllByModalVisible}
                  onbackPress={() => this.setState({ controllByModalVisible: false })}
                  container={styles.equipmentContainer}
                  customMainContainer={styles.renderEquipStyle}
                  equipTextContainer={styles.equipTextStyle}
                  // customTextTitle={styles.textCustomStyle}
                  // closeBtnStyle={styles.closeButtonStyle}
               />

            {/* <DropDownPicker
              items={this.state.controlledByList}
              defaultValue={this.state.selectedControlledBy}
              placeholder={Strings.placeholders.controlledBy}
              placeholderStyle={modalStyles.filterPlaceholder}
              containerStyle={{ height: hp("6%"), marginTop: 30 }}
              style={{
                backgroundColor: Colors.white,
                width: wp("90%"),
                borderColor: "#0000",
                borderBottomColor: Colors.placeholder,
                alignSelf: "center",
                height: hp("5%"),
              }}
              itemStyle={{
                justifyContent: "flex-start",
              }}
              customArrowUp={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              customArrowDown={(size) => (
                <Image
                  source={Images.downArr}
                  style={{ width: size, height: size, alignSelf: "flex-end" }}
                />
              )}
              dropDownStyle={{
                backgroundColor: Colors.white,
                width: "90%",
                alignSelf: "center",
              }}
              onChangeItem={(item) =>
                this.setState({
                  selectedControlledBy: item.value,
                  selectedControlledById: item.id,
                })
              }
              selectedLabelStyle={{ color: Colors.black,height:hp('5.5') }}
              zIndex={3000}
            /> */}

            <View style={modalStyles.buttonContainer}>
              <TouchableOpacity
                style={
                  this.state.filter == true
                    ? [
                        modalStyles.cancelButton,
                        { backgroundColor: Colors.themeOpacity },
                      ]
                    : modalStyles.cancelButton
                }
                onPress={() =>
                  this.setState(
                    {
                      showFilter: false,
                      selectedControlledBy: null,
                      selectedControlledById: 0,
                      selectedEquipName: null,
                      selectedEquipId: '',
                      searchId: "",
                      searchText: "",
                      searchCompany:'',
                    },
                    () => {
                      if (this.state.filter == true) {
                        this.setState({ filter: false }, () => {
                          this.getMemeberList();
                        });
                      }
                    }
                  )
                }
              >
                <Text
                  style={[
                    modalStyles.cancelText,
                    {
                      color:
                        this.state.filter == true
                          ? Colors.themeColor
                          : Colors.buttonBackground,
                    },
                  ]}
                >
                  {this.state.filter == true
                    ? Strings.addMember.reset
                    : Strings.addMember.cancel}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={modalStyles.applyButton}
                onPress={() => this.applyFilter()}
              >
                <Text style={modalStyles.applyText}>
                  {Strings.filter.apply}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAwareScrollView>
      </SafeAreaView>
    );
  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.parentContainer}>
          {this.renderHeader()}

          <FlatList
            data={this.state.memberslist}
            renderItem={this.renderFlatListItem}
            keyExtractor={(item, index) => index.toString()}
            onEndReached={() => this.onEndReached()}
            onEndReachedThreshold={0}
            onMomentumScrollBegin={() => {
              this.onEndReachedCalledDuringMomentum = false;
            }}
            onRefresh={() => this._onReset()}
            refreshing={this.state.refreshing}
          />

          {this.state.showNoData == true && (
            <Text
              style={{
                alignSelf: "center",
                position: "absolute",
                fontSize: wp("6%"),
                fontFamily: Fonts.montserratRegular,
                marginTop: hp("45%"),
              }}
            >
              No Equipments Found
            </Text>
          )}
        </View>
        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}
        {this.state.isModal && (
          <ResDropdown
            data={this.state.responsiblePerson}
            title={Strings.deliverydetails.responsiblePerson}
            value={""}
            closeBtn={() =>
              this.setState({ isModal: false, downloaditem: {} })
            }
            onPress={()=>null}
            visible={this.state.isModal}
            onbackPress={() =>
              this.setState({ isModal: false })
            }
            //container={{  alignItems: "center" }}
            textContainer={{ fontSize: 16 }}
          />
          )
          }
        {this.state.showDelete && (
          <DeletePop
            title={Strings.popup.success}
            desc={Strings.popup.delete}
            acceptTap={() => {
              this.setState({ showDelete: false });
              this.deleteEqui(
                this.state.selectedEquip,
                this.state.selectedIndex
              );
            }}
            container={{height:Platform.OS=="android"?hp('38%'):hp('40%'),bottom:0,}}
            declineTap={() => {
              this.setState({
                showDelete: false,
                selectedEquip: [],
                selectedIndex: null,
              });
            }}
          />
        )}

        {this.state.showError && (<DeleteError message={this.state.errorMessage} close={() => this.setState({ showError: false })} />)}

        <Modal
          isVisible={this.state.showFilter}
          style={{
            paddingTop: 45,
            paddingBottom: 30,
            margin: 0,
            backgroundColor: Colors.white,
          }}
        >
          {this.renderFilter()}
        </Modal>
      </SafeAreaView>
      }
      </>
    );
  }
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
    height: hp("50%"),
  },
  parentContainer: {
    flex: 1,
    backgroundColor: "#FCFBFC",
  },
  headerContainer: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderBottomWidth: 0.3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  titleView: {
    flex: 1,
    minHeight: 50,
    minWidth: "60%",
    justifyContent: "center",
    marginLeft: wp("4%"),
  },
  title: {
    color: Colors.black,
    fontSize: 22,
    fontFamily: Fonts.montserratBold,
  },
  headerRowContainer: {
    flex: 1,
    height: hp("15%"),
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  backIconView: {
    width: 50,
    minHeight: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  image: {
    marginRight: wp("6%"),
    marginBottom: hp("2%"),
  },
  flatlistContainer: {
    width: wp("90%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    alignSelf: "center",
    borderRadius: wp("2%"),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 6,
  },
  nameContainer: {
    height: hp("7%"),
    width: wp("90%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
    borderRadius: wp("2%"),
  },
  detailContainer: {
    width: wp("53%"),
    marginLeft: 20,
    justifyContent: "center",
  },
  imagePlaceholder: {
    width: wp("14%"),
    height: wp("14%"),
    borderRadius: wp("7%"),
    marginLeft: wp("6%"),
  },
  nameText: {
    color: Colors.black,
    fontSize: wp("5%"),
    fontFamily: Fonts.montserratSemiBold,
  },
  companyText: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  dotMenu: {
    marginTop: hp("1%"),
  },
  emailContainer: {
    flex: 1,
    marginLeft: 10,
    marginRight: 10,
  },
  emailTitle: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    margin: 5,
  },
  emailText: {
    color: "#1E1E1E",
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginTop: 0,
  },
  equipContainer: {
    flex: 1,
    width: wp("90%"),
    justifyContent: "space-between",
  },
  titleEquip: {
    width: wp("38%"),
    fontSize: 11,
    color: "#5B5B5B",
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginLeft: 10,
  },
  valueEquip: {
    fontSize: 14,
    color: "#1E1E1E",
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginLeft: 10,
    width: wp("38%"),
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    marginRight: wp("15%"),
    borderWidth: 0.3,
    alignSelf: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
    elevation: 16
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: wp("15%"),
  },
  equipmentContainer:{
    height: hp("4%"),
    paddingBottom:5
  },
  renderEquipStyle:{
   marginBottom:10,
  },
  equipTextStyle:{
    width:'100%',
    fontSize:16
  },
});

const modalStyles = StyleSheet.create({
  container: {
    flex: 1,
    width: wp("100%"),
    height: hp("95%"),
  },
  topContainer: {
    flexDirection: "row",
    margin: 20,
    height: 40,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 20,
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  buttonContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-end",
    marginBottom: 50,
    justifyContent: "space-around",
  },
  cancelButton: {
    backgroundColor: `rgba(117,117,117,0.2)`,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonBackground,
    fontSize: 14,
    fontFamily: Fonts.montserratBold,
  },
  applyButton: {
    backgroundColor: Colors.themeOpacity,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  applyText: {
    color: Colors.themeColor,
    fontSize: 14,
    fontFamily: Fonts.montserratBold,
  },
  filterPlaceholder:{
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
  },
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    updatelist,
    refresh,
    projectSwitched,
    userDetails,
  } = state.LoginReducer;
  return {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    updatelist,
    refresh,
    projectSwitched,
    userDetails,
  };
};

export default connect(mapStateToProps, {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  editData,
  clickAdd,
  onTapSearch,
  storeRole,
  updateList,
  refreshPage,
})(EquipmentList);
