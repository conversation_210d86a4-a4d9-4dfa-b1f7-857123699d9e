import React, { Component } from "react";
import { View, StyleSheet, SafeAreaView, Platform, Text } from "react-native";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import { storeUserid } from "../../actions/postAction";
import {
  Image as AnimatableImage,
  View as AnimatableView,
} from "react-native-animatable";
import Images from "../../common/images";
import Strings from "../../common/string";
import HeaderAnimation from "../../components/logoAnimation/logoAnimation";
import Header from "../../components/headerComponent/Header";
import { TextField } from "../../components/textinput/Textinput";
import Fonts from "../../common/fonts";
import NextButton from "../../components/nextButton/NextButton";
import { FORGOT_PASSWORD } from "../../api/Constants";
import { forgotPassword } from "../../api/Api";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import { isEmpty, isValidEmail } from "../../common/validators";
import Alert from "../../components/toastpopup/alert";
import { CommonActions } from '@react-navigation/native';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import AppLoader from '../../components/apploader/AppLoader'
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";

const rotation = {
  from: {
    rotate: "0deg",
  },
  to: {
    rotate: "360deg",
  },
};

class Forgotpassword extends Component {
  constructor(props) {
    super(props);
    this.state = {
      email: "",
      showLoader: false,
      showToaster: false,
      toastType: "error",
      toastMessage: "",
      showAlert: false,
      isNetworkCheck: false
    };
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck()
    }
  }
  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
    }
    })
  }

  //ON BACK PRESS
  onBackPress = () => {
    this.props.navigation.goBack();
  };

  //ON CHANGE TEXT
  updateMasterState = (key, value) => {
    if (key == Strings.placeholders.email) {
      this.setState({ email: value });
    }
  };

  //NEXT CLICK
  nextClick = async () => {
    if (isEmpty(this.state.email.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyEmail,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
    } else if (isValidEmail(this.state.email)) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.validEmail,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
    } else {
      this.setState({ showLoader: true });

      let data = {
        email: this.state.email.trim(),
        requestType: 1,
      };
      await forgotPassword(
        FORGOT_PASSWORD,
        data,
        () => {},
        (response) => {
          this.setState({ showLoader: false });
          if (response.status) {
            if (
              response.data.message == "Reset password email sent successfully"
            ) {
              this.setState({ showAlert: true });
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.data.message,
                  toastType: "error",
                },
                () => {
                  setTimeout(() => {
                    this.setState({ showToaster: false });
                  }, 2000);
                }
              );
            }
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.toString(),
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          }
        }
      );
    }
  };

  //OK TAP
  okTap = () => {
    this.setState({ showAlert: false });
    const resetAction = CommonActions.reset({
      index: 0,
      routes: [{ name: "Dashboard" }],
    });
    this.props.navigation.dispatch(resetAction);
  };

  //Main Render method
  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAwareScrollView extraScrollHeight={50}>
          <View style={styles.parentContainer}>
            <HeaderAnimation />
            <View style={styles.subContainer}>
              <AnimatableView animation={"bounceInLeft"} duration={800}>
                <AnimatableImage
                  animation={rotation}
                  duration={800}
                  source={Images.path3}
                  style={styles.path}
                />
              </AnimatableView>

              <View
                style={{
                  width: "100%",
                  height: hp("77%"),
                  backgroundColor: "#0000",
                  justifyContent: "flex-end",
                }}
              >
                <AnimatableView duration={500} style={styles.signupContainer}>
                  <Header
                    backPress={() => this.onBackPress()}
                    title={Strings.login.forgotPassword}
                  />

                  <Text style={styles.enterEmail}>
                    {Strings.login.enterEmail}
                  </Text>

                  {/* <View style={{width: wp('90%'), alignItems: 'center', justifyContent: 'center'}}> */}
                  <TextField
                    attrName={Strings.placeholders.email}
                    title={Strings.placeholders.email}
                    value={this.state.email.toLowerCase()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: wp("4.5%"),
                    }}
                    container={{
                      width: wp("80%"),
                    }}
                    progressWidth={wp("80%")}
                  />

                  <NextButton
                    title={Strings.login.submit}
                    containerStyles={{
                      marginTop: hp("6%"),
                      marginRight: wp("10%"),
                    }}
                    nextClick={() => this.nextClick()}
                  />

                  {/* </View> */}
                </AnimatableView>
              </View>
            </View>
          </View>
        </KeyboardAwareScrollView>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.forgotSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}
      </SafeAreaView>
      }
      </>
    );
  }
}

const mapStateToProps = (state) => {
  const { userid } = state.LoginReducer;

  return {
    userid,
  };
};

export default connect(mapStateToProps, {
  storeUserid,
})(Forgotpassword);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  subContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  enterEmail: {
    width: wp("80%"),
    alignSelf: "center",
    marginTop: hp("2%"),
    color: Colors.planDesc,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("3.5%"),
  },
  signupContainer: {
    width: wp("100%"),
    height: hp("60%"),
    borderTopLeftRadius: hp("6%"),
    borderTopRightRadius: hp("6%"),
    zIndex: 999,
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0.8,
    shadowOffset: { width: 10, height: 10 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  path: {
    width: wp("75%"),
    height: hp("43%"),
    marginBottom: -hp("25%"),
    marginRight: -wp("40%"),
    alignSelf: "flex-end",
  },
});
