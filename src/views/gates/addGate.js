import React, { Component } from "react";
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
  
  Platform
} from "react-native";

import {
  changeTab,
  showSideMenu,
  cameBack,
  editData,
  refreshPage,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import Alert from "../../components/toastpopup/alert";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import { TextField } from "../../components/textinput/addMemberTextinput";
import { isAlphaNumeric, isEmpty } from "../../common/validators";
import { ADD_GATES, UPDATE_GATES } from "../../api/Constants";
import { addGate, updateGate } from "../../api/Api";
import DeletePop from "../../components/toastpopup/logoutPop";
import AppLoader from '../../components/apploader/AppLoader'
import { trackScreen,trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import NetInfo from '@react-native-community/netinfo';
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";import NoInternet from "../../components/NoInternet/noInternet";
class AddGates extends Component {
  constructor(props) {
    super(props);
    this.state = {
      memberId: this.props.lastid,
      update: false,
      gateName: "",
      gateAutoId: 0,
      showCancel: false,
      comparision: "",
      isNetworkCheck: false,
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      },
    };
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck()
    } else {
      if (this.props.editedData.item) {
        trackScreen('Edit Gate')
        this.setState(
          {
            memberId: this.props.editedData.item.id,
            gateAutoId: this.props.editedData.item.gateAutoId,
            update: true,
            gateName: this.props.editedData.item.gateName,
            comparision: this.props.editedData.item.gateName,
          },
          () => {
            this.props.editData({});
          }
        );
      } else {
        trackScreen('Add Gate')
        this.setState({
          memberId: this.props.lastid,
        });
      }
    }
  }
  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      if (this.props.editedData.item) {
        trackScreen('Edit Gate')
        this.setState(
          {
            memberId: this.props.editedData.item.id,
            gateAutoId: this.props.editedData.item.gateAutoId,
            update: true,
            gateName: this.props.editedData.item.gateName,
            comparision: this.props.editedData.item.gateName,
          },
          () => {
            this.props.editData({});
          }
        );
      } else {
        trackScreen('Add Gate')
        this.setState({
          memberId: this.props.lastid,
        });
      }
    }
    })
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      

      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }
  }

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  onPressTab = (item, data) => {
  };

  //HEADER COMPONENT
  renderHeader() {
    return (
      <View style={styles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.props.cameBack(true);
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={styles.title}>{this.state.update?Strings.addGate.edit:Strings.addGate.add}</Text>
      </View>
    );
  }

  renderImage() {
    return (
      <View style={styles.imageContainer}>
        <TouchableWithoutFeedback
          onPress={() => {
            // alert('coming soon')
          }}
        >
          <View style={styles.imageButton}>
            <Image
              resizeMode={"center"}
              source={Images.placeholder}
              style={styles.imageButton}
            />
            <View style={styles.camera}>
              <Image source={Images.camera} />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  submit = async () => {
    if (isEmpty(this.state.gateName.trim())) {
      this.showError("error", Strings.errors.emptyGateName);
    } else if (this.state.gateName.length < 3) {
      this.showError("error", "Gate name" + Strings.errors.lengthError);
    } else if (isAlphaNumeric(this.state.gateName)) {
      this.showError("error", Strings.errors.validgate);
    } else {
      this.setState({ showLoader: true });

      if (this.state.update) {
        let data = {
          id: this.state.memberId,
          ProjectId: this.props.projectDetails.id,
          gateName: this.state.gateName,
          ParentCompanyId: this.props.projectDetails.ParentCompany.id,
        };
        await updateGate(
          UPDATE_GATES,
          data,
          () => {},
          (resp) => {
            
            this.setState({ showLoader: false });

            if (resp.status) {
              if (resp.data.message == Strings.popup.gateUpdate) {
                this.setState(
                  {
                    showToaster: true,
                    toastMessage: resp.data.message,
                    toastType: "success",
                    gateName: "",
                  },
                  () => {
                    setTimeout(() => {
                      this.setState({ showToaster: false });
                      this.props.refreshPage(true);
                      this.props.cameBack(true);
                                              if (this.props.route.params?.from == "search") {
                        this.props.route.params.updateData("data");
                      }
                      this.props.navigation.goBack();
                    }, 2000);
                  }
                );
                trackEvent('Edited_Gate')
                mixPanelTrackEvent('Edited Gate',this.state.mixpanelParam)
              } else {
                this.setState(
                  {
                    showToaster: true,
                    toastMessage: Strings.popup.updateFailed,
                    toastType: "error",
                    gateName: "",
                  },
                  () => {
                    setTimeout(() => {
                      this.setState({ showToaster: false });
                      this.props.cameBack(true);
                      this.props.navigation.goBack();
                    }, 2000);
                  }
                );
              }
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: resp.toString(),
                  toastType: "error",
                },
                () => {
                  setTimeout(() => {
                    this.setState({
                      showToaster: false,
                    });
                  }, 2000);
                }
              );
            }
          }
        );
      } else {
        let data = {
          ProjectId: this.props.projectDetails.id,
          gateName: this.state.gateName,
          ParentCompanyId: this.props.projectDetails.ParentCompany.id,
        };
        await addGate(
          ADD_GATES,
          data,
          () => {},
          (resp) => {
            
            this.setState({ showLoader: false });

            if (resp.status) {
              if (resp.data.message.message) {
                if (resp.data.message.message == "Validation Failed") {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: resp.data.message.message,
                      toastType: "error",
                    },
                    () => {
                      setTimeout(() => {
                        this.setState({
                          showToaster: false,
                        });
                      }, 2000);
                    }
                  );
                }
              } else if (resp.data.message) {
                if (resp.data.message === "Gate added successfully.") {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: resp.data.message,
                      toastType: "success",
                      gateName: "",
                    },
                    () => {
                      setTimeout(() => {
                        this.setState({ showToaster: false });
                        this.props.refreshPage(true);
                        this.props.cameBack(true);
                        this.props.navigation.goBack();
                      }, 2000);
                    }
                  );
                  trackEvent('Added_Gate')
                  mixPanelTrackEvent('Added Gate',this.state.mixpanelParam)
                } else {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: resp.data.message,
                      toastType: "error",
                    },
                    () => {
                      setTimeout(() => {
                        this.setState({ showToaster: false });
                      }, 2000);
                    }
                  );
                }
              }
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: resp.toString(),
                  toastType: "error",
                },
                () => {
                  setTimeout(() => {
                    this.setState({
                      showToaster: false,
                    });
                  }, 2000);
                }
              );
            }
          }
        );
      }
    }
  };

  showError = (type, message) => {
    this.setState(
      { showToaster: true, toastType: type, toastMessage: message },
      () => {
        setTimeout(() => {
          this.setState({ showToaster: false });
        }, 2000);
      }
    );
  };
  onPressCancel = () => {
    if (this.state.update) {
      if (this.state.comparision == this.state.gateName) {
        this.props.cameBack(true);
        this.props.navigation.goBack();
      } else {
        this.setState({ showCancel: true });
      }
    } else {
      this.props.cameBack(true);
      this.props.navigation.goBack();
    }
  };
  bottomContainer = () => {
    return (
      <View style={styles.bottomContainer}>
        <TouchableOpacity onPress={this.onPressCancel}>
          <View style={styles.cancel}>
            <Text style={styles.cancelText}>{Strings.addMember.cancel}</Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            this.submit();
          }}
        >
          <View style={styles.submit}>
            <Text style={styles.submitText}>
              {this.state.update
                ? Strings.addMember.update
                : Strings.addMember.submit}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  renderMemberId() {
    return (
      <View style={styles.memberContainer}>
        <Text style={styles.idTitle}>{Strings.gates.gateId}</Text>
        <Text style={styles.idText}>
          {this.state.gateAutoId !== 0
            ? this.state.gateAutoId
            : this.state.memberId}
        </Text>
      </View>
    );
  }

  updateMasterState = (key, value) => {    
    this.setState({ gateName: value });
  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.parentContainer}>
          {this.renderHeader()}
          {this.renderMemberId()}

          <TextField
            attrName={Strings.placeholders.gateName}
            title={Strings.placeholders.gateName}
            value={this.state.gateName}
            maxLength={150}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput styles
              color: Colors.black,
              fontSize: wp("4.5%"),
            }}
          />

          {this.bottomContainer()}
        </View>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.loginSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}
        {this.state.update && this.state.showCancel && (
          <DeletePop
            title={Strings.popup.cancel}
            desc={Strings.popup.cancel}
            acceptTap={() => {
              this.setState({ showCancel: false });
              this.props.cameBack(true);
              this.props.navigation.goBack();
            }}
            container={{ bottom: 0 }}
            declineTap={() => {
              this.setState({ showCancel: false });
            }}
          />
        )}
      </SafeAreaView>
      }
      </>
    );
  }
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("80%"),
    fontSize: wp("6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratBold,
  },
  imageContainer: {
    width: wp("100%"),
    height: hp("20%"),
    justifyContent: "center",
    alignItems: "center",
  },
  imageButton: {
    width: wp("24%"),
    height: wp("24%"),
    borderRadius: wp("12%"),
    backgroundColor: "#F5F5F5",
    borderWidth: 1,
    borderColor: "#00000029",
  },
  placeholder: {
    width: wp("15%"),
    alignSelf: "center",
  },
  camera: {
    position: "absolute",
    bottom: 10,
    right: -10,
    width: wp("8%"),
    height: wp("8%"),
    borderRadius: wp("4%"),
    backgroundColor: Colors.white,
    justifyContent: "center",
    alignItems: "center",
  },
  memberContainer: {
    width: wp("90%"),
    height: hp("10%"),
    alignSelf: "center",
    marginTop: hp("2%"),
    justifyContent: "center",
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  idText: {
    width: wp("90%"),
    height: hp("5%"),
    backgroundColor: "#EFEFEF",
    marginTop: wp("1%"),
    padding: hp("1%"),
    paddingLeft: 15,
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("4%"),
  },
  bottomContainer: {
    flex: 1,
    width: wp("90%"),
    flexDirection: "row",
    marginBottom: hp("5%"),
    marginTop: hp("8%"),
    alignSelf: "center",
    alignItems: "flex-end",
    justifyContent: "center",
  },
  cancel: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
    bottom: 0,
  },
  submit: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
    bottom: 0,
  },
  cancelText: {
    color: Colors.buttonGray,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    lastid,
    projectDetails,
    editedData,
    userDetails,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    lastid,
    projectDetails,
    editedData,
    userDetails,
  };
};

export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    cameBack,
    editData,
    refreshPage,
  }),
  withBackHandler
)(AddGates);
