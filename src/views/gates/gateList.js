import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Image,
  FlatList,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Platform,
} from "react-native";

import {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  editData,
  clickAdd,
  onTapSearch,
  updateList,
  refreshPage,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import { GET_GATE_LIST, DELETE_GATES } from "../../api/Constants";
import { getGateList, deleteGate } from "../../api/Api";
import Alert from "../../components/toastpopup/alert";
import Toastpopup from "../../components/toastpopup/Toastpopup";

import DeletePop from "../../components/toastpopup/logoutPop";
import AppLoader from '../../components/apploader/AppLoader';
import { trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import DeleteError from "../../components/DeleteError/DeleteError";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";

class GateList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      memberslist: [],
      showLoader: false,
      showToaster: false,
      showAlert: false,
      toastMessage: "",
      toastType: "error",
      refreshing: false,
      lastId: 0,
      selectedAll: false,
      selectedGates: [],
      showNoData: false,
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      },
      showError:false,
      errorMessage:'',
      isNetworkCheck: false
    };

    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;
  }

  componentDidMount() {
    // Add focus listener for navigation events
    this.focusListener = this.props.navigation?.addListener('focus', () => {
      this.renderInital();
    });

    if(Platform.OS === 'ios') {
    this.networkCheck()
    } else {
      this.renderInital();
    }
  }

  componentWillUnmount() {
    // Remove the focus listener
    if (this.focusListener) {
      this.focusListener();
    }
  }
  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.renderInital();
    }
    })
  }

  renderInital = () => {
    this.setState({ showLoader: true });
    this.page_number = 1;
    this.getCompanyList();
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }

    if (nextProps.refresh === true) {
      this.setState({ showLoader: true });
      this.renderInital();
      this.props.updateList(false);
      this.props.refreshPage(false);
    }

    if (this.props.projectSwitched != nextProps.projectSwitched) {
      this.renderInital();
      this.props.updateList(false);
      this.props.refreshPage(false);
    }
  }

  getCompanyList = async () => {
    this.setState({ showLoader: true, showNoData: false });
    let param={
      sort:'ASC',
      sortByField:'id',
      isFilter:false,
      showActivatedAlone: false,
    }
  
    let url = `${GET_GATE_LIST}${this.props.projectDetails.id}/20/${this.page_number}/${this.props.projectDetails.ParentCompany.id}`;

    await getGateList(
      url,
      param,
      () => {},
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.message === "Gate Listed successfully.") {
            this.setState({ lastId: response.data.lastId.id });
            let data = this.state.memberslist;
            this.props.storeLastid(response.data.lastId.id);
            if (response.data.data.count == 0) {
              this.setState({ showNoData: true });
              // this.setState({showToaster: true, toastMessage: Strings.errors.noData, toastType: 'error'}, ()=>{
              //   setTimeout(()=>{
              //     this.setState({showToaster: false})
              //   }, 2000)
              // })
              if (this.page_number == 1) {
                this.setState({
                  memberslist: [],
                });
              }
            } else if (this.page_number == 1) {
              let gateresp = response.data.data.rows;
              for (let i = 0; i < response.data.data.rows.length; i++) {
                gateresp[i].selected = false;
              }
              this.setState({
                memberslist: response.data.data.rows,
                totalCount: response.data.data.count,
              });
            } else {
              let data1 = response.data.data.rows;
              for (let i = 0; i < data1.length; i++) {
                data1[i].selected = false;
              }
              this.setState({
                memberslist: data.concat(data1),
                totalCount: response.data.data.count,
              });
            }
          } else if (response.data.message == Strings.errors.noData) {
            if (this.page_number == 1) {
              this.setState({
                memberslist: [],
              });
            }
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({ showToaster: false });
              }, 2000);
            }
          );
        }
      }
    );
  };

  renderEmail = (title, name) => {
    return (
      <View style={styles.emailContainer}>
        <Text style={styles.emailTitle}>{title}</Text>
        <Text style={styles.emailText}>{name}</Text>
      </View>
    );
  };

  editGate = (item, index) => {
    this.props.editData({
      item: item,
      index: index,
    });
    this.props.clickAdd(true);
  };

  deleteApi = (param) => {
    this.setState({ showLoader: true });
    deleteGate(
      DELETE_GATES,
      param,
      () => {},
      (response) => {
        this.setState({ showLoader: false });

        this.page_number = 1;
        if (response.status) {
          if(response.status==500){
            this.setState(
              {
                errorMessage:response.data.message,
                showLoader: false,
                showError:true,
              });
          }
          else if (response.data.message.details) {
            let array = Object.values(response.data.message.details[0]);

            this.setState(
              {
                showToaster: true,
                toastMessage: array.toString(),
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          } else if (response.data.message == "Gate deleted successfully.") {
            this.setState(
              {
                showToaster: true,
                toastMessage: "Gate deleted successfully.",
                toastType: "success",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
            this.getCompanyList();
            trackEvent('Deleted_Gate')
            mixPanelTrackEvent('Deleted Gate',this.state.mixpanelParam)
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "success",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
            this.getCompanyList();
          }
          // if(response.data.)
          // this.setState({showToaster: true})
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({ showToaster: false });
              }, 2000);
            }
          );
        }
      }
    );

    this.setState({ selectedGates: [], selectedAll: false });
  };

  selectGate = (item, index) => {

    let data = this.state.memberslist;
    let selectedGate = [];
    for (let i = 0; i < this.state.memberslist.length; i++) {
      if (index == i) {
        data[i].selected = !data[i].selected;
      }

      if (data[i].selected == true) {
        selectedGate.push(data[i]);
      }
    }

    this.setState(
      { memberslist: data, selectedGates: selectedGate, selectedAll: false });
  };

  checkAll = () => {
    let data = this.state.memberslist;
    this.setState(
      {
        selectedAll: !this.state.selectedAll,
        showDeleteicon: !this.state.selectedAll,
      },
      () => {
        for (let i = 0; i < this.state.memberslist.length; i++) {
          if (this.state.selectedAll == true) {
            data[i].selected = true;
          } else {
            data[i].selected = false;
          }
        }
        this.setState({
          memberslist: data,
          selectedGates: this.state.selectedAll == true ? data : [],
        });
      }
    );
  };

  renderFlatListItem = ({ item, index }) => {

    return (
      <View>
        <View style={styles.flHeader}>
          <View style={styles.checkbox}>
            <TouchableWithoutFeedback
              onPress={() => {
                this.selectGate(item, index);
              }}
            >
              <Image
                resizeMode={"contain"}
                source={item.selected == true ? Images.check : Images.uncheck}
                style={{ width: wp("9%"), height: hp("4%") }}
              />
            </TouchableWithoutFeedback>
          </View>
          <View style={[styles.checkbox, { width: wp("15%") }]}>
            <Text style={styles.flatlistTitle}>{item.gateAutoId}</Text>
          </View>
          <View
            style={[
              styles.checkbox,
              { width: wp("50%"), marginBottom: hp("1%") },
            ]}
          >
            <Text style={styles.flatlistTitle} numberOfLines={2}>
              {item.gateName}
            </Text>
          </View>
          <View
            style={[
              styles.checkbox,
              {
                width: wp("18%"),
                flexDirection: "row",
                justifyContent: "space-around",
              },
            ]}
          >
            <TouchableWithoutFeedback
              onPress={() => this.editGate(item, index)}
            >
              <Image
                resizeMode={"contain"}
                source={Images.edit}
                style={{ width: wp("7%") }}
              />
            </TouchableWithoutFeedback>

            <TouchableWithoutFeedback
              onPress={() =>
                this.setState({
                  showDelete: true,
                  selectedGate: item,
                  selectedIndex: index,
                })
              }
            >
              <Image
                resizeMode={"contain"}
                source={Images.delete}
                style={{ width: wp("7%") }}
              />
            </TouchableWithoutFeedback>
            {/* <Text style={styles.flatlistTitle}>{Strings.gates.action}</Text> */}
          </View>
        </View>
        <View
          style={{
            backgroundColor: "#EFEFEF",
            height: hp("0.3%"),
            width: wp("96%"),
            alignSelf: "center",
          }}
        />
      </View>
    );
  };

  onPressDelete = () => {
    let data = this.state.selectedGates;
    let id = [];
    for (let i = 0; i < data.length; i++) {
      id.push(data[i].id);
    }
    let param = {
      id: id,
      ProjectId: this.props.projectDetails.id,
      isSelectAll: this.state.selectedAll,
    };
    if (this.state.selectedGates.length > 0) {
      this.deleteApi(param);
    }
    this.setState({ selectedGates: [] });
  };

  deleteGate = (item, index) => {
    // let data = this.state.memberslist
    // data.splice(index, 1)

    let param = {
      id: [item.id],
      ProjectId: this.props.projectDetails.id,
      isSelectAll: false,
    };
    this.deleteApi(param);
  };

  renderHeader() {
    let showButton = false;

    if (this.state.selectedGates.length > 0 || this.state.selectedAll == true) {
      showButton = true;
    }
    /*   else {
      showButton = false;
    } */

    return (
      <View style={styles.headerContainer}>
        <Text style={styles.title}>{Strings.menu.gates}</Text>
        <View style={styles.headerRowContainer}>
          {showButton == true && (
            <TouchableOpacity
              onPress={() => {
                this.setState({ showAllDelete: true });
              }}
              style={[styles.image, { marginTop: wp("1%") }]}
            >
              <Image source={Images.delete} />
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.image}
            onPress={() => this.props.onTapSearch("gateSearch")}
          >
          <Image source={Images.Search1} style={{height:21,width:21,}}/>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  onEndReached = () => {
    // if( this.state.memberslist.length < this.state.totalCount){
    //   this.page_number = this.page_number + 1
    //   this.getCompanyList()
    //   this.onEndReachedCalledDuringMomentum = true;
    // }
  };

  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        memberslist: [],
        totalCount: 0,
        showLoader: true,
        //load: true,
      },
      () => {
        this.getCompanyList();
      }
    );
  };

  renderFlatlistHeader = () => {
    if (this.state.showNoData == true || this.state.memberslist.length == 0) {
      return null;
    } else {
      return (
        <View>
          <View style={[styles.flHeader, { backgroundColor: "#f5f5f5" }]}>
            <View style={styles.checkbox}>
              <TouchableWithoutFeedback
                onPress={() => {
                  this.checkAll();
                }}
              >
                <Image
                  resizeMode={"contain"}
                  source={
                    this.state.selectedAll ? Images.check : Images.uncheck
                  }
                  style={{ width: wp("9%"), height: hp("4%") }}
                />
              </TouchableWithoutFeedback>
            </View>
            <View style={[styles.checkbox, { width: wp("15%") }]}>
              <Text
                style={[
                  styles.flatlistTitle,
                  { fontFamily: Fonts.montserratBold, fontWeight: "bold" },
                ]}
              >
                {Strings.gates.id}
              </Text>
            </View>
            <View style={[styles.checkbox, { width: wp("50%") }]}>
              <Text
                style={[
                  styles.flatlistTitle,
                  { fontFamily: Fonts.montserratBold, fontWeight: "bold" },
                ]}
              >
                {Strings.gates.gateName}
              </Text>
            </View>
            <View style={[styles.checkbox, { width: wp("18%") }]}>
              <Text
                style={[
                  styles.flatlistTitle,
                  { fontFamily: Fonts.montserratBold, fontWeight: "bold" },
                ]}
              >
                {Strings.gates.action}
              </Text>
            </View>
          </View>
          <View
            style={{
              backgroundColor: "#EFEFEF",
              height: hp("0.3%"),
              width: wp("96%"),
              alignSelf: "center",
            }}
          />
        </View>
      );
    }
  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView ref={this.props.refer} style={styles.safeArea}>

        <View style={styles.parentContainer}>
          {this.renderHeader()}

          <FlatList
            data={this.state.memberslist}
            renderItem={this.renderFlatListItem}
            ListHeaderComponent={this.renderFlatlistHeader()}
            // ItemSeparatorComponent={this.itemSeparator}
            keyExtractor={(item, index) => index.toString()}
            onEndReached={() => this.onEndReached()}
            onEndReachedThreshold={0}
            onMomentumScrollBegin={() => {
              this.onEndReachedCalledDuringMomentum = false;
            }}
            onRefresh={() => this._onReset()}
            refreshing={this.state.refreshing}
          />

          {this.state.showNoData == true && (
            <Text
              style={{
                alignSelf: "center",
                position: "absolute",
                fontSize: wp("6%"),
                fontFamily: Fonts.montserratRegular,
                marginTop: hp("45%"),
              }}
            >
              No Gates Found
            </Text>
          )}
        </View>
        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.forgotSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}
        {this.state.showError &&(  <DeleteError message={this.state.errorMessage} close={()=>this.setState({showError:false})}/>)}
        {this.state.showDelete && (
          <DeletePop
            title={Strings.popup.success}
            desc={Strings.popup.delete}
            acceptTap={() => {
              this.setState({ showDelete: false, });
              this.deleteGate(
                this.state.selectedGate,
                this.state.selectedIndex
              );
            }}
            declineTap={() => {
              this.setState({
                showDelete: false,
                selectedGate: [],
                selectedIndex: null,
              });
            }}
            container={styles.deletePopContainer}
          />
        )}

        {this.state.showAllDelete && (
          <DeletePop
            title={Strings.popup.success}
            desc={Strings.popup.delete}
            acceptTap={() => {
              this.setState({ showAllDelete: false });
              this.onPressDelete();
            }}
            declineTap={() => {
              this.setState({
                showAllDelete: false,
                selectedGate: [],
                selectedIndex: null,
              });
            }}
            container={styles.deletePopContainer}
          />
        )}
      </SafeAreaView>
      }
      </>
    );
  }
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
    height: hp("50%"),
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  headerContainer: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderBottomWidth: 0.3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 6,
  },
  title: {
    color: Colors.black,
    fontSize: wp("7%"),
    fontFamily: Fonts.montserratBold,
    marginBottom: hp("2%"),
    marginLeft: wp("4%"),
  },
  headerRowContainer: {
    flex: 1,
    height: hp("15%"),
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  image: {
    marginRight: wp("6%"),
    marginBottom: hp("2%"),
  },
  flatlistContainer: {
    width: wp("85%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  nameContainer: {
    height: hp("14%"),
    width: wp("85%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  detailContainer: {
    width: wp("58%"),
    marginLeft: 20,
    justifyContent: "center",
    backgroundColor: Colors.white,
  },
  imagePlaceholder: {
    width: wp("14%"),
    height: wp("14%"),
    borderRadius: wp("7%"),
    marginLeft: wp("6%"),
  },
  nameText: {
    color: Colors.black,
    fontSize: wp("5%"),
    fontFamily: Fonts.montserratSemiBold,
  },
  companyText: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  dotMenu: {
    marginTop: hp("2%"),
  },
  emailContainer: {
    flex: 1,
    marginLeft: 10,
    marginRight: 10,
  },
  emailTitle: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    margin: 5,
  },
  emailText: {
    color: "#1E1E1E",
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginTop: 0,
  },
  flHeader: {
    width: wp("100%"),
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "center",
    paddingTop: 5,
    paddingBottom: 5,
  },
  checkbox: {
    width: wp("13%"),
    // height: hp('6%'),
    justifyContent: "center",
    alignItems: "center",
  },
  flatlistTitle: {
    color: "#292529",
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
  },
  deletePopContainer:{
    height:'35%'
   },
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    updatelist,
    refresh,
    projectSwitched,
    userDetails,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    updatelist,
    refresh,
    projectSwitched,
    userDetails,
  };
};

export default connect(mapStateToProps, {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  editData,
  clickAdd,
  onTapSearch,
  updateList,
  refreshPage,
})(GateList);
