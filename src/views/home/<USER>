import React, { Component } from "react";
import { View, BackHandler } from "react-native";
import TabNavigator from "../../navigation/TabNavigation";
import {
  changeTab,
  showSideMenu,
  clickAdd,
  onTapSearch,
  onTapDetail,
  goToVoid,
  setPage,
  updateList,
  toggleAssociatedWithCrane,
  eventDisplayPage,
  onPressConcreteDetail,
} from "../../actions/postAction";
import { connect } from "react-redux";
import Strings from "../../common/string";
var memberId, parentCompanyId, email;
import { trackScreen } from "../../Google Analytics/GoogleAnalytics";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";
class Home extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isNetworkCheck: false
    };
  }

  onBackPress = () => {
    return true;
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {     
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }

    if (nextProps.currentPage == "Profile") {
      this.props.setPage("");
      this.props.navigation.navigate("Profile");
    }

    if (nextProps.currentPage == "Member") {
      this.props.setPage("");
      this.props.navigation.navigate("Member");
    }

    if (nextProps.currentPage == "EquipmentList") {
      this.props.setPage("");
      this.props.navigation.navigate("EquipmentList");
    }
    if (nextProps.currentPage == "Company") {
      this.props.setPage("");
      this.props.navigation.navigate("Company");
    }

    if (nextProps.currentPage == "Delivery") {
      this.props.setPage("");
      this.props.navigation.navigate("Delivery");
    }
    // if (nextProps.currentPage === 'Calendar Settings') {
    //  //this.props.setPage('')
    //   this.props.navigation.navigate('CalendarSettings')

    if (nextProps.currentPage == Strings.menu.addNewDr) {
      console.log('🏠 Home: Navigating to AddDR with selectedDate:', this.props.selectedCalendarDate);
      this.props.setPage("");
      this.props.navigation.navigate("AddDR", { 
        selectedDate: this.props.selectedCalendarDate 
      });
    }

    if (nextProps.currentPage == Strings.menu.addNewCrane) {
      this.props.setPage("");
      this.props.navigation.navigate("AddCrane", { 
        selectedDate: this.props.selectedCalendarDate 
      });
    }
    if (nextProps.currentPage == Strings.menu.addNewConcrete) {
      this.props.setPage("");
      this.props.navigation.navigate("AddConcrete", { 
        selectedDate: this.props.selectedCalendarDate 
      });
    }
    if (nextProps.currentPage == Strings.menu.addNewIn) {
      console.log('🏠 Home: Navigating to addinspection with selectedDate:', this.props.selectedCalendarDate);
      this.props.setPage("");
      this.props.navigation.navigate("addinspection", { 
        selectedDate: this.props.selectedCalendarDate 
      });
    }
    if (nextProps.currentPage == "timeSlot") {
      this.props.setPage("");
      this.props.navigation.navigate("timeSlot");
    }
    if (nextProps.currentPage == "timeSlotCrane") {
      this.props.setPage("");
      this.props.navigation.navigate("timeSlotCrane");
    }
    if (nextProps.currentPage == "timeSlotConcrete") {
      this.props.setPage("");
      this.props.navigation.navigate("timeSlotConcrete");
    }
    if (nextProps.currentPage == "timeSlotInspection") {
      this.props.setPage("");
      this.props.navigation.navigate("timeSlotInspection");
    }
    if (nextProps.detailsPage == "drdetailspage") {
      this.props.navigation.navigate("Details");
      this.props.onTapDetail("");
    }
    if(nextProps.concreteDetails==="ConcreteDetails"){
      this.props.navigation.navigate("DetailsConcrete");
      this.props.onPressConcreteDetail("");
    }

    if (nextProps.detailsPage == "drdetailspage") {
      this.props.navigation.navigate("Details");
      this.props.onTapDetail("");
    }
    if (nextProps.eventDisplay === "EventDisplay") {
      this.props.navigation.navigate("EventDisplay");
      this.props.eventDisplayPage("");
    }
    if (nextProps.addPress == true) {
      this.props.clickAdd(false);

      if (nextProps.currentPage == Strings.menu.editMembers) {
        this.props.navigation.navigate("AddMember", {
          updateData: this.updateData,
        });
      } else if (nextProps.currentPage == Strings.menu.members) {

        this.props.navigation.navigate("InviteMember", {
          updateData: this.updateData,
        });
        trackScreen('Invite Member')
      } else if (nextProps.currentPage == Strings.menu.company) {
        this.props.navigation.navigate("AddCompany");
      } else if (nextProps.currentPage == Strings.menu.gates) {
        this.props.navigation.navigate("AddGates");
      } else if (nextProps.currentPage == Strings.menu.equip) {
        this.props.navigation.navigate("AddEquip");
      } else if (nextProps.currentPage == Strings.menu.df) {
        this.props.navigation.navigate("AddDFOW");
      } else if (nextProps.currentPage == Strings.menu.calendarSettings) {
        this.props.navigation.navigate("AddNewEvent");
      }  else if (nextProps.currentPage == Strings.menu.dr) {
        if (this.props.isAddDelivery === "Delivery") {
          this.props.navigation.navigate("AddDR");
        } else if (this.props.isAddDelivery === "Inspection") {
          this.props.navigation.navigate("addinspection");
        } 
        else if (this.props.isAddDelivery === "Crane") {
          if (this.props.isAssociatedCrane) {
            this.props.navigation.navigate("AddDR");
            this.props.toggleAssociatedWithCrane(false);
          } else {
            this.props.navigation.navigate("AddCrane");
          }
        } else if (this.props.isAddDelivery === "Concrete") {
          this.props.navigation.navigate("AddConcrete");
        }
      }
    }

    if (nextProps.searchTap == "equipSearch") {
      this.props.navigation.navigate("Search", { from: "Equip" });
      this.props.onTapSearch("");
    } else if (nextProps.searchTap == "gateSearch") {
      this.props.navigation.navigate("Search", { from: "Gate" });
      this.props.onTapSearch("");
    } else if (nextProps.searchTap == "companySearch") {
      this.props.navigation.navigate("Search", { from: "Company" });
      this.props.onTapSearch("");
    } else if (nextProps.searchTap == "memberSearch") {
      this.props.navigation.navigate("Search", { from: "Member" });
      this.props.onTapSearch("");
    } else if (nextProps.searchTap == "dfowSearch") {
      this.props.navigation.navigate("Search", { from: "DFOW" });
      this.props.onTapSearch("");
    }

    if (nextProps.navigateToVoid == true) {
      this.props.navigation.navigate("VoidList");
      this.props.goToVoid(false);
    }
  }

  //RENDER
  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <View style={{ width: "100%", height: "100%" }}>
        {/* <NavigationEvents onDidFocus={() => {
          this.props.updateList(true)
        }
        }
        /> */}
        <TabNavigator />
      </View>
      }
      </>
    );
  }
}

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    addPress,
    currentPage,
    searchTap,
    detailsPage,
    navigateToVoid,
    updatelist,
    refresh,
    isAddDelivery,
    isAddDeliveryCalendar,
    isAssociatedCrane,
    eventDisplay,
    concreteDetails,
    selectedCalendarDate,
  } = state.LoginReducer;
  return {
    changeTab,
    showMenu,
    addPress,
    currentPage,
    searchTap,
    detailsPage,
    navigateToVoid,
    updatelist,
    refresh,
    isAddDelivery,
    isAddDeliveryCalendar,
    isAssociatedCrane,
    eventDisplay,
    concreteDetails,
    selectedCalendarDate,
  };
};

export default compose(
  connect(mapStateToProps, {
  changeTab,
  showSideMenu,
  clickAdd,
  onTapSearch,
  goToVoid,
  onTapDetail,
  setPage,
  updateList,
  toggleAssociatedWithCrane,
  eventDisplayPage,
  onPressConcreteDetail,
  }),
  withBackHandler
  )(Home);