import React, { Component } from "react";
import { View, StyleSheet, Text, SafeAreaView, Platform } from "react-native";

import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import { getUserDetails } from "../../actions/postAction";
import { Image as AnimatableView } from "react-native-animatable";
import Images from "../../common/images";
import Strings from "../../common/string";
import HeaderAnimation from "../../components/logoAnimation/logoAnimation";
import Header from "../../components/headerComponent/Header";
import { TextField } from "../../components/textinput/Textinput";
import Fonts from "../../common/fonts";
import NextButton from "../../components/nextButton/NextButton";
import { isEmpty, isValidEmail } from "../../common";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import { LOGIN } from "../../api/Constants";
import { login} from "../../api/Api";
import Alert from "../../components/toastpopup/alert";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Axios from "axios";
import { CommonActions } from '@react-navigation/native';
import DeepLinking from "react-native-deep-linking";
import AppLoader from '../../components/apploader/AppLoader'
import { trackScreen,trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent,mixpanelLogin} from "../../MixPanel/MixPanel";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import Intercom from '@intercom/intercom-react-native'
 import { intercomLogin } from "../../Intercom/Intercom";
var isFromDeepLink = false;

class Login extends Component {
  constructor(props) {
    super(props);

    if (
      this.props.route.params != null ||
      this.props.route.params != undefined
    ) {
      isFromDeepLink = true;
    }

    const { userDetail } =
      this.props.route.params != null
        ? this.props.route.params
        : { userDetail: null };

    this.state = {
      showPass: false,
      showToaster: false,
      toastMessage: "",
      toastType: "error",
      showLoader: false,
      showAlert: false,
      email: userDetail == null ? "" : userDetail.email,
      password: userDetail == null ? "" : userDetail.password,
      fgtPwdTextWidth: 0,
      isNetworkCheck: false
    };
  }

  componentWillUnmount() {}

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck()
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state?.isConnected && !state?.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
    }
    })
  }

  handleUrl = ({ url }) => {
    Linking.canOpenURL(url).then((supported) => {
      if (supported) {
        DeepLinking.evaluateUrl(url);
      }
    });
  };

  onBackPress = () => {
    this.props.navigation.goBack();
  };

  updateMasterState = (key, value) => {
    if (key == "Password") {
      this.setState({ password: value });
    } else {
      this.setState({ email: value });
    }
  };

  onFgtPwdLayout = (event) => {
    this.setState({ fgtPwdTextWidth: event.nativeEvent.lines[0].width });
  };

  doValidation = () => {
    if (isEmpty(this.state.email)) {
      this.setState(
        {
          showToaster: true,
          toastType: "error",
          toastMessage: Strings.errors.emptyEmail,
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (isValidEmail(this.state.email.trim())) {
      this.setState(
        {
          showToaster: true,
          toastType: "error",
          toastMessage: Strings.errors.validEmail,
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (isEmpty(this.state.password.trim())) {
      this.setState(
        {
          showToaster: true,
          toastType: "error",
          toastMessage: Strings.errors.emptyPassword,
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    }
    // else if(!isValidPassword(this.state.password)){
    //   this.setState(
    //     {
    //       showToaster: true,
    //       toastType: 'error',
    //       toastMessage: Strings.errors.validCurrent,
    //     },
    //     () => {
    //       setTimeout(() => {
    //         this.setState({showToaster: false});
    //       }, 2000);
    //     },
    //   );
    //   return false;
    // }
    else {
      return true;
    }
  };

  //Next click
  nextClick = async () => {
    if (this.doValidation()) {
      this.setState({ showLoader: true });
      let data = {
        email: this.state.email.trim(),
        password: this.state.password.trim(),
      };
      await login(
        LOGIN,
        data,
        () => {},
        (response) => {
          this.setState({ showLoader: false });
          if (
            response.toString() == Strings.errors.timeout ||
            response.toString() == "Error: Network Error"
          ) {
            this.setState(
              {
                showToaster: true,
                toastMessage: Strings.errors.checkInternet,
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          } else if (response.status) {
            if (response.status == 200) {
             mixpanelLogin(response.data)
             Intercom.registerIdentifiedUser({email:response.data.userDetails.email})
              intercomLogin(response.data)
              this.props.getUserDetails(response.data.userDetails);
              Axios.defaults.headers.common["Authorization"] =
                "JWT " + response.data.token;
              AsyncStorage.setItem("AccessToken", response.data.token)
              .then(()=>this.reDirectToRegisterRoute());
            } else if (response.data.message.message) {
              let array = Object.values(response.data.message.details[0]);
              this.setState(
                {
                  showToaster: true,
                  toastMessage: array.toString(),
                  toastType: "error",
                },
                () => {
                  setTimeout(() => {
                    this.setState({
                      showToaster: false,
                    });
                  }, 3500);
                }
              );
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.data.message,
                  toastType: "error",
                },
                () => {
                  setTimeout(() => {
                    this.setState({ showToaster: false });
                  }, 2000);
                }
              );
            }
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.toString(),
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          }
        }
      );
    }
  };
  renderForgotPwdUnderLine(textWidth) {
    return (
      <View
        style={{
          borderBottomColor: Colors.themeColor,
          borderBottomWidth: .5,
          backgroundColor:"pink",
          width:textWidth,
          marginTop:3,
        }}
      />
    );
  }

  //The reDirectToRegisterRoute will navigate the login screen to Registered Route
  reDirectToRegisterRoute = () => {
    const resetAction = CommonActions.reset({
      index: 0,
      routes: [{ name: "RegisteredRoute" }],
    });
    trackScreen('DashBoard')
    trackEvent('User_LoggedIn')
    mixPanelTrackEvent('User LoggedIn', {Date:new Date()})
  
    this.props.navigation.dispatch(resetAction);
    this.setState({ showAlert: false });
  };

  //Main Render method
  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAwareScrollView extraScrollHeight={50}>
          <View style={styles.parentContainer}>
            <HeaderAnimation />
            <View style={styles.subContainer}>
              <AnimatableView source={Images.path} style={styles.path} />
              {/* <Image resizeMode={'center'} source={Images.path} style={{width: wp('50%')}}/> */}

              <View style={styles.signupContainer}>
                <Header
                  backPress={() => this.onBackPress()}
                  title={Strings.login.welcomeBack}
                />

                <View style={{ flex: 1, marginTop: 20 }}>
                  <TextField
                    autoCapitalize="none"
                    attrName={Strings.placeholders.email}
                    title={Strings.placeholders.email}
                    value={this.state.email.toLowerCase().trim()}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                  />

                  <TextField
                    attrName={Strings.placeholders.password}
                    title={Strings.placeholders.password}
                    value={this.state.password}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    hideShow={true}
                    hideImage={
                      //this.state.showPass ? Images.hide : Images.unhide
                      this.state.showPass ? Images.unhide : Images.hide
                    }
                    onPressHideImage={() => {
                      this.setState({ showPass: !this.state.showPass });
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    secureTextEntry={!this.state.showPass}
                  />
                  <View style={styles.forgotContainer}>
                    <Text
                      onTextLayout={this.onFgtPwdLayout}
                      onPress={() => {
                        // this.props.navigation.navigate('Home')
                        this.props.navigation.navigate("ForgotPassword");
                      }}
                      style={styles.forgot}
                    >
                      {Strings.login.forgotPassword}
                    </Text>
                    {this.renderForgotPwdUnderLine(this.state.fgtPwdTextWidth)}
                  </View>
                  <NextButton
                    title={Strings.login.login}
                    nextClick={() => this.nextClick()}
                  />
                </View>
              </View>
            </View>
          </View>
        </KeyboardAwareScrollView>
        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.loginSuccess}
            okTap={() => {
              this.reDirectToRegisterRoute();
            }}
          />
        )}
      </SafeAreaView>
      }
      </>
    );
  }
}

const mapStateToProps = (state) => {
  const { userid } = state.LoginReducer;

  return {
    userid,
  };
};

export default connect(mapStateToProps, {
  getUserDetails,
})(Login);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  subContainer: {
    flex: 1,
    justifyContent: "flex-end",
    height: hp("85%"),
  },
  signupContainer: {
    width: wp("100%"),
    height: hp("60%"),
    borderTopLeftRadius: hp("6%"),
    borderTopRightRadius: hp("6%"),
    zIndex: 999,
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0.8,
    shadowOffset: { width: 10, height: 10 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  path: {
    width: wp("70%"),
    height: hp("25%"),
    marginBottom: -hp("4.5%"),
    marginLeft: -wp("25%"),
  },
  forgotContainer: {
    marginBottom: hp("7%"),
    marginLeft: wp("6%"),
    flexDirection: "column",
  },
  forgot: {
    color: Colors.themeColor,
    fontSize: 15,
    fontFamily: Fonts.montserratMedium,
    letterSpacing: 0.42,
  },
});