import React, { Component } from "react";
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Platform
} from "react-native";

import {
  changeTab,
  showSideMenu,
  cameBack,
  editData,
  updateList,
  setPage,
  refreshDashboard,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import Alert from "../../components/toastpopup/alert";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { TextField } from "../../components/textinput/addMemberTextinput";
import Dropdown from "../../components/dropdown/dropdown";
import { isEmpty, isValidEmail, isName } from "../../common/validators";
import { isValidNumber } from "../../common/Validator";
import DeletePop from "../../components/toastpopup/logoutPop";
import {
  GET_ROLE,
  GET_NEW_COMPANIES,
  ADD_MEMBER,
  EDIT_MEMBER,
} from "../../api/Constants";
import {
  getRole,
  getNewCompanyList,
  addMemeber,
  editMember,
} from "../../api/Api";
import mobileCountryCodes from "../../common/country";
import { MobilenumberInput } from "../../components/textinput/MobilenumberInput";
import AppLoader from '../../components/apploader/AppLoader'
import { trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";
class AddMember extends Component {
  constructor(props) {
    super(props);
    this.state = {
      memberid: 0,
      projectModal: false,
      roleModal: false,
      role: "",
      name: "",
      lastName:"",
      companyName: "",
      mobile: "",
      email: "",
      assignProject: this.props.projectDetails.projectName,
      projectlist: [],
      rolelist: [],
      countryCode: "+1",
      edit: false,
      countryData: mobileCountryCodes.mobileCountryCodes,
      memberDataId: 0,
      showCancel: false,
      comparsion: [],
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      },
      profilePic:'',
      isNetworkCheck: false,
    };
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.refreshData();
    }
  }
  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.refreshData();
    }
    })
  }

  refreshData() {
    this.checkData();
    this.getRoles();
    this.storeProjects();
    this.getCompanYlist();
  }

  onBackPress = () => {
    // this.props.setPage("Members");
    // this.props.navigation.goBack();
    return false;
  };

  checkData() {

    if (this.props.editedData.item) {
      let data = this.props.editedData.item;
      this.setState(
        {
          roleid: data.RoleId,
          memberid: data.memberId,
          role: data.Role.roleName,
          name: data.User.firstName == null ? "" : data.User.firstName,
          lastName:data.User.lastName==null?"":data.User.lastName,
          companyName: data.Company == null ? "" : data.Company.companyName,
          mobile: data.phoneNumber,
          countryCode: data.phoneCode == null ? "+1" : data.phoneCode,
          email: data.User.email,
          companyid: data.CompanyId,
          projectid: this.props.projectDetails.id,
          edit: true,
          memberDataId: data.id,
          comparsion: data,
          profilePic:data.User.profilePic!=null?data.User.profilePic:'',
        },
        () => {
          this.props.editData({});
        }
      );
    } else {
      this.setState({
        memberid: this.props.lastid,
      });
    }
  }

  getCompanYlist = () => {
    getNewCompanyList(
      GET_NEW_COMPANIES +
        this.props.projectDetails.id +
        "/" +
        this.props.projectDetails.ParentCompany.id,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.data.message == "Company list.") {
            this.storeCompanyName(response.data.data);
          } else if (response.data.message.message) {
            this.showError("error", response.data.message.message);
          } else {
            this.showError("error", response.data.message);
          }
        } else {
          this.showError("error", response.toString());
        }
      }
    );
  };

  storeCompanyName = (data) => {
    let item = [];

    for (let ele of data) {
      item.push({
        id: ele.id,
        name: ele.companyName,
      });
    }

    this.setState({ companylist: item });
  };

  storeProjects = () => {
    let item = this.props.projectlist;
    let data = [];

    for (let ele of item) {
      data.push({
        id: ele.id,
        name: ele.projectName,
      });
    }

    this.setState({ projectlist: data });
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }
  }

  getRoles = () => {
    this.setState({ showLoader: true });

    getRole(
      GET_ROLE,
      {},
      () => null,
      (response) => {
        this.setState({ showLoader: false });

        if (response.status) {
          if (response.data.message == "Role list.") {
            this.storeRole(response.data.data);
          } else if (response.data.message.message) {
            this.showError("error", response.data.message.message);
          } else {
            this.showError("error", response.data.message);
          }
        } else {
          this.showError("error", response.toString());
        }
      }
    );
  };

  storeRole = (data) => {
    let roles = [];

    for (let item of data) {
      roles.push({
        id: item.id,
        name: item.roleName,
      });
    }

    this.setState({ rolelist: roles });
  };

  //HEADER COMPONENT
  renderHeader() {
    return (
      <View style={styles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.props.setPage("Members");
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={styles.title}>{Strings.addMember.add}</Text>
      </View>
    );
  }

  renderImage() {
    return (
      <View style={styles.imageContainer}>
        <TouchableWithoutFeedback>
          <View style={styles.imageButton}>
            <Image
              source={Images.placeholder}
              style={{
                width: hp("5%"),
                height: hp("5%"),
                borderRadius: hp("2.5%"),
              }}
            />
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  renderMemberId() {
    return (
      <View style={styles.memberContainer}>
        <Text style={styles.idTitle}>{Strings.addMember.id}</Text>
        <Text style={styles.idText}>{this.state.memberid}</Text>
      </View>
    );
  }

  validate() {
    if (isEmpty(this.state.role)) {
      this.showError("error", Strings.errors.emptyRole);
      return false;
    } else if (isEmpty(this.state.name.trim())) {
      this.showError("error", Strings.errors.enterFirst);
      return false;
    } else if (isEmpty(this.state.lastName.trim())) {
      this.showError("error", Strings.errors.enterlast);
      return false;
    } else if (isName(this.state.name)) {
      this.showError("error", Strings.errors.validName);
      return false;
    } else if (this.state.name.length < 3) {
      this.showError("error", "Member name " + Strings.errors.lengthError);
      return false;
    } else if (isEmpty(this.state.companyName.trim())) {
      this.showError("error", Strings.errors.emptyCompany);
      return false;
    } else if (isEmpty(this.state.mobile.trim())) {
      this.showError("error", Strings.errors.emptyMobile);
      return false;
    } else if (
      isValidNumber(this.state.mobile) ||
      this.state.mobile.length < 10
    ) {
      this.showError("error", Strings.errors.validMobile);
      return false;
    } else if (isEmpty(this.state.email.trim())) {
      this.showError("error", Strings.errors.emptyEmail);
      return false;
    } else if (isValidEmail(this.state.email)) {
      this.showError("error", Strings.errors.validEmail);
      return false;
    } else if (isEmpty(this.state.assignProject.trim())) {
      this.showError("error", Strings.errors.emptyAssign);
      return false;
    } else {
      return true;
    }
  }

  hideToast = () =>
    setTimeout(() => this.setState({ showToaster: false }), 2000);

  submit = () => {
    if (this.validate()) {
      this.setState({ showLoader: true });

      if (this.state.edit == true) {
        let param = {
          firstName: this.state.name,
          lastName:this.state.lastName,
          email: this.state.email.trim(),
          phoneNumber: this.state.mobile,
          phoneCode: this.state.countryCode,
          ProjectId: this.props.projectDetails.id,
          RoleId: this.state.roleid.toString(),
          CompanyId: this.state.companyid,
          id: this.state.memberDataId,
          ParentCompanyId: this.props.projectDetails.ParentCompany.id,
        };

        editMember(
          EDIT_MEMBER,
          param,
          () => null,
          (response) => {


            this.setState({
              showLoader: false,
            });

            if (response.status) {
              if (response.data.message) {
                if (response.data.message.message) {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: response.data.message.message,
                      toastType: "error",
                    },
                    () => this.hideToast()
                  );
                } else if (
                  response.data.message == "Member Updated Successfully."
                ) {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: response.data.message,
                      toastType: "success",
                    },
                    () => this.hideToast()
                  );

                  this.props.cameBack(true);
                  this.props.refreshDashboard(true, "Add member submit edit");

                  if (this.props.route?.params?.from == "search") {
                    this.props.navigation.state.params.updateData("data");
                  }
                  this.props.setPage("Members");
                  this.props.navigation.goBack();
                  trackEvent('Edited_Member')
                  mixPanelTrackEvent('Edited Member',this.state.mixpanelParam)
                } else {
                  this.setState(
                    {
                      showToaster: true,
                      toastMessage: response.data.message,
                      toastType: "error",
                    },
                    () => this.hideToast()
                  );
                }
              } else {
                this.setState(
                  {
                    showToaster: true,
                    toastMessage: "Failed to update member",
                    toastType: "error",
                  },
                  () => this.hideToast()
                );
              }
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.toString(),
                  toastType: "error",
                },
                () => this.hideToast()
              );
            }
          }
        );
      } else {
        let param = {
          firstName: this.state.name,
          email: this.state.email.trim(),
          phoneNumber: this.state.mobile,
          phoneCode: this.state.countryCode,
          ProjectId: this.props.projectDetails.id,
          RoleId: this.state.roleid.toString(),
          CompanyId: this.state.companyid,
        };
        addMemeber(
          ADD_MEMBER,
          param,
          () => null,
          (response) => {
            this.setState({ showLoader: false });
            if (response.status) {
              if (response.data.message == Strings.popup.memberCreated) {
                //     this.props.cameBack(true)
                this.props.updateList(true);
                this.props.refreshDashboard(true, "add member submit create");
                this.props.navigation.goBack();
              } else if (response.data.message.message) {
                this.showError("error", response.data.message.message);
              } else {
                this.showError("error", response.data.message);
              }
            } else {
              this.showError("error", response.toString());
            }
          }
        );
      }
    }
  };
  onPressCancel = () => {
    let checkName = this.state.comparsion.firstName;
    let checkRole = this.state.comparsion?.Role?.roleName;
    let checkCompany = this.state.comparsion?.Company?.companyName;
    let checkMobile = this.state.comparsion?.phoneNumber;
    if (
      checkName == this.state.name &&
      checkRole == this.state.role &&
      checkCompany == this.state.companyName &&
      checkMobile == this.state.mobile
    ) {
      this.props.setPage("Members");
      this.props.navigation.goBack();
    } else {
      this.setState({ showCancel: true });
    }
  };
  bottomContainer = () => {
    return (
      <View style={styles.bottomContainer}>
        <TouchableOpacity onPress={this.onPressCancel}>
          <View style={styles.cancel}>
            <Text style={styles.cancelText}>{Strings.addMember.cancel}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            this.submit();
          }}
        >
          <View style={styles.submit}>
            <Text style={styles.submitText}>
              {this.state.edit == true
                ? Strings.addMember.update
                : Strings.addMember.submit}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  updateMasterState = (key, value) => {


    if (key == Strings.placeholders.firstname) {
      this.setState({ name: value });
    }else if (key == Strings.placeholders.lastname) {
      this.setState({ lastName: value });
    }
    else if (key == Strings.placeholders.companyName) {
      this.setState({ companyName: value });
    } else if (key == Strings.placeholders.mobile) {
      this.setState({ mobile: value });
    } else if (key == Strings.placeholders.email) {
      this.setState({ email: value });
    }
  };

  showError = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
      },
      () => this.hideToast()
    );
  };

  onPressrole = (item) => {
    this.setState({
      role: item.name,
      roleid: item.id,
      roleModal: false,
    });
  };

  onPressCountry = (item) => {

    this.setState({
      countryCode: item.dialCode,
      countryVisible: false,
    });
  };

  onPressCompany = (item) => {


    this.setState({
      companyName: item.name,
      companyid: item.id,
      companyModal: false,
    });
  };

  onPressproject = (item) => {


    this.setState({
      assignProject: item.name,
      projectid: item.id,
      projectModal: false,
    });
  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.parentContainer}>
          {this.renderHeader()}
          <KeyboardAwareScrollView extraScrollHeight={50}>
            {this.renderImage()}
            {this.renderMemberId()}

            <TextField
              attrName={Strings.placeholders.role}
              title={Strings.placeholders.role}
              value={this.state.role}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color:
                  this.state.email == this.props.userDetails.email
                    ? Colors.placeholder
                    : Colors.black,
                fontSize: 14,
              }}
              showButton={true}
              onPress={() => {
                if (this.state.email != this.props.userDetails.email) {
                  this.setState({ roleModal: true });
                }
              }}
              imageSource={Images.downArr}
              placeholder={"Select"}
            />

            <TextField
              attrName={Strings.placeholders.firstname}
              title={Strings.placeholders.firstname}
              value={this.state.name}
              maxLength={150}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
            />
            <TextField
              attrName={Strings.placeholders.lastname}
              title={Strings.placeholders.lastname}
              value={this.state.lastName}
              maxLength={150}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={styles.text}
            />

            <TextField
              attrName={Strings.placeholders.companyName}
              title={Strings.placeholders.companyName}
              value={this.state.companyName}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              showButton={true}
              onPress={() => {
                this.setState({ companyModal: true });
              }}
            />

            <MobilenumberInput
              attrName={Strings.placeholders.mobile}
              title={Strings.placeholders.mobile}
              value={this.state.mobile}
              countryCode={this.state.countryCode}
              imageSource={Images.downArr}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              maxLength={10}
              keyboardType={"number-pad"}
              onSubmitEditing={() => {
                // this.submitEditing('mobile')
              }}
              onPresscountry={() => {
                this.setState({ countryVisible: true });
              }}
            />

            <TextField
              attrName={Strings.placeholders.email}
              title={Strings.placeholders.email}
              value={this.state.email}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              editable={this.state.edit == true ? false : true}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color:
                  this.state.edit == true ? Colors.placeholder : Colors.black,
                fontSize: 14,
              }}
            />

            {this.bottomContainer()}
          </KeyboardAwareScrollView>
        </View>

        <Dropdown
          data={this.state.countryData}
          title={Strings.addMember.chooseCode}
          value={this.state.countryCode}
          closeBtn={() => this.setState({ countryVisible: false })}
          onPress={(item) => this.onPressCountry(item)}
          visible={this.state.countryVisible}
          onbackPress={() => this.setState({ countryVisible: false })}
        />

        <Dropdown
          data={this.state.companylist}
          title={Strings.addMember.chooseCompany}
          value={this.state.companyName}
          closeBtn={() => this.setState({ companyModal: false })}
          onPress={(item) => this.onPressCompany(item)}
          visible={this.state.companyModal}
          onbackPress={() => this.setState({ companyModal: false })}
        />

        <Dropdown
          data={this.state.rolelist}
          title={Strings.addMember.chooseRole}
          value={this.state.role}
          closeBtn={() => this.setState({ roleModal: false })}
          onPress={(item) => this.onPressrole(item)}
          visible={this.state.roleModal}
          onbackPress={() => this.setState({ roleModal: false })}
        />
        {this.state.showCancel && (
          <DeletePop
            title={Strings.popup.cancel}
            desc={Strings.popup.cancel}
            acceptTap={() => {
              this.setState({ showCancel: false });
              this.props.navigation.goBack();
            }}
            container={{ bottom: 0 }}
            declineTap={() => {
              this.setState({ showCancel: false });
            }}
          />
        )}
        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.loginSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}
      </SafeAreaView>
      }
      </>
    );
  }
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("80%"),
    fontSize: wp("6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratBold,
  },
  imageContainer: {
    width: wp("100%"),
    height: hp("20%"),
    justifyContent: "center",
    alignItems: "center",
  },
  imageButton: {
    width: wp("24%"),
    height: wp("24%"),
    borderRadius: wp("12%"),
    backgroundColor: "#F5F5F5",
    borderWidth: 1,
    borderColor: "#00000029",
    justifyContent: "center",
    alignItems: "center",
  },
  placeholder: {
    width: wp("15%"),
    alignSelf: "center",
  },
  camera: {
    position: "absolute",
    bottom: 10,
    right: -10,
    width: wp("8%"),
    height: wp("8%"),
    borderRadius: wp("4%"),
    backgroundColor: Colors.white,
    justifyContent: "center",
    alignItems: "center",
  },
  memberContainer: {
    width: wp("90%"),
    height: hp("10%"),
    alignSelf: "center",
    marginTop: hp("2%"),
    justifyContent: "center",
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  idText: {
    width: wp("90%"),
    height: hp("5%"),
    backgroundColor: "#EFEFEF",
    marginTop: wp("1%"),
    padding: hp("1%"),
    paddingLeft: 15,
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("4%"),
  },
  bottomContainer: {
    width: wp("90%"),
    flexDirection: "row",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: hp("4%"),
    marginTop: hp("8%"),
    // backgroundColor: Colors.white
  },
  cancel: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: wp("35%"),
    height: hp("7%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonGray,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  text:{
    color: Colors.black,
    fontSize: 14,
  }
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    updatelist,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    updatelist,
  };
};

export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    cameBack,
    editData,
    updateList,
    setPage,
    refreshDashboard,
  }),
  withBackHandler
)(AddMember);
