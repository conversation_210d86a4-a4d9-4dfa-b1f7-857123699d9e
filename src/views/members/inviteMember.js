import React, { Component } from "react";
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  Image,
  TouchableOpacity,
  TouchableWithoutFeedback,
  FlatList,
  Keyboard,
  Platform
} from "react-native";
import {
  changeTab,
  showSideMenu,
  cameBack,
  editData,
  updateList,
  refreshPage,
  refreshDashboard,
  clickAdd,
  updateInviteMember,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import Alert from "../../components/toastpopup/alert";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import {
  GET_ROLE,
  GET_NEW_COMPANIES,
  INVITE_MEMBER,
  CHECK_MEMBER,
} from "../../api/Constants";
import { getRole, getNewCompanyList, invite, check } from "../../api/Api";
import { Selectize } from "react-native-material-selectize";
import ModalDropdown from "react-native-modal-dropdown";
import mobileCountryCodes from "../../common/country";
import AppLoader from '../../components/apploader/AppLoader'
let DROPDOWNOPTIONSROLE = [
  { id: "Project Admin", image: Images.edit1, name: "Project Admin" },
  { id: "General Contractor", image: Images.edit1, name: "General Contractor" },
  { id: "Sub Contractor", image: Images.edit1, name: "Sub Contractor" },
];
let CompanyDrop={
  id:'Company', companyName:'Add Company'
}
import { trackEvent } from "../../Google Analytics/GoogleAnalytics"
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";

class InviteMember extends Component {
  constructor(props) {
    super(props);
    this.state = {
      inviteMember: [],
      memberRole: null,
      memberid: 0,
      projectModal: false,
      roleModal: false,
      role: "",
      name: "",
      companyName: "",
      mobile: "",
      email: "",
      assignProject: this.props.projectDetails.projectName,
      responsiblePersonData: [],
      selectedItems: [],
      unwantedData: [],
      projectlist: [],
      rolelist: [],
      countryCode: "+1",
      edit: false,
      refresh: false,
      showToaster: false,
      countryData: mobileCountryCodes.mobileCountryCodes,
      memberDataId: 0,
      isKeyboardShow: false,
      companyList:[],
      roleid:"",
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      },
      isNetworkCheck: false,
    };

    this.textEnterDone = this.textEnterDone.bind(this);
    this.keyboardDidShow = this.keyboardDidShow.bind(this);
    this.keyboardDidHide = this.keyboardDidHide.bind(this);

    this.keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      this.keyboardDidShow
    );
    this.keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      this.keyboardDidHide
    );
  }

  keyboardDidShow = (e) => {
    this.setState({
      isKeyboardShow: true,
    });
  };

  keyboardDidHide(e) {
    this.setState({
      isKeyboardShow: false,
    });
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.getRoles();
      this.getCompanYlist();
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.getRoles();
      this.getCompanYlist();
    }
    })
  }

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  refreshData() {
    this.checkData();
    this.storeProjects();
    this.getCompanYlist();
  }

  checkData() {

    if (this.props.editedData.item) {
      let data = this.props.editedData.item;
      this.setState(
        {
          roleid: data.RoleId,
          memberid: data.memberId,
          role: data.Role.roleName,
          name: data.firstName,
          companyName: data.Company.companyName,
          mobile: data.phoneNumber,
          countryCode: data.phoneCode,
          email: data.User.email,
          companyid: data.CompanyId,
          projectid: this.props.projectDetails.id,
          edit: true,
          memberDataId: data.id,
        },
        () => {
          this.props.editData({});
        }
      );
    } else {
      this.setState({
        memberid: this.props.lastid,
      });
    }
  }

  getCompanYlist = () => {
    getNewCompanyList(
      GET_NEW_COMPANIES +
        this.props.projectDetails.id +
        "/" +
        this.props.projectDetails.ParentCompany.id,
      {},
      () => null,
      (response) => {


        if (response.status) {
          if (response.status == 200) {
              let data=response.data.data;
            data.push(CompanyDrop)
            this.setState({companyList:data})
            //this.storeCompanyName(response.data.data);
            
          } else if (response.data.message.message) {
            this.showError("error", response.data.message.message);
          } else {
            this.showError("error", response.data.message);
          }
        } else {
          this.showError("error", response.toString());
        }
      }
    );
  };

  storeCompanyName = (data) => {
    let item = [];

    for (let ele of data) {
      item.push({
        id: ele.id,
        name: ele.companyName,
      });
    }

    this.setState({ companyList: item});
  };

  storeProjects = () => {

    let item = this.props.projectlist;
    let data = [];

    for (let ele of item) {
      data.push({
        id: ele.id,
        name: ele.projectName,
      });
    }

    this.setState({ projectlist: data });
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }
    if(nextProps.updateInviteMem==true){
      this.getCompanYlist();
      this.props.updateInviteMember(false)
    }
  }

  getRoles = () => {
    this.setState({ showLoader: true });

    getRole(
      GET_ROLE,
      {},
      () => null,
      (response) => {

        this.setState({ showLoader: false });

        if (response.status) {
          if (response.status == 200) {
            this.storeRole(response.data.data);
          } else if (response.data.message.message) {
            this.showError("error", response.data.message.message);
          } else {
            this.showError("error", response.data.message);
          }
        } else {
          this.showError("error", response.toString());
        }
      }
    );
  };

  storeRole = (data) => {
    let roles = [];
    for (let item of data) {
      roles.push({
        id: item.id,
        name: item.roleName,
      });
    }
    this.setState({ rolelist: roles });
  };

  //HEADER COMPONENT
  renderHeader() {
    return (
      <View style={styles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.props.updateList(false);
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={styles.title1}>{Strings.inviteMember.title1}</Text>
      </View>
    );
  }

  renderImage() {
    return (
      <View style={styles.imageContainer}>
        <TouchableWithoutFeedback onPress={() => null}>
          <View style={styles.imageButton}>
            <Image
              source={Images.placeholder}
              style={{
                width: hp("5%"),
                height: hp("5%"),
                borderRadius: hp("2.5%"),
              }}
            />
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  renderMemberId() {
    return (
      <View style={styles.memberContainer}>
        <Text style={styles.idTitle}>{Strings.addMember.id}</Text>
        <Text style={styles.idText}>{this.state.memberid}</Text>
      </View>
    );
  }

  validate() {
    var validate = true;

    for (let item of this.state.inviteMember) {
      let obj = item;
      if (obj.role == "") {
        validate = false;
        this.showError("error", `Please select the Role`);
        break;
      }
      else if(obj.companyId ==""){
        validate = false;
        this.showError("error", `Please select the Company`);
        break;
      }
    }

    return validate;
  }

  hideToast = () =>
    setTimeout(() => this.setState({ showToaster: false }), 2000);

  submit = async () => {
    if (this.validate() && this.state.inviteMember.length > 0) {
      this.setState({ showLoader: true });

      var inviteMemObj = [];

      for (let item of this.state.inviteMember) {
        let obj = item;
        var roleId;

        if (obj.role == "Project Admin") {
          roleId = 2;
        } else if (obj.role == "General Contractor") {
          roleId = 3;
        } else if (obj.role == "Sub Contractor") {
          roleId = 4;
        }

        let resObj = {
          email: obj.mail,
          id: obj.mail,
          RoleId: roleId.toString(),
          profilePic: "",
          CompanyId:obj.companyId,
        };
        inviteMemObj.push(resObj);
      }

      this.setState({ showLoader: true });
      let data = {
        membersList: inviteMemObj,
        ProjectId: this.props.projectDetails.id,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
        requestType: 1,
      };

      invite(
        INVITE_MEMBER,
        data,
        () => null,
        (response) => {
          this.setState({ showLoader: false });
          if (
            response.toString() == Strings.errors.timeout ||
            response.toString() == "Error: Network Error"
          ) {
            this.setState(
              {
                showToaster: true,
                toastMessage: Strings.errors.checkInternet,
                toastType: "error",
              },
              () => this.hideToast()
            );
          } else if (response.status) {
            if (response.status == 200 || response.status == 201) {
              Keyboard.dismiss();
              this.input.clearSelectedItems();
              this.setState(
                {
                  inviteMember: [],
                  unwantedData: [],
                  showToaster: true,
                  toastType: "",
                  toastMessage: "Invited Successfully.",
                },
                () => {
                  setTimeout(() => {
                    this.setState({
                      showToaster: false,
                      showLoader: false,
                    });
                    //this.input.clearSelectedItems();
                    this.props.refreshPage(true);
                    this.props.refreshDashboard(true,"Invite member submit");
                    this.props.navigation.goBack();
                  }, 2000);
                }
              );
              trackEvent('Member_Onboard_Invite_Sent')
              mixPanelTrackEvent('Member Onboard Invite Sent',this.state.mixpanelParam)
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.data.message.toString(),
                  toastType: "error",
                },
                () => this.hideToast()
              );
            }
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.toString(),
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        }
      );
    } else {
      if (this.state.inviteMember.length > 0) {
      } else {
        this.setState(
          {
            showToaster: true,
            toastMessage: "*Please choose At least one member.",
            toastType: "error",
          },
          () => this.hideToast()
        );
      }
    }
  };

  updateMasterState = (key, value) => {
    if (key == Strings.placeholders.name) {
      this.setState({ name: value });
    } else if (key == Strings.placeholders.companyName) {
      this.setState({ companyName: value });
    } else if (key == Strings.placeholders.mobile) {
      this.setState({ mobile: value });
    } else if (key == Strings.placeholders.email) {
      this.setState({ email: value });
    }
  };

  showError = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
      },
      () => this.hideToast()
    );
  };

  onPressrole = (item) => {

    this.setState({
      role: item.name,
      roleid: item.id,
      roleModal: false,
    });
  };

  onPressCountry = (item) => {

    this.setState({
      countryCode: item.code,
      countryVisible: false,
    });
  };

  onPressCompany = (item) => {

    this.setState({
      companyName: item.name,
      companyid: item.id,
      companyModal: false,
    });
  };

  onPressproject = (item) => {
    this.setState({
      assignProject: item.name,
      projectid: item.id,
      projectModal: false,
    });
  };

  bottomContainer = () => {
    return (
      <View style={styles.bottomContainer}>
        <TouchableOpacity onPress={() => this.props.navigation.goBack()}>
          <View style={styles.cancel}>
            <Text style={styles.cancelText}>{Strings.addMember.cancel}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            this.submit();
          }}
        >
          <View style={styles.submit}>
            <Text style={styles.submitText}>{Strings.addMember.invite}</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  renderRowCompany = (option, index, isSelected) => {


    return (
      <View
        style={{
          width: 150,
          flexDirection: "row",
          alignItems: "center",
          height: hp("5%"),
        }}
      >
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: option.companyName=='Add Company'?Colors.themeColor:Colors.planDesc,
            marginLeft: 5,
          }}
        >
          {option.companyName}
        </Text>
      </View>
    );
  };
  renderRow = (option, index, isSelected) => {


    return (
      <View
        style={{
          width: 150,
          flexDirection: "row",
          alignItems: "center",
          height: hp("5%"),
        }}
      >
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: Colors.planDesc,
            marginLeft: 5,
          }}
        >
          {option.id}
        </Text>
      </View>
    );
  };

  renderSeparator = () => <View style={styles.seperator} />;

  
  onSelectDropdownCompany = (option, index, item) => {
    
    if(this.state.companyList[option].companyName ==='Add Company'){
      this.props.navigation.navigate('AddCompany',{inviteMember:true});
    }else{
    const newArray = [...this.state.inviteMember];
    newArray[index].companyId = this.state.companyList[option].id;
    newArray[index].companyName = this.state.companyList[option].companyName;
    this.setState({ inviteMember: newArray, refresh: !this.state.refresh });
  }
  };
  onSelectDropdown = (option, index, item) => {
    const newArray = [...this.state.inviteMember];
    newArray[index].role = DROPDOWNOPTIONSROLE[option].name;
    this.setState({ inviteMember: newArray, refresh: !this.state.refresh });
  };

  renderItem = (obj) => {
    const { mail, role ,companyName} = obj.item;
    return (
      <View
        style={{
          flex: 1,
          marginTop: 10,
          marginBottom: 10,
          //flexDirection: "row",
        }}
      >
        <View style={{ flex: 1, height: 50, justifyContent: "center" }}>
          <Text
            style={{
              marginLeft: 3,
              marginRight: 10,
              fontSize: wp("3.5%"),
              fontFamily: Fonts.montserratRegular,
            }}
          >
            {mail}
          </Text>
        </View>
        <View style={{flex:1,flexDirection:"row",alignItems:'flex-end',alignSelf:'flex-end'}}>
        <View style={{ width: 150, height: 50, marginRight: 20 ,alignItems:'flex-end',justifyContent:'flex-end',alignSelf:'flex-end'}}>
          
          <ModalDropdown
            saveScrollPosition={false}
            style={styles.customDropdownStyle}
            dropdownStyle={[styles.customOptionsStyle, { height: hp("14%") }]}
            dropdownTextStyle={styles.customOptionsTextStyle}
            options={this.state.companyList}
            renderRow={this.renderRowCompany}
            renderSeparator={this.renderSeparator}
            showsVerticalScrollIndicator={false}
            onSelect={(options) =>
              this.onSelectDropdownCompany(options, obj.index, obj.item)
            }
            defaultValue=""
            extraData={this.state}
            dropdownListProps={{}}
          >
            <View style={{ flex: 1, flexDirection: "row" }}>
              <View
                style={{ width: 110, height: 50, justifyContent: "center" }}
              >
                <Text
                  style={{
                    marginLeft: 15,
                    marginRight: 5,
                    fontSize: wp("3.5%"),
                    fontFamily: Fonts.montserratMedium,
                  }}
                >
                  {companyName== "" ? "Company *" : companyName}
                </Text>
              </View>

              <View
                style={{
                  width: 50,
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Image
                  style={{ flex: 1 }}
                  resizeMode={"center"}
                  source={Images.downArr}
                />
              </View>
            </View>
          </ModalDropdown>
        </View>
        <View style={{ width: 150, height: 50, marginRight: 10,alignItems:'flex-end',justifyContent:'flex-end',alignSelf:'flex-end'}}>
          
          <ModalDropdown
            saveScrollPosition={false}
            style={styles.customDropdownStyle}
            dropdownStyle={[styles.customOptionsStyle, { height: hp("14%") }]}
            dropdownTextStyle={styles.customOptionsTextStyle}
            options={DROPDOWNOPTIONSROLE}
            renderRow={this.renderRow}
            renderSeparator={this.renderSeparator}
            showsVerticalScrollIndicator={false}
            onSelect={(options) =>
              this.onSelectDropdown(options, obj.index, obj.item)
            }
            defaultValue=""
            dropdownListProps={{}}
          >
            <View style={{ flex: 1, flexDirection: "row" }}>
              <View
                style={{ width: 100, height: 50, justifyContent: "center" }}
              >
                <Text
                  style={{
                    marginLeft: 15,
                    marginRight: 5,
                    fontSize: wp("3.5%"),
                    fontFamily: Fonts.montserratMedium,
                  }}
                >
                  {role == "" ? "Role *" : role}
                </Text>
              </View>

              <View
                style={{
                  width: 50,
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Image
                  style={{ flex: 1 }}
                  resizeMode={"center"}
                  source={Images.downArr}
                />
              </View>
            </View>
          </ModalDropdown>
        </View>
        </View>
      </View>
    );
  };

  renderSeleRow = (id, onPress, item, style) => {
    return (
      <TouchableOpacity
        activeOpacity={0.6}
        key={id}
        onPress={onPress}
        style={{
          width: wp("90%"),
          height: 30,
          alignSelf: "center",
          justifyContent: "center",
        }}
      >
        <Text
          style={{
            color: "rgba(0, 0, 0, 0.87)",
            width: wp("90%"),
            marginLeft: 10,
            marginTop: 2,
          }}
        >
          {item.email}
        </Text>
      </TouchableOpacity>
    );
  };

  validateEmail = (text) => {
    let reg = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
    if (reg.test(text) === false) {
      return false;
    } else {
      return true;
    }
  };

  textEnterDone = (items) => {
    var lastMail = items.result[items.result.length - 1];

    if (
      this.validateEmail(lastMail) == true &&
      this.state.showToaster == false
    ) {
      let data = {
        email: lastMail,
        ProjectId: this.props.projectDetails.id,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
      };

      check(
        CHECK_MEMBER,
        data,
        () => null,
        (response) => {
          this.setState({ showLoader: false });

          if (
            response.toString() == Strings.errors.timeout ||
            response.toString() == "Error: Network Error"
          ) {
            this.setState(
              {
                showToaster: true,
                toastMessage: Strings.errors.checkInternet,
                toastType: "error",
              },
              () => this.hideToast()
            );
          } else if (response.status) {
            if (response.status == 200 || response.status == 201) {
              if (response.data.isMemberExists) {

                this.setState(
                  {
                    showToaster: true,
                    toastMessage: `Member ${lastMail} already exists in this project`,
                    toastType: "error",
                  },
                  () => this.hideToast()
                );
              } else {
                var emailCollection = new Array();

                items.result.forEach((element) => {
                  if (this.validateEmail(element) == true) {
                    const exists = this.state.inviteMember.some(
                      (obj) => obj.mail === element
                    );
                    if (exists) {
                      let data = this.state.inviteMember.filter(
                        (item) => item.mail == element
                      );
                      emailCollection.push(data[0]);
                    } else {
                      let obj = { mail: element, role: "",companyId:"",companyName:"" };
                      emailCollection.push(obj);
                    }
                  } else {
                  }
                });

                this.setState(
                  {
                    inviteMember: emailCollection,
                    refresh: !this.state.refresh,
                  });
              }
            } else {

              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.data.message.toString(),
                  toastType: "error",
                },
                () => this.hideToast()
              );
            }
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.toString(),
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        }
      );
    } else {
      if (items.result.length == 0) {
      } else {
        this.setState(
          {
            showToaster: true,
            toastMessage: Strings.errors.validEmail,
            toastType: "error",
            unwantedData: [...this.state.unwantedData, lastMail],
          },
          () => this.hideToast()
        );
      }
    }
  };

  renderChip = (id, onClose, item, style, iconStyle) => {
    const exists = this.state.unwantedData.some((obj) => obj === id);

    if (!exists) {
      return (
        <View style={[styles.root, style]}>
          <View style={styles.container}>
            <Text style={styles.text} numberOfLines={1}>
              {id}
            </Text>
            <TouchableOpacity
              style={[styles.iconWrapper, iconStyle]}
              onPress={() => {
                this.setState(
                  {
                    inviteMember: this.state.inviteMember.filter(
                      (member) => member.id == item.id
                    ),
                  },
                  () => {
                    onClose();
                  }
                );
              }}
            >
              <Text
                style={[
                  styles.icon,
                  this.isIOS ? styles.iconIOS : styles.iconAndroid,
                ]}
              >
                ✕
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    } else {
      onClose(id);
      return null;
    }
  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.parentContainer} extraScrollHeight={200}>
          {this.renderHeader()}

          <Text style={styles.title2}>{Strings.inviteMember.title2}</Text>

          <View style={{ flex: 1, margin: 15 }}>
            <Text style={styles.title3}>{Strings.inviteMember.title3}</Text>

            <Selectize
              tintColor={Colors.themeColor}
              items={this.state.responsiblePersonData}
              selectedItems={this.state.selectedItem}
              ref={(ref) => {
                this.input = ref;
              }}
              containerStyle={{
                zIndex: 1,
              }}
              listStyle={{
                position: "absolute",
              }}
              renderRow={(id, onPress, item, style) =>
                this.renderSeleRow(id, onPress, item, style)
              }
              renderChip={(id, onClose, item, style, iconStyle) =>
                this.renderChip(id, onClose, item, style, iconStyle)
              }
              onChangeSelectedItems={(text, onClose) => {
                this.textEnterDone(text);
              }}
              textInputProps={{
                onChangeText: this.onChangeperson,
              }}
            />

            <Text style={styles.title4}>
              {Platform.OS === "ios"
                ? Strings.inviteMember.notesIos
                : Strings.inviteMember.notesAndroid}
            </Text>

            <FlatList
              style={
                this.state.keyboardHeight == 0
                  ? { flex: 1 }
                  : { flex: 1, maxHeight: this.state.keyboardHeight }
              }
              data={this.state.inviteMember}
              extraData={this.state}
              renderItem={this.renderItem}
            />

            {this.bottomContainer()}
          </View>
        </View>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}


        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            // container={{ marginBottom: hp("40%") }}
            container={
              this.state.isKeyboardShow == true
                ? { marginBottom: hp("40%") }
                : { marginBottom: hp("16%") }
            }
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.loginSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}
      </SafeAreaView>
      }
      </>
    );
  }
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title1: {
    width: wp("80%"),
    fontSize: wp("6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratBold,
  },

  title2: {
    margin: 5,
    marginLeft: 10,
    marginTop: 15,
    textAlign: "center",
    fontSize: wp("4.0%"),
    color: "#A8B2B9",
    fontFamily: Fonts.montserratRegular,
  },
  title3: {
    textAlign: "left",
    fontSize: wp("3.5%"),
    color: "#A8B2B9",
    marginTop: 15,
    fontFamily: Fonts.montserratRegular,
  },
  title4: {
    textAlign: "left",
    fontSize: wp("3%"),
    color: "#A8B2B9",
    marginTop: 5,
    marginBottom: 10,
    fontFamily: Fonts.montserratRegular,
  },
  profilePic: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },

  memberEmail: {
    marginRight: 10,
    fontSize: wp("3.9%"),
    fontFamily: Fonts.montserratRegular,
  },
  imageContainer: {
    flexDirection: "row",
    flex: 1,
    height: 50,
    alignItems: "center",
    backgroundColor: "red",
  },
  imageButton: {
    width: wp("24%"),
    height: wp("24%"),
    borderRadius: wp("12%"),
    backgroundColor: "#F5F5F5",
    borderWidth: 1,
    borderColor: "#00000029",
    justifyContent: "center",
    alignItems: "center",
  },
  placeholder: {
    width: wp("15%"),
    alignSelf: "center",
  },
  camera: {
    position: "absolute",
    bottom: 10,
    right: -10,
    width: wp("8%"),
    height: wp("8%"),
    borderRadius: wp("4%"),
    backgroundColor: Colors.white,
    justifyContent: "center",
    alignItems: "center",
  },
  memberContainer: {
    width: wp("90%"),
    height: hp("10%"),
    alignSelf: "center",
    marginTop: hp("2%"),
    justifyContent: "center",
  },
  idTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  idText: {
    width: wp("90%"),
    height: hp("5%"),
    backgroundColor: "#EFEFEF",
    marginTop: wp("1%"),
    padding: hp("1%"),
    paddingLeft: 15,
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: wp("4%"),
  },
  bottomContainer: {
    width: wp("90%"),
    flexDirection: "row",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: hp("4%"),
    marginTop: hp("8%"),
    // backgroundColor: Colors.white
  },
  cancel: {
    width: wp("35%"),
    height: hp("6%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: wp("35%"),
    height: hp("6%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: "#757575",
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  root: {
    justifyContent: "center",
    borderRadius: 50,
    backgroundColor: "#e0e0e0",
    paddingHorizontal: 10,
    paddingVertical: 4,
    height: 28,
    marginBottom: 4,
    marginRight: 4,
  },
  container: {
    flexDirection: "row",
    overflow: "hidden",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  text: {
    color: "rgba(0, 0, 0, 0.87)",
  },
  iconWrapper: {
    borderRadius: 50,
    backgroundColor: "#a6a6a6",
    height: 16,
    width: 16,
    overflow: "hidden",
    marginLeft: 4,
    justifyContent: "center",
    alignItems: "center",
  },
  icon: {
    textAlign: "center",
    color: "#e0e0e0",
  },
  iconIOS: {
    fontSize: 14,
  },
  iconAndroid: {
    fontSize: 13,
    lineHeight: 15,
  },
  customDropdownStyle: {
    flex: 1,
    flexDirection: "row",
    width: "100%",
    height: 44,
    borderColor: "#A8B2B9",
    borderWidth: 1,
    borderRadius: 5,
  },
  customOptionsStyle: {
    // marginTop:10,
    // left:'59%',
    // right: 0,
    marginTop: 10,
    // width:wp("40%"),
    // justifyContent: "flex-end",
    height: hp("14%"),
    //   width: wp("50%"),
    maxWidth: 155,
    borderColor: Colors.white,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),

    // //outlineProvider: 'bounds',
    // backgroundColor:'red'
  },
  customOptionsTextStyle: {
    fontFamily: Fonts.montserratMedium,
    color: Colors.planDesc,
    height: hp("5%"),
    backgroundColor: "red",
  },
  seperator: { flex: 1, backgroundColor: Colors.green },
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    updatelist,
    updateInviteMem,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    updatelist,
    updateInviteMem,
  };
};

export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    cameBack,
    editData,
    updateList,
    refreshPage,
    refreshDashboard,
    clickAdd,
    updateInviteMember
  }),
  withBackHandler
)(InviteMember);
