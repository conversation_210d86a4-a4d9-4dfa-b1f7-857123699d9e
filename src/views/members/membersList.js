import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  Platform,
} from "react-native";

import {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  clickAdd,
  editData,
  onTapSearch,
  storeRole,
  updateList,
  setPage,
  refreshPage,
  _getMemberList,
  refreshDashboard,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import {
  MEMBERS_LIST,
  DELETE_MEMBER,
  GET_ROLE,
  GET_NEW_COMPANIES,
  RESEND_LINK,
} from "../../api/Constants";
import {
  getMemberList,
  deleteMember,
  getRole,
  getNewCompanyList,
  resend,
} from "../../api/Api";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Member<PERSON><PERSON>,
} from "../../components";

import { Images, Strings, Fonts, Colors, isEmptyString } from "../../common";

import Modal from "react-native-modal";
import { trackScreen,trackEvent } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent} from "../../MixPanel/MixPanel";
import Dropdown from "../../components/dropdown/dropdown";
import DeleteError from "../../components/DeleteError/DeleteError";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";

let DROPDOWNOPTIONS = [
  { id: "Edit", image: Images.editNew, name: "Edit" },
  { id: "Delete", image: Images.deleteNew, name: "Delete" },
];
let DROPDOWNOPTIONSEDIT = [{ id: "Edit", image: Images.editNew, name: "Edit" }];

let DROPDOWNOPTIONSPENDING = [
  { id: "Resend Link", image: Images.resendNew, name: "Resend Link" },
  { id: "Delete", image: Images.deleteNew, name: "Delete" },
];
let statusList=[{id:1,name:Strings.placeholders.completed},
  {id:2,name:Strings.placeholders.pending}]
class MemberList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      memberslist: [],
      showLoader: false,
      showToaster: false,
      showAlert: false,
      toastMessage: "",
      toastType: "error",
      refreshing: false,
      lastId: 0,
      showFilter: false,
      rolelist: [],
      searchText: "",
      selectedRole: null,
      selectedCompany: null,
      selectedCompanyId: 0,
      selectedRoleId: 0,
      companylist: [],
      statusOption: [],
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this.props.projectDetails.ParentCompany.Company[0].companyName,
        FirstName:this.props.userDetails.firstName,
      },
      companyModalVisible:false,
      roleModalVisible:false,
      showError:false,
      errorMessage:'',
      selectedStatus:'',
      isStatusVisible:false,
      isNetworkCheck: false,
    };

    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;

    this.onSelectDropdown = this.onSelectDropdown.bind(this);
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.renderInital();
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.renderInital();
    }
    })
  }


  renderInital = () => {
    this.setState({ showLoader: true });
    this.page_number = 1;
    this.getRoles();
    //test

    this.getMemeberList();
    this.getCompanylist();
  };

  getCompanylist = () => {
    getNewCompanyList(
      GET_NEW_COMPANIES +
        this.props.projectDetails.id +
        "/" +
        this.props.projectDetails.ParentCompany.id,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.status == 200) {
            this.storeCompanyName(response.data.data);
          } else if (response.data.message.message) {
            this.showError("error", response.data.message.message);
          } else {
            this.showError("error", response.data.message);
          }
        } else {
          this.showError("error", response.toString());
        }
      }
    );
  };

  storeCompanyName = (data) => {
    let item = [];
    for (let ele of data) {
      item.push({
        id: ele.id,
        label: ele.companyName,
        value: ele.companyName,
        name:ele.companyName,
      });
    }
    this.setState({ companylist: item });
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      this.props.navigation.navigate("Menu");
      this.props.showSideMenu(false);
    }

    if (nextProps.refresh == true) {
      this.setState({
        showLoader: true,
      });
      this.renderInital();
      this.props.refreshPage(false);
    }

    if (this.props.projectSwitched != nextProps.projectSwitched) {
      this.renderInital();
    }
  }

  hideToast = () =>
    setTimeout(() => this.setState({ showToaster: false }), 2000);

  getMemeberList = async () => {
    let param = {};

    if (this.state.filter == true) {
      param = {
        companyFilter: this.state.selectedCompany,
        roleFilter: this.state.selectedRoleId,
        nameFilter: this.state.searchText,
        statusFilter: this.state.selectedStatus.toLowerCase(),
      };
    }

    let url = `${MEMBERS_LIST}${this.props.projectDetails.id}/20/${this.page_number}/${this.props.projectDetails.ParentCompany.id}`;

    this.setState({ showNoData: false });

    await getMemberList(
      url,
      param,
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.status == 200) {
            let data = this.state.memberslist;
            this.props.storeLastid(response.data.lastId.memberId);
            if (this.page_number == 1) {
              if (response.data.data.count == 0) {
                this.setState({ showNoData: true, memberslist: [] });
              } else {
                this.setState({
                  memberslist: response.data.data.rows,
                  totalCount: response.data.data.count,
                  lastId: response.data.lastId.id,
                });
              }
            } else {
              let data1 = response.data.data.rows;
              this.setState({
                memberslist: data.concat(data1),
                totalCount: response.data.data.count,
                lastId: response.data.lastId.id,
              });
            }
          } else if (response.status == 400) {
            this.setState(
              {
                showToaster: true,
                toastMessage: "Bad Request",
                toastType: "error",
              },
              () => this.hideToast()
            );
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };

  renderEmail = (title, name) => {
    return (
      <View style={styles.emailContainer}>
        <Text style={styles.emailTitle}>{title}</Text>
        <Text style={styles.emailText}>{name}</Text>
      </View>
    );
  };

  renderRow = (option, index, isSelected) => {
    return (
      <View
        style={{
          width: wp("40%"),
          flexDirection: "row",
          alignItems: "center",
          height: hp("7%"),
          backgroundColor: "white",
        }}
      >
        <Image
          resizeMode={"center"}
          source={option.image}
          style={{
            width: option.id == "Edit" ? wp("5%") : wp("6%"),
            height: option.id == "Edit" ? hp("4%") : hp("5%"),
            marginLeft: 10,
          }}
        />
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: Colors.planDesc,
            marginLeft: 10,
          }}
        >
          {option.id}
        </Text>
      </View>
    );
  };

  renderSeparator = () => {
    return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
  };
  resendInvite = (item) => {
    let data = {
      ParentCompanyId: item.ParentCompanyId,
      email: item.User.email,
      memberId: item.id,
      requestType: 1,
      type: item.Role.roleName,
    };
    resend(
      RESEND_LINK,
      data,
      () => {},
      (response) => {
        if (response.status) {
          if (response.status == 201) {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "success",
              },
              () => this.hideToast()
            );
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        } else {
          this.setState(
            {
              showLoader: false,
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };
  onSelectDropdown = (option, item, index, status) => {
    let name = status[option].id;
    if (name == "Edit") {
       this.editMember(item, index);
       trackScreen('Edit Member')
     } 
    else if (name == "Resend Link") {
      this.resendInvite(item);
    } else {
      this.setState({
        showDelete: true,
        selectedMember: item,
        selectedIndex: index,
      });
    }
  };

  deleteMember = (item, index) => {
    this.setState({ showLoader: true });
    let params = {
      id: [item.id],
      ProjectId: this.props.projectDetails.id,
      isSelectAll: false,
    };

    deleteMember(
      DELETE_MEMBER,
      params,
      () => null,
      (response) => {
        if (response.status) {
          if (response.status==500) {
            this.setState(
              {
                errorMessage:response.data.message,
                showLoader: false,
                showError:true,
              });

          } else if (response.status==200) {
            this.page_number = 1;
            this.getMemeberList();
            this.setState(
              {
                showToaster: true,
                toastMessage: "Deleted succesfully",
                toastType: "success",
              },
              () => this.hideToast()
            );
            setTimeout(()=>{this.props.refreshDashboard(true)},2000)
            trackEvent('Deleted_Member')
            mixPanelTrackEvent('Deleted Member',this.state.mixpanelParam)
            setTimeout(() => {
              this.props.refreshDashboard(true);
            }, 2000);
          } else {
            this.setState(
              {
                showLoader: false,
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        } else {
          this.setState(
            {
              showLoader: false,
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };

  editMember = (item, index) => {
    this.props.setPage("editMembers");

    this.props.editData({
      item: item,
      index: index,
    });

    this.props.clickAdd(true);
  };

  getRoles = () => {
    this.setState({ showLoader: true });

    getRole(
      GET_ROLE,
      {},
      () => null,
      (response) => {
        this.setState({ showLoader: false });

        if (response.status) {
          if (response.status == 200) {
            this.props.storeRole(response.data.data);
            this.storeRole(response.data.data);
          }
        }
      }
    );
  };

  storeRole = (data) => {
    let roles = [];

    for (let item of data) {
      roles.push({
        label: item.roleName,
        value: item.roleName,
        id: item.id,
        name:item.roleName,
      });
    }

    this.setState({ rolelist: roles });
  };

  showError = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
      },
      () => this.hideToast()
    );
  };

  renderFlatListItem = ({ item, index }) => {
    let option = [];
    if (item.status == "pending") {
      option = DROPDOWNOPTIONSPENDING;
    } else {
      option =
        this.props.userDetails.email == item.User.email
          ? DROPDOWNOPTIONSEDIT
          : DROPDOWNOPTIONS;
    }

    return (
      <MemberCard
        item={item}
        options={option}
        onSelect={(options) =>
          this.onSelectDropdown(options, item, index, option)
        }
        userDetails={this.props.userDetails}
        projectRoleId={this.props.projectRoleId}
      />
    );
  };

  renderHeader() {
    let count = 0;
    if (this.state.selectedRole !== null) {
      count = 1;
    }
    if (this.state.searchText !== "") {
      count = count + 1;
    }

    if (this.state.selectedCompany !== null) {
      count = count + 1;
    }
    if (isEmptyString(this.state.selectedStatus)){
      count= count+1;
    }

    return (
      <View style={styles.headerContainer}>
        <View style={{flexDirection: "row" ,width:'60%'}}>
          {/* {this.props.checkCameBack == true && (
            <View
              style={{
                width: 50,
                minHeight: 50,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <TouchableWithoutFeedback
                onPress={() => {
                  this.props.cameBack(true);
                  this.props.navigation.goBack();
                }}
              >
                <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
              </TouchableWithoutFeedback>
            </View>
          )} */}
          <View
            style={{
              flex: 1,
              minHeight: 50,
              marginLeft: 15,
              alignContent: "center",
              justifyContent: "center",
            }}
          >
            <Text style={styles.title}>{Strings.menu.members}</Text>
          </View>
          
        </View>

        <View style={styles.headerRowContainer}>
          <TouchableOpacity
            style={[styles.image, { marginTop: wp("1%"),marginRight:wp('4%') }]}
            onPress={() => this.setState({ showFilter: true })}
          >
            <Image source={Images.filter} />
            {this.state.filter == true && (
              <View
                style={{
                  position: "absolute",
                  marginTop: -10,
                  right: -10,
                  backgroundColor: Colors.themeColor,
                  width: 16,
                  justifyContent: "center",
                  alignItems: "center",
                  height: 16,
                  borderRadius: 8,
                }}
              >
                <Text style={{ color: "white" }}>{count}</Text>
              </View>
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.image,{marginHorizontal:wp('2%'),marginRight:wp('4%')}]}
            onPress={() => {
              this.props.onTapSearch("memberSearch");
            }}
          >
            <Image source={Images.Search1} style={{ height: 21, width: 21 }} />
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  onEndReached = () => {
    if (this.state.memberslist.length < this.state.totalCount) {
      this.page_number = this.page_number + 1;
      this.getMemeberList();
      this.onEndReachedCalledDuringMomentum = true;
    }
  };

  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        memberslist: [],
        totalCount: 0,
        showLoader: true,
      },
      () => {
        this.getMemeberList();
      }
    );
  };

  applyFilter = () => {
    if (
      this.state.selectedRoleId !== null ||
      this.state.searchText !== "" ||
      this.state.selectedCompany !== null
    ) {
      this.setState(
        {
          filter: true,
          showFilter: false,
          showLoader: true,
        },
        () => {
          this.getMemeberList();
        }
      );
    }
  };

  onPressCompanyType=(item)=>{
    this.setState({
      selectedCompany: item.value,
      selectedCompanyId: item.id,
      companyModalVisible: false
    });
  }

  onPressRoleType=(item)=>{
    this.setState({
      selectedRole: item.value,
      selectedRoleId: item.id,
      roleModalVisible:false
    });
  }

  /**
   * onPressStatus set the value of Status
   * @param {object} item 
   */
  onPressStatus=(item)=>{
    this.setState({
      selectedStatus: item.name,
      isStatusVisible:false
    });
  }

  renderFilter =  () => {
    return (
      <View style={modalStyles.container}>
        <View style={modalStyles.topContainer}>
          <TouchableOpacity
            onPress={() => this.setState({ showFilter: false })}
            style={{ width: 40 }}
          >
            <Image source={Images.closeBlack} />
          </TouchableOpacity>
          <View style={modalStyles.titleContainer}>
            <Text style={modalStyles.title}>{Strings.filter.title}</Text>
          </View>
          <View style={{ width: 40, height: 40 }} />
        </View>

        <TextField
          showLeft={true}
          attrName={Strings.placeholders.name}
          title={Strings.placeholders.name}
          value={this.state.searchText}
          updateMasterState={(key, value) => {
            this.setState({
              searchText: value,
            });
          }}
          hideShow={false}
          hideImage={""}
          textInputStyles={{
            color: Colors.black,
            fontSize: 12,
            width: "75%",
            marginLeft: wp("10%"),
            fontFamily: Fonts.montserratMedium,
            paddingTop: 10,
          }}
          textTitleStyles={{
            marginLeft: wp("10%"),
            fontSize: 14,
            fontFamily: Fonts.montserratMedium,
          }}
          leftImage={Images.searchGray}
          leftButton={{ bottom: 0 }}
        />

              <TextField
                  attrName={Strings.placeholders.company}
                  title={Strings.placeholders.company}
                  value={this.state.selectedCompany}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  textInputStyles={modalStyles.dropdownText}
                  showButton={true}
                  onPress={() => {
                    this.setState({ companyModalVisible: true});
                  }}
                  imageSource={Images.downArr}
              
              />

               <Dropdown
                  data={this.state.companylist}
                  title={Strings.placeholders.company}
                  value={this.state.selectedCompany}
                  closeBtn={() => this.setState({ companyModalVisible: false })}
                  onPress={(item) => this.onPressCompanyType(item)}
                  visible={this.state.companyModalVisible}
                  onbackPress={() => this.setState({ companyModalVisible:false })}
                  container={styles.equipmentContainer}
                  customMainContainer={styles.renderEquipStyle}
                  equipTextContainer={styles.equipTextStyle}
               />

            <TextField
                  attrName={Strings.placeholders.role}
                  title={Strings.placeholders.role}
                  value={this.state.selectedRole}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  textInputStyles={modalStyles.dropdownText}
                  showButton={true}
                  onPress={() => {
                    this.setState({ roleModalVisible: true});
                  }}
                  imageSource={Images.downArr}
              
              />

               <Dropdown
                  data={this.state.rolelist}
                  title={Strings.placeholders.role}
                  value={this.state.selectedRole}
                  closeBtn={() => this.setState({ roleModalVisible: false })}
                  onPress={(item) => this.onPressRoleType(item)}
                  visible={this.state.roleModalVisible}
                  onbackPress={() => this.setState({ roleModalVisible:false })}
                  container={styles.equipmentContainer}
                  customMainContainer={styles.renderEquipStyle}
                  equipTextContainer={styles.equipTextStyle}
               />
                   <TextField
                  attrName={Strings.placeholders.status}
                  title={Strings.placeholders.status}
                  value={this.state.selectedStatus}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  textInputStyles={modalStyles.dropdownText}
                  showButton={true}
                  onPress={() => {
                    this.setState({ isStatusVisible: true});
                  }}
                  imageSource={Images.downArr}
              
              />
               <Dropdown
                  data={statusList}
                  title={Strings.placeholders.status}
                  value={this.state.selectedStatus}
                  closeBtn={() => this.setState({ isStatusVisible: false })}
                  onPress={(item) => this.onPressStatus(item)}
                  visible={this.state.isStatusVisible}
                  onbackPress={() => this.setState({ isStatusVisible:false })}
                  container={styles.equipmentContainer}
                  customMainContainer={styles.renderEquipStyle}
                  equipTextContainer={styles.equipTextStyle}
               />
       
        <View style={modalStyles.buttonContainer}>
          <TouchableOpacity
            style={[
              modalStyles.cancelButton,
              { backgroundColor: Colors.white },
            ]}
            onPress={() =>
              this.setState(
                {
                  showFilter: false,
                  selectedRole: null,
                  selectedRoleId: 0,
                  searchText: "",
                  selectedCompanyId: 0,
                  selectedCompany: null,
                  selectedStatus:"",
                },
                () => {
                  if (this.state.filter == true) {
                    this.setState({ filter: false }, () => {
                      this.getMemeberList();
                    });
                  }
                }
              )
            }
          >
            <View
              style={
                this.state.filter == true
                  ? [
                      modalStyles.cancelButton,
                      { backgroundColor: Colors.themeOpacity },
                    ]
                  : modalStyles.cancelButton
              }
            >
              <Text
                style={[
                  modalStyles.cancelText,
                  {
                    color:
                      this.state.filter == true
                        ? Colors.themeColor
                        : Colors.buttonBackground,
                  },
                ]}
              >
                {this.state.filter == true
                  ? Strings.addMember.reset
                  : Strings.addMember.cancel}
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={modalStyles.applyButton}
            onPress={() => {
              this.applyFilter();
            }}
          >
            <Text style={modalStyles.applyText}>{Strings.filter.apply}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  renderNoMember = () => {
    return (
      this.state.showNoData == true && (
        <Text style={styles.noMember}>No Members List Found</Text>
      )
    );
  };

  deletePopupAcceptTap = () => {
    this.setState({ showDelete: false });
    this.deleteMember(this.state.selectedMember, this.state.selectedIndex);
  };

  deletePopupDeclineTap = () => {
    this.setState({
      showDelete: false,
      selectedMember: [],
      selectedIndex: null,
    });
  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <AppView style={{ height: hp("50%") }}>
        <View style={styles.parentContainer}>
          {this.renderHeader()}

          <FlatList
            data={this.state.memberslist}
            renderItem={this.renderFlatListItem}
            keyExtractor={(item, index) => index.toString()}
            onEndReached={() => this.onEndReached()}
            onEndReachedThreshold={0}
            onMomentumScrollBegin={() => {
              this.onEndReachedCalledDuringMomentum = false;
            }}
            onRefresh={() => this._onReset()}
            refreshing={this.state.refreshing}
          />

          {this.renderNoMember()}
        </View>

        {this.state.showLoader && (
          <Modal
            isVisible={true}
            backdropOpacity={0}
            style={{
              paddingTop: 45,
              paddingBottom: 30,
              margin: 0,
              backgroundColor: "#0000",
            }}
          >
            <AppLoader viewRef={this.state.showLoader} />
          </Modal>
        )}
        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.forgotSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}
      {this.state.showError &&(  <DeleteError message={this.state.errorMessage} close={()=>this.setState({showError:false})}/>)}
        {this.state.showDelete && (
          <DeletePop
            title={Strings.popup.success}
            desc={Strings.popup.delete}
            acceptTap={this.deletePopupAcceptTap}
            declineTap={this.deletePopupDeclineTap}
            container={{height:'35%'}}
          />
        )}

        <Modal
          isVisible={this.state.showFilter}
          style={modalStyles.filterModal}
        >
          {this.renderFilter()}
        </Modal>
      </AppView>
      }
      </>
    );
  }
}

const styles = StyleSheet.create({
  parentContainer: {
    flex: 1,
    backgroundColor: "#FCFBFC",
  },
  headerContainer: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderBottomWidth: 0.3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  title: {
    color: Colors.black,
    fontSize: 22,
    fontFamily: Fonts.montserratBold,
  },
  headerRowContainer: {
    flex: 1,
    height: hp("15%"),
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  image: {
    marginRight: wp("6%"),
    marginBottom: hp("2%"),
  },
  flatlistContainer: {
    width: wp("90%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    alignSelf: "center",
    borderRadius: wp("2%"),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,

    elevation: 8,
  },
  nameContainer: {
    minHeight: hp("16%"),
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  detailContainer: {
    width: wp("53%"),
    marginLeft: 20,
    justifyContent: "center",
  },
  imagePlaceholder: {
    width: wp("14%"),
    height: wp("14%"),
    borderRadius: wp("7%"),
    marginLeft: wp("6%"),
  },
  nameText: {
    color: Colors.black,
    fontSize: 15,
    fontFamily: Fonts.montserratSemiBold,
  },
  companyText: {
    color: "#1E1E1E",
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
    marginTop: hp("1%"),
  },
  dotMenu: {
    // marginTop: hp('2%')
  },
  emailContainer: {
    flex: 1,
    marginLeft: 10,
    marginRight: 10,
  },
  emailTitle: {
    color: "#5B5B5B",
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
  },
  emailText: {
    color: "#1E1E1E",
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginTop: 0,
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("32%"),
    borderColor: Colors.white,
    marginRight: wp("28%"),
    borderWidth: 0.3,
    alignSelf: "center",
    borderRadius: wp("5%"),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
    elevation: 16,
    marginLeft: wp("-25%"),
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: wp("28%"),
  },
  noMember: {
    alignSelf: "center",
    position: "absolute",
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("45%"),
  },
  equipmentContainer:{
    height: hp("4%"),
    paddingBottom:5
  },
  renderEquipStyle:{
   marginBottom:10,
  },
  equipTextStyle:{
    width:'100%',
    textAlign:'center',
    fontSize:16
  },
});

const modalStyles = StyleSheet.create({
  container: {
    flex: 1,
    width: wp("100%"),
    height: hp("95%"),
  },
  topContainer: {
    flexDirection: "row",
    margin: 20,
    height: 40,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 20,
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  buttonContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-end",
    marginBottom: 10,
    justifyContent: "space-around",
    backgroundColor: Colors.white,
  },
  cancelButton: {
    backgroundColor: `rgba(117,117,117,0.2)`,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonBackground,
    fontSize: 14,
    fontFamily: Fonts.montserratBold,
  },
  applyButton: {
    backgroundColor: Colors.themeOpacity,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  applyText: {
    color: Colors.themeColor,
    fontSize: 14,
    fontFamily: Fonts.montserratBold,
  },
  noMember: {
    alignSelf: "center",
    position: "absolute",
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("45%"),
  },
  filterModal: {
    paddingTop: 45,
    paddingBottom: 30,
    margin: 0,
    backgroundColor: Colors.white,
  },
  filterPlaceholder:{
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
  
  },
  dropdownText:{
    color: Colors.black,
    fontSize: 14,
  }
});

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    userDetails,
    updatelist,
    refresh,
    projectRoleId,
    membersList,
    projectSwitched,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    userDetails,
    updatelist,
    refresh,
    projectRoleId,
    projectSwitched,
    membersList,
  };
};

export default connect(mapStateToProps, {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  clickAdd,
  editData,
  onTapSearch,
  getRole,
  storeRole,
  updateList,
  setPage,
  refreshPage,
  _getMemberList,
  refreshDashboard,
})(MemberList);
