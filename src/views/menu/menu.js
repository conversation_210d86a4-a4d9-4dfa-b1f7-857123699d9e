import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Image,
  ScrollView,
  TouchableWithoutFeedback,
  TouchableOpacity,
  FlatList,
  StatusBar,
  Platform
} from "react-native";
import Colors from "../../common/color";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import { connect } from "react-redux";
import {
  setPage,
  selectedProjectDetails,
  projectList,
  cameBack,
  getUserDetails,
  logout,
  updateData,
  storeProjectRole,
  goToVoid,
  projectSwitched,
  afterProjectCreated,
  drResponsiblePerson,
  selectedCompany,
} from "../../actions/postAction";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { View as AnimatableView } from "react-native-animatable";
import { getProjectList, getCompanyprojectlist, _getData, getuserDetails, cleardevicetoken } from "../../api/Api";
import {
  GET_PROJECT_ROLE,
  USER_DETAILS,
  CLEAR_DEVICETOKEN,
  GET_COMPANY_PROJECT_LIST,
  GET_ACCOUNT_PROJECT,
} from "../../api/Constants";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import { CommonActions } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DeletePop from "../../components/toastpopup/logoutPop";
import Modal from "react-native-modal";
import { AppLoader } from "../../components";
import { trackScreen } from "../../Google Analytics/GoogleAnalytics";
import { mixPanelTrackEvent } from "../../MixPanel/MixPanel";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import Intercom from '@intercom/intercom-react-native'
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";

//keep apiCalls count
var apiCallCount = 1;

class menu extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      showLoader: false,
      showToaster: false,
      toastMessage: "",
      toastType: "error",
      projectDetails: [],
      projectSelected: [],
      showProjects: false,
      userDetails: [],
      showDelete: false,
      SCcompanyList: false,
      roleId: 2,
      CompanyListData: [],
      parentcompanyid: 0,
      showCompany:false,
      selectedCompany:[],
      companyData:[],
      mixpanelParam:{
        ProjectName:this.props.projectDetails.projectName,
        CompanyName:this?.props?.projectDetails?.ParentCompany?.Company[0]?.companyName,
        FirstName:this.props.userDetails.firstName,
      },
      userRoleID:2,
      isNetworkCheck:false,
      profileName:''
    };
    this.onEndReachedCalledDuringMomentum = true;
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.renderInitial();
    }  
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.renderInitial();
    }
    })
  }

  UNSAFE_componentWillReceiveProps(nextprops) {
    if (nextprops.checkCameBack) {
      this.renderInitial();
      this.props.cameBack(false);
    }
    if (nextprops.afterCreateProject) {
      this.setState({ showProjects: false });
      this.props.afterProjectCreated(false);
      this.getCompanyProject();
    }
    // alert(nextprops.updatedata)
  }

  renderInitial = async () => {

    if (this.props.projectlist.length) {
      this.setState({
        projectDetails: this.props.projectlist,
        companyData:this.props.company_List,
        projectSelected: this.props.projectDetails,
        showLoader: false,
        selectedCompany:this.props.selected_Company,
        parentcompanyid:this.props.selected_Company.id,
        CompanyListData:this.props.company_List,
      });
    } else {
      this.setState({ showLoader: true });
    }
    this.getRole(this.props.projectDetails)
    this.getuserDetails();
  };

  getProjectList = async () => {
    if (this.props.projectDetails.id&& this.props.selected_Company.id) {
      this.setState(
        { parentcompanyid:this.props.selected_Company.id,
        },
        () => {
          this.getCompanyProject();
        }
      );
    } else {
      this.getcomapnylist();
    }
  };

  getcomapnylist = async () => {
    await getCompanyprojectlist(
      GET_COMPANY_PROJECT_LIST,
      {},
      () => {},
      (resp) => {
        if (resp.status == 200) {
          this.setState(
            {
              CompanyListData: resp.data.data,
              parentcompanyid: resp.data.data[0].id,
              selectedCompany:resp.data.data[0]
            },
            () => {
              this.getCompanyProject();
            }
          );

          if (apiCallCount === 1) {
            /** Temperory solution for app crash
             * App crashes while getUserDetails function calls multiple times
             * So restricting the getUserDetails function from calling multiple times using ->
             * api call count
             */
            // this.props.getUserDetails(resp.data);
            apiCallCount = apiCallCount + 1;
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: resp.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
  };

  getuserDetails = async () => {
    await getuserDetails(
      USER_DETAILS,
      {},
      () => {},
      (resp) => {
        if (resp.status) {
          if (resp.data.email) {
            this.setState({ userDetails: resp.data,
              profileName:`${resp.data.firstName.charAt(0)}${resp.data.lastName.charAt(0).toUpperCase()}`
            });
            //from menu -> DRList -> menu
            // this line causes crash
            // this.props.getUserDetails(resp.data);
          }
        } 
        //TODO FOR LATER
        // else {
        //   this.setState(
        //     {
        //       showToaster: true,
        //        toastMessage: resp.toString(),
        //       toastType: "error",
        //     },
        //     () => {
        //       setTimeout(() => {
        //         this.setState({
        //           showToaster: true,
        //         });
        //       }, 2000);
        //     }
        //   );
        // }
      }
    );
  };

  saveSettingsArr = (admin) => {
    if (admin == true) {
      this.setState({
        data: [
          { title: Strings.menu.allRequest, image: Images.dr },
          { title: Strings.menu.members, image: Images.member },
          { title: Strings.menu.company, image: Images.company },
          { title: Strings.menu.gates, image: Images.gate },
          { title: Strings.menu.equip, image: Images.equipment },
          { title: Strings.menu.df, image: Images.df },
          { title: Strings.menu.calendarSettings, image: Images.calendarSettings }, 
          { title: Strings.menu.voidlist, image: Images.voidList },
          { title: Strings.menu.Help, image: Images.help },
          // {title: Strings.menu.settings, image: Images.settings},
        ],
      });
    } else {
      this.setState({
        data: [
          { title: Strings.menu.allRequest, image: Images.dr },
          { title: Strings.menu.members, image: Images.member },
          { title: Strings.menu.company, image: Images.company },
          { title: Strings.menu.voidlist, image: Images.voidList },
          { title: Strings.menu.Help, image: Images.help},
        ],
      });
    }
  };

  getRole = async (data) => {
    // this.setState({ showLoader: false });

    let url = GET_PROJECT_ROLE + data.id + "/" + data.ParentCompany.id;
    this.setState({ showLoader: true });
    let response = await _getData(url);
    this.setState({ showLoader: false });
    if (response.data) {
      this.props.storeProjectRole(response?.data?.data?.RoleId);
      this.setState({ userRoleID:response?.data?.data?.RoleId})
      this.setState({ roleId: response?.data?.data?.RoleId });
      let roleData={
        email:response?.data?.data?.User?.email,
        id:response?.data?.data?.id,
        userId:response?.data?.data?.UserId,
        name:`${response?.data?.data?.User?.firstName} ${response?.data?.data?.User?.lastName}(${response?.data?.data?.User?.email})`,
      }
      this.props.drResponsiblePerson(roleData)
      if (response?.data?.data?.RoleId == 2 || response?.data?.data?.RoleId == 3) {
        this.saveSettingsArr(true);
      } else {
        this.saveSettingsArr(false);
      }
    }
  };

  getCompanyProject = async () => {
    await getProjectList(
      GET_ACCOUNT_PROJECT + `${this.state.parentcompanyid}`,
      {},
      () => {},
      async(response) => {
        console.log("getProjectList", response);
        this.setState({ showLoader: false });

        if (response?.status) {
          if (response?.data?.data) {
             this.getRole(response?.data?.data[0]);
              await this.props.projectList(response?.data?.data);
              await this.props.selectedProjectDetails(response?.data?.data[0])
            if(response.data!=null){
            this.setState({
              projectDetails: response?.data?.data,
              projectSelected: response?.data?.data[0],
            });
          }
          this.props.projectSwitched(new Date());
          } else {
            this.setState(
              {
                projectDetails: [],
                projectSelected: [],
                showToaster: true,
                toastMessage: Strings.errors.projectListFailed,
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              projectDetails: [],
              projectSelected: [],
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
  };

  updateProfile = () => {
    this.getuserDetails();
  };

  renderHeader = () => {
    const { userDetails } = this.props;
    return (
      <AnimatableView
        animation={"fadeIn"}
        style={[styles.header, { width: wp("93%") }]}
      >
        <View style={[styles.header, { width: wp("80%") }]}>
          <TouchableWithoutFeedback
            onPress={() => {
              this.props.navigation.navigate("Profile");
              trackScreen('Profile')
            }}
          >
            <View style={[styles.header]}>
              { this.state.userDetails.profilePic?
              <Image
                source={
                   { uri: this.state.userDetails.profilePic }
                }
                style={{
                  width: hp("6%"),
                  height: hp("6%"),
                  borderRadius: hp("3%"),
                }}
              />
              : 
              <View style={styles.profileContainer}>
                 <Text style={styles.profileText}>{this.state.profileName}</Text>
               </View>
                }
              <Text
                numberOfLines={2}
                style={[styles.usrname, { width: wp("60%") }]}
              >
                {this.state.userDetails.firstName} 
              </Text>
            </View>
          </TouchableWithoutFeedback>
        </View>
        <View style={styles.closecontainer}>
          <TouchableWithoutFeedback
            onPress={() => {
              if (this.state.data.length > 2) {
                //this.props.cameBack(true);
                this.props.navigation.goBack();
              } else {
                const resetAction = CommonActions.reset({
                  index: 0,
                  routes: [{ name: "RegisteredRoute" }],
                });
                this.props.navigation.dispatch(resetAction);
              }
            }}
          >
            <Image
              resizeMode={"contain"}
              source={Images.close}
              style={{ width: wp("12%"), height: hp("3%") }}
            />
          </TouchableWithoutFeedback>
        </View>
      </AnimatableView>
    );
  };

  onTap= (item, index) => {
    if(item.title===Strings.menu.members){
      trackScreen('Members')
    }else if(item.title===Strings.menu.company){
      trackScreen('Companies')
    }else if(item.title===Strings.menu.gates){
      trackScreen('Gates')
    }else if(item.title===Strings.menu.equip){
      trackScreen('Equipments')
    }else if(item.title===Strings.menu.df){
      trackScreen('Definable Fetaures of Work')
    }
    if (item.title === Strings.menu.voidlist) {
      console.log('Navigating to void');
      this.props.goToVoid(true);
      this.props.navigation.navigate("VoidList");
      trackScreen('Void List')
      //  this.props.setPage(item.title);
      //this.props.cameBack(true);
      //this.props.navigation.goBack();
      //this.props.cameBack(false)
    }else if(item.title===Strings.menu.allRequest) {
      console.log('Navigating to Deliveries');
      this.props.setPage(Strings.menu.dr);
      this.props.cameBack(true);
      this.props.navigation.goBack();
      this.props.cameBack(false);
      trackScreen('Deliveries')
    } else if(item.title===Strings.menu.Help) {
      Intercom.displayMessenger()
    }
    else {
      this.props.setPage(item.title);
      this.props.cameBack(true);
      this.props.navigation.goBack();
      this.props.cameBack(false);
    }

  };

  /*  *getRoleOnSelectProject(data) {
    yield this.getRole(data);
  }

  *updateSelectedProject(data) {
    yield this.props.selectedProjectDetails(data);
  } */

  onSelectProject = (item) => {
    console.log("item", item);
    this.props.selectedProjectDetails(item);
    console.log("here 1");
    this.getRole(item);
    console.log("here 2");
    this.setState({ projectSelected: item, showProjects: false }, () => {
      console.log("here 3");
      this.props.projectSwitched(new Date());
    });
    console.log("here 4");
  };

  onTapLogout = () => {
    mixPanelTrackEvent('User Logged Out',this.state.mixpanelParam)
    Intercom.logout()
    this.clearToken();
  };

  clearToken = async () => {
    await cleardevicetoken(
      `${CLEAR_DEVICETOKEN}`,
      {},
      () => {},
      async (compResp) => {
        try {
          AsyncStorage.clear().then(async () => {
            await this.props.logout(true);
            const resetAction = CommonActions.reset({
              index: 0,
              routes: [{ name: "Dashboard" }],
            });
            this.props.navigation.dispatch(resetAction);
          });
        } catch (e) {
          console.error("error",e)
        }
      }
    );
  };

  addnewProject = () => {
    this.props.navigation.navigate("AddProject", { from: "menu" });
  };

  renderProductFlatListItem = ({ item, index }) => {
    return (
      <View>
        <View style={{ width: wp("96%"), marginTop: hp("1%") }}>
          <TouchableOpacity
            onPress={() => {
              let event = {};
              event = this.state.projectDetails.filter(
                (e) => e.projectName === item.projectName
              );

              this.props.selectedProjectDetails(event[0]);
              this.getRole(event[0]);
              this.props.projectList(event[0]);
              // this.setState({ projectDetails: response.data.data, projectSelected: data })
              this.setState({
                projectSelected: event[0],
                SCcompanyList: false,
              });
              this.props.projectSwitched(new Date());
            }}
            style={{ flexDirection: "row" }}
          >
            <Text
              style={[styles.subtext, { width: wp("85%") }]}
              numberOfLines={1}
            >
              {item.projectName}
            </Text>
            <Image
              resizeMode={"contain"}
              source={Images.arrow_right}
              style={{
                width: wp("3%"),
                height: hp("3%"),
              }}
            />
          </TouchableOpacity>
        </View>
        <View
          style={{ height: 0.5, backgroundColor: "#A8B2B9", marginTop: 5 }}
        ></View>
      </View>
    );
  };

  renderCompanyFlatListItem = ({ item, index }) => {
    return (
      <View>
        <View style={{ marginLeft: 20, marginRight: 20, marginBottom: 20 }}>
          <Text style={styles.nameText} numberOfLines={1}>
            {item.companyName}
          </Text>

          <FlatList
            data={item.projectList}
            renderItem={this.renderProductFlatListItem}
            // ItemSeparatorComponent={this.itemSeparator}
            //  keyExtractor={(item, index) => index.toString()}
            //  onEndReached={() => this.onEndReached()}
            onEndReachedThreshold={0}
            onMomentumScrollBegin={() => {
              this.onEndReachedCalledDuringMomentum = false;
            }}

            //  onRefresh={() => this._onReset()}
            //  refreshing={this.state.refreshing}
          />
        </View>
      </View>
    );
  };

  renderCompanyContainer = () => {
    return (
      <View style={modalStyles.container}>
        <View style={modalStyles.topContainer}>
          <TouchableOpacity
            onPress={() => this.setState({ SCcompanyList: false })}
            style={{ width: 40 }}
          >
            <Image source={Images.closeBlack} />
          </TouchableOpacity>
          <View style={modalStyles.titleContainer}>
            <Text style={modalStyles.title}>{Strings.menu.projects}</Text>
          </View>
          <View style={{ width: 40, height: 40 }} />
        </View>
        <View>
          <FlatList
            data={this.state.CompanyListData}
            renderItem={this.renderCompanyFlatListItem}
            // ItemSeparatorComponent={this.itemSeparator}
            // keyExtractor={(item, index) => index.toString()}
            //  onEndReached={() => this.onEndReached()}
            onEndReachedThreshold={0}
            onMomentumScrollBegin={() => {
              this.onEndReachedCalledDuringMomentum = false;
            }}
            //  onRefresh={() => this._onReset()}
            //  refreshing={this.state.refreshing}
          />
        </View>
        {/* <View style={[modalStyles.buttonContainer, { marginBottom: 0, marginTop: 80 }]}>
          <TouchableOpacity onPress={() => { this.addnewProject() }} style={{ width: wp('85%'), height: hp('5%'), flexDirection: 'row', backgroundColor: Colors.themeOpacity, justifyContent: 'center', alignItems: 'center', borderRadius: hp('2.5%') }}>
            <Image source={Images.newpro} />
            <Text style={{ color: Colors.themeColor, marginHorizontal: 10, fontSize: wp('4%'), fontFamily: Fonts.montserratMedium }}>{Strings.menu.addNew}</Text>
          </TouchableOpacity>
        </View> */}
      </View>
    );
  };

  componentWillUnmount() {
    apiCallCount = 0;
  }

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };
  
  addnewCompany=()=>{
    this.props.navigation.navigate("AddCompany", { from: "menu" });
  }
  onSelectCompany=(item,index)=>{
    this.props.selectedCompany(item)
    this.setState({selectedCompany:item,
    parentcompanyid:item.id,
    showCompany:false,
    },()=>{
      this.getCompanyProject();
    })
  }
  renderCompany = () => {
    if (this.state.companyData.length > 1) {
      return (
        <AnimatableView animation={"zoomInUp"} style={[styles.companyContainerSwitch,{height:hp('13%')}]}>
          <ScrollView>
            {this.state.companyData && 
              this.state.companyData.map((item, index) => {
                return (
                  <View key={index.toString()} style={{ flex: 1, margin: 5 }}>
                    <TouchableWithoutFeedback
                      onPress={() => {
                        this.onSelectCompany(item, index);
                        // this.onTap(item, index);
                      }}
                    >
                      <View
                        key={index.toString()}
                        style={{
                          justifyContent: "center",
                          flex: 1,
                          marginLeft: wp("5%"),
                        }}
                      >
                        <Text
                          numberOfLines={1}
                          style={[styles.textTitle, { color: Colors.black }]}
                        >
                          {item.companyName.trim()}
                        </Text>
                      </View>
                    </TouchableWithoutFeedback>
                  </View>
                );
              })}
          </ScrollView>
        </AnimatableView>
      );
    }
    // Returning null since the app crash happens due to data transfer to the projectDetails is delayed.
    else {
      return null;
    }
  };
  renderProjectContainer = () => {
    const { userRoleID } = this.state;
    if (this.state.projectDetails.length > 0) {
      return (
        <AnimatableView animation={"zoomInUp"} style={styles.projectContainer}>
          <ScrollView>
            {this.state.projectDetails &&
              this.state.projectDetails.map((item, index) => {
                return (
                  <View key={index.toString()} style={{ flex: 1, margin: 5 }}>
                    <TouchableWithoutFeedback
                      onPress={() => {
                        console.log("Selected Project Item:", item); // Log the clicked project details
                        console.log("Selected Project Index:", index); // Log the index of the clicked project
                        this.onSelectProject(item, index);
                        // this.onTap(item, index);
                      }}
                    >
                      <View
                        key={index.toString()}
                        style={{
                          justifyContent: "center",
                          flex: 1,
                          marginLeft: wp("5%"),
                        }}
                      >
                        <Text
                          numberOfLines={1}
                          style={[styles.textTitle, { color: Colors.black }]}
                        >
                          {item.projectName.trim()}
                        </Text>
                      </View>
                    </TouchableWithoutFeedback>
                  </View>
                );
              })}
          </ScrollView>
          {userRoleID == 2 && (
            <TouchableOpacity
              onPress={() => {
                console.log("Add New Project Button Pressed"); // Log when the Add New button is clicked
                this.setState({ showProjects: false }),
                  this.addnewProject();
              }}
              style={{
                width: wp("85%"),
                height: hp("7%"),
                flexDirection: "row",
                backgroundColor: Colors.themeOpacity,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Image source={Images.newpro} />
              <Text
                style={{
                  color: Colors.themeColor,
                  marginHorizontal: 10,
                  fontSize: wp("4%"),
                  fontFamily: Fonts.montserratMedium,
                }}
              >
                {Strings.menu.addNew}
              </Text>
            </TouchableOpacity>
          )}
        </AnimatableView>
      );
    } else {
      // Returning null since the app crash happens due to data transfer to the projectDetails is delayed.
      return null;
    }
  };
  

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>
        <StatusBar
          backgroundColor={Colors.themeColor}
          barStyle="light-content"
        />
        <View style={styles.parentContainer}>
          {/* <NavigationEvents
            onDidFocus={() => {
             this.getProjectList();
            }}
            onDidBlur={() => {
               alert('blur')
            }}
          /> */}
          {this.renderHeader()}

          <ScrollView style={styles.parentContainer}>
            {this.state.data.map((item, index) => {
              return (
                <View key={index.toString()} style={styles.themeContainer}>
                  <TouchableWithoutFeedback
                    onPress={() => {
                      this.onTap(item, index);
                    }}
                  >
                    <View style={styles.subContainer}>
                      <Text style={styles.textTitle}>{item.title}</Text>
                      <Image
                        resizeMode={"contain"}
                        source={item.image}
                        style={{
                          width: wp("6%"),
                          height: hp("3%"),
                          marginLeft: 10,
                        }}
                      />
                    </View>
                  </TouchableWithoutFeedback>
                </View>
              );
            })}
          </ScrollView>
          
          <View
            style={[
              styles.themeContainer,
              {
                width: wp("100%"),
                marginBottom: hp("30%"),
                borderBottomWidth: hp("0.3%"),
                borderTopWidth: hp("0.3%"),
                borderColor: "rgba(255, 255, 255, 0.1)",
              },
            ]}
          >
            <TouchableWithoutFeedback
              onPress={() => {
                this.setState({ showDelete: true });
              }}
            >
              <View style={[styles.subContainer, { marginRight: wp("7%") }]}>
                <Text style={styles.textTitle}>{Strings.menu.logout}</Text>
                <Image
                  resizeMode={"contain"}
                  source={Images.logout}
                  style={{
                    width: wp("6%"),
                    height: hp("3%"),
                    marginLeft: 10,
                  }}
                />
              </View>
            </TouchableWithoutFeedback>
          </View>
          <View style={styles.companyContainer}>
            <Text style={styles.projectTitle}>{Strings.menu.company}</Text>
            <View
              style={{
                flex: 1,
                flexDirection: "row",
                alignItems: "center",
                marginLeft: 10,
                marginRight: 10, 
              }}
            >
              <Text style={styles.projectName} numberOfLines={2}>
                {this.state.selectedCompany.companyName
                  ? this.state.selectedCompany.companyName.trim()
                  : ""}
              </Text>
              {this.state.companyData.length > 1 &&
              <TouchableOpacity
                onPress={() => {
                  //    alert(this.state.roleId);
                  // if (this.state.roleId === 4) {
                  //   this.setState({ SCcompanyList: !this.state.SCcompanyList });
                  // } else {
                    this.setState({ showCompany: !this.state.showCompany });
                  // }
                  //  this.props.navigation.navigate('ComanyProjectList')
                }}
              >
                <Image source={Images.upArrow} />
              </TouchableOpacity>}
            </View>
          </View>
          <View style={styles.bottomContainer}>
            <Text style={styles.projectTitle}>{Strings.menu.project}</Text>
            <View
              style={{
                flex: 1,
                flexDirection: "row",
                alignItems: "center",
                marginLeft: 10,
                marginRight: 10,
              }}
            >
              <Text style={styles.projectName} numberOfLines={2}>
                {this.state.projectSelected.projectName
                  ? this.state.projectSelected.projectName.trim()
                  : ""}
              </Text>
              {this.state.projectDetails.length > 0 &&
              <TouchableOpacity
                onPress={() => {
                  //    alert(this.state.roleId);
                  // if (this.state.roleId === 4) {
                  //   this.setState({ SCcompanyList: !this.state.SCcompanyList });
                  // } else {
                    this.setState({ showProjects: !this.state.showProjects });
                  // }
                  //  this.props.navigation.navigate('ComanyProjectList')
                }}
              >
                <Image source={Images.upArrow} />
              </TouchableOpacity>}
            </View>
          </View>
        </View>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        <Modal
          isVisible={this.state.SCcompanyList}
          animationIn={'fadeIn'}
          animationOut={'fadeOut'}
          transparent={true}
          backdropColor={Colors.sign_in_btn_bg}
          onBackdropPress={() => this.setState({ SCcompanyList: false })}
          style={{
            paddingTop: 45,
            paddingBottom: 30,
            margin: 0,
          }}
        >
          {this.renderCompanyContainer()}
        </Modal>

        <Modal
          animationIn={'fadeIn'}
          animationOut={'fadeOut'}
          onBackdropPress={() => this.setState({ showProjects: false })}
          isVisible={this.state.showProjects}
          transparent={true}
          backdropColor={Colors.sign_in_btn_bg}
          style={{
            paddingTop: 10,
            paddingBottom: 90,
            margin: 0,
             backgroundColor: 'transparent',
          }}
        >
          {this.renderProjectContainer()}
        </Modal>


        <Modal
          animationIn={'fadeIn'}
          animationOut={'fadeOut'}
          onBackdropPress={() => this.setState({ showCompany: false })}
          isVisible={this.state.showCompany}
          transparent={true}
          backdropColor={Colors.sign_in_btn_bg}
          style={{
            paddingTop: 10,
            paddingBottom: 90,
            margin: 0,
          }}
        >
          {this.renderCompany()}
        </Modal>

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

        {this.state.showDelete && (
          <DeletePop
            title={Strings.popup.success}
            desc={Strings.popup.logout}
            acceptTap={() => {
              this.setState({ showDelete: false });
              this.onTapLogout();
              // this.deleteEqui(this.state.selectedEquip, this.state.selectedIndex)
            }}
            container={{ bottom: 0 }}
            declineTap={() => {
              this.setState({ showDelete: false });
            }}
          />
        )}
      </SafeAreaView>
      }
      </>
    );
  }
}

const mapStateToProps = (state) => {
  const {
    userid,
    projectDetails,
    projectlist,
    checkCameBack,
    updatedata,
    userDetails,
    afterCreateProject,
    company_List,
    selected_Company,
  } = state.LoginReducer;

  return {
    userid,
    projectDetails,
    projectlist,
    checkCameBack,
    updatedata,
    userDetails,
    afterCreateProject,
    company_List,
    selected_Company,
  };
};

export default compose(
  connect(mapStateToProps, {
    setPage,
    selectedProjectDetails,
    projectList,
    getUserDetails,
    logout,
    cameBack,
    storeProjectRole,
    updateData,
    goToVoid,
    projectSwitched,
    afterProjectCreated,
    drResponsiblePerson,
    selectedCompany,
  }),
  withBackHandler
)(menu);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.themeColor,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.themeColor,
  },
  header: {
    height: hp("10%"),
    alignItems: "center",
    alignSelf: "center",
    flexDirection: "row",
  },
  usrname: {
    color: Colors.white,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    marginLeft: wp("3%"),
  },
  closecontainer: {
    flex: 1,
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  themeContainer: {
    width: wp("90%"),
    height: wp("9%"),
    alignSelf: "center",
    margin: 5,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  subContainer: {
    height: wp("12%"),
    alignSelf: "center",
    margin: 5,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  textTitle: {
    color: Colors.white,
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  projectTitle: {
    color: Colors.placeholder,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratSemiBold,
    margin: 10,
    marginBottom: 5,
  },
  projectName: {
    width: wp("70%"),
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  bottomContainer: {
    width: wp("85%"),
    height: hp("12%"),
    position: "absolute",
    backgroundColor: Colors.white,
    alignSelf: "center",
    bottom: hp("3%"),
    borderRadius: wp("3%"),
  },
  companyContainer: {
    width: wp("85%"),
    height: hp("12%"),
    position: "absolute",
    backgroundColor: Colors.white,
    alignSelf: "center",
    bottom: hp("16%"),
    borderRadius: wp("3%"),
  },
  companyContainerSwitch: {
    position: "absolute",
    width: wp("85%"),
    height: hp("20%"),
    bottom: Platform.OS == 'ios' ? hp("31.5%"): hp("28.5%"),
    alignSelf: "center",
    backgroundColor: Colors.white,
    borderRadius: wp("2%"),
  },
  projectContainer: {
    position: "absolute",
    width: wp("85%"),
    height: hp("25%"),
    bottom: Platform.OS == 'ios' ? hp("19%") : hp("15.5%"),
    alignSelf: "center",
    backgroundColor: Colors.white,
    borderRadius: wp("2%"),
  },
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  nameText: {
    color: "#A8B2B9",
    fontSize: wp("5%"),
    fontFamily: Fonts.montserratMedium,
    marginTop: 10,
    marginBottom: 10,
    width: "75%",
  },
  subtext: {
    width: "80%",
    color: "#292529",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  profileContainer:{
    backgroundColor: 'grey',
    alignItems: 'center',
    justifyContent: 'center',
    width: hp("6%"),
    height: hp("6%"),
    borderRadius: hp("3%"),
  },
  profileText:{
    color: 'white',
     fontSize: 20
  }
});

const modalStyles = StyleSheet.create({
  container: {
    flex: 1,
    width: wp("100%"),
    height: hp("95%"),
    backgroundColor: "red",
  },
  topContainer: {
    flexDirection: "row",
    margin: 20,
    height: 40,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 20,
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  applyButton: {
    backgroundColor: Colors.themeOpacity,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  applyText: {
    color: Colors.themeColor,
    fontSize: wp("4.5%"),
    fontFamily: Fonts.montserratBold,
  },
  buttonContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-end",
    marginBottom: 50,
    justifyContent: "space-around",
  },
});

const data1 = [
  {
    id: "1",
    title: "No. of deliveries",
    amt: "company 1",
  },
  {
    id: "2",
    title: "No. of members",
  },
  {
    id: "3",
    title: "No. of companies",
  },
];