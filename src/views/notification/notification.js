import React, { Component } from "react";
import {
  View,
  Text,
  Image,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  StyleSheet,
  Platform,
  
} from "react-native";

import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import { Colors, Images, Strings, Fonts } from "../../common";

import {
  cameBack,
  storeLastid,
  showDeliverdetailsid,
  onTapDetail,
  showBadgecount,
  tappedNotificationDetails,
  concreteDetailsID,onPressConcreteDetail,
  setSelectedCalendarDate,
} from "../../actions/postAction";
import {
  GET_NOTIFICATION_LIST,
  DELETE_NOTIFICATION,
  READ_NOTIFICATION,
  GET_UNREAD_COUNT,
  GET_MARK_ALL_READ,
} from "../../api/Constants";
import {
  getNotificationList,
  deleteNotification,
  readNotification,
  getUnReadCount,
  getMarkAllRead,
} from "../../api/Api";
import moment from "moment";
import Modal from "react-native-modal";
import with<PERSON><PERSON><PERSON>and<PERSON> from "../../components/backhandler";
import { compose } from "redux";
import { trackScreen } from "../../Google Analytics/GoogleAnalytics";

import {
  AppView,
  AppLoader,
  TextField,
  DeletePop,
  Toastpopup,
  NotificationCard,
} from "../../components";

import DateTimePicker from "@react-native-community/datetimepicker";
import Dropdown from "../../components/dropdown/dropdown";

import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

let dateSele = new Date();
let more=[{name:Strings.notification.markAllRead,id:1,}]
class notification extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showLoader: false,
      showToaster: false,
      showAlert: false,
      toastMessage: "",
      toastType: "error",
      refreshing: false,
      lastId: 0,
      notificationList: [],
      dateFilter: null,
      descriptionFilter: "",
      projectNameFilter: null,
      statusFilter: null,
      showFilter: false,
      filter: false,
      projectFilterlist: [],
      selectedProjectname: null,
      delDate: "",
      selectedProjectId: 0,
      showDateTime: false,
      searchbarShow: false,
      searchText: "",
      showDelete: false,
      selectedNotificationitem: {},
      selectedIndex: null,
      projectModalVisible:false,
      isMore:false,
      isNetworkCheck: false
    };
    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      let projectlist = [];
      projectlist = this.props.projectlist.map((e) => {
        return {
          label: e.projectName,
          value: e.projectName,
          id: e.id,
          name: e.projectName,
        };
      });
     this.subscrib = this.props.navigation.addListener("focus", () => {
       // Reset selected calendar date to current date when navigating to notifications
       console.log('🔔 Notifications: Focus listener - resetting selectedCalendarDate to current date');
       this.props.setSelectedCalendarDate(moment().format('YYYY-MM-DD'));
       this.renderInital();
     });
       
      this.setState({ projectFilterlist: projectlist,  });
      this.subs = [];
    }
    
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      let projectlist = [];
    projectlist = this.props.projectlist.map((e) => {
      return {
        label: e.projectName,
        value: e.projectName,
        id: e.id,
        name: e.projectName,
      };
    });
   this.subscrib = this.props.navigation.addListener("focus", () => {
     // Reset selected calendar date to current date when navigating to notifications
     console.log('🔔 Notifications: Focus listener - resetting selectedCalendarDate to current date');
     this.props.setSelectedCalendarDate(moment().format('YYYY-MM-DD'));
     this.renderInital();
   });
     
    this.setState({ projectFilterlist: projectlist,  });
    this.subs = [];
    }
    })
  }

  componentWillUnmount() {
    // BackHandler cleanup is now handled by withBackHandler HOC
    // Remove the event listener
    this.subscrib?.();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.checkCameBack == true) {
      this.renderInital();
      // this.props.cameBack(false);
    }
    if (this.props.projectSwitched != nextProps.projectSwitched) {
      this.renderInital();
    }
  }
  

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  renderInital = () => {
    this.setState({ showLoader: true });
    this.page_number = 1;
    this.getUnReadCounts();
    this.getnotificationlist();

  };
  getUnReadCounts=()=>{
    let url=`${GET_UNREAD_COUNT}ProjectId=${this?.props?.projectDetails?.id}&ParentCompanyId=${this?.props?.projectDetails?.ParentCompany?.id}`;
    getUnReadCount(
      url,
      {},
      ()=>{},
      (response)=>{
        if(response.status){
          if(response.status==200){
            this.props.showBadgecount(response.data.data);
          }else if (response.data.message) {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
                showLoader: false,
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
          else{
            this.setState(
              {
                showToaster: true,
                toastMessage: 'Request Timeout',
                toastType: "error",
                showLoader: false,
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
        }
        else{
          this.setState(
            {
              showToaster: true,
              toastMessage: 'Bad Request',
              toastType: "error",
              showLoader: false,
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
      );
  }

  getnotificationlist = () => {
    let param = {};
    if (this.state.filter === true) {
      param = {
        dateFilter: this.state.delDate,
        descriptionFilter: this.state.descriptionFilter,
        projectNameFilter: this.state.selectedProjectId,
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
        ProjectId: this?.props?.projectDetails?.id,
      };
    } else {
      param = {
        search: this.state.searchText,
        ParentCompanyId: this?.props?.projectDetails?.ParentCompany?.id,
        ProjectId: this?.props?.projectDetails?.id,
      };
    }

    getNotificationList(
      `${GET_NOTIFICATION_LIST}20/${this.page_number}`,
      param,
      () => {},
      (response) => {
        this.setState({
          showIndicator: false,
          showLoader: false,
          showNoData: false,
          clearSearch: this.state.searchText ? true : false,
        });

        if (response.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastMessage: Strings.errors.checkInternet,
              type: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        } else if (response.status) {
          if (response.data.message == "Notification listed Successfully.") {
            this.setState({ showLoader: false });
            let data = this.state.notificationList;

            //   this.props.storeLastid(response.data.lastId.DeliveryId);

            if (this.page_number == 1) {
              if (response.data.data.count == 0) {
                this.setState({ showNoData: true, notificationList: [] });
              } else {
                this.setState({
                  notificationList: response.data.data.rows,
                  totalCount: response.data.data.count,
                  //      lastId: response.data.lastId.DeliveryId,
                });
              }
            } else {
              let data1 = response.data.data.rows;
              this.setState({
                notificationList: data.concat(data1),
                totalCount: response.data.data.count,
                //      lastId: response.data.lastId.DeliveryId,
              });
            }
          } else if (response.data.message.message) {
            this.showError("error", response.data.message.message);
          } else {
            this.showError("error", response.data.message);
          }
        } else {
          this.showError("error", response.toString());
        }
      }
    );
  };

  deletenotification = (item, index) => {
    this.setState({ showLoader: true });
    let url = `${DELETE_NOTIFICATION}?id=${item.id}&ParentCompanyId=${this?.props?.projectDetails?.ParentCompany?.id}`;
    deleteNotification(
      url,
      {},
      () => {},
      (response) => {
        if (response.status) {
          if (response.data.message.message) {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message.message,
                toastType: "error",
                showLoader: false,
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          } else if (
            response.data.message == "Notification Deleted Successfully."
          ) {
            this.page_number = 1;
            //  this.state.notificationList.splice(index, 1)
            this.getnotificationlist();
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
                showLoader: false,
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
              showLoader: false,
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
  };

  onEndReached = () => {
    if (this.state.notificationList.length < this.state.totalCount) {
      this.page_number = this.page_number + 1;
      this.getnotificationlist();
      this.onEndReachedCalledDuringMomentum = true;
    }
  };

  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        notificationList: [],
        totalCount: 0,
        showLoader: true,
        //load: true,
      },
      () => {
        this.getnotificationlist();
      }
    );
  };

  renderSearchBar = () => {
    if (this.state.searchbarShow == true) {
      return this.searchBar();
    } else {
      return this.renderHeader();
    }
  };

  updateMasterState = (key, value) => {
    this.setState(
      {
        searchText: value,
      },
      () => {
        this.searchList();
      }
    );
  };

  clearSearch = () => {
    this.setState({
      clearSearch: false,
      searchText: "",
      showIndicator: true,
    });
    this.searchList();
  };

  searchList = () => {
    this.setState({ showIndicator: true, clearSearch: false }, () => {
      this.renderInital();
    });
  };

  searchBar = () => {
    return (
      <View style={searchStyles.searchHeader}>
        <View style={searchStyles.mainContainer}>
          <TouchableOpacity
            style={searchStyles.closeBtn}
            onPress={() => {
              this.setState(
                {
                  searchbarShow: false,
                  searchText: "",
                },
                this.clearSearch()
              );
            }}
          >
            <Image
              resizeMode={"contain"}
              source={Images.closeBlack}
              style={searchStyles.closeImg}
            />
          </TouchableOpacity>

          <View
            style={{ flex: 1, justifyContent: "center", alignItems: "center" }}
          >
            <Text style={searchStyles.titleText}>{Strings.search.title}</Text>
          </View>

          <TouchableOpacity
            style={searchStyles.closeBtn}
            onPress={() => {
              if (this.state.showright) {
                this.setState({ showAllDelete: true });
              }
            }}
          >
            {this.state.showright == true && (
              <Image
                resizeMode={"contain"}
                source={Images.delete1}
                style={searchStyles.closeImg}
              />
            )}
          </TouchableOpacity>
        </View>
        <View style={{ flexDirection: "row", justifyContent: "center" }}>
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.SearchHere}
            title={Strings.placeholders.SearchHere}
            value={this.state.searchText}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={styles.textInputStyles}
            textTitleStyles={styles.textTitleStyles}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />
          <View style={styles.clearSearch}>
            {this.state.showIndicator == true && (
              <ActivityIndicator style={{ marginBottom: 5 }} />
            )}
            {this.state.clearSearch == true && (
              <TouchableOpacity onPress={() => this.clearSearch()}>
                <Image source={Images.closeBlack} style={{ marginBottom: 5 }} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    );
  };

  renderHeader = () => {
    let count = 0;

    if (this.state.descriptionFilter !== "") {
      count = count + 1;
    }

    if (this.state.selectedProjectId !== 0) {
      count = count + 1;
    }

    if (this.state.delDate !== "") {
      count = count + 1;
    }

    return (
      <View style={styles.headerContainer}>
        <Text style={styles.title}>{Strings.notification.notification}</Text>
        <View style={styles.headerRowContainer}>
          <TouchableOpacity
            style={[styles.image, { marginTop: wp("1%") }]}
            onPress={() => this.setState({ showFilter: true })}
          >
            <Image source={Images.filter} />
            {this.state.filter == true && (
              <View
                style={{
                  position: "absolute",
                  marginTop: -10,
                  right: -10,
                  backgroundColor: Colors.themeColor,
                  width: 16,
                  justifyContent: "center",
                  alignItems: "center",
                  height: 16,
                  borderRadius: 8,
                }}
              >
                <Text style={{ color: "white" }}>{count}</Text>
              </View>
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.imageSearch}
            onPress={() => {
              this.setState({
                searchbarShow: true,
                drList: [],
              });
            }}
          >
          <Image source={Images.Search1} style={{height:21,width:21,}}/>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.imageMore}
            onPress={() => {
              this.setState({
              isMore:true
              });
            }}
          >
          <Image source={Images.more} style={styles.moreImage}/>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  readnotification = (item) => {
    this.setState({ showLoader: true });
    let url = `${READ_NOTIFICATION}id=${item.DeliveryNotification[0].id}&ParentCompanyId=${this?.props?.projectDetails?.ParentCompany?.id}`;
    readNotification(
      url,
      {},
      () => {},
      async(response) => {
        if (response.data) {
          if (response.data.message == "Notification Read Successfully.") {
            this.setState({ showLoader: false });
            if(item.requestType==='concreteRequest'){
              trackScreen('Concrete Details')
            }else if(item.requestType==='craneRequest'){
              trackScreen('Crane Details')
            }else{
              trackScreen('Delivery Details')
            }
            if(item.requestType==='concreteRequest'){
              this.props.concreteDetailsID(item.ConcreteRequest.ConcreteRequestId)
              this.props.onPressConcreteDetail("ConcreteDetails")
            }else{
            let decidingData={
            id:item.isDeliveryRequest?item.DeliveryRequestId:item.CraneRequest.CraneRequestId,
            isDelivery:item.isDeliveryRequest,
            }
            await this.props.showDeliverdetailsid(decidingData);
            await this.props.tappedNotificationDetails(item);
            await this.props.onTapDetail("drdetailspage");
          }
          this.renderInital();
          } else {
            this.setState({
              showToaster: true,
              toastMessage: response.data.message,
              toastType: "error",
              showLoader: false,
            });
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
              showLoader: false,
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
  };

  renderFlatListItem = ({ item, index }) => {
    return (
      <NotificationCard
        item={item}
        onPress={() => {
          this.readnotification(item);
          // this.props.showDeliverdetailsid(item.DeliveryRequestId);
          // this.props.tappedNotificationDetails(item)
          // this.props.onTapDetail("drdetailspage")
          //this.readnotification(item);
        }}
        onDelete={() => {
          this.setState({
            showDelete: true,
            selectedNotificationitem: item,
            selectedIndex: index,
          });
        }}
      />
    );
  };

  applyFilter = () => {
    this.setState(
      {
        filter: true,
        showFilter: false,
      },
      () => {
        this.renderInital();
      }
    );
  };
  //TODO LATER
  // _onReset = () => {
  //   this.setState(
  //     {
  //       //  totalCount: 0,
  //       showLoader: true,
  //       showIndicator: false,

  //       //load: true,
  //     },
  //     () => {
  //       this.renderInital();
  //     }
  //   );
  // };

  onPressProjectType=(item)=>{
    this.setState({
      selectedProjectname: item.value,
      selectedProjectId: item.id,
      projectModalVisible:false
    })
  }

  renderFilter = () => {
    return (
      <View style={modalStyles.container}>
        <View style={modalStyles.topContainer}>
          <TouchableOpacity
            onPress={() => this.setState({ showFilter: false })}
            style={{ width: 40 }}
          >
            <Image source={Images.closeBlack} />
          </TouchableOpacity>
          <View style={modalStyles.titleContainer}>
            <Text style={modalStyles.title}>{Strings.filter.title}</Text>
          </View>
          <View style={{ width: 40, height: 40 }} />
        </View>

        <KeyboardAwareScrollView>
        {/* <TextField
                  attrName={Strings.placeholders.ProjectName}
                  title={Strings.placeholders.ProjectName}
                  value={this.state.selectedProjectname}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  mandatory={true}
                  textInputStyles={{
                    // here you can add additional TextInput styles
                    color: Colors.black,
                    fontSize: 14,
                  }}
                  showButton={true}
                  onPress={() => {
                    this.setState({ projectModalVisible:true});
                  }}
                  imageSource={Images.downArr}
              
              /> */}

{/*     <DropDownPicker
            items={this.state.projectFilterlist}
            defaultValue={this.state.selectedProjectname}
            placeholder={Strings.placeholders.ProjectName}
            placeholderStyle={{
              color: Colors.placeholder,
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            containerStyle={{
              height: hp("6%"),
              marginTop: 10,
              fontSize: 14,
            }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
              marginTop: 10,
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedProjectname: item.value,
                selectedProjectId: item.id,
              })
            }
            zIndex={5000}
          /> */}
          
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.description}
            title={Strings.placeholders.description}
            value={this.state.descriptionFilter}
            updateMasterState={(key, value) => {
              this.setState({
                descriptionFilter: value,
              });
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
         
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0,}}
          />
          <TextField
            attrName={Strings.placeholders.notificationDate}
            title={Strings.placeholders.notificationDate}
            value={this.state.delDate}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput drStyles
              color: Colors.black,
              fontFamily: Fonts.montserratMedium,
              fontSize: 14,
              marginLeft: wp("4%"),
            }}
            textTitleStyles={{
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
              marginLeft: wp("4%"),
            }}
            showButton={true}
            onPress={() => {
              this.setState({
                showDateTime: true,
              });
            }}
            container={{
              marginTop: 5,
            }}
            imageSource={Images.calGray}
          />

          <View
            style={[
              modalStyles.buttonContainer,
              { marginBottom: 0, marginTop: 50 },
            ]}
          >
            <TouchableOpacity
              style={
                this.state.filter == true
                  ? [
                      modalStyles.cancelButton,
                      { backgroundColor: Colors.themeOpacity },
                    ]
                  : modalStyles.cancelButton
              }
              onPress={() =>
                this.setState(
                  {
                    showFilter: false,
                    searchText: "",
                    delDate: "",
                    descriptionFilter: "",
                    selectedProjectname: null,
                    selectedProjectId: 0,
                  },
                  () => {
                    if (this.state.filter == true) {
                      dateSele=new Date()
                      this.setState({ filter: false, delDate: "", }, () => {
                        this.renderInital();
                      });
                    }
                  }
                )
              }
            >
              <Text
                style={[
                  modalStyles.cancelText,
                  {
                    color:
                      this.state.filter == true
                        ? Colors.themeColor
                        : Colors.buttonBackground,
                  },
                ]}
              >
                {this.state.filter == true
                  ? Strings.addMember.reset
                  : Strings.addMember.cancel}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={modalStyles.applyButton}
              onPress={() => this.applyFilter()}
            >
              <Text style={modalStyles.applyText}>{Strings.filter.apply}</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAwareScrollView>

        <Dropdown
                  data={this.state.projectFilterlist}
                  title={Strings.placeholders.ProjectName}
                  value={this.state.selectedProjectname}
                  closeBtn={() => this.setState({ projectModalVisible:false})}
                  onPress={(item) => this.onPressProjectType(item)}
                  visible={this.state.projectModalVisible}
                  onbackPress={() => this.setState({ projectModalVisible:false })}
                  container={styles.equipmentContainer}
                  customMainContainer={styles.renderEquipStyle}
                  equipTextContainer={styles.equipTextStyle}
               />

        {Platform.OS == Strings.platforms.ios && (
        <Modal
          isVisible={this.state.showDateTime}
          onBackdropPress={() => {
            this.setState({ showDateTime: false,});
          }}
          animationInTiming={500}
          style={styles.modalStyle}
        >
          <DateTimePicker
            // testID="date"
            // timeZoneOffsetInMinutes={0}
            // minuteInterval={interval}
            //  minimumDate={new Date()}
            value={dateSele}
            style={{
              backgroundColor: "#fff",
              width:'100%',
            }}
           display={"inline"}
           themeVariant='light'
           accentColor={Colors.themeColor}
            // mode={mode}
            onChange={(time, date) => {
              this.onchangeDate(time, date);
            }}
          />
          <View>
              <TouchableOpacity
                activeOpacity={0.5}
                style={styles.datePickerOkContainer}
                onPress={() => {
                  this.setState({ showDateTime: false, });
                }}
              >
                <Text style={styles.datePickerOkLabel}>Done</Text>
              </TouchableOpacity>
            </View>
        </Modal>)}
        {Platform.OS == "android" && this.state.showDateTime && (
           <DateTimePicker
           value={new Date()}
           style={{
             backgroundColor: "#fff",
             width:'100%',
           }}
           display="default"
           onChange={(time, date) => {
             this.onchangeDate(time, date);
           }}

         />
        )}

      </View>
    );
  };

  onchangeDate = (tevent, date1) => {
    if (tevent.type == Strings.datePicker.set || tevent.type == Strings.datePicker.dismissed) {
      this.setState({
        showDateTime: false,
      });
    }
    if (Platform.OS == Strings.platforms.ios || tevent.type == Strings.datePicker.set) {
    const fullYear = date1.getFullYear();
    const fullMonth = date1.getMonth();
    const date = date1.getDate();
    this.setState({
     delDate: moment(date1).format("MM/DD/YYYY")
    });
    dateSele = date1;

}
  };

  renderNoNotification = () => {
    return (
      this.state.showNoData == true && (
        <Text style={styles.noNotification}>No Notifications Found</Text>
      )
    );
  };

  deletePopupAcceptTap = () => {
    this.setState({ showDelete: false });
    this.deletenotification(
      this.state.selectedNotificationitem,
      this.state.selectedIndex
    );
  };

  deletePopupDeclineTap = () => {
    this.setState({
      showDelete: false,
      selectedNotificationitem: {},
      selectedIndex: null,
    });
  };
  showToaster = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
        disableSubmit: false,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showToaster: false,
          });
        }, 2000);
      }
    );
  };
  onPressMarkAllRead=()=>{
    this.setState({isMore:false})
    let url=`${GET_MARK_ALL_READ}${this?.props?.projectDetails?.id}`;
    getMarkAllRead(url,{},()=>null,(response)=>{
      if(response.status){
        if(response.status==200){
          this.showToaster(Strings.toast.success,response.data.message)
          this.getnotificationlist()
        }
        else if(response.status==400){
          this.showToaster(Strings.toast.error,response.data.message)
        }
        else if (response.status==401){
          this.showToaster(Strings.toast.error,response.data.message)
        }else{
          this.showToaster(Strings.toast.error,Strings.errors.something)
        }
      }
    })
  }

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <AppView>
        <View
          style={[
            styles.parentContainer,
            {
              backgroundColor:
                this.state.searchbarShow == true ? Colors.white : "FCFBFC",
            },
          ]}
        >
          {this.renderSearchBar()}

          <FlatList
            data={this.state.notificationList}
            renderItem={this.renderFlatListItem}
            // ItemSeparatorComponent={this.itemSeparator}
            keyExtractor={(item, index) => index.toString()}
            onEndReached={() => this.onEndReached()}
            onEndReachedThreshold={0}
            onMomentumScrollBegin={() => {
              this.onEndReachedCalledDuringMomentum = false;
            }}
            onRefresh={() => this._onReset()}
            refreshing={this.state.refreshing}
            extraData={this.state}
          />
          {this.renderNoNotification()}
        </View>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        <Dropdown
          data={more}
          title={Strings.notification.action}
          value={more}
          closeBtn={() => this.setState({ isMore: false })}
          onPress={ this.onPressMarkAllRead}
          visible={this.state.isMore}
          onbackPress={() => this.setState({ isMore: false })}
          customMainContainer={styles.renderEquipCustomStyle}
        />
        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.forgotSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}

        {this.state.showDelete && (
          <DeletePop
            title={Strings.popup.success}
            desc={Strings.popup.delete}
            acceptTap={this.deletePopupAcceptTap}
            declineTap={this.deletePopupDeclineTap}
          />
        )}

        <Modal
          isVisible={this.state.showFilter}
          style={{
            paddingTop: 45,
            paddingBottom: 30,
            margin: 0,
            backgroundColor: Colors.white,
          }}
        >
          {this.renderFilter()}
        </Modal>
      </AppView>
    }
    </>
    );
  }
}

const mapStateToProps = (state) => {
  const { projectDetails, projectlist, projectSwitched } = state.LoginReducer;

  return {
    projectDetails,
    projectlist,
    projectSwitched
  };
};

export default compose(
  connect(mapStateToProps, {
  storeLastid,
  cameBack,
  showDeliverdetailsid,
  onTapDetail,
  showBadgecount,
  tappedNotificationDetails,
  concreteDetailsID,
  onPressConcreteDetail,
  setSelectedCalendarDate,
}),
withBackHandler
)(notification);

const styles = StyleSheet.create({
  parentContainer: {
    flex: 1,
    backgroundColor: "#FCFBFC",
  },
  headerContainer: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderBottomWidth: 0.3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,

    elevation: 6,
  },
  void: {
    alignSelf: "flex-end",
    color: "#FF3939",
    margin: 15,
    fontSize: wp("4%"),
    textDecorationLine: "underline",
  },
  title: {
    color: Colors.black,
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratBold,
    marginBottom: hp("2%"),
    marginLeft: wp("4%"),
  },
  headerRowContainer: {
    flex: 1,
    height: hp("15%"),
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  image: {
    marginRight: wp("6%"),
    marginBottom: hp("2%"),
  },
  imageSearch:{
    marginRight: wp("2%"),
    marginBottom: hp("2%"),
  },
  imageMore:{
     marginRight: wp("2%"),
     padding:hp("2%"),
  },
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  nameContainer: {
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  detailContainer: {
    width: wp("85%"),
    marginLeft: 20,
    justifyContent: "center",
  },
  imagePlaceholder: {
    width: wp("14%"),
    height: wp("14%"),
    borderRadius: wp("7%"),
    marginLeft: wp("6%"),
  },
  nameText: {
    color: Colors.themeColor,
    fontSize: wp("5%"),
    fontFamily: Fonts.montserratSemiBold,
    marginTop: 10,
    marginLeft: 10,
    width: "75%",
  },
  companyText: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  dotMenu: {
    marginTop: hp("2%"),
  },
  emailContainer: {
    flex: 1,
    marginLeft: 10,
    marginRight: 10,
  },
  emailTitle: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    margin: 5,
  },
  emailText: {
    color: "#1E1E1E",
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginTop: 0,
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
    marginTop: 5,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    marginRight: wp("20%"),
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    //outlineProvider: 'bounds'
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: wp("20%"),
  },
  subContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 5,
  },
  subtext: {
    width: "45%",
    color: "#A8B2B9",
    fontSize: wp("4%"),
    marginLeft: 10,
    fontFamily: Fonts.montserratMedium,
  },
  desctext: {
    width: wp("92%"),
    color: "#5b5b5b",
    fontSize: wp("4%"),
    marginLeft: 10,
    fontFamily: Fonts.montserratMedium,
  },
  datetext: {
    color: "#5b5b5b",
    fontSize: wp("4%"),
    marginLeft: 10,
    fontFamily: Fonts.montserratMedium,
  },
  noNotification: {
    alignSelf: "center",
    position: "absolute",
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("45%"),
  },
  textInputStyles: {
    color: Colors.black,
    fontSize: 14,
    width: "75%",
    marginLeft: wp("10%"),
    fontFamily: Fonts.montserratMedium,
    paddingTop: 10,
  },
  textTitleStyles: {
    marginLeft: wp("10%"),
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratMedium,
  },
  clearSearch: {
    position: "absolute",
    right: wp("5%"),
    width: wp("10%"),
    height: hp("5%"),
    marginTop: hp("3%"),
    justifyContent: "flex-end",
    alignItems: "center",
  },
  datePickerOkLabel: {
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 6,
    paddingBottom: 6,
    fontFamily: Fonts.montserratBold,
  },
  datePickerOkContainer: {
    width: "100%",
    alignItems: "center",
    backgroundColor: Colors.white,
    paddingBottom: hp("2%"),
  },
  modalStyle:{
    paddingTop: 45,
    margin: 0,
    justifyContent: "flex-end",
  },
  equipmentContainer:{
    height: hp("4%"),
    paddingBottom:5
  },
  renderEquipStyle:{
   marginBottom:10,
  },
  renderEquipCustomStyle:{
    marginBottom:hp("-9%"),
  },
  equipTextStyle:{
    width:'100%',
    fontSize:16
  },
  moreImage:{
    height:20,
    width:4.5
  }
});

const modalStyles = StyleSheet.create({
  container: {
    flex: 1,
    width: wp("100%"),
    height: hp("95%"),
  },
  topContainer: {
    flexDirection: "row",
    margin: 20,
    height: 40,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 20,
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  buttonContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "flex-end",
    marginBottom: 50,
    justifyContent: "space-around",
  },
  cancelButton: {
    backgroundColor: `rgba(117,117,117,0.2)`,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonBackground,
    fontSize: 14,
    fontFamily: Fonts.montserratBold,
  },
  applyButton: {
    backgroundColor: Colors.themeOpacity,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  applyText: {
    color: Colors.themeColor,
    fontSize: 14,
    fontFamily: Fonts.montserratBold,
  },
});

const searchStyles = StyleSheet.create({
  searchHeader: {
    marginTop: hp("2%"),
    height: hp("18%"),
    width: wp("95%"),
    alignSelf: "center",
  },
  mainContainer: {
    width: "100%",
    height: hp("6%"),
    flexDirection: "row",
    alignItems: "center",
  },
  closeBtn: {
    width: wp("15%"),
    height: hp("8%"),
    marginLeft: wp("2%"),
    justifyContent: "center",
    alignItems: "center",
  },
  closeImg: {
    width: wp("5%"),
    height: hp("5%"),
  },
  titleText: {
    color: Colors.black,
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratSemiBold,
  },
});
