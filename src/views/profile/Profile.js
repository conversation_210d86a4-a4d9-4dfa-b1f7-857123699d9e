import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  TouchableWithoutFeedback,
  StatusBar,
  Platform,
  Linking,
} from "react-native";

import {
  changeTab,
  showSideMenu,
  cameBack,
  editData,
  updateData,
  getUserDetails,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import {
  AppLoader,
  AppView,
  Toastpopup,
  Alert,
  Dropdown,
  DeletePop,
  MobilenumberInput,
} from "../../components";

import { TextField } from "../../components/textinput/Textinput";
import { TextField as TextInput } from "../../components/textinput/addMemberTextinput";

import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import {PERMISSIONS} from 'react-native-permissions';

import {
  isEmpty,
  isName,
  isValidPassword,
  getuniqId,
  mobileCountryCodes,
  Images,
  Strings,
  Fonts,
  Colors,
} from "../../common";
import { isWebsite } from "../../common/Validator";

import { CommonActions } from '@react-navigation/native';
import {
  CHANGE_PASSWORD,
  GET_PLAN_PROJECT,
  CANCEL_SUBSCRIPTION,
  GET_OVERVIEW_DETAIL,
  UPDATE_PROFILE,
  UPDATE_IMAGE,
} from "../../api/Constants";
import {
  changePassword,
  getPlansAndProject,
  cancelProject,
  getOverViewDetail,
  updateProfile,
  updateImage,
} from "../../api/Api";


import Axios from "axios";
import AsyncStorage from '@react-native-async-storage/async-storage';

import { TabView, SceneMap, TabBar } from 'react-native-tab-view';
import { Dimensions } from 'react-native';
import moment from "moment";

import ImagePicker from "react-native-image-crop-picker";

import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import { checkAndRequestPermission } from "../../utils/PermissionUtils";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";

let DROPDOWNOPTIONS = [
  { id: "upgrade", name: "upgrade" },
  { id: "Cancel", name: "Cancel" },
];
let UPGRADE = [{ id: "upgrade", name: "upgrade" }];

class Profile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
      showCurren: false,
      showNew: false,
      showConfirm: false,
      showToaster: false,
      showLoader: false,
      toastMessage: "",
      toastType: "",
      showAlert: false,
      firstname: "",
      lastname: "",
      companyname: "",
      workEmail: "",
      companywebSite: "",
      mobilenumber: "",
      companyaddress: "",
      addressLine2: "",
      countrycode: "",
      companyId: 0,
      role: "",
      city: "",
      country: "",
      state: "",
      zipCode: "",

      imagePopup: false,

      refreshing: false,
      selectedProject: [],
      countryCodeList: mobileCountryCodes.mobileCountryCodes,
      countryVisible: false,
      isNetworkCheck: false,
      showPopUp: false,
      showText: '',
      imageOptions: [
        {
          id: 1,
          name: "Camera",
        },
        {
          id: 2,
          name: "Gallery",
        },
      ],
      profileName:'',
      
      index: 0,
      routes: [
        { key: 'profile', title: 'Profile Management' },
        { key: 'password', title: 'Change Password' },
      ],
    };

    this.onEndReachedCalledDuringMomentum = true;
    // this.page_number = 1;
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.getOverView();
      let data = [];
      for (let i = 0; i < this.state.countryCodeList.length; i++) {
        data.push({
          item: this.state.countryCodeList[i],
          name: this.state.countryCodeList[i].name,
          code: this.state.countryCodeList[i].dialCode,
        });
      }
      this.setState({ countryData: data });
    }
  }

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.getOverView();
    //this.getPlans();

    let data = [];
    for (let i = 0; i < this.state.countryCodeList.length; i++) {
      data.push({
        item: this.state.countryCodeList[i],
        name: this.state.countryCodeList[i].name,
        code: this.state.countryCodeList[i].dialCode,
      });
    }
    this.setState({ countryData: data });
    }
    })
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.updatedata == true) {
      this.getPlans();
      this.props.updateData(false);
    }
  }

  getOverView = (check) => {
    this.setState({
      showLoader: true,
    });
    let url = `${GET_OVERVIEW_DETAIL}${this?.props?.projectDetails?.id}/${this?.props?.projectDetails?.ParentCompany?.id}`;

    getOverViewDetail(
      url,
      {},
      () => {},
      (overViewresp) => {
        if (overViewresp.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (overViewresp.status) {
          if (overViewresp.data.message == "Member listed Successfully.") {
            let data = overViewresp.data.data;
            if (check == "ImageUpload") {
              this.setState({
                profilePicture: data.User.profilePic,
                showLoader: false,
              });
              this.props.getUserDetails({
                ...this.props.userDetails,
                profilePic: data.User.profilePic,
              });
            } else {
              this.setState({
                firstname: data.User.firstName,
                workEmail: data.User.email,
                lastname: data.User.lastName,
                companyname: data.Company.companyName,
                companywebSite: data.Company.website,
                countrycode: data.User.phoneCode,
                mobilenumber: data.User.phoneNumber,
                companyaddress: data.Company.address,
                addressLine2: data.Company.secondAddress,
                companyId: data.Company.id,
                profilePicture: data.User.profilePic,
                showLoader: false,
                role: data.Role.roleName,
                city: data.Company.city,
                country: data.Company.country,
                state: data.Company.state,
                zipCode: data.Company.zipCode,
                profileName:`${data.User.firstName.charAt(0)}${data.User.lastName.charAt(0).toUpperCase()}`
              });
              this.props.getUserDetails({
                ...this.props.userDetails,
                firstName: data.User.firstName,
              });
            }
          } else if (overViewresp.data.data.message) {
            this.showErrorMessage("error", overViewresp.data.data.message);
          } else {
            this.showErrorMessage("error", overViewresp.data.message);
          }
        } else {
          this.showErrorMessage("error", overViewresp.toString());
        }
      }
    );
  };

  getPlans = () => {
    let url = `${GET_PLAN_PROJECT}200/1/ASC/projectName`;
    getPlansAndProject(
      url,
      {
        search:'',
      },
      () => {},
      (resp) => {
        this.setState({
          showLoader: false,
        });
        if (resp.toString() == Strings.errors.timeout) {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (resp.status) {
          if (resp.data.data.rows.length > 0) {
            this.setState({
              plans: resp.data.data.rows,
            });
          }
        } else {
          this.showErrorMessage("error", resp.toString());
        }
      }
    );
  };

  showErrorMessage = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
        showLoader: false,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showToaster: false,
          });
        }, 2000);
      }
    );
  };

  //HEADER COMPONENT
  renderHeader() {
    return (
      <View style={styles.header}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.props.cameBack(true);
            this.props.navigation.goBack();
          }}
        >
          <Image source={Images.backArrow} style={{ marginBottom: 5 }} />
        </TouchableWithoutFeedback>
        <Text style={styles.title}>{Strings.profile.profile}</Text>
      </View>
    );
  }

  //RENDER IMAGE
  renderImage() {
    return (
      <View style={styles.imageContainer}>
        <TouchableWithoutFeedback
          onPress={() => {
            this.setState({
              imageModal: true,
            });
          }}
        >
          <View style={styles.imageButton}>
            <View>
              {this.state.profilePicture
                    ?
              <Image
                source={
                 { uri: this.state.profilePicture }
                }
                style={styles.profileAvatar}
                resizeMode="stretch"
              /> : 
              <View style={styles.profileContainer}>
                 <Text style={styles.profileText}>{this.state.profileName}</Text>
               </View>
              }
            </View>
          </View>
        </TouchableWithoutFeedback>
        <TouchableWithoutFeedback
          onPress={() => {
            this.setState({
              imageModal: true,
            });
          }}
        >
          <View style={styles.camera}>
            <Image source={Images.camRound} />
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  }

  updateMasterState = (key, value) => {
    if (key == Strings.placeholders.currentPassword) {
      this.setState({ currentPassword: value });
    } else if (key == Strings.placeholders.newPassword) {
      this.setState({ newPassword: value });
    } else {
      this.setState({ confirmPassword: value });
    }
  };

  doValidation = () => {
    if (isEmpty(this.state.currentPassword)) {
      this.setState(
        {
          showToaster: true,
          toastType: "error",
          toastMessage: Strings.errors.emptyCurrentPassword,
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (isEmpty(this.state.newPassword)) {
      this.setState(
        {
          showToaster: true,
          toastType: "error",
          toastMessage: Strings.errors.emptyPassword,
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (isValidPassword(this.state.newPassword)) {
      this.setState(
        {
          showToaster: true,
          toastType: "error",
          toastMessage: Strings.errors.validNew,
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 3000);
        }
      );
      return false;
    } else if (isEmpty(this.state.confirmPassword)) {
      this.setState(
        {
          showToaster: true,
          toastType: "error",
          toastMessage: Strings.errors.emptyconfirm,
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (this.state.newPassword !== this.state.confirmPassword) {
      this.setState(
        {
          showToaster: true,
          toastType: "error",
          toastMessage: Strings.errors.mismatch,
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else {
      return true;
    }
  };

  //Next click
  nextClick = async () => {
    if (this.doValidation()) {
      this.setState({ showLoader: true });
      let data = {
        oldPassword: this.state.currentPassword.trim(),
        newPassword: this.state.newPassword.trim(),
      };

      await changePassword(
        CHANGE_PASSWORD,
        data,
        () => {},
        (response) => {
          this.setState({ showLoader: false });

          if (response.status) {
            if (response?.data?.message?.message) {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response?.data?.message?.message,
                  toastType: "error",
                },
                () => {
                  setTimeout(() => {
                    this.setState({
                      showToaster: false,
                    });
                  }, 2000);
                }
              );
            } else if (
              response?.data?.message == "Password Updated Successfully."
            ) {
              Axios.defaults.headers.common["Authorization"] = "";
              AsyncStorage.setItem("AccessToken", "");
              this.setState({ showAlert: true });
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response?.data?.message,
                  toastType: "error",
                },
                () => {
                  setTimeout(() => {
                    this.setState({
                      showToaster: false,
                    });
                  }, 2000);
                }
              );
            }
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.toString(),
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({ showToaster: false });
                }, 2000);
              }
            );
          }
        }
      );
    }
  };

  okTap = () => {
    const resetAction = CommonActions.reset({
      index: 0,
      routes: [{ name: "Dashboard" }],
    });
    this.props.navigation.dispatch(resetAction);
    this.setState({ showAlert: false });
  };

  renderChangrPassword = () => {
    return (
      <View style={{ flex: 1 }}>
        <TextField
          showLeft={true}
          attrName={Strings.placeholders.currentPassword}
          title={Strings.placeholders.currentPassword}
          value={this.state.currentPassword}
          updateMasterState={(key, value) => {
            this.updateMasterState(key, value);
          }}
          hideShow={true}
          maxLength={20}
          hideImage={this.state.showCurren ? Images.unhide : Images.hide}
          onPressHideImage={() => {
            this.setState({ showCurren: !this.state.showCurren });
          }}
          textInputStyles={{
            // here you can add additional TextInput styles
            color: Colors.black,
            fontSize: 14,
            width: "75%",
            marginLeft: wp("10%"),
          }}
          textTitleStyles={{
            marginLeft: wp("10%"),
          }}
          leftImage={Images.lock}
          secureTextEntry={!this.state.showCurren}
        />

        <TextField
          showLeft={true}
          attrName={Strings.placeholders.newPassword}
          title={Strings.placeholders.newPassword}
          value={this.state.newPassword}
          updateMasterState={(key, value) => {
            this.updateMasterState(key, value);
          }}
          hideShow={true}
          maxLength={20}
          hideImage={this.state.showNew ? Images.unhide : Images.hide}
          onPressHideImage={() => {
            this.setState({ showNew: !this.state.showNew });
          }}
          textInputStyles={{
            // here you can add additional TextInput styles
            color: Colors.black,
            fontSize: 14,
            width: "75%",
            marginLeft: wp("10%"),
          }}
          leftImage={Images.lock}
          textTitleStyles={{
            marginLeft: wp("10%"),
          }}
          secureTextEntry={!this.state.showNew}
        />

        <TextField
          showLeft={true}
          attrName={Strings.placeholders.confirmPassword}
          title={Strings.placeholders.confirmPassword}
          value={this.state.confirmPassword}
          updateMasterState={(key, value) => {
            this.updateMasterState(key, value);
          }}
          hideShow={true}
          maxLength={20}
          hideImage={this.state.showConfirm ? Images.unhide : Images.hide}
          onPressHideImage={() => {
            this.setState({ showConfirm: !this.state.showConfirm });
          }}
          textInputStyles={{
            // here you can add additional TextInput styles
            color: Colors.black,
            fontSize: 14,
            width: "75%",
            marginLeft: wp("10%"),
          }}
          leftImage={Images.lock}
          textTitleStyles={{
            marginLeft: wp("10%"),
          }}
          secureTextEntry={!this.state.showConfirm}
        />

        <TouchableOpacity
          onPress={() => {
            this.nextClick();
          }}
          style={{
            width: wp("30%"),
            alignSelf: "center",
            height: hp("5%"),
            marginTop: hp("5%"),
            backgroundColor: Colors.themeOpacity,
            borderRadius: hp("3.5%"),
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <View
          // style={{
          //   width: wp("50%"),
          //   alignSelf: "center",
          //   height: hp("7%"),
          //   marginTop: hp("5%"),
          //   backgroundColor: Colors.themeOpacity,
          //   borderRadius: hp("3.5%"),
          //   justifyContent: "center",
          //   alignItems: "center",
          // }}
          >
            <Text
              style={{
                fontSize: wp("5%"),
                color: Colors.themeColor,
                fontFamily: Fonts.montserratSemiBold,
              }}
            >
              {Strings.profile.update}
            </Text>
          </View>
        </TouchableOpacity>

        {/* <NextButton
          title={Strings.login.signIn}
          nextClick={() => this.nextClick()}
        /> */}
      </View>
    );
  };

  onchangeText = (key, value) => {
    if (key == Strings.placeholders.firstname) {
      this.setState({ firstname: value });
    } else if (key == Strings.placeholders.lastname) {
      this.setState({ lastname: value });
    } else if (key == Strings.placeholders.companyName) {
      this.setState({
        companyname: value,
      });
    } else if (key == Strings.placeholders.companyWebsite) {
      this.setState({
        companywebSite: value,
      });
    } else if (key == Strings.placeholders.mobile) {
      this.setState({ mobilenumber: value });
    } else if (key == Strings.placeholders.companyAddress) {
      this.setState({ companyaddress: value });
    } else if (key == Strings.placeholders.addressline2) {
      this.setState({ addressLine2: value });
    }else if (key == Strings.placeholders.country) {
      this.setState({ country: value });
    }else if (key == Strings.placeholders.state) {
      this.setState({ state: value });
    }else if (key == Strings.placeholders.city) {
      this.setState({ city: value });
    } else if (key == Strings.placeholders.zipcode) {
      this.setState({zipCode: value });
    }
  };

  submitEditing = () => {};

  checkeditProfile = () => {
    if (isEmpty(this.state.firstname)) {
      this.showErrorMessage("error", Strings.errors.enterFirst);
      return false;
    } else if (isName(this.state.firstname)) {
      this.showErrorMessage("error", Strings.errors.validFirst);
      return false;
    } else if (this.state.lastname && isName(this.state.lastname)) {
      this.showErrorMessage("error", Strings.errors.validlast);
      return false;
    } else if (isEmpty(this.state.companyname)) {
      this.showErrorMessage("error", Strings.errors.emptyCompany);
      return false;
    } else if (isName(this.state.companyname)) {
      this.showErrorMessage("error", Strings.errors.validCompany);
      return false;
    } 
    else if (this.state.mobilenumber.length < 10) {
      this.showErrorMessage("error", Strings.errors.validMobile);
      return false;
    } 
     else {
      return true;
    }
  };

  editProfile = () => {
    if (this.checkeditProfile()) {
      let param = {
        firstName: this.state.firstname,
        lastName: this.state.lastname,
        phoneNumber: this.state.mobilenumber,
        phoneCode: this.state.countrycode,
        ProjectId: this.props?.projectDetails.id,
        companyName: this.state.companyname,
        CompanyId: this.state.companyId,
        ParentCompanyId: this.props?.projectDetails?.ParentCompany?.id,
      };

      this.setState({
        showLoader: true,
      });

      updateProfile(
        UPDATE_PROFILE,
        param,
        () => {},
        (updateResp) => {
          this.setState({
            showLoader: false,
          });

          if (updateResp.toString() == Strings.errors.timeout) {
            this.showErrorMessage("error", Strings.errors.checkInternet);
          } else if (updateResp.status) {
            if (updateResp.data.message == "Profile Updated Successfully.") {
              this.showErrorMessage("success", updateResp.data.message);
            } else if (updateResp.data.message.message) {
              this.showErrorMessage("error", updateResp.data.message.message);
            } else {
              this.showErrorMessage("error", updateResp.data.message);
            }
          } else {
            this.showErrorMessage("error", updateResp.toString());
          }
        }
      );
    }
  };

  renderProfileManagement = () => {
    return (
      <View style={{ flex: 1 }}>
        <TextInput
          attrName={Strings.placeholders.firstname}
          title={Strings.placeholders.firstname}
          value={this.state.firstname}
          updateMasterState={(key, value) => {
            this.onchangeText(key, value);
          }}
          textInputStyles={{
            color: Colors.black,
            fontSize: 14,
          }}
          onSubmitEditing={() => {
            this.submitEditing("name");
          }}
          mandatory={true}
        />
        <TextInput
          attrName={Strings.placeholders.lastname}
          title={Strings.placeholders.lastname}
          value={this.state.lastname}
          updateMasterState={(key, value) => {
            this.onchangeText(key, value);
          }}
          textInputStyles={{
            // here you can add additional TextInput styles
            color: Colors.black,
            fontSize: 14,
          }}
          onSubmitEditing={() => {
            this.submitEditing("name");
          }}
        />
        <TextInput
          attrName={Strings.placeholders.companyName}
          title={Strings.placeholders.companyName}
          value={this.state.companyname}
          updateMasterState={(key, value) => {
            this.onchangeText(key, value);
          }}
          textInputStyles={{
            // here you can add additional TextInput styles
            color: Colors.black,
            fontSize: 14,
          }}
          onSubmitEditing={() => {
            this.submitEditing("companyName");
          }}
          mandatory={true}
        />
        <TextInput
          attrName={Strings.placeholders.workEmail}
          title={Strings.placeholders.workEmail}
          value={this.state.workEmail}
          updateMasterState={(key, value) => {
            this.onchangeText(key, value);
          }}
          textInputStyles={{
            // here you can add additional TextInput styles
            color: Colors.placeholder,
            fontSize: 14,
          }}
          onSubmitEditing={() => {
            this.submitEditing("companyName");
          }}
          editable={false}
          mandatory={true}
        />
        <MobilenumberInput
          attrName={Strings.placeholders.mobile}
          title={Strings.placeholders.mobile}
          value={this.state.mobilenumber}
          countryCode={this.state.countrycode}
          imageSource={Images.downArr}
          updateMasterState={(key, value) => {
            this.onchangeText(key, value);
          }}
          maxLength={10}
          keyboardType={"number-pad"}
          onSubmitEditing={() => {
            this.submitEditing("mobile");
          }}
          onPresscountry={() => {
            this.setState({ countryVisible: true });
          }}
          mandatory={true}
        />
        <TouchableOpacity
          onPress={() => {
            this.editProfile();
          }}
          style={{
            width: wp("30%"),
            alignSelf: "center",
            height: hp("5%"),
            marginTop: hp("5%"),
            backgroundColor: Colors.themeOpacity,
            borderRadius: hp("3.5%"),
            justifyContent: "center",
            alignItems: "center",
            marginBottom: 15,
          }}
        >
          <View>
            <Text
              style={{
                fontSize: wp("5%"),
                color: Colors.themeColor,
                fontFamily: Fonts.montserratSemiBold,
              }}
            >
              {Strings.profile.update}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  renderPlans = () => {
    return (
      <View style={{ flex: 1 }}>
        <FlatList
          // nestedScrollEnabled={true}
          data={this.state.plans}
          renderItem={this.renderFlatListItem}
          style={{ marginBottom: 50 }}
          // ItemSeparatorComponent={this.itemSeparator}
          keyExtractor={(item, index) => index.toString()}
          onEndReached={() => this.onEndReached()}
          onEndReachedThreshold={0}
          onMomentumScrollBegin={() => {
            this.onEndReachedCalledDuringMomentum = false;
          }}
          onRefresh={() => this._onReset()}
          refreshing={this.state.refreshing}
        />
      </View>
    );
  };

  renderRow = (option, index, isSelected) => {
    return (
      <View
        style={{
          width: wp("40%"),
          flexDirection: "row",
          alignItems: "center",
          height: hp("5%"),
          backgroundColor: "white",
        }}
      >
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: Colors.planDesc,
            marginLeft: 10,
          }}
        >
          {option.id}
        </Text>
      </View>
    );
  };

  onSelectDropdown = (options, index, item) => {

    if (options == 0) {
      this.props.navigation.navigate("UpgradeProject", {
        from: "upgradePlan",
        step3: {
          showStep: false,
          projectItem: item,
        },
      });
    } else {
      this.setState({
        showCancel: true,
        selectedProject: item,
      });
    }
  };

  renderSeparator = () => {
    return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
  };

  renderFlatListItem = ({ item, index }) => {
    let itemStatus = "Active";

    if (item.status == "trialoverdue" || item.status == "canceled") {
      itemStatus = "Expired";
    }

    return (
      <View style={styles.FlatlistContainer}>
        <View
          style={{ flexDirection: "row", marginVertical: 15, marginLeft: 15 }}
        >
          <Text
            style={{
              color: Colors.black,
              width: wp("70%"),
              paddingTop: 10,
              fontFamily: Fonts.montserratMedium,
              fontSize: 14,
            }}
          >
            {item.projectName}
          </Text>
          <View style={{ width: wp("20") }}>
            <ModalDropdown
              saveScrollPosition={false}
              style={styles.customDropdownStyle}
              dropdownStyle={
                item.status == "trialoverdue" ||
                item.status == "canceled" ||
                item.stripePlan.stripeProductName == "Trial Plan"
                  ? styles.customOptionStyle
                  : styles.customOptionsStyle
              }
              dropdownTextStyle={styles.customOptionsTextStyle}
              options={
                item.status == "trialoverdue" ||
                item.status == "canceled" ||
                item.stripePlan.stripeProductName == "Trial Plan"
                  ? UPGRADE
                  : DROPDOWNOPTIONS
              }
              renderRow={this.renderRow}
              renderSeparator={this.renderSeparator}
              showsVerticalScrollIndicator={false}
              onSelect={(options) =>
                this.onSelectDropdown(options, index, item)
              }
              defaultValue=""
              dropdownListProps={{}}
            >
              <View>
                <Image source={Images.dotmenu} />
              </View>
            </ModalDropdown>
          </View>
        </View>
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          <View style={{ width: wp("40%"), marginLeft: 15 }}>
            <Text
              style={{
                color: "#5B5B5B",
                fontSize: wp("4%"),
                fontFamily: Fonts.montserratRegular,
              }}
            >
              Subscription Plan
            </Text>
            <Text
              style={{
                color: Colors.black,
                fontSize: 14,
                fontFamily: Fonts.montserratRegular,
              }}
            >
              {item.stripePlan.stripeProductName}
            </Text>
          </View>
          <View style={{ width: wp("40%"), right: 15, marginLeft: 10 }}>
            <Text
              style={{
                color: "#5B5B5B",
                fontSize: wp("4%"),
                fontFamily: Fonts.montserratRegular,
              }}
            >
              Subscribed On
            </Text>
            <Text
              style={{
                color: Colors.black,
                fontSize: 14,
                fontFamily: Fonts.montserratRegular,
              }}
            >
              {item.subscribedOn !== null
                ? moment(item.subscribedOn).format("L")
                : "---"}
            </Text>
          </View>
        </View>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            marginTop: 15,
            marginBottom: 15,
          }}
        >
          <View style={{ width: wp("40%"), marginLeft: 15 }}>
            <Text
              style={{
                color: "#5B5B5B",
                fontSize: wp("4%"),
                fontFamily: Fonts.montserratRegular,
              }}
            >
              Auto Renewal On
            </Text>
            <Text
              style={{
                color: Colors.black,
                fontSize: 14,
                fontFamily: Fonts.montserratRegular,
              }}
            >
              {item.status == "trialoverdue"
                ? "---"
                : item.subDetail
                ? moment(item.subDetail.autoRenewal).format("L")
                : "---"}
            </Text>
          </View>
          <View style={{ width: wp("40%"), right: 15, marginLeft: 10 }}>
            <Text
              style={{
                color: "#5B5B5B",
                fontSize: wp("4%"),
                fontFamily: Fonts.montserratRegular,
              }}
            >
              Status
            </Text>
            <Text
              style={{
                color: Colors.black,
                fontSize: 14,
                fontFamily: Fonts.montserratRegular,
              }}
            >
              {itemStatus}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  _onReset = () => {
    this.setState({
      showLoader: true,
    });
    this.getPlans();
  };

  onEndReached = () => {};

  onCancelSubscription = () => {
    this.setState({ showLoader: true });
    let url = `${CANCEL_SUBSCRIPTION}${this.state.selectedProject.memberDetails[0].ProjectId}`;

    cancelProject(
      url,
      {},
      () => {},
      (resp) => {
        if (resp.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastType: "error",
              toastMessage: Strings.errors.checkInternet,
              showLoader: false,
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        } else if (resp.status) {
          if (resp.data.data.message == "Subscription Canceled Successfully") {
            this.setState(
              {
                showToaster: true,
                toastType: "error",
                toastMessage: "Subscription Canceled Successfully",
                showLoader: false,
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
            this.setState({
              showLoader: true,
            });
            this.getPlans();
          } else {
            this.setState(
              {
                showToaster: true,
                toastType: "error",
                toastMessage: resp.data.data.message,
                showLoader: false,
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastType: "error",
              toastMessage: resp.toString(),
              showLoader: false,
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
  };

  onPressCountry = (item) => {
    this.setState({
      countrycode: item.code,
      countryVisible: false,
    });
  };

  onPressOption = (item) => {
    this.setState({imageModal: false})
    if (item.name == "Camera") {
      checkAndRequestPermission(PERMISSIONS.ANDROID.CAMERA).then(async res => {
        if(res || Platform.OS ==='ios') {
     await ImagePicker.openCamera({
        width: 300,
        height: 400,
        cropping: true,
      }).then((image) => {
        this.uploadImage(image);
      });
    } else {
      this.setState({showPopUp: true, showText: Strings.permissions.camera_permission });
    }
  })
    } else {
      checkAndRequestPermission(Platform.Version >= 33 ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE).then(async res => {
        if(res || Platform.OS ==='ios') {
      await ImagePicker.openPicker({
        width: 300,
        height: 400,
        cropping: true,
      }).then((image) => {
        this.uploadImage(image);
      });
  } else {
    this.setState({showPopUp: true, showText: Strings.permissions.files_media_permission });
  }
})
  };
}

  uploadImage = async (response) => {
   

    this.setState({
      showLoader: true,
      imageModal: false,
    });

    let headers = {
      Accept: "application/json",
      "Content-Type": "multipart/form-data",
      Authorization: `JWT ${await AsyncStorage.getItem("AccessToken")}`,
    };

    let path = response.path;
    let formData = new FormData();

    // if(Platform.OS == 'ios'){
    //   path = response.path
    // }else {
    //   path = response.path.replace('file://', '')
    // }

    formData.append(
      "profile",
      {
        fileCopyUri: path,
        name: Platform.OS == "ios" ? response.filename : `${getuniqId()}.jpg`,
        size: response.size,
        type: "image/jpg",
        uri: path,
      },
      Platform.OS == "ios" ? response.filename : `${getuniqId()}.jpg`
    );

    updateImage(
      `${UPDATE_IMAGE}`,
      formData,
      headers,
      () => {},
      (imgresp) => {
        this.setState({
          showLoader: false,
        });
        if (imgresp.toString() == "Error: timeout of 15000ms exceeded") {
          this.showErrorMessage("error", Strings.errors.checkInternet);
        } else if (imgresp.status) {
          if (imgresp.status == 200 || imgresp.status == 201) {
            this.showErrorMessage("success", "Image Uploaded successFully");
            this.getOverView("ImageUpload");
          } else {
            this.showErrorMessage("error", "Failed to upload image");
          }
        } else {
          this.showErrorMessage("error", "Failed to upload image");
        }
      }
    );
  };

  renderScene = ({ route }) => {
    switch (route.key) {
      case 'profile':
        return (
          <KeyboardAwareScrollView extraScrollHeight={100}>
            {this.renderProfileManagement()}
          </KeyboardAwareScrollView>
        );
      case 'password':
        return (
          <KeyboardAwareScrollView>
            {this.renderChangrPassword()}
          </KeyboardAwareScrollView>
        );
      default:
        return null;
    }
  };

  renderTabBar = props => (
    <TabBar
      {...props}
      indicatorStyle={{ backgroundColor: Colors.themeColor }}
      style={{ backgroundColor: Colors.white, marginTop: 20 }}
      activeColor={Colors.themeColor}
      inactiveColor="#A8B2B9"
      labelStyle={{
        fontSize: wp("4%"),
        fontFamily: Fonts.montserratMedium,
        fontWeight: 'bold',
        textTransform: 'capitalize',
      }}
    />
  );

  //RENDER
  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <AppView>
        <StatusBar backgroundColor='white' barStyle='dark-content'/>
        <View style={styles.parentContainer}>
          {this.renderHeader()}
          {this.renderImage()}
          <TabView
            navigationState={{ index: this.state.index, routes: this.state.routes }}
            renderScene={this.renderScene}
            renderTabBar={this.renderTabBar}
            onIndexChange={index => this.setState({ index })}
            initialLayout={{ width: Dimensions.get('window').width }}
          />
        </View>
        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}
        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}
        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.changepasswordSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}
        {this.state.showCancel && (
          <DeletePop
            title={Strings.popup.success}
            desc={Strings.popup.cancelSub}
            descStyles={{
              width: "80%",
            }}
            acceptTap={() => {
              this.setState({ showCancel: false });
              this.onCancelSubscription();
              // this.deleteEqui(this.state.selectedEquip, this.state.selectedIndex)
            }}
            container={{ bottom: 0 }}
            declineTap={() => {
              this.setState({ showCancel: false });
            }}
          />
        )}
        {this.state.showPopUp && (
          <DeletePop
            container={styles.containerStyles}
            descStyles={styles.descStyles}
            desc={this.state.showText}
            declineText={Strings.permissions.no_thanks}
            acceptText={Strings.permissions.go_to_settings}
            declineTap={() => this.setState({ showPopUp: false })}
            acceptTap={() => {
              Linking.openSettings()
              this.setState({ showPopUp: false })
            }}
            declineTextStyle={styles.textStyle}
            acceptTextStyle={styles.textStyle}
          />
        )}
        <Dropdown
          data={this.state.countryData}
          title={Strings.addCompany.chooseCountry}
          value={this.state.countryCode}
          closeBtn={() => this.setState({ countryVisible: false })}
          onPress={(item) => this.onPressCountry(item)}
          visible={this.state.countryVisible}
          onbackPress={() => this.setState({ countryVisible: false })}
        />
        <Dropdown
          data={this.state.imageOptions}
          title={Strings.profile.ChooseOption}
          value={""}
          closeBtn={() => this.setState({ imageModal: false })}
          onPress={(item) => this.onPressOption(item)}
          visible={this.state.imageModal}
          onbackPress={() => this.setState({ imageModal: false })}
          container={{ justifyContent: "center", alignItems: "center" }}
          textContainer={{ fontSize: 14 }}
        />
      </AppView>
      }
      </>
    );
  }
}

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    projectRoleId,
    updatedata,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    lastid,
    projectlist,
    projectDetails,
    editedData,
    userDetails,
    projectRoleId,
    updatedata,
  };
};

export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    cameBack,
    editData,
    updateData,
    getUserDetails,
  }),
  withBackHandler
)(Profile);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  header: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    paddingBottom: hp("3%"),
    paddingLeft: wp("4%"),
  },
  title: {
    width: wp("80%"),
    fontSize: wp("6%"),
    textAlign: "center",
    fontFamily: Fonts.montserratBold,
  },
  imageContainer: {
    width: wp("100%"),
    height: hp("13%"),
    alignItems: "center",
  },
  imageButton: {
    width: hp("12%"),
    height: hp("12%"),
    borderRadius: hp("6%"),
    backgroundColor: "#F5F5F5",
    borderWidth: 1,
    borderColor: "#00000029",
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  placeholder: {
    width: wp("15%"),
    alignSelf: "center",
  },
  camera: {
    position: "absolute",
    bottom: 10,
    //right: -10,
    right: 140,
    width: wp("8%"),
    height: wp("8%"),
    borderRadius: wp("4%"),
    backgroundColor: Colors.white,
    justifyContent: "center",
    alignItems: "center",
  },
  FlatlistContainer: {
    width: wp("90%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("10%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    //outlineProvider: 'bounds'
  },
  customOptionStyle: {
    justifyContent: "flex-end",
    height: hp("5%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("3%"),
  },
  profileAvatar:{ 
    width: hp("12%"),
    height: hp("12%"),
    borderRadius: hp("6%")
  },
  profileContainer:{
    backgroundColor: 'grey',
    alignItems: 'center',
    justifyContent: 'center',
    width: hp("12%"),
    height: hp("12%"),
    borderRadius: hp("6%"),
  },
  profileText:{
    color: 'white', 
    fontSize: 20 
  },
  containerStyles: {
    bottom: 0
  },
  descStyles: { 
    fontSize: wp("4.5%"), 
    marginRight: wp("5%"),
  },
  textStyle: { 
    fontSize: wp("4%")
  },
});
