import React, { Component } from "react";
import { View, StyleSheet, SafeAreaView, Platform } from "react-native";

import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import { getUserDetails } from "../../actions/postAction";
import { Image as AnimatableView } from "react-native-animatable";
import Images from "../../common/images";
import Strings from "../../common/string";
import HeaderAnimation from "../../components/logoAnimation/logoAnimation";
import Header from "../../components/headerComponent/Header";
import { TextField } from "../../components/textinput/Textinput";
import Fonts from "../../common/fonts";
import NextButton from "../../components/nextButton/NextButton";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import Alert from "../../components/toastpopup/alert";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { RESET_TOKEN, RESET_PASSWORD } from "../../api/Constants";
import { checkResetToken, resetPasswordEmail } from "../../api/Api";
import { isValidPassword } from "../../common/Validator";
import AppLoader from '../../components/apploader/AppLoader'
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";

class Login extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showPass: false,
      showConfPass: false,
      showToaster: false,
      toastMessage: "",
      toastType: "error",
      showLoader: false,
      showAlert: false,
      newPassword: "",
      confirmPassword: "",
      resetToken: "",
      errorTitle: "",
      errorDescription: "",
      showCnfPass: false,
      isResetPasswordSuccess: false,
      isNetworkCheck: false,
    };
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck()
    } else {
      const resetToken = this.props.route.params.resetToken;
      this.setState({
        resetToken: resetToken.resetPasswordToken,
      });
    }
  }
  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      const resetToken = this.props.route.params.resetToken;
      this.setState({
        resetToken: resetToken.resetPasswordToken,
      });
    }
    })
  }

  onBackPress = () => {
    this.props.navigation.goBack();
  };

  updateMasterState = (key, value) => {
    if (key == "New Password") {
      this.setState({ newPassword: value });
    } else if (key == "Confirm Password") {
      this.setState({ confirmPassword: value });
    }
  };

  nextClick = async () => {
    if (this.state.newPassword == this.state.confirmPassword) {
      //validate password
      if (!isValidPassword(this.state.confirmPassword)) {
        let token = this.state.resetToken;
        //reset token API
        await checkResetToken(
          RESET_TOKEN + token,
          {},
          () => {},
          (response) => {
            let params = { password: this.state.confirmPassword };
            if (response.status == 200) {
              //reset password API
              resetPasswordEmail(
                RESET_PASSWORD + token,
                params,
                () => {},
                (res) => {
                  if (res.status == 200) {
                    this.setState({
                      errorTitle: Strings.passwordChangeSuccess.title,
                      errorDescription: Strings.passwordChangeSuccess.message,
                      showAlert: true,
                      isResetPasswordSuccess:true
                    });
                  }
                }
              );
            } else {
              this.setState({
                errorTitle: Strings.passwordValidationMessage.title,
                errorDescription:
                  Strings.passwordValidationMessage.tokenExpired,
                showAlert: true,
              });
            }
          }
        );
      } else {
        this.setState({
          errorTitle: Strings.passwordValidationMessage.title,
          errorDescription: Strings.passwordValidationMessage.errorMessage,
          showAlert: true,
        });
      }
    } else {
      this.setState({
        errorTitle: Strings.passwordValidationMessage.title,
        errorDescription: Strings.passwordMissmatch.errorMessage,
        showAlert: true,
      });
    }
  };

  onAlertOkPressed() {
    this.setState({ showAlert: false });
    if (this.state.isResetPasswordSuccess) {
      this.props.navigation.navigate("Login");
    }
  }

  // //validate the password fields
  // checkValidPassword() {
  //   if (this.state.newPassword == this.state.confirmPassword) {
  //     alert("sample data");
  //     this.setState({
  //       errorTitle: Strings.passwordValidationMessage.title,
  //       errorDescription: Strings.passwordValidationMessage.errorMessage,
  //       showAlert: true,
  //     });
  //     return false;
  //   } else if (isValidPassword) {
  //     alert("sample");
  //   }
  // }

  //Main Render method
  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAwareScrollView extraScrollHeight={50}>
          <View style={styles.parentContainer}>
            <HeaderAnimation />
            <View style={styles.subContainer}>
              <AnimatableView source={Images.path} style={styles.path} />
              <View style={styles.signupContainer}>
                <Header
                  backPress={() => this.onBackPress()}
                  title={Strings.login.resetPassword}
                />
                <View>
                  <TextField
                    attrName={Strings.placeholders.newPassword}
                    title={Strings.placeholders.newPassword}
                    value={this.state.newPassword}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    hideShow={true}
                    hideImage={
                      this.state.showPass ? Images.hide : Images.unhide
                    }
                    onPressHideImage={() => {
                      this.setState({ showPass: !this.state.showPass });
                    }}
                    textInputStyles={{
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    secureTextEntry={!this.state.showPass}
                  />

                  <TextField
                    attrName={Strings.placeholders.confirmPassword}
                    title={Strings.placeholders.confirmPassword}
                    value={this.state.confirmPassword}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    hideShow={true}
                    hideImage={
                      this.state.showCnfPass ? Images.hide : Images.unhide
                    }
                    onPressHideImage={() => {
                      this.setState({ showCnfPass: !this.state.showCnfPass });
                    }}
                    textInputStyles={{
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    secureTextEntry={!this.state.showCnfPass}
                  />

                  <NextButton
                    title={Strings.login.submit}
                    nextClick={() => this.nextClick()}
                  />
                </View>
              </View>
            </View>
          </View>
        </KeyboardAwareScrollView>
        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={this.state.errorTitle}
            desc={this.state.errorDescription}
            okTap={() => {
              this.onAlertOkPressed();
            }}
          />
        )}
      </SafeAreaView>
      }
      </>
    );
  }
}

const mapStateToProps = (state) => {
  const { userid } = state.LoginReducer;

  return {
    userid,
  };
};

export default connect(mapStateToProps, {
  getUserDetails,
})(Login);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  subContainer: {
    flex: 1,
    justifyContent: "flex-end",
    height: hp("85%"),
  },
  signupContainer: {
    width: wp("100%"),
    height: hp("60%"),
    borderTopLeftRadius: hp("6%"),
    borderTopRightRadius: hp("6%"),
    zIndex: 999,
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0.8,
    shadowOffset: { width: 10, height: 10 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  path: {
    width: wp("70%"),
    height: hp("25%"),
    marginBottom: -hp("4.5%"),
    marginLeft: -wp("25%"),
  },
  forgot: {
    color: Colors.themeColor,
    marginLeft: wp("6%"),
    textDecorationLine: "underline",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratMedium,
    marginBottom: hp("8%"),
  },
});
