import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Image,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Linking,
  Platform,
} from "react-native";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import { TextField } from "../../components/textinput/Textinput";
import {
  onTapSearch,
  editData,
  refreshPage,
  cameBack,
} from "../../actions/postAction";
import _ from "lodash";
import {
  getEquipList,
  deleteEquipment,
  getGateList,
  deleteGate,
  getCompanyList,
  deleteCompany,
  getMemberList,
  deleteMember,
  getDefinableList,
  deleteDfow,
} from "../../api/Api";
import {
  GET_EQUIP_LIST,
  DELETE_EQUIP,
  GET_GATE_LIST,
  DELETE_GATES,
  GET_COMPANY_LIST,
  DELETE_COMPANY,
  MEMBERS_LIST,
  DELETE_MEMBER,
  GET_DEFINABLE,
  DELETE_DFOW,
} from "../../api/Constants";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import ModalDropdown from "react-native-modal-dropdown";
import DeletePop from "../../components/toastpopup/logoutPop";
import AppLoader from '../../components/apploader/AppLoader';
import DeleteError from "../../components/DeleteError/DeleteError";
import { Avatar } from 'react-native-paper';
import ResDropdown from "../../components/dropdown/ResDropdown";
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";

const DROPDOWNOPTIONS = [
  { id: "Edit", image: Images.editNew, name: "Edit" },
  { id: "Delete", image: Images.deleteNew, name: "Delete" },
];

const DROPDOWNEDIT = [{ id: "Edit", image: Images.editNew, name: "Edit" }];

class Search extends Component {
  constructor(props) {
    super(props);
    this.searchList = _.debounce(this.searchList, 1000);

    this.state = {
      from: "",
      searchText: "",
      showToaster: false,
      toastMessage: "",
      toastType: "error",
      totalCount: 0,
      refreshing: false,
      showDelete: false,
      selectedItem: [],
      selectedIndex: null,
      showNoData: false,
      dataList: [],
      showright: false,
      selectedAll: false,
      showError:false,
      errorMessage:"",
      isNetworkCheck: false,
    };

    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;
  }

  onBackPress() {
    this.props?.refreshPage?.(true);
    this.props?.navigation?.goBack();
    return true;
  }
  
  backHandlerEvent = null;

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck()
    } else {
      this.setState({ from: this.props.route?.params?.from });
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.setState({ from: this.props.route?.params?.from });
    }
    })
  }

  updateData = () => {
    this.setState({ clearSearch: false });
    this.searchList();
  };

  updateMasterState = (key, value) => {
    this.setState({ searchText: value }, () => {
      this.searchList();
    });
  };

  searchList = () => {
    if (this.state.from == "Equip") {
      this.setState({ showIndicator: true, clearSearch: false }, () => {
        this.searchEquipList();
      });
    } else if (this.state.from == "Gate") {
      this.setState({ showIndicator: true, clearSearch: false }, () => {
        this.searchGateList();
      });
    } else if (this.state.from == "Company") {
      this.setState({ showIndicator: true, clearSearch: false }, () => {
        this.searchCompanyList();
      });
    } else if (this.state.from == "Member") {
      this.setState({ showIndicator: true, clearSearch: false }, () => {
        this.searchMemberList();
      });
    } else if (this.state.from == "DFOW") {
      this.setState({ showIndicator: true, clearSearch: false }, () => {
        this.searchDfow();
      });
    }
  };

  clearSearch = () => {
    this.setState({
      clearSearch: false,
      searchText: "",
      showIndicator: true,
    });
    this.searchList();
  };

  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        dataList: [],
        totalCount: 0,
        //load: true,
      },
      () => {
        if (this.state.searchText) {
          this.searchList();
        }
      }
    );
  };

  //API CALLS

  hideToast = () =>
    setTimeout(() => this.setState({ showToaster: false }), 2000);

  searchEquipList = () => {
    const searchUrl = `${GET_EQUIP_LIST}${this.props.projectDetails.id}/200/${this.page_number}/${this.props.projectDetails.ParentCompany.id}`;

    const searchParam = {
      search: this.state.searchText,
      isFilter: false,
      showActivatedAlone: false,
    };

    getEquipList(
      searchUrl,
      searchParam,
      () => null,
      (resp) => {
        this.setState({ showNoData: false });

        if (resp.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastMessage: Strings.errors.checkInternet,
              toastType: "error",
              showLoader: false,
            },
            () => this.hideToast()
          );
        } else if (resp.status) {
          if (resp.data.message == "Equipment Listed successfully.") {
            if (resp.data.data.count == 0) {
              this.setState(
                {
                  toastMessage: Strings.errors.noData,
                  toastType: "error",
                  dataList: [],
                  showLoader: false,
                  showNoData: true,
                },
                () => this.hideToast()
              );
            } else if (resp.data.data.count > 0) {
              this.setState({
                dataList: resp.data.data.rows,
                totalCount: resp.data.data.count,
                showLoader: false,
              });
            }
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: resp.toString(),
              toastType: "error",
              showLoader: false,
            },
            () => this.hideToast()
          );
        }

        this.setState({
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          showLoader: false,
        });
      }
    );
  };

  deleteEquipment = (item, index) => {
    let params = {
      id: [item.id],
      ProjectId: this.props.projectDetails.id,
      isSelectAll: false,
    };

    this.setState({ showLoader: true });

    deleteEquipment(
      DELETE_EQUIP,
      params,
      () => null,
      (resp) => {
        if (resp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (resp.status) {
          if (resp.data.message.message) {
            this.showError("error", resp.data.message.message);
          } else if (resp.data.message == "Equipment deleted successfully.") {
            this.searchList();
          } else {
            this.showError("error", resp.data.message);
          }
        } else {
          this.showError("error", resp.toString());
        }
      }
    );
  };

  deleteCompany = (item, index) => {
    let param = {
      id: [item.id],
      ProjectId: this.props.projectDetails.id,
      isSelectAll: false,
    };
    this.setState({ showLoader: true });

    deleteCompany(
      DELETE_COMPANY,
      param,
      () => null,
      (response) => {

        if (response.status) {
          if (response.status==500) {
            this.setState(
              {
                errorMessage:response.data.message,
                showLoader: false,
                showError:true,
              });
          } else if (response.data.message.message) {
            this.setState(
              {
                showLoader: false,
                showToaster: true,
                toastMessage: response.data.message.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          } else if (response.data.message == "Company Deleted Successfully.") {
            this.page_number = 1;
            this.searchList();
          } else {
            this.setState(
              {
                showLoader: false,
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        } else {
          this.setState(
            {
              showLoader: false,
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };

  deleteMember = (item, index) => {
    this.setState({ showLoader: true });

    let params = {
      id: [item.id],
      ProjectId: this.props.projectDetails.id,
      isSelectAll: false,
    };

    deleteMember(
      DELETE_MEMBER,
      params,
      () => null,
      (response) => {

        if (response.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (response.status) {
          if (response.status==500) {
            this.setState(
              {
                errorMessage:response.data.message,
                showLoader: false,
                showError:true,
              });
          } else if (response.data.message == "Member Deleted Successfully.") {
            this.page_number = 1;
            this.searchList();
            this.setState(
              {
                showToaster: true,
                toastMessage: "Deleted succesfully",
                toastType: "success",
              },
              () => this.hideToast()
            );
          } else if (response.data.message.message) {
            this.setState(
              {
                showLoader: false,
                showToaster: true,
                toastMessage: response.data.message.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          } else {
            this.setState(
              {
                showLoader: false,
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        } else {
          this.setState(
            {
              showLoader: false,
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };

  searchGateList = () => {
    let url = `${GET_GATE_LIST}${this.props.projectDetails.id}/200/${this.page_number}/${this.props.projectDetails.ParentCompany.id}`;
    const searchParam = {
      search: this.state.searchText,
      sort:'ASC',
      sortByField:'id',
      isFilter:false,
      showActivatedAlone: false,
    };
    this.setState({ showNoData: false });

    getGateList(
      url,
      searchParam,
      () => null,
      (resp) => {
        if (resp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (resp.status) {
          if (resp.data.message == "Gate Listed successfully.") {
            if (resp.data.data.count == 0) {
              this.setState({ dataList: [], showNoData: true });
            } else if (resp.data.data.count > 0) {
              let gateResp = resp.data.data.rows;
              for (let i = 0; i < resp.data.data.rows.length; i++) {
                gateResp[i].selected = false;
              }
              this.setState({
                dataList: gateResp,
                totalCount: resp.data.data.count,
                showLoader: false,
              });
            }
          } else if (resp.data.message.message) {
            this.showError("error", resp.data.message.message);
          } else {
            this.showError("error", resp.data.message);
          }
        } else {
          this.showError("error", resp.toString());
        }

        this.setState({
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          showLoader: false,
        });
      }
    );
  };

  deleteAllDfow = (param) => {
    this.setState({ showLoader: true });
    deleteDfow(
      DELETE_DFOW,
      param,
      () => null,
      (dldeR) => {
        this.setState({ showLoader: false });

        if (dldeR.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (dldeR.status) {
          if (dldeR.data.data.message == "Deleted Successfully.") {
            this.searchList();
          } else if (response.data.data.message) {
            this.showError("error", dldeR.data.data.message);
          } else {
            this.showError("error", dldeR.data.message);
          }
        } else {
          this.showError("error", dldeR.toString());
        }
      }
    );
  };

  deleteAllGates = (param) => {
    this.setState({ showLoader: true });
    deleteGate(
      DELETE_GATES,
      param,
      () => null,
      (deleteResp) => {
        if (deleteResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (deleteResp.status) {
          if (deleteResp.data.message == "Gate deleted successfully.") {
            this.showError("error", "Gate deleted successfully.");
            this.searchList();
          } else if (deleteResp.data.message.message) {
            this.showError("error", resp.data.message.message);
          } else {
            this.showError("error", resp.data.message);
          }
        } else {
          this.showError("error", resp.toString());
        }

        this.setState({
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
        });
      }
    );
  };

  checkAll = () => {
    let data = this.state.dataList;
    this.setState(
      {
        selectedAll: !this.state.selectedAll,
        showright: !this.state.selectedAll,
      },
      () => {
        for (let i = 0; i < this.state.dataList.length; i++) {
          if (this.state.selectedAll == true) {
            data[i].selected = true;
          } else {
            data[i].selected = false;
          }
        }
        this.setState({
          memberslist: data,
          selectedGates: this.state.selectedAll == true ? data : [],
        });
      }
    );
  };

  searchCompanyList = () => {
    let companyParam = {
      search: this.state.searchText,
      inviteMember:false,
    };  
    this.setState({ showNoData: false });

    let url = `${GET_COMPANY_LIST}${this.props.projectDetails.id}/20/${this.page_number}/${this.props.projectDetails.ParentCompany.id}`;
    getCompanyList(
      url,
      companyParam,
      () => null,
      (responseData) => {
        //onsole.log('search',JSON.stringify(responseData))
        if (responseData.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (responseData.status) {
          if (responseData.data.message == "Company list.") {
          
            let parentCompany = responseData.data.parentCompany;
            if (responseData.data.data.count == 0) {
              this.setState({
                dataList: parentCompany,
                totalCount: 1,
              });
            } else if (responseData.data.data.count > 0) {
              
              this.setState({
                dataList: parentCompany.concat(responseData.data.data.rows),
                totalCount: responseData.data.data.count,
                showLoader: false,
              });
            }
            
          } else if (responseData.data.message.message) {
            this.showError("error", responseData.data.message.message);
          } else {
            this.showError("error", responseData.data.message);
          }
        } else {
          this.showError("error", responseData.toString());
        }
        this.setState({
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          showLoader: false,
        });
      }
    );
  };

  searchDfow = async () => {
    let param = {
      search: this.state.searchText,
      ParentCompanyId: this.props.projectDetails.ParentCompany.id,
    };

    let url = `${GET_DEFINABLE}${this.props.projectDetails.id}/30/${
      this.page_number
    }/ASC/${0}/DFOW`;

    await getDefinableList(
      url,
      param,
      () => null,
      (dlResp) => {
        this.setState({ showLoader: false });

        if (dlResp.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastType: "error",
              toastMessage: Strings.errors.checkInternet,
            },
            () => this.hideToast()
          );
        } else if (dlResp.status) {
          if (
            dlResp.data.message ===
              "Definable Feature of Work Listed Successfully." ||
            dlResp.data.message === "Gate Listed successfully."
          ) {
            if (dlResp.data.data.count == 0) {
              this.setState({ showNoData: true });
              this.setState({
                dataList: [],
              });
            } else {
              let dataResp = dlResp.data.data.rows;

              for (let i = 0; i < dlResp.data.data.rows.length; i++) {
                dataResp[i].selected = false;
              }

              this.setState(
                {
                  dataList: dlResp.data.data.rows,
                  totalCount: dlResp.data.data.count,
                  showNoData: false,
                });
            }
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastType: "error",
              toastMessage: dlResp.toString(),
              showNoData: false,
            },
            () => this.hideToast()
          );
        }
        this.setState({
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          showLoader: false,
        });
      }
    );
  };

  searchMemberList = async () => {
    let memberParam = {
      search: this.state.searchText,
    };

    let url = `${MEMBERS_LIST}${this.props.projectDetails.id}/20/${this.page_number}/${this.props.projectDetails.ParentCompany.id}`;

    this.setState({ showNoData: false });
    await getMemberList(
      url,
      memberParam,
      () => null,
      (memResp) => {
        this.setState({ showLoader: false });
        if (memResp.status) {
          if (memResp.data.message === "Member listed Successfully.") {
            let data = this.state.dataList;
            if (this.page_number == 1) {
              if (memResp.data.data.count == 0) {
                this.setState({
                  showNoData: true,
                  dataList: [],
                  showLoader: false,
                });
              } else {
                this.setState({
                  dataList: memResp.data.data.rows,
                  showLoader: false,
                  totalCount: memResp.data.data.count,
                  lastId: memResp.data.lastId.id,
                });
              }
            } else {
              let data1 = memResp.data.data.rows;
              this.setState({
                dataList: data.concat(data1),
                showLoader: false,
                totalCount: memResp.data.data.count,
                lastId: memResp.data.lastId.id,
              });
            }
          } else {
            this.setState(
              {
                showToaster: true,
                showLoader: false,
                toastMessage: memResp.data.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              showLoader: false,
              toastMessage: memResp.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }

        this.setState({
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          showLoader: false,
        });
      }
    );
  };

  renderSearchBar = () => {
    return (
      <View style={styles.searchHeader}>
        <View
          style={{
            width: "100%",
            height: hp("6%"),
            flexDirection: "row",
            alignItems: "center",
          }}
        >
          <TouchableOpacity
            style={styles.closeBtn}
            onPress={() => {
              this.onBackPress();
            }}
          >
            <Image
              resizeMode={"contain"}
              source={Images.closeBlack}
              style={styles.closeImg}
            />
          </TouchableOpacity>
          <View
            style={{ flex: 1, justifyContent: "center", alignItems: "center" }}
          >
            <Text style={styles.titleText}>{Strings.search.title}</Text>
          </View>
          <TouchableOpacity
            style={styles.closeBtn}
            onPress={() => {
              if (this.state.showright) {
                this.setState({ showAllDelete: true });
              }
            }}
          >
            {this.state.showright == true && (
              <Image
                resizeMode={"contain"}
                source={Images.delete1}
                style={styles.closeImg}
              />
            )}
          </TouchableOpacity>
        </View>
        <View style={{ flexDirection: "row", justifyContent: "center" }}>
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.SearchHere}
            title={Strings.placeholders.SearchHere}
            value={this.state.searchText}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: 12,
              fontFamily: Fonts.montserratMedium,
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />
          <View
            style={{
              position: "absolute",
              right: wp("5%"),
              width: wp("10%"),
              height: hp("5%"),
              marginTop: hp("3%"),
              justifyContent: "flex-end",
              alignItems: "center",
            }}
          >
            {this.state.showIndicator == true && (
              <ActivityIndicator style={{ marginBottom: 5 }} />
            )}
            {this.state.clearSearch == true && (
              <TouchableOpacity onPress={() => this.clearSearch()}>
                <Image source={Images.closeBlack} style={{ marginBottom: 5 }} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    );
  };

  onEndReached = () => {
    if (this.state.dataList.length < this.state.totalCount) {
      this.page_number = this.page_number + 1;
      //   this.getMemeberList()
      this.onEndReachedCalledDuringMomentum = true;
    }
  };

  renderDropDownRow = (option, index, isSelected) => {
    return (
      <View
        style={{
          width: wp("40%"),
          flexDirection: "row",
          backgroundColor: "white",
          alignItems: "center",
          height: hp("5%"),
        }}
      >
        <Image
          resizeMode={"center"}
          source={option.image}
          style={{
            width: option.id == "Edit" ? wp("5%") : wp("6%"),
            height: option.id == "Edit" ? hp("4%") : hp("5%"),
            marginLeft: 10,
          }}
        />
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: Colors.planDesc,
            marginLeft: 10,
          }}
        >
          {option.id}
        </Text>
      </View>
    );
  };

  renderMemberRow = (option, index, isSelected) => {
    return (
      <View
        style={{
          width: wp("40%"),
          flexDirection: "row",
          alignItems: "center",
          height: hp("7%"),
          backgroundColor: "white",
        }}
      >
        <Image
          resizeMode={"center"}
          source={option.image}
          style={{
            width: option.id == "Edit" ? wp("5%") : wp("6%"),
            height: option.id == "Edit" ? hp("4%") : hp("5%"),
            marginLeft: 10,
          }}
        />
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: Colors.planDesc,
            marginLeft: 10,
          }}
        >
          {option.id}
        </Text>
      </View>
    );
  };

  renderSeparator = () => {
    return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
  };

  onSelectDropdown = (option, index, item) => {
    if (option == 0) {
      if (this.state.from == "Equip") {
        this.editData(item, index);
      } else if (this.state.from == "Company") {
        this.editCompany(item, index);
      } else if (this.state.from == "Member") {
        this.editMember(item, index);
      }
    } else {
      this.setState({
        showDelete: true,
        selectedItem: item,
        selectedIndex: index,
      });
    }
  };

  editData = (item, index) => {
    this.props.editData({
      item: item,
      index: index,
    });
    this.props.navigation.navigate("AddEquip", {
      updateData: this.updateData,
      from: "search",
    });
    //   this.props.clickAdd(true)
  };

  editCompany = (item, index) => {
    this.props.editData({
      item: item,
      index: index,
    });
    this.props.navigation.navigate("AddCompany", {
      updateData: this.updateData,
      from: "search",
    });
  };

  editMember = (item, index) => {
    this.props.editData({
      item: item,
      index: index,
    });
    this.props.navigation.navigate("AddMember", {
      updateData: this.updateData,
      from: "search",
    });
  };

  renderItem=({item,index})=>{
    if(index<3){
   return(
  <Avatar.Text size={24} label={item.label} color="white" theme="grey" style={{backgroundColor:'grey', marginLeft:5}}/>)
  }
  };

  renderEquip = (item, index) => {
    let responsiblePersons=[];
    let isAdditionalAvatar=false; 
    let company='---';
  if(item.controllUserDetails){
  let name= item.controllUserDetails.User.firstName!=null?`${item.controllUserDetails.User.firstName} ${item.controllUserDetails.User.lastName}`:item.controllUserDetails.User.email;
  let email= item.controllUserDetails.User.email;
  let phoneNumber= item.controllUserDetails.phoneCode!=null? item.controllUserDetails.phoneCode+ item.controllUserDetails.phoneNumber:null;
    let data= item.controllUserDetails.firstName!=null?`${item.controllUserDetails.User.firstName.charAt(0)}${item.controllUserDetails.User.lastName.charAt(0)}`:"uu";
    responsiblePersons.push({"label":data,"name":name,'email':email,'phoneNumber':phoneNumber})
    company=item.controllUserDetails.Company!=null||item.controllUserDetails.Company ?item.controllUserDetails.Company.companyName:"---";
    }
    return (
      <View style={equipStyles.equipContainer}>
        <View style={styles.overAllContainer}>
        <View style={equipStyles.nameContainer}>
           <Text numberOfLines={1} style={equipStyles.equipName}>{item.equipmentName}</Text>
           <View>
              <ModalDropdown
                saveScrollPosition={false}
                style={equipStyles.dropDownContainer}
                dropdownStyle={equipStyles.dropdownOption}
                dropdownTextStyle={equipStyles.dropDownText}
                options={DROPDOWNOPTIONS}
                renderRow={this.renderDropDownRow}
                showsVerticalScrollIndicator={false}
                onSelect={(options) => this.onSelectDropdown(options, index, item)}
                renderSeparator={this.renderSeparator}
                defaultValue=""
                dropdownListProps={{}}
              >
                <View style={equipStyles.imageContainer}>
                  <Image source={Images.dotmenu} style={styles.dotMenu} />
                </View>
              </ModalDropdown>
           </View>
        </View>
        
        <View style={equipStyles.equipType}>
            <View style={equipStyles.flexContainer}>
              <Text style={[equipStyles.titleEquip]}>{Strings.equip.company}</Text>
              <Text style={[equipStyles.titleEquip, { marginLeft: 15 }]}>
                {Strings.equip.contact}
              </Text>
            </View>

            <View style={equipStyles.flexContainer}>
              <Text style={[equipStyles.valueEquip]}>
                {company}
              </Text>

              <TouchableOpacity style={[equipStyles.flexContainer,{marginLeft:10,}]} onPress={()=>this.setState({responsiblePerson:responsiblePersons,isModal:true})}>
                <FlatList
                data={responsiblePersons}
                renderItem={this.renderItem}
                horizontal={true}
                scrollEnabled={false}
               />
                { isAdditionalAvatar&&
                  <Avatar.Text size={24} label={`+${count}`} color="white" theme="grey" style={{backgroundColor:'grey', marginLeft:5}}/>

                }
              </TouchableOpacity>

            </View>

            <View style={equipStyles.flexContainer}>
              <Text style={equipStyles.titleEquip}>{Strings.equip.type}</Text>

              <Text style={[equipStyles.titleEquip, { marginLeft: 15 }]}>
                {Strings.equip.id}
              </Text>
            </View>

            <View style={[equipStyles.flexContainer,{marginBottom: 15 }]}>
              <Text style={equipStyles.valueEquip}>{item.PresetEquipmentType.equipmentType}</Text>
              <Text style={[equipStyles.valueEquip, { marginLeft: 15 }]}>
                {item.equipmentAutoId}
              </Text>
            </View>
          </View>

        </View>
      </View>
    );
  };

  selectGate = (item, index) => {
    let data = this.state.dataList;
    let selectedGate = [];
    for (let i = 0; i < this.state.dataList.length; i++) {
      if (index == i) {
        data[i].selected = !data[i].selected;
      } 

      if (data[i].selected == true) {
        selectedGate.push(data[i]);
      }
    }

    this.setState({
      dataList: data,
      selectedGates: selectedGate,
      selectedAll: selectedGate.length == data.length ? true : false,
      showright: selectedGate.length == data.length ? true : false,
    });
  };

  onPressGateDelete = (item, index) => {
    let param = {
      id: [item.id],
      ProjectId: this.props.projectDetails.id,
      isSelectAll: false,
    };
    if (this.state.from == "Gate") {
      this.deleteAllGates(param);
    } else {
      param = {
        deleteData: [item.id],
        ProjectId: this.props.projectDetails.id,
        isSelectAll: false,
      };
      this.deleteAllDfow(param);
    }
  };

  onPressAllGate = () => {
    let data = this.state.selectedGates;
    let id = [];

    for (let item of data) {
      id.push(item.id)
    }

    let param = {
      id: id,
      ProjectId: this.props.projectDetails.id,
      isSelectAll: false,
    };

    if (this.state.selectedGates.length > 0) {
      if (this.state.from == "Gate") {
        this.deleteAllGates(param);
      } else {
        param = {
          deleteData: id,
          ProjectId: this.props.projectDetails.id,
          isSelectAll: false,
        };
        this.deleteAllDfow(param);
      }
    }
    this.setState({ selectedGates: [] });
  };

  editGate = (item, index) => {
    this.props.editData({
      item: item,
      index: index,
    });
    this.props.navigation.navigate("AddGates", {
      updateData: this.updateData,
      from: "search",
    });
  };

  renderGate = (item, index) => {
    return (
      <View>
        <View style={gateStyles.flHeader}>
          <View style={gateStyles.checkbox}>
            <TouchableWithoutFeedback
              onPress={() => {
                this.selectGate(item, index);
              }}
            >
              <Image
                resizeMode={"contain"}
                source={item.selected == true ? Images.check : Images.uncheck}
                style={{ width: wp("9%"), height: hp("4%") }}
              />
            </TouchableWithoutFeedback>
          </View>
          <View style={[gateStyles.checkbox, { width: wp("15%") }]}>
            <Text style={gateStyles.flatlistTitle}>{item.gateAutoId}</Text>
          </View>
          <View style={[gateStyles.checkbox, { width: wp("50%") }]}>
            <Text style={gateStyles.flatlistTitle}>{item.gateName}</Text>
          </View>
          <View
            style={[
              gateStyles.checkbox,
              {
                width: wp("18%"),
                flexDirection: "row",
                justifyContent: "space-around",
              },
            ]}
          >
            <TouchableWithoutFeedback
              onPress={() => this.editGate(item, index)}
            >
              <Image
                resizeMode={"contain"}
                source={Images.edit}
                style={{ width: wp("7%") }}
              />
            </TouchableWithoutFeedback>

            <TouchableWithoutFeedback
              onPress={() =>
                this.setState({
                  showDelete: true,
                  selectedItem: item,
                  selectedIndex: index,
                })
              }
            >
              <Image
                resizeMode={"contain"}
                source={Images.delete}
                style={{ width: wp("7%") }}
              />
            </TouchableWithoutFeedback>
            {/* <Text style={styles.flatlistTitle}>{Strings.gates.action}</Text> */}
          </View>
        </View>
        <View
          style={{
            backgroundColor: "#EFEFEF",
            height: hp("0.3%"),
            width: wp("96%"),
            alignSelf: "center",
          }}
        />
      </View>
    );
  };

  renderCompany = (item, index) => {

    return (
      <View style={CompanyStyles.flatlistContainer}>
          <View style={CompanyStyles.overallCompanyContainer}>
            <View style={CompanyStyles.nameContainer}>
              <View
                style={CompanyStyles.borderColorContainer}
              />
               <View>
              <View style={[CompanyStyles.detailContainer, {
                      flexDirection: "row",
                      width: "95%",
                      marginLeft: 0,
                      marginTop: 15,
                    },]}>
                    <View style={{width:wp(20)}}>
                     <View style={CompanyStyles.imgContainer}>
                         <Image
                           source={item.logo ? { uri: item.logo } : Images.companylogo}
                            style={[CompanyStyles.profAvatar,{width:  item.logo ?'100%':25, height:  item.logo ?'100%':25,}]}
                          />
                      </View>
                    </View>
                  <Text
                    style={[
                      CompanyStyles.nameText,CompanyStyles.titleText]}
                    numberOfLines={2}>
                    {item.companyName}
                  </Text>
             
                <ModalDropdown
                  saveScrollPosition={false}
                  style={CompanyStyles.dropDownContainer}
                  dropdownStyle={[
                      CompanyStyles.customOptionsStyle,
                      { height: index == 0 ? hp("6%") : hp("14%") },
                    ]}
                  dropdownTextStyle={CompanyStyles.customOptionsTextStyle}
                  options={index == 0 ? DROPDOWNEDIT : DROPDOWNOPTIONS}
                  renderRow={this.renderDropDownRow}
                  renderSeparator={this.renderSeparator}
                  showsVerticalScrollIndicator={false}
                  onSelect={(options) =>
                    this.onSelectDropdown(options, index, item)
                  }
                  defaultValue=""
                  dropdownListProps={{}}
                >
                  <View style={CompanyStyles.imageContainer}>
                    <Image style={CompanyStyles.dotMenu} source={Images.dotmenu} />
                  </View>
                </ModalDropdown>
          
              </View>

                <View style={CompanyStyles.containerAddress}>
                    <View style={CompanyStyles.insideContainerAddress}>
                       <Image source={Images.company_address} />
                        <Text style={CompanyStyles.companyText}>
                          {item.address != null
                            ? `${item.address.trim()}, ${item.city}, ${
                                item.state
                              }, ${item.country}.`
                            : ""}
                        </Text>
                    </View>

                    <View style={CompanyStyles.memberContainer}>
                        <Image
                          source={Images.membersCount}
                          style={CompanyStyles.memberIcon}
                        />
                        <Text style={CompanyStyles.companyText}>
                          {item.Members.length}
                        </Text>
                    </View>
                </View>        
              </View>          
            </View>
          </View>
      </View>
    );
  };

  renderEmail = (title, name) => {
    return (
      <View style={memberStyles.emailContainer}>
        <Text style={memberStyles.emailTitle}>{title}</Text>
        <Text style={memberStyles.emailText}>{name}</Text>
      </View>
    );
  };

  phoneNumber=(code,num)=>{
    if(code!=null && num!=null){
    let phoneNumber='';
    let number=code+num;
    if (Platform.OS === 'android') {
      phoneNumber = `tel:${number}`;} else {
      phoneNumber = `telprompt:${number}`;}
      Linking.openURL(phoneNumber);}
  }

  renderMember = (item, index) => {
    return (
      <View style={memberStyles.flatlistContainer}>
        <View style={styles.overAllContainer}>
          <View style={memberStyles.nameContainer}>

            <View style={styles.imgContainer}>
              <Image
                source={Images.placeholder}
                style={memberStyles.imagePlaceholder}
              />
            </View>

            <View style={styles.roleContainer}>
            <View style={memberStyles.nameTextContainer}>
              <Text style={memberStyles.nameText} numberOfLines={2}>
                {item.User.firstName == null ? "-" : item.User.firstName+' '+item.User.lastName}
              </Text>
            </View>

              <Text
                style={[ memberStyles.companyText,{
                    fontSize: wp("3.5%"),
                    fontFamily: Fonts.montserratRegular,
                  },]}>
                {item.Role.roleName}
              </Text>
          </View>
          <View style={memberStyles.menuContainer}>
              <ModalDropdown
                saveScrollPosition={false}
                style={memberStyles.customDropdownStyle}
                dropdownStyle={[
                  memberStyles.customOptionsStyle,
                  {
                    height:
                      this.props.userDetails.email == item.User.email
                        ? hp("7%")
                        : hp("14%"),
                  },
                ]}
                dropdownTextStyle={memberStyles.customOptionsTextStyle}
                options={
                  this.props.userDetails.email == item.User.email
                    ? DROPDOWNEDIT
                    : DROPDOWNOPTIONS
                }
                renderRow={this.renderMemberRow}
                renderSeparator={this.renderSeparator}
                showsVerticalScrollIndicator={false}
                onSelect={(options) =>
                  this.onSelectDropdown(options, index, item)
                }
                defaultValue=""
                dropdownListProps={{}}
              >
                <View style={memberStyles.imageContainer}>
                  <Image style={memberStyles.menuIcon}
                  source={Images.dotmenu} />
                </View>
              </ModalDropdown>
            </View>
            </View>

            <View style={memberStyles.middleContainer}>
              <View style={{ flex: 1 }}>
                  <Image source={Images.companyHolder} />
                <View style={memberStyles.companyContainer}>
                  <Text style={[memberStyles.companyText]} numberOfLines={2}>{`${
                    item.Company ? item.Company.companyName : "-"
                  }`}</Text>
                </View>
              </View>

            <View style={memberStyles.phoneContainer}>
                <Image source={Images.phone} />
              <TouchableOpacity style={styles.companyContainer} onPress={()=>phoneNumber(item.phoneCode,item.phoneNumber)}>
                <Text style={[styles.companyText,{color:item.phoneNumber == null ?Colors.black:Colors.blue,textDecorationLine:item.phoneNumber != null ? 'underline':"none"}]} numberOfLines={2}>
                  {item.phoneCode == null ? "-" : item.phoneCode}{" "}
                  {item.phoneNumber == null ? "-" : item.phoneNumber}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={memberStyles.cardFooter}>
            <View style={memberStyles.mailIconContainer}>
                <Image source={Images.mail} />
              <View style={memberStyles.footerContainer}>

                <TouchableOpacity style={memberStyles.emailContainer} onPress={()=>Linking.openURL(`mailto:${item.User.email}`)}>
                  <Text style={[memberStyles.companyText,{color:Colors.blue,textDecorationLine: 'underline'}]} numberOfLines={2}>
                    {item.User.email}
                  </Text>
                </TouchableOpacity>

                <View style={memberStyles.statusContainer}>
                  <Image
                  style={memberStyles.statusImage}
                  source={item.status=="completed"?Images.completed:Images.pending}
                  resizeMode='contain'/>

                  <Text style={[memberStyles.statusText,{color:item.status=="completed"?Colors.green:Colors.red}]}>
                    {item.status=="completed"?"Completed":"Pending"}
                  </Text>
                </View>
            
              </View>
            </View>
        </View>
        </View>
      </View>
    );
  };

  renderDfow = (item, index) => {
    return (
      <View>
        <View style={gateStyles.flHeader}>
          <View style={gateStyles.checkbox}>
            <TouchableWithoutFeedback
              onPress={() => {
                this.selectGate(item, index);
              }}
            >
              <Image
                resizeMode={"contain"}
                source={item.selected == true ? Images.check : Images.uncheck}
                style={{ width: wp("9%"), height: hp("4%") }}
              />
            </TouchableWithoutFeedback>
          </View>
          <View style={[gateStyles.checkbox]}>
            <Text style={[gateStyles.flatlistTitle, { marginLeft: 5 }]}>
              {item.autoId}
            </Text>
          </View>
          <View style={{ width: wp("50%"), alignItems: "flex-start" }}>
            <Text style={[gateStyles.flatlistTitle, { marginLeft: 5 }]}>
              {item.DFOW}
            </Text>
          </View>
          <View
            style={[
              gateStyles.checkbox,
              {
                width: wp("18%"),
                marginLeft: 7,
                alignItems: "center",
                flexDirection: "row",
                justifyContent: "space-around",
              },
            ]}
          >
            <TouchableWithoutFeedback
              onPress={() => {
                this.props.editData({
                  item: item,
                  index: index,
                });
                this.props.navigation.navigate("AddDFOW", {
                  updateData: this.updateData,
                  from: "search",
                });
              }}
            >
              <Image
                resizeMode={"contain"}
                source={Images.edit}
                style={{ width: wp("7%") }}
              />
            </TouchableWithoutFeedback>

            <TouchableWithoutFeedback
              onPress={() =>
                this.setState({
                  showDelete: true,
                  selectedItem: item,
                  selectedIndex: index,
                })
              }
            >
              <Image
                resizeMode={"contain"}
                source={Images.delete}
                style={{ width: wp("7%") }}
              />
            </TouchableWithoutFeedback>
          </View>
        </View>
        <View
          style={{
            backgroundColor: "#EFEFEF",
            height: hp("0.3%"),
            width: wp("96%"),
            alignSelf: "center",
          }}
        />
      </View>
    );
  };

  renderSearchItem = ({ item, index }) => {
    if (this.state.from == "Equip") {
      return this.renderEquip(item, index);
    } else if (this.state.from == "Gate") {
      return this.renderGate(item, index);
    } else if (this.state.from == "Company") {
      return this.renderCompany(item, index);
    } else if (this.state.from == "Member") {
      return this.renderMember(item, index);
    } else if (this.state.from == "DFOW") {
      return this.renderDfow(item, index);
    }
  };

  acceptDelete = (item, index) => {
    if (this.state.from == "Equip") {
      this.deleteEquipment(item, index);
    } else if (this.state.from == "Gate") {
      return this.onPressGateDelete(item, index);
    } else if (this.state.from == "Company") {
      this.deleteCompany(item, index);
    } else if (this.state.from == "Member") {
      this.deleteMember(item, index);
    } else if (this.state.from == "DFOW") {
      this.onPressGateDelete(item, index);
    }
  };

  showError = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
        showLoader: false,
      },
      () => this.hideToast()
    );
  };

  gateHeader = () => {
    return (
      <View>
        <View style={gateStyles.flHeader}>
          <View style={gateStyles.checkbox}>
            <TouchableWithoutFeedback
              onPress={() => {
                this.checkAll();
              }}
            >
              <Image
                resizeMode={"contain"}
                source={this.state.selectedAll ? Images.check : Images.uncheck}
                style={{ width: wp("9%"), height: hp("4%") }}
              />
            </TouchableWithoutFeedback>
          </View>
          <View style={[gateStyles.checkbox, { width: wp("15%") }]}>
            <Text style={gateStyles.flatlistTitle}>
              {Strings.gates.id}
            </Text>
          </View>
          <View style={[gateStyles.checkbox, { width: wp("50%") }]}>
            <Text style={gateStyles.flatlistTitle}>
              {Strings.gates.gateName}
            </Text>
          </View>
          <View style={[gateStyles.checkbox, { width: wp("18%") }]}>
            <Text style={gateStyles.flatlistTitle}>{Strings.gates.action}</Text>
          </View>
        </View>
        <View
          style={{
            backgroundColor: "#EFEFEF",
            height: hp("0.3%"),
            width: wp("96%"),
            alignSelf: "center",
          }}
        />
      </View>
    );
  };

  dfowHeader = () => {
    return (
      <View>
        <View style={gateStyles.flHeader}>
          <View style={gateStyles.checkbox}>
            <TouchableWithoutFeedback
              onPress={() => {
                this.checkAll();
              }}
            >
              <Image
                resizeMode={"contain"}
                source={this.state.selectedAll ? Images.check : Images.uncheck}
                style={{ width: wp("9%"), height: hp("4%") }}
              />
            </TouchableWithoutFeedback>
          </View>
          <View style={[gateStyles.checkbox, { width: wp("15%") }]}>
            <Text style={gateStyles.flatlistTitle}>{Strings.gates.id}</Text>
          </View>
          <View
            style={[
              gateStyles.checkbox,
              { width: wp("50%"), alignItems: "flex-start" },
            ]}
          >
            <Text style={gateStyles.flatlistTitle}>{Strings.menu.dfow}</Text>
          </View>
          <View style={[gateStyles.checkbox, { width: wp("18%") }]}>
            <Text style={gateStyles.flatlistTitle}>{Strings.gates.action}</Text>
          </View>
        </View>
        <View
          style={{
            backgroundColor: "#EFEFEF",
            height: hp("0.3%"),
            width: wp("96%"),
            alignSelf: "center",
          }}
        />
      </View>
    );
  };

  rendertHeader = () => {
    if (
      this.state.from == "Gate" &&
      this.state.showNoData == false &&
      this.state.dataList.length !== 0
    ) {
      return this.gateHeader();
    } else if (
      this.state.from == "DFOW" &&
      this.state.showNoData == false &&
      this.state.dataList.length !== 0
    ) {
      return this.dfowHeader();
    } else {
      return null;
    }

  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.parentContainer}>
          {this.renderSearchBar()}

          <FlatList
            data={this.state.dataList}
            renderItem={this.renderSearchItem}
            // ItemSeparatorComponent={this.itemSeparator}
            keyExtractor={(item, index) => index.toString()}
            onEndReached={() => this.onEndReached()}
            ListHeaderComponent={this.rendertHeader()}
            onEndReachedThreshold={0}
            onMomentumScrollBegin={() => {
              this.onEndReachedCalledDuringMomentum = false;
            }}
            onRefresh={() => this._onReset()}
            refreshing={this.state.refreshing}
          />
          {this.state.showNoData == true && (
            <Text
              style={{
                alignSelf: "center",
                position: "absolute",
                fontSize: wp("6%"),
                fontFamily: Fonts.montserratRegular,
                marginTop: hp("45%"),
              }}
            >
              {Strings.errors.noRecords}
            </Text>
          )}
        </View>

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
            container={{ marginBottom: hp("12%") }}
          />
        )}

        {this.state.showError &&(  <DeleteError message={this.state.errorMessage} close={()=>this.setState({showError:false})}/>)}

        {this.state.showDelete && (
          <DeletePop
            container={{
              bottom: 0,
            }}
            title={Strings.popup.success}
            desc={Strings.popup.delete}
            acceptTap={() => {
              this.setState({ showDelete: false, showLoader: true });
              this.acceptDelete(
                this.state.selectedItem,
                this.state.selectedIndex
              );
              //   this.deleteEqui(this.state.selectedEquip, this.state.selectedIndex)
            }}
            declineTap={() => {
              this.setState({
                showDelete: false,
                selectedItem: [],
                selectedIndex: null,
              });
            }}
          />
        )}
        {this.state.isModal && (
          <ResDropdown
            data={this.state.responsiblePerson}
            title={Strings.deliverydetails.responsiblePerson}
            value={""}
            closeBtn={() =>
              this.setState({ isModal: false, downloaditem: {} })
            }
            onPress={()=>null}
            visible={this.state.isModal}
            onbackPress={() =>
              this.setState({ isModal: false })
            }
            textContainer={{ fontSize: 16 }}
          />
          )
          }

        {this.state.showAllDelete && (
          <DeletePop
            container={{
              bottom: 0,
            }}
            title={Strings.popup.success}
            desc={Strings.popup.delete}
            acceptTap={() => {
              this.setState({ showAllDelete: false, showLoader: true });
              this.onPressAllGate();
            }}
            declineTap={() => {
              this.setState({
                showAllDelete: false,
                selectedItem: [],
                selectedIndex: null,
              });
            }}
          />
        )}

{this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

      </SafeAreaView>
      }
      </>
    );
  }
}

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    searchTap,
    userDetails,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    searchTap,
    userDetails,
  };
};

export default compose(
  connect(mapStateToProps, {
    onTapSearch,
    editData,
    refreshPage,
    cameBack,
  }),
  withBackHandler
)(Search);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: "#fff",
  },
  searchHeader: {
    marginTop: hp("2%"),
    height: hp("18%"),
    width: wp("95%"),
    alignSelf: "center",
  },
  closeBtn: {
    width: wp("15%"),
    height: hp("8%"),
    justifyContent: "center",
    alignItems: "center",
  },
  closeImg: {
    width: 17,
    height: 17,
  },
  titleText: {
    color: Colors.black,
    fontSize: 20,
    fontFamily: Fonts.montserratSemiBold,
  },
  dotMenu: {
    height: 10,
    width: 30,
  },
  imgContainer: {
     width: 60,
     height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    borderColor: "#D8D8D8",
    borderWidth: 0.5,
    margin:5,
  },
  roleContainer: {
    marginLeft: 10,
    marginRight: 5,
    justifyContent: "center",
  },
  overAllContainer:{
    width: wp("95%")
  },
  
});

const equipStyles = StyleSheet.create({
  equipContainer: {
    width: wp("90%"),
    minHeight: hp("5%"),
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    elevation: 6,
    shadowColor: Colors.black,
    alignSelf: "center",
    marginVertical: 10,
    borderRadius: wp("2%"),
    borderWidth: 0.3,
    shadowRadius: 4.65,
  },
  overAllContainer:{
    flexDirection: "row", width: wp("90%")
  },
  nameContainer:{
    height: hp("7%"),
    width: wp("90%"),
    alignSelf: "center",
    flexDirection: "row",
    borderRadius: wp("2%"),
  },
  equipName: {
    width: wp("70%"),
    color: Colors.drListSubText,
    fontSize: 15,
    margin: 10,
    fontFamily: Fonts.montserratSemiBold,
  },
  dropDownContainer: {
    marginRight: 10,
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
  },
  dropdownOption: {
    height: hp("10%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    borderWidth: 0.3,
    shadowOffset: { width: 0, height: 8 },
    shadowColor: Colors.black,
    alignSelf: "center",
    borderRadius: wp("2%"),
    justifyContent: "flex-end",
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
    elevation: 16,
    marginRight: wp("15%"),
  },
  dropDownText: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: wp("20%"),
  },
  equipType: {
    flex: 1,
    width: wp("90%"),
    justifyContent: "space-between",
  },
  flexContainer:{
    flexDirection: "row"
  },
  titleEquip: {
    width: wp("38%"),
    fontSize: 11,
    color: Colors.planDesc,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginLeft: 10,
  },
  valueEquip: {
    fontSize: 14,
    color: Colors.drListSubText,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginLeft: 10,
    width: wp("38%"),
  },
});

const gateStyles = StyleSheet.create({
  flHeader: {
    width: wp("96%"),
    height: hp("6%"),
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "center",
  },
  checkbox: {
    width: wp("13%"),
    height: hp("6%"),
    justifyContent: "center",
    alignItems: "center",
  },
  flatlistTitle: {
    color: "#292529",
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
  },
});

const CompanyStyles = StyleSheet.create({
  flatlistContainer: {
    width: wp("90%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    elevation: 8,
    shadowRadius: 4.65,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  nameContainer: {
    minHeight: hp("14%"),
    width: wp("95%"),
    alignSelf: "center",
    flexDirection: "row",
  },
  detailContainer: {
    marginLeft: 30,
    justifyContent: "center",
    backgroundColor: Colors.white,
  },
  imagePlaceholder: {
    width: wp("14%"),
    height: wp("14%"),
    borderRadius: wp("7%"),
    marginLeft: wp("6%"),
  },
  nameText: {
    color: Colors.black,
    fontSize: 15,
    fontFamily: Fonts.montserratSemiBold,
  },
  companyText: {
    color: Colors.planDesc,
    fontSize: 13,
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  titleText:{
    width:wp('55%'),
    alignSelf:"center"
  },
  dotMenu: {
    height: 8,
    width: 25,
  },
  imageContainer:{
    marginRight: 0,
  },
  overallCompanyContainer:{
      width: wp("95%"),
  },
  profAvatar: {borderRadius: 30 },
  imgContainer: {
    flex:1,
    maxWidth: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    borderColor: Colors.borderGreyColor,
    borderWidth: 0.5,
    margin:5,
    backgroundColor:Colors.imageGreyColor,
  },
  containerAddress:{
    flexDirection: "row",
    width:"95%",
    marginBottom:20
  },
  insideContainerAddress:{
    marginLeft: 15,
    marginTop: 10,
    width:'60%',
  },
  memberContainer:{
    marginLeft: 30,
    marginTop: 10,
    marginBottom: hp("3%"),
  },
  memberIcon:{
    height: 17.5, 
    width: 25 
   },
   dropDownContainer:{},
   customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  borderColorContainer:{
    width: wp("2%"),
    backgroundColor: Colors.cardBorder,
    borderTopLeftRadius: wp("4%"),
    borderBottomLeftRadius: wp("4%"),
  },
});

const memberStyles = StyleSheet.create({
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  nameContainer: {
    flex: 1,
    margin: 5,
    marginBottom: 10,
    flexDirection: "row",
  },
  detailContainer: {
    width: wp("53%"),
    marginLeft: 20,
    justifyContent: "center",
  },
  imagePlaceholder: {
    width: "100%", height: "100%", borderRadius: 30 ,
  },
  nameText: {
    color: Colors.black,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratSemiBold,
  },
  companyText: {
    color: Colors.planDesc,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  dotMenu: {
  },
  emailTitle: {
    color: Colors.planDesc,
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    margin: 5,
  },
  emailText: {
    color: Colors.drListSubText,
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginTop: 0,
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    marginRight: wp("20%"),
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: wp("15%"),
  },
  menuIcon: {
    height: 10,
    width: 30,
    backgroundColor: "white",
  },
  middleContainer: {
    margin: 10, 
    flexDirection: "row" ,
    width:wp('85%'),
   },
   menuContainer: { flex: 1, maxWidth: 45, height: 50, top: 0 },
   cardFooter: {
    flex: 1,
    marginBottom: 0,
    flexDirection: "row",
    borderTopColor: "#00000029",
    borderTopWidth: 1,
    width: wp("90%"),
  },
  mailIconContainer: { flex: 1, marginTop: 10, marginLeft: 10 },
  footerContainer:{
    flexDirection:"row",
    justifyContent:"space-between",
    alignContent:'space-between',
    paddingRight:'2%',
  },
  emailContainer: { marginBottom: 10, justifyContent: "center",width:'65%' },
  statusContainer:{
    marginTop:10,
    justifyContent: "flex-end", 
    marginRight:10,
    flexDirection:'row',
  },
  statusImage:{
    marginTop:5,
    justifyContent:'center',
    marginRight:5,
  },
  statusText:{
    fontFamily:Fonts.montserratRegular,
    fontSize:wp('3.1%'),
  },
  nameTextContainer:{
    alignContent: "flex-end",width:wp('55') 
  },
  companyContainer:{
    justifyContent: "center"
  },
  phoneContainer:{
    justifyContent:'flex-end'
  }
});

const dfowStyles = StyleSheet.create({
  flHeader: {
    width: wp("96%"),
    height: hp("6%"),
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "center",
    marginTop: hp("3%"),
  },
  checkbox: {
    width: wp("13%"),
    height: hp("6%"),
    justifyContent: "center",
  },
  flatlistTitle: {
    color: Colors.planDesc,
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
  },
});
