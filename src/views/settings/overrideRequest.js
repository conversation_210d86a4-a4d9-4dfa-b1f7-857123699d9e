import React, {Component} from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  Platform,
} from 'react-native';
import {connect} from 'react-redux';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Colors from '../../common/color';
import {getUserDetails} from '../../actions/postAction';
import Images from '../../common/images';
import Strings from '../../common/string';
import Header from '../../components/headerComponent/Header';
import {TextField} from '../../components/textinput/Textinput';
import Fonts from '../../common/fonts';
import NextButton from '../../components/nextButton/NextButton';
import {isEmpty, isValidPassword} from '../../common';
import Toastpopup from '../../components/toastpopup/Toastpopup';
import {LOGIN} from '../../api/Constants';
import {login} from '../../api/Api';
import Alert from '../../components/toastpopup/alert';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Axios from 'axios';
import { CommonActions } from '@react-navigation/native';
import AppLoader from '../../components/apploader/AppLoader'
import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
class OverrideRequest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showPass: false,
      showNew: false,
      showConfirm: false,
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    //   password: 'i958_wu6!q',
      showToaster: false,
      toastMessage: '',
      toastType: 'error',
      showLoader: false,
      showAlert: false,
      isNetworkCheck: false
    };
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck()
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
    }
    })
  }

  onBackPress = () => {
    this.props.navigation.goBack();
  };

  updateMasterState = (key, value) => {
    if (key == Strings.placeholders.currentPassword) {
      this.setState({currentPassword: value});
    } else if(key == Strings.placeholders.newPassword) {
      this.setState({newPassword: value});
    } else{
        this.setState({confirmPassword: value});
    }
  };

  doValidation = () => {
    if (isEmpty(this.state.currentPassword)) {
      this.setState(
        {
          showToaster: true,
          toastType: 'error',
          toastMessage: Strings.errors.emptyCurrentPassword,
        },
        () => {
          setTimeout(() => {
            this.setState({showToaster: false});
          }, 2000);
        },
      );
      return false;
    } else if (isValidPassword(this.state.currentPassword)) {
      this.setState(
        {
          showToaster: true,
          toastType: 'error',
          toastMessage: Strings.errors.validCurrent,
        },
        () => {
          setTimeout(() => {
            this.setState({showToaster: false});
          }, 2000);
        },
      );
      return false;
    } else if (isEmpty(this.state.newPassword)) {
      this.setState(
        {
          showToaster: true,
          toastType: 'error',
          toastMessage: Strings.errors.emptyPassword,
        },
        () => {
          setTimeout(() => {
            this.setState({showToaster: false});
          }, 2000);
        },
      );
      return false;
    } else if (isValidPassword(this.state.newPassword)) {
        this.setState(
          {
            showToaster: true,
            toastType: 'error',
            toastMessage: Strings.errors.validPassword,
          },
          () => {
            setTimeout(() => {
              this.setState({showToaster: false});
            }, 2000);
          },
        );
        return false;
      } else if (isEmpty(this.state.confirmPassword)) {
        this.setState(
          {
            showToaster: true,
            toastType: 'error',
            toastMessage: Strings.errors.emptyconfirm,
          },
          () => {
            setTimeout(() => {
              this.setState({showToaster: false});
            }, 2000);
          },
        );
        return false;
      }else if (this.state.newPassword !== this.state.confirmPassword) {
        this.setState(
          {
            showToaster: true,
            toastType: 'error',
            toastMessage: Strings.errors.mismatch,
          },
          () => {
            setTimeout(() => {
              this.setState({showToaster: false});
            }, 2000);
          },
        );
        return false;
      } else {
      return true;
    }
  };

  //Next click
  nextClick = async () => {
    if (this.doValidation()) {
      this.setState({showLoader: true})
      let data = {
        email: this.state.email.trim(),
        password: this.state.password.trim()
      };
       await login(LOGIN, data, () => {}, (response) => {

        this.setState({showLoader: false})

        if(response.status){
          if(response.data.message == 'Login Successfully.'){
            this.props.getUserDetails(response.data.userDetails)
            Axios.defaults.headers.common["Authorization"] = "JWT " + response.data.token;
            AsyncStorage.setItem('AccessToken', response.data.token)
            this.setState({showAlert: true})
          }else if(response.data.message.message){
            let array = Object.values(response.data.message.details[0])

            this.setState({showToaster: true, toastMessage: array.toString(), toastType: 'error'},()=>{
            setTimeout(()=>{
              this.setState({showToaster: false})
            }, 2000)
          })
           
          }else {
            this.setState({showToaster: true, toastMessage: response.data.message, toastType: 'error'},()=>{
              setTimeout(()=>{
                this.setState({showToaster: false})
              }, 2000)
            })
          }
        }else{
          this.setState({showToaster: true, toastMessage: response.toString(), toastType: 'error'},()=>{
            setTimeout(()=>{
              this.setState({showToaster: false})
            }, 2000)
          })
        }
      })
    }
  };

  //Ok Tap
  okTap = () => {
    const resetAction = CommonActions.reset({
      index: 0,
      routes: [{ name: 'RegisteredRoute' }]
    });
    this.props.navigation.dispatch(resetAction);
    this.setState({showAlert: false})
  }

  //Main Render method
  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>        
        <View style={styles.parentContainer}>
            {/* <Image resizeMode={'center'} source={Images.path} style={{width: wp('50%')}}/> */}

            <View style={styles.signupContainer}>
              <Header
                backPress={() => this.onBackPress()}
                title={Strings.login.welcomeBack}
              />

              <View style={{flex: 1}}>
              <TextField
                  attrName={Strings.placeholders.currentPassword}
                  title={Strings.placeholders.currentPassword}
                  value={this.state.currentPassword}
                  updateMasterState={(key, value) => {
                    this.updateMasterState(key, value);
                  }}
                  hideShow={true}
                  hideImage={this.state.showPass ? Images.hide : Images.unhide}
                  onPressHideImage={() => {
                    this.setState({showPass: !this.state.showPass});
                  }}
                  textInputStyles={{
                    // here you can add additional TextInput styles
                    color: Colors.black,
                    fontSize: wp('4.5%'),
                  }}
                  secureTextEntry={!this.state.showPass}
                />

                <NextButton
                  title={Strings.login.signIn}
                  nextClick={() => this.nextClick()}
                />
            </View>
          </View>

          {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

          {this.state.showToaster && (
                  <Toastpopup
                    backPress={() => this.setState({showToaster: false})}
                    toastMessage={this.state.toastMessage}
                    type={this.state.toastType}
                  />
                )}

          {this.state.showAlert && 
                  <Alert 
                    title={Strings.popup.success}
                    desc={Strings.popup.loginSuccess}
                    okTap={()=> {this.okTap()}}
                    />
                }
        </View>

      </SafeAreaView>
      }
      </>
    );
  }
}

const mapStateToProps = (state) => {
  const {userid} = state.LoginReducer;

  return {
    userid,
  };
};

export default connect(mapStateToProps, {
  getUserDetails,
})(OverrideRequest);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  subContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    height: hp('90%')
  },
  signupContainer: {
    width: wp('100%'),
    height: hp('60%'),
    borderTopLeftRadius: hp('6%'),
    borderTopRightRadius: hp('6%'),
    zIndex: 999,
    backgroundColor: '#FFF',
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == 'ios' ? 1 : 0.8,
    shadowOffset: {width: 10, height: 10},
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: 'rgba(0,0,0,0.14)',
  },
  path: {
    width: wp('70%'),
    height: hp('25%'),
    marginBottom: -hp('4.5%'),
    marginLeft: -wp('25%')
  },
  forgot: {
    color: Colors.themeColor,
    marginLeft: wp('6%'),
    textDecorationLine: 'underline',
    fontSize: wp('3.5%'),
    fontFamily: Fonts.montserratMedium,
    marginBottom: hp('8%'),
  },
});