import React, { Component } from "react";
import { View, StyleSheet, Platform } from "react-native";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import {
  storeUserid,
  countryList,
  showSignupparams,
} from "../../actions/postAction";
import { Image as AnimatableView } from "react-native-animatable";

import {
  AppView,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>erAni<PERSON>,
  Header,
  TextField,
  Toastpopup,
  NextButton,
  Steps,
  Alert,
  MobilenumberInput,
  Dropdown,
} from "../../components";

import {
  isEmpty,
  isValidEmail,
  isName,
  isValidPassword,
  Colors,
  Images,
  Strings,
  mobileCountryCodes,
} from "../../common";

import { isValidNumber } from "../../common/Validator";

import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

import { CHECK_EMAIl, GET_STATE, GET_COUNTRY } from "../../api/Constants";
import { checkEmail, getStateList, getCountryList } from "../../api/Api";


var isFromDeepLink = false;

class Step1 extends Component {
  constructor(props) {
    super(props);

    if (
      this.props.route.params != null ||
      this.props.route.params != undefined
    ) {
      isFromDeepLink = true;

    }

    const { userDetail, memberDetail, domainName } =
      this.props.route.params != null
        ? this.props.route.params
        : { userDetail: null, memberDetail: null, domainName: null };

    this.state = {
      name: "",
      email: userDetail == null ? "" : userDetail.email,
      userDetail: userDetail,
      memberDetail: memberDetail,
      domainName: domainName,
      mobile: "",
      password: "",
      confirmPassword: "",
      showToaster: false,
      showLoader: false,
      showAlert: false,
      countryCode: "+1",
      selectedCountry: [],
      countryData: mobileCountryCodes.mobileCountryCodes,
      countryVisible: false,
      checkCameBack: false,
      showPass: false,
      showPassCon: false,
    };
  }

  componentDidMount() {
    //this.getCountries();
  }
  componentWillReceiveProps(nextProps){ 
      if (
        nextProps.route.params != null ||
        nextProps.route.params != undefined
      ) {
      if(nextProps.route.params!=this.props.route.params){
      const { userDetail, memberDetail, domainName } =
      nextProps.route.params != null
        ? nextProps.route.params
        : { userDetail: null, memberDetail: null, domainName: null };
        this.setState({
          showLoader:false,
          email: userDetail == null ? "" : userDetail.email,
          userDetail: userDetail,
          memberDetail: memberDetail,
          domainName: domainName,
          mobile: "",
          password: "",
          confirmPassword: "",
        })
        isFromDeepLink=true;
      }
    }
  }

  getCountries = () => {
    getCountryList(
      GET_COUNTRY,
      {},
      () => null,
      (response) => {
        if (response.status) {
          if (response.status == 200) {
            this.props.countryList(response.data.countryList);
            this.getStates(response.data.countryList[0]);
          }
        }
      }
    );
  };

  getStates = (data) => {
    let id = data.id;

    let url = GET_STATE + id;
    getStateList(
      url,
      {},
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.stateList) {
          }
        }
      }
    );
  };

  onBackPress = () => {
    isFromDeepLink=false;
    this.props.navigation.goBack();
  };

  cameBackData = (data) => {};

  updateMasterState = (key, value) => {
    if (key == Strings.placeholders.name) {
      this.setState({ name: value });
    } else if (key == Strings.placeholders.email) {
      this.setState({ email: value });
    } else if (key == Strings.placeholders.mobile) {
      this.setState({ mobile: value });
    } else if (key == Strings.placeholders.password) {
      this.setState({ password: value });
    } else if (key == Strings.placeholders.confirmPassword) {
      this.setState({ confirmPassword: value });
    }
  };

  hideToast = () => {
    setTimeout(() => {
      this.setState({ showToaster: false });
    }, 2000);
  };

  validation = () => {
    if (isEmpty(this.state.email.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyEmail,
          toastType: "error",
        },
        () => this.hideToast()
      );
      return false;
    } else if (isValidEmail(this.state.email)) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.validEmail,
          toastType: "error",
        },
        () => this.hideToast()
      );
      return false;
    } else if (isEmpty(this.state.mobile.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyMobile,
          toastType: "error",
        },
        () => this.hideToast()
      );
      return false;
    } else if (
      isValidNumber(this.state.mobile) ||
      this.state.mobile.length < 10
    ) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.validMobile,
          toastType: "error",
        },
        () => this.hideToast()
      );
      return false;
    } else if (isFromDeepLink == true) {
      if (isEmpty(this.state.password)) {
        this.setState(
          {
            showToaster: true,
            toastMessage: Strings.errors.emptyPassword,
            toastType: "error",
          },
          () => this.hideToast()
        );
        return false;
      }
      if (isValidPassword(this.state.password)) {
        this.setState(
          {
            showToaster: true,
            toastMessage: Strings.errors.validPassword,
            toastType: "error",
          },
          () => this.hideToast()
        );

        return false;
      } else if (isEmpty(this.state.confirmPassword)) {
        this.setState(
          {
            showToaster: true,
            toastMessage: Strings.errors.emptyPassword,
            toastType: "error",
          },
          () => this.hideToast()
        );
        return false;
      } else if (isValidPassword(this.state.confirmPassword)) {
        this.setState(
          {
            showToaster: true,
            toastMessage: Strings.errors.validPassword,
            toastType: "error",
          },
          () => this.hideToast()
        );
        return false;
      } else if (this.state.password != this.state.confirmPassword) {
        this.setState(
          {
            showToaster: true,
            toastMessage: Strings.errors.mismatch,
            toastType: "error",
          },
          () => this.hideToast()
        );
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  };

  nextClick = () => {
    if (this.validation()) {
      let data = {
        email: this.state.email,
        mobile: this.state.mobile,
        countryCode: this.state.countryCode,
      };

      if (this.state.checkCameBack == true) {
        this.props.navigation.navigate("Step2", { step1: data });
      } else {
        this.setState({ showLoader: true });
        this.checkEmail(data);
      }
    }
  };

  checkEmail = async (data) => {
    let paramCheck = {
      email: data.email,
      phoneNumber: data.mobile,
      phoneCode: data.countryCode,
    };

    if (isFromDeepLink == true) {
      
      let paramCheckWithPassword = {
        email: data.email,
        isFromDeepLink: true,
        phoneNumber: data.mobile,
        phoneCode: data.countryCode,
        password: this.state.password,
        domainName: this.state.domainName,
        userDetail: this.state.userDetail,
        memberDetail: this.state.memberDetail,
      };
      this.setState({ showLoader: false });
      this.props.navigation.navigate("Step2", {
        step1: paramCheckWithPassword,
      });
    } else {
      await checkEmail(
        CHECK_EMAIl,
        paramCheck,
        () => null,
        (response) => {
          this.setState({ showLoader: false });

          if (
            response.toString() == Strings.errors.timeout ||
            response.toString() == "Error: Network Error"
          ) {
            this.setState(
              {
                showToaster: true,
                toastMessage: Strings.errors.checkInternet,
                toastType: "error",
              },
              () => this.hideToast()
            );
          } else if (response.status) {
            if (response.data.message.message) {
              let array = Object.values(response.data.message.details[0]);
              this.setState(
                {
                  showToaster: true,
                  toastMessage: array.toString(),
                  toastType: "error",
                },
                () => this.hideToast()
              );
            } else if (response.data.message == "Not exist") {
              if (isFromDeepLink == true) {
                this.props.navigation.navigate("Step2", {
                  step1: paramCheckWithPassword,
                });
              } else {
                if (response.data.company.Company) {
                  let newValue = {
                    email: this.state.email,
                    mobile: this.state.mobile,
                    countryCode: this.state.countryCode,
                    id:response.data.company.Company[0].id,
                    companyName:response.data.company.Company[0].companyName,
                    website:response.data.company.Company[0].website!=null?response.data.company.Company[0].website:'',
                    address:response.data.company.Company[0].address,
                    secondAddress:response.data.company.Company[0].secondAddress!=null?response.data.company.Company[0].secondAddress!=null:'',
                    country:response.data.company.Company[0].country,
                    city:response.data.company.Company[0].city,
                    companyAutoId:response.data.company.Company[0].companyAutoId,
                    state:response.data.company.Company[0].state,
                    zipCode:response.data.company.Company[0].zipCode,
                    scope:response.data.company.Company[0].scope,
                    logo: response.data.company.Company[0].logo,
                    showCompany:true,                
                  };  
                this.props.navigation.navigate("Step2", { step1: newValue });
                }
                else{
                this.props.navigation.navigate("Step2", { step1: data });
                }
              }
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.data.message,
                  toastType: "error",
                },
                () => this.hideToast()
              );
            }
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.toString(),
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        }
      );
    }
  };

  submitEditing = (key) => {
    if (key == "name") {
      if (isEmpty(this.state.name)) {
        this.setState(
          {
            showToaster: true,
            toastMessage: Strings.errors.emptyName,
            toastType: "error",
          },
          () => this.hideToast()
        );
      } else if (isName(this.state.name)) {
        this.setState(
          {
            showToaster: true,
            toastMessage: Strings.errors.validName,
            toastType: "error",
          },
          () => this.hideToast()
        );
      }
    }
    // else if(key == Strings.placeholders.email){
    // }else if(key == Strings.placeholders.mobile){
    // }
  };

  onPressCountry = (item) => {
    this.setState({
      countryCode: item.dialCode,
      countryVisible: false,
    });
  };

  //Main Render method
  render() {
    return (
      <AppView>
        <KeyboardAwareScrollView
          extraScrollHeight={50}
          nestedScrollEnabled={true}
        >
          <View style={styles.parentContainer}>
            <View style={styles.parentContainer}>
              <HeaderAnimation />
              <View style={styles.subContainer}>
                <AnimatableView source={Images.path} style={styles.path} />
                {/* <Image resizeMode={'center'} source={Images.path} style={{width: wp('50%')}}/> */}

                <View
                  style={[
                    styles.signupContainer,
                    { height: isFromDeepLink == true ? hp("70%") : hp("50%") },
                  ]}
                >
                  <Header
                    backPress={() => this.onBackPress()}
                    title={Strings.step1.welcome}
                  />

                  <View style={{ flex: 1 }}>
                    <TextField
                      autoCapitalize="none"
                      editable={!isFromDeepLink}
                      attrName={Strings.placeholders.email}
                      title={Strings.placeholders.email}
                      value={this.state.email.trim()}
                      updateMasterState={(key, value) => {
                        this.updateMasterState(key, value);
                      }}
                      textInputStyles={{
                        // here you can add additional TextInput styles
                        color:
                          isFromDeepLink == true ? "#99A3A4" : Colors.black,
                        fontSize: 14,
                      }}
                      onSubmitEditing={() => {
                        this.submitEditing("email");
                      }}
                    />

                    <MobilenumberInput
                      attrName={Strings.placeholders.mobile}
                      title={Strings.placeholders.mobile}
                      value={this.state.mobile}
                      countryCode={this.state.countryCode}
                      imageSource={Images.downArr}
                      updateMasterState={(key, value) => {
                        this.updateMasterState(key, value);
                      }}
                      maxLength={10}
                      keyboardType={"number-pad"}
                      onSubmitEditing={() => {
                        this.submitEditing("mobile");
                      }}
                      onPresscountry={() => {
                        this.setState({ countryVisible: true });
                      }}
                    />

                    {isFromDeepLink == true && (
                      <TextField
                        attrName={Strings.placeholders.password}
                        title={Strings.placeholders.password}
                        value={this.state.password}
                        updateMasterState={(key, value) => {
                          this.updateMasterState(key, value);
                        }}
                        hideShow={true}
                        hideImage={
                          this.state.showPass ? Images.unhide : Images.hide
                        }
                        onPressHideImage={() => {
                          this.setState({ showPass: !this.state.showPass });
                        }}
                        textInputStyles={{
                          // here you can add additional TextInput styles
                          color: Colors.black,
                          fontSize: 14,
                        }}
                        secureTextEntry={!this.state.showPass}
                      />
                    )}

                    {isFromDeepLink == true && (
                      <TextField
                        attrName={Strings.placeholders.confirmPassword}
                        title={Strings.placeholders.confirmPassword}
                        value={this.state.confirmPassword}
                        updateMasterState={(key, value) => {
                          this.updateMasterState(key, value);
                        }}
                        hideShow={true}
                        hideImage={
                          this.state.showPassCon ? Images.unhide : Images.hide
                        }
                        onPressHideImage={() => {
                          this.setState({
                            showPassCon: !this.state.showPassCon,
                          });
                        }}
                        textInputStyles={{
                          // here you can add additional TextInput styles
                          color: Colors.black,
                          fontSize: 14,
                        }}
                        secureTextEntry={!this.state.showPassCon}
                      />
                    )}

                    <NextButton nextClick={() => this.nextClick()} />

                    <View style={{ flex: 1, justifyContent: "flex-end" }}>
                      <Steps page={"1"} />
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </KeyboardAwareScrollView>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.forgotSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}

        <Dropdown
          data={this.state.countryData}
          title={Strings.addCompany.chooseCountry}
          value={this.state.countryCode}
          closeBtn={() => this.setState({ countryVisible: false })}
          onPress={(item) => this.onPressCountry(item)}
          visible={this.state.countryVisible}
          onbackPress={() => this.setState({ countryVisible: false })}
        />
      </AppView>
    );
  }
}

const mapStateToProps = (state) => {
  const { userid, countrylist, signupParams } = state.LoginReducer;

  return {
    userid,
    countrylist,
    signupParams,
  };
};

export default connect(mapStateToProps, {
  storeUserid,
  countryList,
  showSignupparams,
})(Step1);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  subContainer: {
    flex: 1,
    justifyContent: "flex-end",
    height: hp("85%"),
  },
  signupContainer: {
    width: wp("100%"),
    height: isFromDeepLink == true ? hp("70%") : hp("50%"),
    borderTopLeftRadius: hp("6%"),
    borderTopRightRadius: hp("6%"),
    zIndex: 999,
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0.8,
    // shadowOffset: {width: 10, height: 10},
    shadowOpacity: 1,
    elevation: 200,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  path: {
    width: wp("70%"),
    height: hp("25%"),
    marginBottom: -hp("4.5%"),
    marginLeft: -wp("25%"),
  },
});
