import React, { Component } from "react";
import {
  View,
  StyleSheet,
  Platform,
  TouchableOpacity,
  Text,
} from "react-native";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { CommonActions } from '@react-navigation/native';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { storeUserid, countryList } from "../../actions/postAction";
import {
  Image as AnimatableImage,
  View as AnimatableView,
} from "react-native-animatable";
import axios from "axios";
import {
  AppView,
  AppLoader,
  HeaderAnimation,
  Header,
  TextField,
  NextButton,
  Steps,
  Alert,
  Toastpopup,
  Dropdown,
  AutoCompleteList,
} from "../../components";

import {
  isEmpty,
  isName,
  isCompany,
  Colors,
  Images,
  Strings,
  Fonts,
} from "../../common";
import {
  GET_STATE,
  GET_CITY,
  GET_COUNTRY,
  UPDATE_MEMBER,
} from "../../api/Constants";
import {
  getStateList,
  getCityList,
  getCountryList,
  updateMember,
} from "../../api/Api";

const rotation = {
  from: {
    rotate: "0deg",
  },
  to: {
    rotate: "360deg",
  },
};

import { ApiKeys, googleAutoCompleteAPI } from '../../api/GoogleServices'

/* const googleAutoCompleteAPI = (apiKey, keyWord) =>
  `https://maps.googleapis.com/maps/api/place/autocomplete/json?key=${apiKey}&input=${keyWord}`;

const API_KEY = "AIzaSyB2EHXZ7yQZ3KItOpshW2F4DJ5ewmagOWU"; */

var isFromDeepLink = false;
const API_KEY = Platform.OS == "android" ? ApiKeys.android : ApiKeys.ios;
var showCompany=false;
var editable=false;
class Step2 extends Component {
  constructor(props) {
    super(props);
    let step1 = this.props.route?.params?.step1;
    if (step1.isFromDeepLink != null) {
      isFromDeepLink = true;
    }
    if(step1.showCompany != null){
      showCompany=true;
    }
    if(isFromDeepLink ==true||showCompany==true){
      editable=true
    }
    this.state = {
      firstName: "",
      lastName: "",
      companyName:isFromDeepLink?step1.memberDetail.Company.companyName: showCompany?step1.companyName:"",
      website: isFromDeepLink?step1.memberDetail.Company.website:showCompany?step1.website:"",
      street: isFromDeepLink?step1.memberDetail.Company.address:showCompany?step1.address:"",
      addressLine2:isFromDeepLink?step1.memberDetail.Company.secondAddress:showCompany?step1.secondAddress:"",
      country:isFromDeepLink?step1.memberDetail.Company.country:showCompany?step1.country: "",
      state: isFromDeepLink?step1.memberDetail.Company.state:showCompany?step1.state:"",
      city:isFromDeepLink?step1.memberDetail.Company.city:showCompany?step1.city: "",
      zipcode:isFromDeepLink?step1.memberDetail.Company.zipCode :showCompany?step1.zipCode:"",
      statelist: [],
      selectedState: "",
      citylist: [],
      selectedCity: [],
      countrylist: [],
      selectedCountry: [],
      showToaster: false,
      toastType: "error",
      toastMessage: "",
      showLoader: false,
      showAlert: false,
      stateVisible: false,
      cityVisible: false,
      predictionList: [],
      showAutoComplete: false,
    };

    this.submit = this.submit.bind(this);
  }

  componentDidMount() {
    this.setState({ showLoader: true });

    if (this.props.countrylist.length > 0) {
      this.setState({ showLoader: false });
      this.storeCountryList(this.props.countrylist);
    } else {
      this.setState({ showLoader: true });
    }
    this.getCountries();
    // this.getStateList()
  }

  hideToast = () => {
    setTimeout(() => {
      this.setState({
        showToaster: false,
      });
    }, 2000);
  };

  getCountries = () => {
    getCountryList(
      GET_COUNTRY,
      {},
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.countryList) {
            this.storeCountryList(response.data.countryList);
            this.props.countryList(response.data.countryList);
            // this.setState({countryData: response.data.countryList})
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };

  storeCountryList = (data) => {
    let list = [];

    for (let item of data) {
      list.push({
        id: item.id,
        name: item.countryName,
        code: item.countryDialCode,
      });
    }

    this.setState({ countrylist: list });
  };

  getStateList = (id) => {
    let url = GET_STATE + id;
    getStateList(
      url,
      {},
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.stateList) {
            this.storeStateList(response.data.stateList);
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };

  storeStateList = (data) => {
    let list = [];

    for (let item of data) {
      list.push({
        id: item.id,
        name: item.stateName,
      });
    }

    this.setState({ statelist: list, selectedState: [] });
  };

  //BACK PRESS
  onBackPress = () => {
    this.setState({
      companyName:"",
      website: "",
      street:"",
      addressLine2:"",
      country:"",
      state: "",
      city:"",
      zipcode:""
    })
    editable=false;
    showCompany=false;
    this.state={};
    this.props.navigation.goBack();
  };

  //ON CHANGE TEXT
  updateMasterState = (key, value) => {
    if (key == Strings.placeholders.firstname) {
      this.setState({ firstName: value });
    } else if (key == Strings.placeholders.lastname) {
      this.setState({ lastName: value });
    } else if (key == Strings.placeholders.companyName) {
      this.setState({ companyName: value });
    } else if (key == Strings.placeholders.companyWebsite) {
      this.setState({ website: value });
    } else if (key == Strings.placeholders.streetName) {
      this.setState({ street: value });
    }else if(key== Strings.placeholders.addressline2){
      this.setState({addressLine2:value})
    } 
    else if (key == Strings.placeholders.country) {
      this.setState({ country: value });
    } else if (key == Strings.placeholders.state) {
      this.setState({ state: value });
    } else if (key == Strings.placeholders.city) {
      this.setState({ city: value });
    } else if (key == Strings.placeholders.zipcode) {
      this.setState({ zipcode: value });
    }
  };

  validateWebsite(input) {
    var res = input.match(
      /(http(s)?:\/\/.)?(www\.)[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g
    );

    return res == null ? false : true;
  }

  //VALIDATE FIELDS
  validate = () => {
    if (isEmpty(this.state.firstName.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.enterFirst,
          toastType: "error",
        },
        () => this.hideToast()
      );
      return false;
    } else if (this.state.firstName.length < 3) {
      this.setState(
        {
          showToaster: true,
          toastMessage: "Name " + Strings.errors.lengthError,
          toastType: "error",
        },
        () => this.hideToast()
      );
      return false;
    } else if (isName(this.state.firstName.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.validFirst,
          toastType: "error",
        },
        () => this.hideToast()
      );
      return false;
    } else if (isEmpty(this.state.lastName.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.enterlast,
          toastType: "error",
        },
        () => this.hideToast()
      );
      return false;
    } else if (this.state.lastName.length < 3) {
      this.setState(
        {
          showToaster: true,
          toastMessage: "Name " + Strings.errors.lengthError,
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({ showToaster: false });
          }, 2000);
        }
      );
      return false;
    } else if (isName(this.state.lastName.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.validlast,
          toastType: "error",
        },
        () => this.hideToast()
      );
      return false;
     }
     // else if (isEmpty(this.state.companyName.trim())) {
    //   this.setState(
    //     {
    //       showToaster: true,
    //       toastType: "error",
    //       toastMessage: Strings.errors.emptyCompany,
    //     },
    //     () => this.hideToast()
    //   );
    //   return false;
    // } else if (
    //   this.state.companyName[0] == "&" ||
    //   isCompany(this.state.companyName.trim())
    // ) {
    //   this.setState(
    //     {
    //       showToaster: true,
    //       toastType: "error",
    //       toastMessage: Strings.errors.validCompany,
    //     },
    //     () => this.hideToast()
    //   );
    //   return false;
    // } else if (this.state.companyName.length < 3) {
    //   this.setState(
    //     {
    //       showToaster: true,
    //       toastType: "error",
    //       toastMessage: `Company name ${Strings.errors.minLength}`,
    //     },
    //     () => this.hideToast()
    //   );
    // } 
    //  else if (isEmpty(this.state.street.trim())&& !isFromDeepLink) {
    //   this.setState(
    //     {
    //       showToaster: true,
    //       toastType: "error",
    //       toastMessage: Strings.errors.emptyStreet,
    //     },
    //     () => this.hideToast()
    //   );
    //   return false;
    // } else if (this.state.street.length < 3 && !isFromDeepLink) {
    //   this.setState(
    //     {
    //       showToaster: true,
    //       toastType: "error",
    //       toastMessage: `Street name ${Strings.errors.minLength}`,
    //     },
    //     () => this.hideToast()
    //   );
    // } else if (isEmpty(this.state.country) && !isFromDeepLink) {
    //   this.setState(
    //     {
    //       showToaster: true,
    //       toastType: "error",
    //       toastMessage: Strings.errors.emptyCountry,
    //     },
    //     () => this.hideToast()
    //   );
    //   return false;
    // } else if (isEmpty(this.state.state) && !isFromDeepLink) {
    //   this.setState(
    //     {
    //       showToaster: true,
    //       toastType: "error",
    //       toastMessage: Strings.errors.emptyState,
    //     },
    //     () => this.hideToast()
    //   );
    //   return false;
    // } else if (isEmpty(this.state.city)&& !isFromDeepLink) {
    //   this.setState(
    //     {
    //       showToaster: true,
    //       toastType: "error",
    //       toastMessage: Strings.errors.emptyCity,
    //     },
    //     () => this.hideToast()
    //   );
    //   return false;
    // } else if (isEmpty(this.state.zipcode)&& !isFromDeepLink) {
    //   this.setState(
    //     {
    //       showToaster: true,
    //       toastType: "error",
    //       toastMessage: Strings.errors.emptyZipcode,
    //     },
    //     () => this.hideToast()
    //   );
    //   return false;
    // }else if (this.state.website!='') {
    //   if (this.validateWebsite(this.state.website) == false && !isFromDeepLink) {
    //   this.setState(
    //     {
    //       showToaster: true,
    //       toastType: "error",
    //       toastMessage: Strings.errors.validWebsite,
    //     },
    //     () => this.hideToast()
    //   );
    //   return false;
    //   }
    //   else{
    //     return true;
    //   }
    // }
     else {
      return true;
    }
  };

  //NEXT CLICK
  nextClick = () => {

    if (this.validate()) {
      let step1 = this.props.route?.params?.step1;
      let step2 = {
        fullName: this.state.firstName,
        lastName: this.state.lastName,
        companyname: this.state.companyName,
        website: this.state.website,
        street: this.state.street,
        secondAddress:this.state.addressLine2,
        country: this.state.country,
        state: this.state.state,
        city: this.state.city,
        zipcode: this.state.zipcode,
      };

      let data = {
        step1: step1,
        step2: step2,
      };
      this.props.navigation.navigate("Step3", { step2: data });
    }
 
  };

  submitEditing = (type) => {};

  onPressState = (item) => {
    this.setState(
      {
        selectedState: item,
        stateVisible: false,
        state: item.name,
        showLoader: true,
      },
      () => {
        this.getCityList();
      }
    );
  };

  onPressCountry = (item) => {
    this.setState(
      {
        selectedCountry: item,
        countryVisible: false,
        country: item.name,
        showLoader: true,
      },
      () => {
        this.getStateList(item.id);
      }
    );
  };

  getCityList = () => {
    getCityList(
      GET_CITY + this.state.selectedState.id,
      {},
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.cityList) {
            this.storeCityList(response.data.cityList);
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };

  storeCityList = (data) => {
    let list = [];

    for (let item of data) {
      list.push({
        id: item.id,
        name: item.cityName,
      });
    }
    this.setState({ citylist: list, selectedCity: [] });
  };

  onPressCity = (item) => {
    this.setState({ selectedCity: item, cityVisible: false, city: item.name });
  };

  onPressCityDrop = () => {
    if (this.state.selectedState.id) {
      this.setState({ cityVisible: true });
    } else {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.selectState,
          toastType: "error",
        },
        () => this.hideToast()
      );
    }
  };

  onPressStateDrop = () => {
    if (this.state.selectedCountry.id) {
      this.setState({ stateVisible: true });
    } else {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.selectCountry,
          toastType: "error",
        },
        () => this.hideToast()
      );
    }
    // this.setState({stateVisible: true})
  };

  bottomContainer = () => {
    return (
      <View style={styles.bottomContainer}>
        <TouchableOpacity onPress={() => this.props.navigation.goBack()}>
          <View style={styles.cancel}>
            <Text style={styles.cancelText}>{Strings.addMember.cancel}</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            this.submit();
          }}
        >
          <View style={styles.submit}>
            <Text style={styles.submitText}>{Strings.addMember.submit}</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  submit = async () => {
    if (this.validate()) {
      let step1 = this.props.route?.params?.step1;
      let step2 = {
        fullName: this.state.firstName,
        lastName: this.state.lastName,
        companyname: this.state.companyName,
        website: this.state.website,
        street: this.state.street,
        secondAddress:this.state.addressLine2,
        country: this.state.country,
        state: this.state.state,
        city: this.state.city,
        zipcode: this.state.zipcode,
      };
      let data = {
        step1: step1,
        step2: step2,
      };

      let ParamCheck = {
        memberDetail: {
          UserId: data.step1.userDetail.id,
          id: data.step1.memberDetail.id,
          RoleId: data.step1.memberDetail.RoleId,
          ParentCompanyId: data.step1.memberDetail.ParentCompanyId,
          email: data.step1.email,
          firstName: data.step2.fullName,
          lastName: data.step2.lastName,
          password: data.step1.password,
          phoneNumber: data.step1.phoneNumber,
          phoneCode: data.step1.phoneCode,
          ProjectId: data.step1.memberDetail.ProjectId,
          action: "onboarding",
          status: "completed",
        },
        companyDetail: {
          companyName: data.step2.companyname,
          companyId:step1.memberDetail.Company.id,
          fullName: data.step2.fullName,
          lastName: data.step2.lastName,
          isParent: true,
          address: data.step2.street,
          secondAddress:data.step2.secondAddress,
          country: data.step2.country,
          city: data.step2.city,
          state: data.step2.state,
          website: data.step2.website,
          zipCode: data.step2.zipcode,
        },
        domainName: data.step1.domainName,
        requestType: 1,
      };

      this.setState({ showLoader: true });
      updateMember(
        UPDATE_MEMBER,
        ParamCheck,
        () => null,
        (response) => { 
          this.setState({ showLoader: false });
          if (
            response.toString() == Strings.errors.timeout ||
            response.toString() == "Error: Network Error"
          ) {
            this.setState(
              {
                showToaster: true,
                toastMessage: Strings.errors.checkInternet,
                toastType: "error",
              },
              () => this.hideToast()
            );
          } else if (response.status) {
            if (response.status == 200 || response.status == 201) {
              if (response.status == 200 || response.status == 201) {
                this.setState(
                  {
                    showToaster: true,
                    toastMessage: response.data.message.toString(),
                    toastType: "err",
                  },
                  () => {
                    setTimeout(() => {
                      this.setState({ showToaster: false });

                      const resetAction = CommonActions.reset({
                        index: 0,
                        routes: [{ name: "Dashboard" }],
                      });
                      this.props.navigation.dispatch(resetAction);
                    }, 2000);
                  }
                );
              } else {
                this.setState(
                  {
                    showToaster: true,
                    toastMessage: response.data.message.toString(),
                    toastType: "error",
                  },
                  () => this.hideToast()
                );
              }
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.toString(),
                  toastType: "error",
                },
                () => this.hideToast()
              );
            }
          }
        }
      );
    }
  };

  onSelectPlace = (place) => {
    const { terms, structured_formatting } = place;
     let city="";
    let mainText = structured_formatting.main_text;
    let secondaryText = structured_formatting.secondary_text;
    let street = `${mainText}`;
    let country = terms[terms.length - 1].value;
    let state =
      terms[terms.length - 2].value != undefined &&
      terms[terms.length - 2].value;
    if(terms.length>2){
       city =
      terms[terms.length - 3].value != undefined &&
      terms[terms.length - 3].value;
    }else{
       city="";
    }
   
    this.setState({
      street: street,
      country: country,
      state: state,
      city: city,
      showAutoComplete: false,
    });
  };

  getPlacePrediction = async (keyword) => {
    let showPlaces = keyword.toString().length > 0 ? true : false;
    
    let placeURL = googleAutoCompleteAPI(API_KEY, keyword);
    let placeResponse = await axios.get(placeURL);
    this.setState({
      showAutoComplete: showPlaces,
      predictionList: placeResponse.data.predictions,
    });
  };

  //Main Render method
  render() {
    return (
      <AppView>
        <KeyboardAwareScrollView
          extraScrollHeight={100}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.parentContainer}>
            <HeaderAnimation />
            <View style={styles.subContainer}>
              <AnimatableView animation={"bounceInLeft"} duration={800}>
                <AnimatableImage
                  animation={rotation}
                  duration={800}
                  source={Images.path2}
                  style={styles.path}
                />
              </AnimatableView>

              <AnimatableView
                animation={"fadeInUpBig"}
                duration={500}
                style={styles.signupContainer}
              >
                <Header
                  backPress={() => this.onBackPress()}
                  title={Strings.step2.company}
                />

                <View style={{ flex: 1 }}>
                  <TextField
                    attrName={Strings.placeholders.firstname}
                    title={Strings.placeholders.firstname}
                    value={this.state.firstName}
                    maxLength={50}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    onSubmitEditing={() => {
                      this.submitEditing("firstName");
                    }}
                  />

                  <TextField
                    attrName={Strings.placeholders.lastname}
                    title={Strings.placeholders.lastname}
                    value={this.state.lastName}
                    maxLength={50}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                    onSubmitEditing={() => {
                      this.submitEditing("lastName");
                    }}
                  />

                  <TextField
                    editable={!editable}
                    attrName={Strings.placeholders.companyName}
                    title={Strings.placeholders.companyName}
                    value={this.state.companyName}
                    maxLength={50}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: editable == true ? "#99A3A4" : Colors.black,
                      fontSize: 14,
                    }}
                    onSubmitEditing={() => {
                      this.submitEditing("companyName");
                    }}
                  />
                 {isFromDeepLink == false && (<>
                  <TextField
                    editable={!editable}
                    attrName={Strings.placeholders.companyWebsite}
                    title={Strings.placeholders.companyWebsite}
                    value={this.state.website}
                    autoCapitalize="none"
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: editable == true ? "#99A3A4" : Colors.black,
                      fontSize: 14,
                    }}
                  />

                  {/* AutoComplete Google Places  */}

                  <TextField
                   editable={!editable}
                    attrName={Strings.placeholders.streetName}
                    title={Strings.placeholders.streetName}
                    value={this.state.street}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                      this.getPlacePrediction(value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: editable == true ? "#99A3A4" : Colors.black,
                      fontSize: 14,
                    }}
                  />

                  {this.state.showAutoComplete && (
                    <AutoCompleteList
                      source={this.state.predictionList}
                      onSelectPlace={(place) => this.onSelectPlace(place)}
                    />
                  )}
                  <TextField
                     editable={!editable}
                    attrName={Strings.placeholders.addressline2}
                    title={Strings.placeholders.addressline2}
                    value={this.state.addressLine2}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    // showButton={true}
                    // imageSource={Images.downArr}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color:  editable == true ? "#99A3A4" : Colors.black,
                      fontSize: 14,
                    }}
                    onPress={() => {
                      //this.onPressStateDrop();
                    }}
                  />
                  <TextField
                    editable={!editable}
                    ref={this.txt_country}
                    attrName={Strings.placeholders.country}
                    title={Strings.placeholders.country}
                    value={this.state.country}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                   // onContentSizeChange={()=>{this.setState({country:""})}}
                    // showButton={true}
                    // imageSource={Images.downArr}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color:  editable== true ? "#99A3A4" : Colors.black,
                      fontSize: 14,
                    }}
                    onPress={() => {
                      // this.setState({ countryVisible: true });
                    }}
                  />

                  <TextField
                     editable={!editable}
                    attrName={Strings.placeholders.state}
                    title={Strings.placeholders.state}
                    value={this.state.state}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    // showButton={true}
                    // imageSource={Images.downArr}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color:  editable == true ? "#99A3A4" : Colors.black,
                      fontSize: 14,
                    }}
                    onPress={() => {
                      //this.onPressStateDrop();
                    }}
                  />

                  <TextField 
                    editable={!editable}
                    attrName={Strings.placeholders.city}
                    title={Strings.placeholders.city}
                    value={this.state.city}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    // showButton={true}
                    // imageSource={Images.downArr}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: editable == true ? "#99A3A4" : Colors.black,
                      fontSize: 14,
                    }}
                    onPress={() => {
                      //  this.onPressCityDrop();
                    }}
                  />

                  <TextField
                     editable={!editable}
                    attrName={Strings.placeholders.zipcode}
                    title={Strings.placeholders.zipcode}
                    value={this.state.zipcode}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    maxLength={10}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: editable == true ? "#99A3A4" : Colors.black,
                      fontSize: 14,
                    }}
                  />
                 </>)}

                  {isFromDeepLink == false && (
                    <NextButton nextClick={() => this.nextClick()} />
                  )}

                  {isFromDeepLink == true && this.bottomContainer()}

                  <View style={{ flex: 1, justifyContent: "flex-end" }}>
                    <Steps page={" 2"} />
                  </View>
                </View>
              </AnimatableView>
            </View>
          </View>
        </KeyboardAwareScrollView>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.forgotSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}

        <Dropdown
          data={this.state.countrylist}
          title={Strings.addCompany.chooseCountry}
          value={this.state.country}
          closeBtn={() => this.setState({ countryVisible: false })}
          onPress={(item) => this.onPressCountry(item)}
          visible={this.state.countryVisible}
          onbackPress={() => this.setState({ countryVisible: false })}
        />

        <Dropdown
          data={this.state.statelist}
          title={Strings.addCompany.chooseState}
          value={this.state.state}
          closeBtn={() => this.setState({ stateVisible: false })}
          onPress={(item) => this.onPressState(item)}
          visible={this.state.stateVisible}
          onbackPress={() => this.setState({ stateVisible: false })}
        />

        <Dropdown
          data={this.state.citylist}
          title={Strings.addCompany.chooseCity}
          value={this.state.city}
          closeBtn={() => this.setState({ cityVisible: false })}
          onPress={(item) => this.onPressCity(item)}
          visible={this.state.cityVisible}
          onbackPress={() => this.setState({ cityVisible: false })}
        />
      </AppView>
    );
  }
}

const mapStateToProps = (state) => {
  const { userid, countrylist } = state.LoginReducer;

  return {
    userid,
    countrylist,
  };
};

export default connect(mapStateToProps, {
  storeUserid,
  countryList,
})(Step2);

const styles = StyleSheet.create({
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  subContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  signupContainer: {
    flex: 1,
    width: wp("100%"),
    borderTopLeftRadius: hp("6%"),
    borderTopRightRadius: hp("6%"),
    zIndex: 999,
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0.8,
    shadowOffset: { width: 10, height: 10 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  path: {
    width: wp("70%"),
    height: hp("58%"),
    marginBottom: -hp("33%"),
    marginRight: -wp("26%"),
    alignSelf: "flex-end",
  },
  bottomContainer: {
    width: wp("90%"),
    flexDirection: "row",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: hp("8%"),
    marginTop: hp("2%"),
    // backgroundColor: Colors.white
  },
  cancel: {
    width: wp("35%"),
    height: hp("6%"),
    backgroundColor: Colors.shadowColor,
    marginRight: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  submit: {
    width: wp("35%"),
    height: hp("6%"),
    backgroundColor: Colors.themeOpacity,
    marginLeft: wp("3%"),
    borderRadius: hp("3.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: "#757575",
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
  submitText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("4%"),
  },
});

