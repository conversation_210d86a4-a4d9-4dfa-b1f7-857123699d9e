import React, { Component } from "react";
import { View, StyleSheet, Platform, Dimensions,Text } from "react-native";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import { storeUserid, countryList , afterProjectCreated} from "../../actions/postAction";
import {
  Image as AnimatableImage,
  View as AnimatableView,
} from "react-native-animatable";
import * as RNLocalize from "react-native-localize";
const deviceWidth = Dimensions.get("window").width;
const deviceHeight = Dimensions.get("window").height;

import {
  AppView,
  AppLoader,
  HeaderAnimation,
  Header,
  TextField,
  NextButton,
  Steps,
  Alert,
  Toastpopup,
  Dropdown,
  Map,
} from "../../components";

import { SIGN_UP, GET_COUNTRY, CREATE_PROJECT } from "../../api/Constants";
import { signup, getCountryList, addProject } from "../../api/Api";
import { isEmpty, Colors, Images, Strings,Fonts } from "../../common/";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Modal from "react-native-modal";

const reverseRotation = {
  from: {
    rotate: "-180deg",
  },
  to: {
    rotate: "0deg",
  },
};

class Step3 extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showLoader: false,
      showToaster: false,
      showAlert: false,
      toastType: "error",
      toastMessage: "",
      projectName: "",
      location: "",
      countryData: [],
      selectedCountry: [],
      country: "",
      showStep: true,
      switch: false,
      desc: "",
      showMap: false,
    };
  }

  componentDidMount() {
    if (this.props.route?.params?.from == "menu") {
      this.setState({ showStep: false });
    }

    if (this.props.countrylist.length > 0) {
      this.storeCountryList(this.props.countrylist);
    } else {
      this.setState({ showLoader: true });
      this.getCountry();
    }
  }

  hideToast = (timeout = 2000) => {
    setTimeout(() => {
      this.setState({
        showToaster: false,
      });
    }, timeout);
  };

  getCountry = () => {
    getCountryList(
      GET_COUNTRY,
      {},
      () => null,
      (response) => {
        this.setState({ showLoader: false });
        if (response.status) {
          if (response.data.countryList) {
            this.storeCountryList(response.data.countryList);
            this.props.countryList(response.data.countryList);
            // this.setState({countryData: response.data.countryList})
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };

  storeCountryList = (data) => {
    let list = [];

    for (let item of data) {
      list.push({
        id: item.id,
        name: item.countryName,
        // code: data[i].countryDialCode
      });
    }

    this.setState({
      countryData: list,
      countryCode: list[0].code,
      selectedCountry: list[0],
    });
  };

  onPressCountry = (item) => {
    this.setState({
      countryCode: item.code,
      selectedCountry: item,
      countryVisible: false,
      country: item.name,
    });
  };

  onBackPress = () => {
    this.props.navigation.goBack();
  };

  updateMasterState = (key, value) => {
    this.setState({ projectName: value });
  };

  /* Sign Up happens here */
  /* 
  Get all params for Signup API calls here
  */
  nextClick = async () => {

    // if (!this.state.showStep) {
    //   /**
    //    * If user navigates from menu screen
    //    * showStep will be false. Which means 
    //    * we can call add project function only
    //    */

    //   this.addProject()
    //   return
    // }


    if (isEmpty(this.state.projectName.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyProject,
          toastType: "error",
        },
        () => this.hideToast()
      );
    } else if (this.state.projectName.length < 3) {
      this.setState(
        {
          showToaster: true,
          toastMessage: "Project name " + Strings.errors.lengthError,
          toastType: "error",
        },
        () => this.hideToast()
      );
    } else if (this.state.projectName.length < 2) {
      this.setState(
        {
          showToaster: true,
          toastType: "error",
          toastMessage: `Project name ${Strings.errors.minLength}`,
        },
        () => this.hideToast()
      );
    } else if (isEmpty(this.state.country.trim())) {
      this.setState(
        {
          showToaster: true,
          toastMessage: Strings.errors.emptyLocation,
          toastType: "error",
        },
        () => this.hideToast()
      );
    }else if (!this.state.showStep) {
      /**
       * If user navigates from menu screen
       * showStep will be false. Which means 
       * we can call add project function only
       */

      this.addProject()
      return
    } 
    else {
      let step2 = this.props.route?.params?.step2;
      let step3 = {
        projectName: this.state.projectName,
        location: this.state.country,
      };
      let data = [];
      if (this.state.showStep == true) {
        data = {
          step1: step2.step1,
          step2: step2.step2,
          step3: step3,
          showStep: true,
        };
      } else {
        data = {
          step3: step3,
          showStep: false,
        };
      }

      let ParamCheck = {
        basicDetails: {
          email: step2.step1.email,
          phoneNumber: step2.step1.mobile,
          phoneCode: step2.step1.countryCode,
        },
        companyDetails: {
          companyName: step2.step2.companyname,
          fullName: step2.step2.fullName,
          lastName: step2.step2.lastName,
          scope: "site",
          isParent: true,
          address: step2.step2.street,
          secondAddress:step2.step2.secondAddress,
          country: step2.step2.country,
          city: step2.step2.city,
          state: step2.step2.state,
          website: step2.step2.website,
          zipCode: step2.step2.zipcode,
        },
        projectDetails: {
          projectName: this.state.projectName,
          projectLocation: this.state.country,
        },
        planData: {
          //By default passing trial plan id - 1
          id: 1,
        },
      };
      this.setState({
        showLoader: true,
      });

      await signup(
        SIGN_UP,
        ParamCheck,
        () => null,
        (response) => {
          this.setState({ showLoader: false });
          if (response.status) {
            if (response.status==201) {
              this.props.navigation.navigate("SubscriptionThanks");
            } else if (response.data.message == Strings.popup.contaceSales) {
              this.setState({
                showAlert: true,
                desc: Strings.popup.contaceSales,
              });
            } else if (response.data.message.message) {
              let array = Object.values(response.data.message.details[0]);
              this.setState(
                {
                  showToaster: true,
                  toastMessage: array.toString(),
                  toastType: "error",
                },
                () => {
                  setTimeout(() => {
                    this.setState({
                      showToaster: false,
                    });
                  }, 3500);
                }
              );
            } else {
              this.setState(
                {
                  showToaster: true,
                  toastMessage: response.data.message,
                  toastType: "error",
                },
                () => this.hideToast()
              );
            }
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.toString(),
                toastType: "error",
              },
              () => this.hideToast()
            );
          }
        }
      );
    }


  };

  addProject = () => {
    let data = {
      firstName: this.props.userDetails.firstName,
      email: this.props.userDetails.email,
      phoneNumber: this.props.userDetails.phoneNumber,
      projectName: this.state.projectName,
      projectLocation: this.state.country,
      PlanId: 1,
      existcard: false,
      ParentCompanyId: this.props.projectDetails.ParentCompany.id,
      timezone:RNLocalize.getTimeZone(),
    };
    this.setState({ showLoader: true });

    addProject(
      CREATE_PROJECT,
      data,
      () => null,
      (response) => {
        if (__DEV__) {
          this.setState({ showLoader: false });
        }
        if (response.status) {
          if (response.data.message.message) {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          } else if (response.data.message) {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType:
                  response.data.message == "Project Created Successfully."
                    ? "success"
                    : "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                  this.props.afterProjectCreated(true)
                  this.props.navigation.goBack();
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };


  onSelectLocation = () => {
    this.setState({ showMap: true });
  /*   if (Platform.OS == "android") {
      this.setState({ showMap: true });
    } else {
      this.setState({ countryVisible: true });
    } */
  };

  //Main Render method
  render() {
    return (
      <AppView>
        <KeyboardAwareScrollView
          extraScrollHeight={50}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.parentContainer}>
            <HeaderAnimation />

            <View style={styles.subContainer}>
              <AnimatableView animation={"bounceInRight"} duration={1300}>
                <AnimatableImage
                  animation={reverseRotation}
                  duration={1300}
                  source={Images.path}
                  style={styles.path}
                />
              </AnimatableView>

              <View style={styles.signupContainer}>
                <Header
                  backPress={() => this.onBackPress()}
                  title={Strings.step3.projectDetails}
                />

                <View style={{ flex: 1 }}>
                  <TextField
                    attrName={Strings.placeholders.ProjectName}
                    title={Strings.placeholders.ProjectName}
                    value={this.state.projectName}
                    maxLength={150}
                    updateMasterState={(key, value) => {
                      this.updateMasterState(key, value);
                    }}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                    }}
                  />
                   <Text  style={[styles.pinText]}>{Strings.placeholders.location}</Text>

                  <TextField
                    attrName={Strings.placeholders.location}
                   // title={Strings.placeholders.location}
                    value={this.state.country}
                    multiline={true}
                    // updateMasterState={(key, value) => {
                    //   this.updateMasterState(key, value);
                    // }}
                    showButton={true}
                    imageSource={Images.map}
                    container={{height: Platform.OS == "android" ?hp("11%"):hp("9.5%"),}}
                    textInputStyles={{
                      // here you can add additional TextInput styles
                      color: Colors.black,
                      fontSize: 14,
                      height: Platform.OS == "android" ? hp('7.5%') :hp('6%'),
                      textAlignVertical:'bottom',
                      //padding: Platform.OS == "android" ? 5 : 0
                    }}
                    onPress={() => {
                      this.onSelectLocation();
                      // this.setState({ countryVisible: true });
                    }}
                    //textTitleStyles={{marginBottom:40,}}
                  />

                  <NextButton
                    nextClick={() => this.nextClick()}
                    title={
                      this.state.showStep == true
                        ? Strings.step3.signUp
                        : Strings.step3.next
                    }
                  />

                  {this.state.showStep == true && (
                    <View style={{ flex: 1, justifyContent: "flex-end" }}>
                      <Steps page={" 3"} />
                    </View>
                  )}
                </View>
              </View>
            </View>
          </View>
        </KeyboardAwareScrollView>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.forgotSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}

        <Dropdown
          data={this.state.countryData}
          title={Strings.addCompany.chooseLocation}
          value={this.state.country}
          closeBtn={() => this.setState({ countryVisible: false })}
          onPress={(item) => this.onPressCountry(item)}
          visible={this.state.countryVisible}
          onbackPress={() => this.setState({ countryVisible: false })}
        />

        <Modal
          style={{ margin: 0, }}
          hasBackdrop={false}
          isVisible={this.state.showMap}
          animationIn="bounceInUp"
          animationOut="bounceOutDown"
        >
          <Map
            initialLocation={ this.state.country}
            backPress={() => {
              this.setState({
                showMap: false,
              });
            }}
            pressContinue={(location) => {
              this.setState({
                country: location,
                showMap: false,
              });
            }}
            
          />
        </Modal>
      </AppView>
    );
  }
}

const mapStateToProps = (state) => {
  const { userid, countrylist, userDetails,projectDetails } = state.LoginReducer;

  return {
    userid,
    countrylist,
    userDetails,
    projectDetails,
  };
};

export default connect(mapStateToProps, {
  storeUserid,
  countryList,
  afterProjectCreated,
})(Step3);

const styles = StyleSheet.create({
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  subContainer: {
    flex: 1,
    justifyContent: "flex-end",
    height: hp("85%"),
  },
  signupContainer: {
    width: wp("100%"),
    height: hp("50%"),
    borderTopLeftRadius: hp("6%"),
    borderTopRightRadius: hp("6%"),
    zIndex: 999,
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderWidth: Platform.OS == "ios" ? 1 : 0.8,
    shadowOffset: { width: 10, height: 10 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  pinText: {
    color: Colors.themeColor,
    fontSize: 15,
    fontFamily: Fonts.montserratSemiBold,
     marginLeft:Platform.OS == "android" ?hp('3.4%'):hp('2.5%'),
     marginBottom:hp('-3%')
  },
  path: {
    width: wp("70%"),
    height: hp("25%"),
    marginBottom: -hp("3.5%"),
   marginLeft: -wp("25%"),
  },
});
