import React, { Component } from "react";
import {
  View,
  StyleSheet,
  Text,
  Platform,
  Switch,
  TouchableWithoutFeedback,
  TouchableOpacity,
} from "react-native";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";

import {
  AppView,
  App<PERSON>oader,
  Header,
  Steps,
  Alert,
  Toastpopup,
} from "../../components";

import { Colors, Fonts, Strings } from "../../common";

import { storeUserid } from "../../actions/postAction";
import Swiper from "react-native-swiper";

import { SIGN_UP,CREATE_PROJECT } from "../../api/Constants";
import { signup,addProject} from "../../api/Api";
import { CommonActions } from '@react-navigation/native';


class Step4 extends Component {
  constructor(props) {
    super(props);
    this.state = {
      switch: false,
      pagArr: [1, 2, 3],
      slider: 0,
      showToaster: false,
      toastMessage: "",
      toastType: "",
      showAlert: false,
      showLoader: false,
      projectItem: [],
      upgradeTo: "",
      showSwitch: true,
    };
  }

  componentDidMount() {
    let data = this.props.route?.params?.step3;
    if (data.showStep == true) {
      this.setState({ showStep: true });
    } else {
      this.setState({ showStep: false });
    }

    if (this.props.route?.params?.from == "upgradePlan") {
      let item = data.projectItem;
      let upgradeto = "";
      let pageArr = [];
      let showSwitch = true;
      let switchenable = false;
      if (item.stripePlan.stripeProductName == "Project Plan") {
        if (item.stripePlan.stripePlanName == "yearly") {
          switchenable = true;
          //      this.setState({switch:true})
        }

        upgradeto = "trail";
        pageArr = [1, 2];
   
      } else if (item.stripePlan.stripeProductName == "Trial Plan") {
        pageArr = [1, 2];
        upgradeto = "trail";
      }
      this.setState({
        UpgradeData: item,
        upgradePlan: true,
        upgradeTo: upgradeto,
        pagArr: pageArr,
        showSwitch: showSwitch,
        switch: switchenable,
      });
    } else {
      this.setState({ upgradePlan: false, upgradeTo: "", pagArr: [1, 2, 3] });
    }
  }

  hideToast = () => {
    setTimeout(() => {
      this.setState({
        showToaster: false,
      });
    }, 2000);
  }

  //on back press
  onBackPress = () => {
    this.props.navigation.goBack();
  };

  //GO TO CREDIT CARD PAGE
  onPressGetStart = (text1) => {
    if (text1 === "Pro") {
      let step4 = this.props.route?.params?.step3;
      let data = {
        step4: step4,
        switch: this.state.switch,
        showStep: this.state.showStep,
        from: this.props.route?.params?.from,
        upgradePlan: this.state.upgradePlan,
        projectData: this.state.projectItem,
      };
      this.props.navigation.navigate("CreditCard", { step4: data });
    } else {
      if (text1 === "Tri") {
        if (this.state.showStep == true) {
          this.doSignup(1);
        } else {
          this.addProject();
        }
      } else if (text1 === "Ent") {
        this.setState({ showAlert: true, desc: Strings.popup.contaceSales });


      }
    }
  };

  addProject = () => {
    let param = this.props.route?.params?.step3;
    let data = {
      firstName: this.props.userDetails.firstName,
      email: this.props.userDetails.email,
      phoneNumber: this.props.userDetails.phoneNumber,
      projectName: param.step3.projectName,
      projectLocation: param.step3.location,
      PlanId: 1,
      existcard: false,
    };

    this.setState({ showLoader: true });

    addProject(
      CREATE_PROJECT,
      data,
      () => null,
      (response) => {
        if (__DEV__) {
          this.setState({ showLoader: false });
        }
        // this.setState({showLoader: false})
        if (response.status) {
          if (response.data.message.message) {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message.message,
                toastType: "error",
              },
              () => this.hideToast()
            );
          } else if (response.data.message) {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType:
                  response.data.message == "Project Created Successfully."
                    ? "success"
                    : "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                  this.props.navigation.navigate("Menu");
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => this.hideToast()
          );
        }
      }
    );
  };

  //SIGNUP
  doSignup = async (plan) => {
    let data = this.props.route?.params?.step3;
    this.setState({ showLoader: true });

    let ParamCheck = {
      basicDetails: {
        email: data.step1.email,
        phoneNumber: data.step1.mobile,
        phoneCode: data.step1.countryCode,
      },
      companyDetails: {
        companyName: data.step2.companyname,
        fullName: data.step2.fullName,
        lastName: data.step2.lastName,
        scope: "site",
        isParent: true,
        address: data.step2.street,
        country: data.step2.country,
        city: data.step2.city,
        state: data.step2.state,
        website: data.step2.website,
        zipCode: data.step2.zipcode,
      },
      projectDetails: {
        projectName: data.step3.projectName,
        projectLocation: data.step3.location,
      },
      planData: {
        id: plan,
      },
    };

    await signup(
      SIGN_UP,
      ParamCheck,
      () => {},
      (response) => {
        this.setState({ showLoader: false });

        if (response.status) {
          if (
            response.data.message == "Registered Successfully." ||
            response.data.message == "Email/Mobile Number already exist"
          ) {
            this.props.navigation.navigate("SubscriptionThanks");
          } else if (response.data.message == Strings.popup.contaceSales) {
            this.setState({
              showAlert: true,
              desc: Strings.popup.contaceSales,
            });
          } else if (response.data.message.message) {
            let array = Object.values(response.data.message.details[0]);
            this.setState(
              {
                showToaster: true,
                toastMessage: array.toString(),
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 3500);
              }
            );
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
  };

  // RENDER PLAN VIEW
  planContainer = (text1, text2, desc, amount, getStarted) => {
    let color = "#FFF";

    if (this.props.route?.params?.from == "upgradePlan") {
      if (
        text1 == "Pro" &&
        this.state.UpgradeData &&
        this.state.UpgradeData.stripePlan.stripeProductName == "Project Plan"
      ) {
        if (
          !this.state.switch &&
          this.state.UpgradeData.stripePlan.stripePlanName == "monthly"
        ) {
          color = "#add8e6";
        } else if (
          this.state.switch &&
          this.state.UpgradeData.stripePlan.stripePlanName == "yearly"
        ) {
          color = "#add8e6";
        }
      }
    }
    return (
      <View style={styles.inner1}>
        <View style={[styles.inner2, { backgroundColor: color }]}>
          <View style={styles.inner3}>
            <View style={styles.round}>
              <Text style={styles.ciruclarText}>{text1}</Text>
            </View>
            <View style={styles.genTextContainer}>
              <Text style={styles.genText}>{text2}</Text>
            </View>
          </View>

          <View style={{ justifyContent: "center", alignItems: "center" }}>
            <Text style={styles.description}>{desc}</Text>
          </View>

          {text1 !== "Ent" && (
            <Text style={styles.cost}>
              {text1 == "Tri"
                ? "Free"
                : this.state.switch == true
                ? "999 $"
                : "49 $"}
            </Text>
          )}
          <TouchableWithoutFeedback
            onPress={() => {
              this.onPressGetStart(text1);
            }}
          >
            <View style={styles.getStarted}>
              <Text style={styles.startedText}>{getStarted}</Text>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </View>
    );
  };

  //ON TAP PAGINATION DOT
  tapDot = (index) => {
    this.refs.swiper.scrollTo(index - 1);
    this.setState({ slider: index - 1 });
  };

  //RENDER PAGINATION
  renderPagination = (page, total, context) => {
    return (
      <View
        style={{
          alignItems: "center",
          left: 0,
          right: 0,
          position: "absolute",
          bottom: 50,
          flexDirection: "row",
          justifyContent: "center",
        }}
      >
        {this.state.pagArr.map((index) => {
          return (
            <TouchableOpacity
              onPress={() => {
                this.tapDot(index);
              }}
              key={index.toString()}
              style={{
                backgroundColor:
                  this.state.slider + 1 == index ? "#B1B1B1" : "#FFF",
                width: wp("4%"),
                borderWidth: this.state.slider + 1 == index ? 0 : 1,
                borderColor: "#B1B1B1",
                borderRadius: wp("2%"),
                height: wp("4%"),
                marginLeft: 10,
              }}
            ></TouchableOpacity>
          );
        })}
        {/* <View
          style={{
            borderRadius: 7,
            padding: 3,
            paddingHorizontal: 7,
          }}>
          <Text
            style={{
              color: '#fff',
              fontSize: 14,
            }}>
            {index + 1} / {total}
          </Text>
        </View> */}
      </View>
    );
  };

  okTap = () => {
    if (this.state.showStep == true) {
      const resetAction = CommonActions.reset({
        index: 0,
        routes: [{ name: "Dashboard" }],
      });
      this.props.navigation.dispatch(resetAction);
    } else {
      this.setState({ showAlert: false });
    }
  };

  //Main Render method
  render() {
    return (
      <AppView>
        <View style={styles.parentContainer}>
          <Header backPress={() => this.onBackPress()} />

          <Text style={styles.choose}>{Strings.step4.choose}</Text>

          <View style={styles.planContainer}>
            <View style={styles.swithContainer}>
              {this.state.slider == 1 && this.state.upgradePlan == false && (
                <View style={styles.swithContainer}>
                  <Text
                    style={
                      this.state.switch == false
                        ? styles.boldMonth
                        : styles.lightMonth
                    }
                  >
                    {Strings.step4.month}
                  </Text>
                  <Switch
                    style={
                      Platform.OS == "android"
                        ? styles.androidSwitchStyle
                        : styles.iosSwitchStyle
                    }
                    trackColor={{ false: "#000", true: "#000" }}
                    thumbColor={"#fff"}
                    ios_backgroundColor="#3e3e3e"
                    onValueChange={() =>
                      this.setState({ switch: !this.state.switch })
                    }
                    value={this.state.switch}
                  />
                  <Text
                    style={
                      this.state.switch == true
                        ? styles.boldMonth
                        : styles.lightMonth
                    }
                  >
                    {Strings.step4.year}
                  </Text>
                </View>
              )}

              {this.state.showSwitch == true && (
                <View>
                  {this.state.upgradePlan == true &&
                    this.state.upgradeTo == "trail" &&
                    this.state.slider == 0 && (
                      <View style={styles.swithContainer}>
                        <Text
                          style={
                            this.state.switch == false
                              ? styles.boldMonth
                              : styles.lightMonth
                          }
                        >
                          {Strings.step4.month}
                        </Text>
                        <Switch
                          style={{
                            transform: [{ scaleX: 0.6 }, { scaleY: 0.6 }],
                          }}
                          trackColor={{ false: "#000", true: "#000" }}
                          thumbColor={"#fff"}
                          ios_backgroundColor="#3e3e3e"
                          onValueChange={() =>
                            this.setState({ switch: !this.state.switch })
                          }
                          value={this.state.switch}
                        />
                        <Text
                          style={
                            this.state.switch == true
                              ? styles.boldMonth
                              : styles.lightMonth
                          }
                        >
                          {Strings.step4.year}
                        </Text>
                      </View>
                    )}
                </View>
              )}
            </View>

            {this.state.upgradePlan == false && (
              <Swiper
                ref="swiper"
                style={styles.wrapper}
                onIndexChanged={(slider) => this.setState({ slider: slider })}
                loop={false}
                renderPagination={(item, index, context) =>
                  this.renderPagination(item, index, context)
                }
              >
                <View style={{ flex: 1, alignItems: "flex-end" }}>
                  {this.planContainer(
                    "Tri",
                    "al Plan",
                    Strings.step4.trailDes,
                    "Free",
                    "Get Started"
                  )}
                </View>
                <View style={{ flex: 1, alignItems: "flex-end" }}>
                  {this.planContainer(
                    "Pro",
                    "ject Plan",
                    Strings.step4.projectDes,
                    this.state.projectAmount,
                    "Get Started"
                  )}
                </View>
                <View style={{ flex: 1, alignItems: "flex-end" }}>
                  {this.planContainer(
                    "Ent",
                    "erprise Plan",
                    Strings.step4.enterpriseDes,
                    "",
                    "Contact Sales"
                  )}
                </View>
              </Swiper>
            )}

            {this.state.upgradePlan == true && this.state.upgradeTo == "trail" && (
              <Swiper
                ref="swiper"
                style={styles.wrapper}
                onIndexChanged={(slider) => this.setState({ slider: slider })}
                loop={false}
                renderPagination={(item, index, context) =>
                  this.renderPagination(item, index, context)
                }
              >
                <View style={{ flex: 1, alignItems: "flex-end" }}>
                  {this.planContainer(
                    "Pro",
                    "ject Plan",
                    Strings.step4.projectDes,
                    this.state.projectAmount,
                    "Get Started"
                  )}
                </View>
                <View style={{ flex: 1, alignItems: "flex-end" }}>
                  {this.planContainer(
                    "Ent",
                    "erprise Plan",
                    Strings.step4.enterpriseDes,
                    "",
                    "Contact Sales"
                  )}
                </View>
              </Swiper>
            )}

            {this.state.upgradePlan == true &&
              this.state.upgradeTo == "project" && (
                <Swiper
                  ref="swiper"
                  style={styles.wrapper}
                  onIndexChanged={(slider) => this.setState({ slider: slider })}
                  loop={false}
                  renderPagination={(item, index, context) =>
                    this.renderPagination(item, index, context)
                  }
                >
                  <View style={{ flex: 1, alignItems: "flex-end" }}>
                    {this.planContainer(
                      "Ent",
                      "erprise Plan",
                      Strings.step4.enterpriseDes,
                      "",
                      "Contact Sales"
                    )}
                  </View>
                </Swiper>
              )}
          </View>
          <View style={styles.endContainer}>
            {this.state.showStep == true && <Steps page={" 4"} />}
          </View>
        </View>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={this.state.desc}
            okTap={() => {
              this.okTap();
            }}
          />
        )}
      </AppView>
    );
  }
}

const mapStateToProps = (state) => {
  const { userid, userDetails } = state.LoginReducer;

  return {
    userid,
    userDetails,
  };
};

export default connect(mapStateToProps, {
  storeUserid,
})(Step4);

const styles = StyleSheet.create({
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  choose: {
    color: Colors.black,
    fontSize: 18,
    width: wp("80%"),
    textAlign: "center",
    alignSelf: "center",
    fontFamily: Fonts.montserratSemiBold,
  },
  planContainer: {
    flex: 2,
    alignItems: "flex-end",
    marginTop: hp("2%"),
  },
  inner1: {
    width: wp("83%"),
    height: hp("45%"),
    borderRadius: wp("5%"),
    zIndex: 999,
    backgroundColor: "#F3F3F3",
    marginRight: wp("6%"),
  },
  inner2: {
    width: wp("76%"),
    height: hp("45%"),
    borderRadius: wp("5%"),
    zIndex: 999,
    backgroundColor: "#FFF",
    borderWidth: Platform.OS == "ios" ? 1 : 0.8,
    shadowOffset: { width: 14, height: 6 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    borderLeftWidth: 1,
    borderColor: "#E5E5E5",
    // justifyContent: 'center',
    // alignItems: 'center',
  },
  inner3: {
    flexDirection: "row",
    width: wp("70%"),
    height: hp("10%"),
    marginTop: wp("4%"),
    justifyContent: "center",
  },
  round: {
    width: hp("10%"),
    height: hp("10%"),
    borderRadius: hp("5%"),
    justifyContent: "flex-end",
    alignItems: "center",
    alignSelf: "flex-end",
    backgroundColor: Colors.themeColor,
  },
  ciruclarText: {
    color: Colors.white,
    alignSelf: "flex-end",
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("5.5%"),
    marginBottom: wp("2.5%"),
  },
  genTextContainer: {
    justifyContent: "flex-end",
    alignItems: "flex-start",
  },
  genText: {
    color: Colors.black,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("5.5%"),
    bottom: wp("2.5%"),
  },
  description: {
    color: Colors.planDesc,
    fontFamily: Fonts.montserratRegular,
    fontSize: 13,
    width: wp("35%"),
    alignSelf: "center",
    justifyContent: "center",
    textAlign: "center",
    lineHeight: hp("4%"),
    marginTop: hp("2%"),
  },
  cost: {
    marginTop: hp("4%"),
    color: Colors.planCost,
    alignSelf: "center",
    fontSize: 16,
    fontFamily: Fonts.montserratMedium,
  },
  getStarted: {
    width: wp("60%"),
    alignSelf: "center",
    backgroundColor: Colors.sign_in_btn_bg,
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    marginTop: hp("2%"),
    justifyContent: "center",
    alignItems: "center",
  },
  startedText: {
    textAlign: "center",
    color: Colors.themeColor,
    fontSize: 14,
    fontFamily: Fonts.montserratSemiBold,
  },
  swithContainer: {
    width: wp("100%"),
    height: hp("4%"),
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    marginTop: wp("2%"),
    marginBottom: wp("2%"),
  },
  boldMonth: {
    color: Colors.black,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("3.5%"),
  },
  lightMonth: {
    color: Colors.yearLight,
    fontFamily: Fonts.montserratSemiBold,
    fontSize: wp("3.5%"),
  },
  endContainer: {
    width: wp("100%"),
    flex: 0.3,
    justifyContent: "flex-end",
  },
  androidSwitchStyle: { transform: [{ scaleX: 1 }, { scaleY: 1 }] },
  iosSwitchStyle: { transform: [{ scaleX: 0.6 }, { scaleY: 0.6 }] },
});
