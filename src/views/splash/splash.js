import React, { Component } from "react";
import {
  View,
  StyleSheet,
  SafeAreaView,
  Image,
  Text,
  Platform,
} from "react-native";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Axios from "axios";
import {
  storeUserid,
  checkNotification,
  refreshDashboard,
} from "../../actions/postAction";
import { View as AnimatableView } from "react-native-animatable";
import { Images, Fonts } from "../../common";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { CommonActions } from '@react-navigation/native';
 import branch from "react-native-branch";
import crashlytics from "@react-native-firebase/crashlytics";
import { Reports } from "../../configs/Configs.json";

let _unsubscribeFromBranch = null;
import { NEW_MEMBER } from "../../api/Constants";
import { getNewUser } from "../../api/Api";

import { Version, Appcenter } from "../../configs/Configs.json";
 import * as Sentry from "@sentry/react-native";
const appVersion = Platform.OS === "ios" ? Version.Ios : Version.Android;
import { trackScreen } from "../../Google Analytics/GoogleAnalytics";
import codePush from "react-native-code-push";
import { Mixpanel } from 'mixpanel-react-native';
import { compose } from "redux";
import withBackHandler from "../../components/backhandler";
class Splash extends Component {
  constructor(props) {
    super(props);
    this.state = {
      token: "",
      isDeeplinkClicked: false,
    };
  }

  //LIFE CYCLE METHODS
  async componentDidMount() {

    const mixpanel = new Mixpanel("a7f3fd49a6dfdbbe81727f3beadcd383");
    mixpanel.init();
    await crashlytics().setCrashlyticsCollectionEnabled(
      Reports.CrashlyticsEnabled
    );

    Sentry.init({
      dsn:"https://<EMAIL>/5831477" 
    });
    codePush.sync({
      deploymentKey: Appcenter.key,
      installMode: codePush.InstallMode.IMMEDIATE,
    });

    this.checkAccessToken();

    _unsubscribeFromBranch =  branch.subscribe(({ params, error }) => {
      if (params["+clicked_branch_link"] == true) {
        this.setState({ isDeeplinkClicked: true });
        if (params["type"] == "invite_member") {
          if (params["ParentCompanyId"] != null) {
            this.getUserDetail({
              ParentCompanyId: params["ParentCompanyId"],
              email: params["email"],
              memberId: params["memberId"],
              domainName: params["domainName"],
              requestType: "1",
            });
          }
        } else if (params["type"] == "reset_password") {
          this.props.navigation.navigate("ResetPassword", {
            resetToken: {
              resetPasswordToken: params["resetPasswordToken"],
              type: params["type"],
            },
          });
        } else if (params["type"] == "register") {
          this.props.navigation.navigate("Login", {
            userDetail: {
              email: params["email"],
              password: params["password"],
            },
          });
        }
      }
    });
  }

  onBackPress() {
    return true;
  }

  getUserDetail = async (data) => {
    let paramCheck = data;
    await getNewUser(
      NEW_MEMBER,
      paramCheck,
      () => {},
      (response) => {
        if (response.status) {
          if (response.status == 200) {
            if (response.data.memberDetail.status == "pending") {
              this.props.navigation.navigate("Step1", {
                memberDetail: response.data.memberDetail,
                userDetail: response.data.userDetail,
                domainName: data.domainName,
              });
            }
          }
        }
      }
    );
  };

  //CHECK TOKEN
  checkAccessToken = async () => {
    AsyncStorage.getItem("AccessToken")
      .then((response) => {
        if (
          response !== null &&
          response !== undefined &&
          response.trim() != ""
        ) {
          // access_token = response
          this.setState({ token: response });
          Axios.defaults.headers.common["Authorization"] = "JWT " + response;
        }
        setTimeout(() => {
          this.navigationMethods();
        }, 5000);

        // this.createNotificationListeners()
      })
      .catch((error) => {
        setTimeout(() => {
          this.navigationMethods();
        }, 5000);
        // this.createNotificationListeners()
      });
  };

  //NAVIGATION METHODS
  navigationMethods = () => {
    console.log('=== NAVIGATION DEBUG ===');
    console.log('Token exists:', !!this.state.token);
    console.log('isDeeplinkClicked:', this.state.isDeeplinkClicked);
    this.props.navigation.navigate("Dashboard");
    if (!this.state.isDeeplinkClicked) {
      if (this.state.token) {
        console.log('Navigating to Registered route');
        // Navigate to Registered route in the switch navigator
        this.props.navigation.navigate("Registered");
        trackScreen('DashBoard')
      } else {
        console.log('Navigating to Dashboard');
        // Navigate to Dashboard in the current stack
        this.props.navigation.navigate("Dashboard");
        trackScreen('Landing Screen')
      }
    }
  };

  //Main Render method
  render() {
    
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.parentContainer}>
          <AnimatableView
            animation="slideInDown"
            duration={3000}
            style={styles.folloContainer}
          >
            <Image source={Images.logoFollo} />
          </AnimatableView>
          <AnimatableView
            animation="slideInRight"
            duration={3000}
            style={styles.logoContainer}
          >
            <Image source={Images.logoArr} style={styles.imageLogo} />
          </AnimatableView>
        </View>
        <View style={styles.splashContainer}>
          <Text style={styles.versionText}>
            Version {appVersion.VersionNumber + "-" + appVersion.Build}
          </Text>
        </View>
      </SafeAreaView>
    );
  }
}

const mapStateToProps = (state) => {
  const { userid } = state.LoginReducer;

  return {
    userid,
  };
};

export default compose(
  connect(mapStateToProps, {
    storeUserid,
    checkNotification,
    refreshDashboard,
  }),
  withBackHandler
)(Splash);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: Colors.white,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  folloContainer: {
    width: wp("60%"),
    height: hp("14%"),
    alignItems: "flex-end",
    justifyContent: "center",
  },
  logoContainer: {
    width: wp("40%"),
    height: hp("14%"),
    alignItems: "flex-start",
    justifyContent: "center",
  },
  imageLogo: {
    marginLeft: wp("2%"),
  },
  splashContainer: {
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  versionText: {
    fontSize: 11,
    fontFamily: Fonts.montserratLight,
    textAlign: "center",
  },
});
