import React, { Component } from "react";
import { View, StyleSheet, Text, SafeAreaView, Image, TouchableWithoutFeedback } from 'react-native';
import { connect } from 'react-redux'
import {
    widthPercentageToDP as wp,
    heightPercentageToDP as hp,
  } from 'react-native-responsive-screen';
  import Colors from '../../common/color'
  import { storeUserid } from '../../actions/postAction'
  import {View as AnimatableView} from 'react-native-animatable';
  import Images from '../../common/images'
  import HeaderAnimation from '../../components/logoAnimation/logoAnimation';
  import Strings from '../../common/string';
import Fonts from "../../common/fonts";
import { CommonActions } from '@react-navigation/native';



  const rotation = {
    from: {
      rotate: '90deg',
    },
    to: {
      rotate: '360deg',
    },
  };

class SubscriptionThanks extends Component {
    constructor(props) {
        super(props)
        this.state = {
        }
    }

    componentDidMount() {
    }

    closeTap = () => {
        const resetAction = CommonActions.reset({
            index: 0,
            routes: [{ name: 'Dashboard' }]
          });
          this.props.navigation.dispatch(resetAction);
    }

    //Main Render method
    render() {
        return (
            <SafeAreaView style={styles.safeArea}>
                <View style={styles.parentContainer}>
                    <View >
                <HeaderAnimation duration={1500}/>
                <AnimatableView animation={'bounceIn'} duration = {3000} style={styles.tickContainer}>
                    <Image source={Images.blackTick} resizeMode = {'contain'} style={{width: wp('20%'), height: hp('8%')}}/>
                </AnimatableView>
        <Text style={styles.jokes}>{Strings.subsriptionThanks.desc}</Text>

        {/* <View style={styles.close}> */}
            <TouchableWithoutFeedback onPress={()=> {this.closeTap()}}>
            <View style={styles.close}>
           <Text style={styles.closeText}>{Strings.subsriptionThanks.close}</Text>
           </View>
            </TouchableWithoutFeedback>
        {/* </View> */}
                </View>
                </View>
            </SafeAreaView>
        )
    }
}

const mapStateToProps = state => {
    const {
        userid
    } = state.LoginReducer

    return {
        userid
    }
}

export default connect(
    mapStateToProps,
    {
        storeUserid
    }
)(SubscriptionThanks)

const styles = StyleSheet.create({
    safeArea: {
        flex: 1, 
        backgroundColor: Colors.white
    },
    parentContainer: {
        flex: 1,
        backgroundColor: Colors.white,
        flexDirection: 'row',
        justifyContent: 'center',
    },
    folloContainer: {
        width: wp('60%'),
        height: hp('14%'),
        alignItems: 'flex-end',
        justifyContent: 'center'
    },
    logoContainer: {
        width: wp('40%'),
        height: hp('14%'),
        alignItems: 'flex-start',
        justifyContent: 'center'
    },
    imageLogo: {
        marginLeft: wp('2%')
    },
    tickContainer: {
        justifyContent: 'center', 
        alignItems: 'center', 
        alignSelf: 'center', 
        marginTop: hp('1%'),
        borderWidth: wp('3%'),
        borderColor: Colors.themeColor,
        borderRadius: wp('17%'),
        width: wp('34%'), 
        height: wp('34%'),
    },
    innerTickContainer: {
        width: wp('34%'), 
        height: wp('44%'), 
        borderWidth: wp('3%'),
        borderColor: Colors.themeColor,
        borderRadius: wp('17%'),
        position: 'absolute',
        backgroundColor: Colors.themeOpacity,
    },
    jokes: {
        color: Colors.planDesc,
        fontSize: wp('4%'),
        fontFamily: Fonts.montserratRegular,
        alignSelf: 'center',
        width: wp('70%'),
        textAlign: 'center',
        marginTop: hp('3%'),
        lineHeight: hp('5%')
    },
    close: {
        width: wp('60%'), 
        alignSelf: 'center', 
        marginTop: hp('4%'), 
        backgroundColor: Colors.themeOpacity, 
        height: hp('6%'), 
        borderRadius: hp('3%'),
        justifyContent: 'center',
        alignItems: 'center'
    },
    closeText: {
        color: Colors.themeColor,
        fontSize: wp('4.5%'),
        fontFamily: Fonts.montserratSemiBold,
        alignSelf: 'center'
    }
})