import React, { Component } from "react";
import { View, Text, StyleSheet, TouchableOpacity,FlatList } from "react-native";
import { Images, Fonts, Colors,Strings } from "../../common";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import moment from "moment";
let isViewAll=true;
let eventcolorobj = {
  Approved: Colors.approvedEventColor,
  Pending: Colors.pendingEventColor,
  Declined: Colors.declinedEventColor,
  Expired: Colors.expiredEventColor,
  Delivered: Colors.deliveredEventColor,
  Completed: Colors.deliveredEventColor,
};
let colorobj = {
  Approved: Colors.calendarSettingsText,
  Pending: Colors.calendarSettingsText,
  Declined: Colors.calendarSettingsText,
  Expired: Colors.calendarSettingsText,
  Delivered: Colors.calendarSettingsText,
};

let upcomingListHeader = (index,onViewAll,item) => {
  titleHeading = (index) => {
    if(index == 0) {
      return Strings.dashboard.upcomingDeliveries
    } else if(index == 1) {
      return Strings.dashboard.upcomingCraneDeliveries
    } else {
      return Strings.dashboard.upcomingConcreteDeliveries
    }
      }
  return (
    <View style={{ width: wp("90%"), flexDirection: "row" }}>
      <Text
        style={[styles.upcomingText, { marginLeft: wp("3%") }]}
        numberOfLines={1}
      >
        {this?.titleHeading(index)}
      </Text>

      {item?.noData==undefined &&
        <TouchableOpacity
          style={{ alignItems: "flex-end" }}
          onPress={() => {
            onViewAll();
          }}
        >
          <Text style={styles.viewallText} numberOfLines={1}>
            {Strings.dashboard.viewall}
          </Text>
        </TouchableOpacity>
    }
    </View>
  );
};
renderUpcomingFlatListItem=(item,index,onPressData)=>{
   let responsiblePerson = "";
  if(item.item.responsiblePerson!=null||item.item.responsiblePerson!=undefined){
  for (let index = 0; index < item.item.responsiblePerson.length; index++) {
    if (item.item.responsiblePerson[index].Member!= null) {
    if (item.item.responsiblePerson[index].Member.User.firstName != null) {
      if (index == item.item.responsiblePerson.length - 1) {
        responsiblePerson +=
          item.item.responsiblePerson[index].Member.User.firstName+" "+ item.item.responsiblePerson[index].Member.User.lastName;
      } else {
        responsiblePerson +=
          item.item.responsiblePerson[index].Member.User.firstName+" "+ item.item.responsiblePerson[index].Member.User.lastName+ ", ";
      }
    }
  }
  }
}
if(item.index<=1){
  return(
   
    <View style={styles.container}>
        {item.item.day && (
          <Text style={[styles.dayText, { marginLeft: wp("3%") }]} numberOfLines={1}>
            {moment(item.item.day).format("ddd MMM DD")}
          </Text>
        )}
        <TouchableOpacity onPress={()=>{onPressData(item.item)}}>
          <View style={styles.parentView}>
            <View style={styles.subview1}>
              <Text style={styles.startText} numberOfLines={1} ellipsizeMode="clip">
              {moment(item.item.start).format("hh:mm A")}
              </Text>
              <Text style={styles.endText} numberOfLines={1} ellipsizeMode="clip">
              {moment(item.item.end).format("hh:mm A")}
              </Text>
            </View>
            <View style={[styles.subview2, { backgroundColor: eventcolorobj[item.item.status] }]}>
              <Text style={[styles.titleText,{color: colorobj[item.item.status],}]} numberOfLines={1}>
                {item.item.label1Data}
              </Text>
              <Text style={[styles.responsible,{color: colorobj[item.item.status]}]} numberOfLines={1}>
               {item.item.label2Data}
               </Text>
             </View>
           </View>
         </TouchableOpacity>
       </View>
     
  )}
};
renderEmptyList = (text) => {
  return (
    <View style={styles.emptyview}>
      <Text style={styles.textEmpty}>{text}</Text>
    </View>
  );
};
export default function UpcomingListItem(props) {
  const { onPressData, item,onViewAll,index, color, defaultColor } = props;
  eventcolorobj = {
    Approved: Colors.approvedEventColor,
    Pending: Colors.pendingEventColor,
    Declined: Colors.declinedEventColor,
    Expired: Colors.expiredEventColor,
    Delivered: Colors.deliveredEventColor,
    Completed: Colors.deliveredEventColor,
  };
  colorobj = {
    Approved: Colors.calendarSettingsText,
    Pending: Colors.calendarSettingsText,
    Declined: Colors.calendarSettingsText,
    Expired: Colors.calendarSettingsText,
    Delivered: Colors.calendarSettingsText,
    Completed: Colors.calendarSettingsText,
  };
  
  !defaultColor && color.map((data)=>{
    if(data.status == 'approved') {
      colorobj.Approved = data.fontColor
      eventcolorobj.Approved = data.backgroundColor
     } else if(data.status == 'pending') {
      colorobj.Pending = data.fontColor
      eventcolorobj.Pending = data.backgroundColor
     } else if(data.status == 'delivered') {
      colorobj.Delivered = data.fontColor
      eventcolorobj.Delivered = data.backgroundColor
      colorobj.Completed = data.fontColor
      eventcolorobj.Completed = data.backgroundColor
     } else if(data.status == 'rejected') {
      colorobj.Declined = data.fontColor
      eventcolorobj.Declined = data.backgroundColor
     } else if(data.status == 'expired') {
      colorobj.Expired = data.fontColor
      eventcolorobj.Expired = data.backgroundColor
     }
  })

  //isViewAll=item[0].text==Strings.dashboard.u
  return (
    <View
    style={[
      styles.upcomingdeliveryContainer,
      { flexDirection: "column" },
    ]}
  >
  {item[0]?.noData==undefined&&
    <FlatList
      ListHeaderComponent={()=>upcomingListHeader(index,onViewAll,item[0])}
      data={item}
      renderItem={(item,index)=>renderUpcomingFlatListItem(item,index,onPressData)}
      //ItemSeparatorComponent={()=>itemSeparator()}
      keyExtractor={(item, index) => index.toString()}
      onEndReachedThreshold={0}
       scrollEnabled={false}
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
      style={{ marginBottom: hp("2%") }}
    />
    } 
    {item[0]?.noData == true &&(<>
      {upcomingListHeader(index,onViewAll,item[0])}
    <View style={[styles.emptyview,{height:hp('25%')}]}>
    <Text style={styles.textEmpty}>{item[0].text}</Text>
    </View>
    </>
 
  )
    }

      </View>
  );
}

const styles = StyleSheet.create({
  container: { flexDirection: "column", marginBottom:hp("2%")},
  dayText: {
    color: Colors.planCost,
    fontSize: wp("3%"),
    fontFamily: Fonts.montserratRegular,
    width: wp("65%"),
    marginTop: hp("1%"),
   
  },
  parentView: { flexDirection: "row", marginLeft: wp("3%") },
  subview1: {
    flexDirection: "column",
    marginRight: wp("4.5%"),
    width: wp("14%"),
  },
  subview2: {
    flexDirection: "column",
    width: wp("65%"),
    //backgroundColor: item.color,
    borderRadius: 5,
    marginTop: hp("1%"),
  },
  startText: {
    fontSize: wp("3%"),
    fontFamily: "Montserrat-regular",
    color: "#BEBEBE",
    marginBottom: hp("2.5%"),
    marginTop: hp("1%"),
 
  },
  endText: {
    fontSize: wp("3%"),
    fontFamily: "Montserrat-regular",
    color: "#BEBEBE",
  },
  titleText: {
    marginLeft: 20,
    marginTop: hp("0.5%"),
    fontSize: wp("4%"),
    fontFamily: "Montserrat-semibold",
  },
  responsible: {
    marginLeft: 20,
    marginTop: hp("0.5%"),
    marginBottom: hp("0.5%"),
    color: Colors.calendarSettingsText,
    fontSize: wp("3%"),
    fontFamily: Fonts.montserratSemiBold,
  },
  upcomingdeliveryContainer: {
    width: wp("92%"),
    // height: hp('15%'),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("3%"),
    margin: wp("1%"),
  },
  upcomingText: {
    color: "#2E3039",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratMedium,
    marginTop: hp("2%"),
    width: wp("70%"),
  },
  viewallText: {
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratMedium,
    marginTop: hp("2%"),
    width: wp("30%"),
    alignItems: "flex-end",
    color: "#F35E28",
  },
  emptyview: {
    justifyContent: "center",
    alignSelf: "center",
    height: hp("20%"),
  },
  textEmpty: {
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    textAlign: "center",
  },
});
