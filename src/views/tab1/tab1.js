import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  FlatList,
  TouchableWithoutFeedback,
  SafeAreaView,
  StatusBar,
  Platform,
  RefreshControl,
  Linking,
  Dimensions,
  Alert
} from "react-native";
import {
  GET_PROJECTADMIN_DETAIL,
  GET_DR_LIST,
  GET_PROJECT_ROLE,
  SET_DEVICETOKEN,
  GET_UNREAD_COUNT,
  GET_CRANE_UPCOMING,
  GET_APP_VERSION,
  UPDATE_APP_VERSION,
  GET_PROJECT_SETTING_DETAILS,
} from "../../api/Constants";

import {
  getprojectadmindetail,
  getEquipList,
  _getUserDetails,
  _getCompanyProjectList,
  _getProjectList,
  _getData,
  setdevicetoken,
  getUnReadCount,
  getCraneUpcomingList,
  getAppVersion,
  updateAppVersion,
  getProjectSettingsDetails,
} from "../../api/Api";

import {
  selectedProjectDetails,
  projectList,
  onTapDetail,
  showDeliverdetailsid,
  cameBack,
  setPage,
  showBadgecount,
  getUserDetails,
  refreshDashboard,
  storeProjectRole,
  storeLastid,
  updateDrList,
  drResponsiblePerson,
  selectedCompany,
  companyList,
  getDrPage,
  concreteDetailsID,
  onPressConcreteDetail,
  setSelectedCalendarDate,
} from "../../actions/postAction";

import { connect } from "react-redux";

import { AppLoader, OverviewCard, Toastpopup } from "../../components";
import AssignedListItem from "./AssignedListItem";
import UpcomingListItem from "./UpcomingListItem";

import moment from "moment";

import { Colors, Images, Strings, Fonts } from "../../common";
import messaging from "@react-native-firebase/messaging";
import { Notifications } from "react-native-notifications";
import PushNotificationIOS from "@react-native-community/push-notification-ios";
// var PushNotification = require("react-native-push-notification");
import Modal from "react-native-modal";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { ScrollView } from "react-native-gesture-handler";
import { concat, remove } from "lodash";
import { trackScreen } from "../../Google Analytics/GoogleAnalytics";
import ForceUpdateDropdown from '../../components/dropdown/forceUpdateDropdown';
import Share from "react-native-share";

// Current PlayStore App Version Check NPM
import VersionCheck from 'react-native-version-check';
import AsyncStorage from '@react-native-async-storage/async-storage';

import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import { checkAndRequestPermission } from "../../utils/PermissionUtils";
import {PERMISSIONS} from 'react-native-permissions';
import ModalDropdown from "react-native-modal-dropdown";
import Clipboard from "@react-native-clipboard/clipboard";
import RNFS, { stat } from "react-native-fs";
import FileViewer from "react-native-file-viewer";
import ReactNativeBlobUtil from "react-native-blob-util";
// import Pdf from "react-native-pdf";
import Loader from "../../components/loader/Loader";
import Orientation, { OrientationLocker } from 'react-native-orientation-locker';
let eventcolorobj = {
  Approved: Colors.approvedEventColor,
  Pending: Colors.pendingEventColor,
  Declined: Colors.declinedEventColor,
  Expired: Colors.expiredEventColor,
  Delivered: Colors.deliveredEventColor,
  Completed: Colors.deliveredEventColor,
};

let colorobj = {
  Approved: "#00D623",
  Pending: "#FF7D4E",
  Declined: "#FF1414",
  Expired: "#BEBEBE",
  Delivered: "#0078d4",
  Completed: "#0078d4",
};

const dashboardOverview = [
  {
    id: "1",
    title: "Deliveries",
  },
  {
    id: "2",
    title: "Members",
  },
  {
    id: "3",
    title: "Companies",
  },
  {
    id: "4",
    title: "Equipments",
  },
];

let DROPDOWNOPTIONS = [
  { id: "Expand", image: Images.expand, name: "Expand" },
  { id: "Copy link", image: Images.copyLink, name: "Copy link" },
  { id: "Download", image: Images.download, name: "Download" },
];

const PLATFORM_IOS = Platform.OS == 'ios';
const PLATFORM_ANDROID = Platform.OS == 'android';

// App Store 
const APP_STORE = 'https://apps.apple.com/in/app/follo-project-logistics-app/id1560780162';
// Play Store
const PLAY_STORE = 'https://play.google.com/store/apps/details?id=com.follo.scm&hl=en&gl=US';

class tab1 extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentDate: moment(new Date()).format("YYYY-MM-DD"),
      dashboardOverview: [],
      showLoader: false,
      upcomingList: [],
      showUpcomingNoData: false,
      assignedmeList: [],
      showAssignedmeNoData: false,
      upcomingViewAll: false,
      assignedmeViewAll: false,
      parentcompanyidd: 0,
      dashboardDetails: "",
      refreshing: false,
      craneUpcomingList: [],
      isForceUpdate: false,
      version: '',
      isNetworkCheck: false,
      profileName:'',
      colorData: [],
      isDefaultColor: false,
      sampleUpcomingList: [],
      logisticsLink: "",
      logisticImageLink:[],
      isPdfShow: false,
      logisticFileName: "",
      logisticFileExtension: "",
      showToaster:false,
      toastType: "",
      toastMessage:"",
      deviceOrientation: false,
      isScreenFocused: false,
    };
  }

  async componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck()
    Orientation.lockToPortrait();
    Orientation.addOrientationListener(this._onOrientationDidChange);
    } else {
      this.checkPushNotificationPermission();
      this.fetchInitialData();
      this.onFetchAppVersion();
      Orientation.lockToPortrait();
      Orientation.addOrientationListener(this._onOrientationDidChange);
    }

    const { navigation } = this.props;

    this.focusListener = navigation?.addListener('focus', () => {
      this.setState({ isScreenFocused: true });
      // Reset selected calendar date to current date when navigating back to home
      console.log('Tab1: Focus listener - resetting selectedCalendarDate to current date');
      this.props.setSelectedCalendarDate(moment().format('YYYY-MM-DD'));
    });

    this.blurListener = navigation?.addListener('blur', () => {
      this.setState({ isScreenFocused: false });
    });
  }
  _onOrientationDidChange=()=>{
    this.setState({ showLoader: false })
  }

  componentWillUnmount() {
    this.clearTimer();
    this.focusListener && this.focusListener();
    this.blurListener && this.blurListener();
  }

  clearTimer = () => {
    if (this.timerHandle) {
      clearTimeout(this.timerHandle);
      this.timerHandle = 0;
    }
  };
  
  networkCheck=()=>{
    NetInfo.fetch().then(state => {
      if(!state.isConnected && !state.isInternetReachable){
        this.setState({isNetworkCheck: true})
       } else{
        this.setState({isNetworkCheck: false})
      }
    })
    
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.checkPushNotificationPermission();
      this.fetchInitialData();
      this.onFetchAppVersion();  
    }
    })
  }

    componentWillUnmount() {
      
      this.clearTimer();
      Orientation.removeOrientationListener(this._onOrientationDidChange);
    }


  appUpdate() {
    this.setState({ isForceUpdate: false });
    let reDirectUrl = PLATFORM_IOS ? APP_STORE : PLAY_STORE;
    Linking.openURL(reDirectUrl);
  }

  compareVersions(version1, version2) {
    console.log('Comparing versions:', version1, version2);

    const parseVersion = (version) => {
        if (!version) return [0, 0, 0]; // Handle empty version case
        return version.replace(/[^\d.]/g, '').split('.').map(Number);
    };

    const parts1 = parseVersion(version1);
    const parts2 = parseVersion(version2);

    for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
        const part1 = parts1[i] || 0;
        const part2 = parts2[i] || 0;
        if (part1 < part2) {
            console.log('Version1 is older:', version1, '<', version2);
            return -1;
        } else if (part1 > part2) {
            console.log('Version1 is newer:', version1, '>', version2);
            return 1;
        }
    }

    console.log('Versions are equal:', version1, '=', version2);
    return 0;
}

onFetchAppVersion = async () => {
    // Default version
    let currentAppVersion = await VersionCheck.getCurrentVersion();
    console.log("Current App Version:", currentAppVersion);

    getAppVersion(GET_APP_VERSION, (response) => {
        console.log("onFetchAppVersion Response:", response);

        if (response?.status == 200) {
            const { iosversion, version } = response?.data?.data;
            const latestVersion = PLATFORM_IOS ? iosversion : version;

            console.log("Latest App Version from API:", latestVersion);

            this.onSetAppVersion(latestVersion); // Update version state

            if (this.compareVersions(currentAppVersion, latestVersion) < 0) {
                console.log("Force update required!");
                this.setState({ isForceUpdate: true });
                // TODO: Enable later
                // AsyncStorage.setItem("forceUpdate", 'true');
            }
        } else {
            console.error("Error fetching app version:", response?.data?.message || "Unknown error");
        }
    });
};

onSetAppVersion = async (version = "0.0.0") => {
  let currentAppVersion = await VersionCheck.getCurrentVersion();
  
    console.log("Checking version update:", currentAppVersion, "vs", version);

    if (this.compareVersions(currentAppVersion, version) >= 0) {
        console.log("No update needed.");
        return;
    }

    let versionName = Strings.versionName.version;
    if (PLATFORM_IOS) versionName = Strings.versionName.iosVersion;

    let param = {
        [versionName]: currentAppVersion,
        releasenote: Strings.versionName.addContent
    };

    updateAppVersion(UPDATE_APP_VERSION, param, (response) => {
        console.log("updateAppVersion Response:", response);
        if (response?.status == 200) {
            console.info(response?.data?.message || "App version updated successfully");
        } else {
            console.error(response?.data?.message || "Failed to update app version");
        }
    });
};

  async checkPushNotificationPermission() {
    checkAndRequestPermission(Platform.OS === 'android' && Platform.Version >= 33 ? PERMISSIONS.ANDROID.POST_NOTIFICATIONS : "").then(async res => {
      if(res || Platform.OS ==='ios' || (Platform.OS === 'android' && Platform.Version <= 33)) {
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      await messaging()
        .registerDeviceForRemoteMessages()
        .then(async () => {
          const token = await messaging().getToken();
          this.setDeviceToken(token);
          this.bindNotification();
          if(this.props.projectDetails!=''){
            this.getUnReadCounts();
          }
        });
    }
  }
  })
  }

  async setDeviceToken(token) {
    if (token !== null && token !== undefined) {
      console.log('Device Token:', token);
      let data = {
        deviceToken: token,
      };
      setdevicetoken(
        SET_DEVICETOKEN,
        data,
        () => {},
        (response) => {
          console.log('response Token:', response);
          //do nothing
        }
      );
    }
  }

  async bindNotification() {
    messaging().onMessage(async (remoteMessage) => {
      console.log("remoteMessage",remoteMessage)
      Notifications.postLocalNotification(
        remoteMessage.notification,
        parseInt(remoteMessage.messageId.match(/\d+/g).join(""), 10)
      );
    });
    messaging().setBackgroundMessageHandler(async () => {});
    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          // do nothing
        }
      });
    messaging().onNotificationOpenedApp((remoteMessage) => {
      //do nothing
    });

    Notifications.registerRemoteNotifications();
    Notifications.events().registerNotificationReceivedForeground(
      (notification) => {
        //this.props.refreshDashboard(true,"Splash Screen");
        /**
         * Update dashboard page when notification is recieved
         */
      }
    );

    // if (Platform.OS === "ios") {
    //   PushNotificationIOS.addEventListener(
    //     "notification",
    //     this.onRemoteNotification
    //   );
    // }
    // PushNotification.configure({
    //   // (optional) Called when Token is generated (iOS and Android)
    //   onRegister: function (token) {},

    //   // (required) Called when a remote is received or opened, or local notification is opened
    //   onNotification: function (notification) {
    //     // process the notification
    //     // (required) Called when a remote is received or opened, or local notification is opened
    //     notification.finish(PushNotificationIOS.FetchResult.NoData);
    //   },

    //   // (optional) Called when Registered Action is pressed and invokeApp is false, if true onNotification will be called (Android)
    //   onAction: function (notification) {
    //     // process the action
    //   },

    //   // (optional) Called when the user fails to register for remote notifications. Typically occurs when APNS is having issues, or the device is a simulator. (iOS)
    //   onRegistrationError: function (err) {
    //     console.error(err.message, err);
    //   },

    //   // IOS ONLY (optional): default: all - Permissions to register.
    //   permissions: {
    //     alert: true,
    //     badge: true,
    //     sound: true,
    //   },

    //   // Should the initial notification be popped automatically
    //   // default: true
    //   popInitialNotification: true,

    //   /**
    //    * (optional) default: true
    //    * - Specified if permissions (ios) and token (android and ios) will requested or not,
    //    * - if not, you must call PushNotificationsHandler.requestPermissions() later
    //    * - if you are not using remote notification or do not have Firebase installed, use this:
    //    *     requestPermissions: Platform.OS === 'ios'
    //    */
    //   requestPermissions: true,
    // });
    

    Notifications.registerRemoteNotifications();

    Notifications.events().registerNotificationOpened(
      (notification, completion) => {
        // this.props.successType(STEP3);
        //       NavigatorUtilities.navigate('SteptwoPage')
        //   NavigatorUtilities.navigate("StepthreePage")
        completion();
      }
    );

    Notifications.events().registerNotificationReceivedBackground(
      (
        notification,
        completion
      ) => {
        // this.props.successType(STEP3);
        // NavigatorUtilities.navigate('SteptwoPage')
        // NavigatorUtilities.navigate("StepthreePage")
        // Calling completion on iOS with `alert: true` will present the native iOS inApp notification.
        completion();
      }
    );

    messaging().onMessage(async () => {
      //     notify = JSON.stringify(remoteMessage);
    });
  }

  fetchInitialData() {
    this.setState({ showLoader: true });
    this.getUserDetails();
    this.getProjectLists();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.refresh_dashboard === true) {
      this.fetchInitialData();

      this.props.refreshDashboard(false, "False");
    }

    if (this.props.projectSwitched != nextProps.projectSwitched) {
      this.fetchInitialData();
    }
  }

  getUserDetails = async () => {
    ///  get userdetails from api
    let resp = await _getUserDetails();
    if (resp.status) {
      if (resp.data.email) {
        this.props.getUserDetails(resp.data);
        this.setState({
          profileName:`${resp.data.firstName.charAt(0)}${resp.data.lastName.charAt(0).toUpperCase()}`
        })
      }
    } else {
      this.setState(
        {
          showToaster: true,
          toastMessage: resp.toString(),
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({
              showToaster: true,
            });
          }, 2000);
        }
      );
    }
  };

  getUnReadCounts=()=>{
    let url=`${GET_UNREAD_COUNT}ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`;
    getUnReadCount(
      url,
      {},
      ()=>{},
      (response)=>{
        console.log("getUnReadCounts",response)
        if(response.status){ if(response.status==200){
            this.props.showBadgecount(response.data.data);
          }else if (response?.data?.message) {
            this.setState(
              {
                showToaster: true,
                toastMessage: response.data.message,
                toastType: "error",
                showLoader: false,
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
          else{
            this.setState(
              {
                showToaster: true,
                toastMessage: 'Request Timeout',
                toastType: "error",
                showLoader: false,
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
        }
        else{
          this.setState(
            {
              showToaster: true,
              toastMessage: 'Bad Request',
              toastType: "error",
              showLoader: false,
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
      );
  }
  getProjectAdminDetail = () => {
    let url = `${GET_PROJECTADMIN_DETAIL}${this.props.projectDetails.id}`;
    getprojectadmindetail(
      url,
      {},
      () => {},
      (response) => {
        console.log("getProjectAdminDetail",response)
        if (response.status == 200) {
          if (response.data.data) {
            let data = response.data.data;
            dashboardOverview.forEach((element) => {
              if (element.title == "Deliveries") {
                element.total = data.deliveryRequest;
                element.difftotal = data.diffDeliveryRequest;
                element.image=Images.nodelivery
              } else if (element.title == "Members") {
                element.total = data.member;
                element.difftotal = data.diffMember;
                element.image=Images.nomember
              } else if (element.title == "Companies") {
                element.total = data.company;
                element.difftotal = data.diffCompany;
                element.image=Images.nocompany;
              }
              else if (element.title == "Equipments") {
                element.total = data.equipment;
                element.difftotal = data.diffEquipment;
                element.image=Images.noequipment;
              }
            });
            this.setState({ showLoader: false });
            this.setState({ dashboardOverview: dashboardOverview });
          } else {
            this.setState(
              {
                showToaster: true,
                toastMessage: Strings.errors.projectListFailed,
                toastType: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                  });
                }, 2000);
              }
            );
          }
        } else {
          this.setState(
            {
              showToaster: true,
              toastMessage: response.toString(),
              toastType: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        }
      }
    );
  };
  colorDisplay = (colorData,defaultValue) => {
    eventcolorobj = {
      Approved: Colors.approvedEventColor,
      Pending: Colors.pendingEventColor,
      Declined: Colors.declinedEventColor,
      Expired: Colors.expiredEventColor,
      Delivered: Colors.deliveredEventColor,
      Completed: Colors.deliveredEventColor,
    };
    if(!defaultValue) {
    colorData.map((data)=>{
       if(data.status == 'approved') {
        eventcolorobj.Approved = data.backgroundColor
       } else if(data.status == 'pending') {
        eventcolorobj.Pending = data.backgroundColor
       } else if(data.status == 'delivered') {
        eventcolorobj.Delivered = data.backgroundColor
        eventcolorobj.Completed = data.backgroundColor
       } else if(data.status == 'rejected') {
        eventcolorobj.Declined = data.backgroundColor
       } else if(data.status == 'expired') {
        eventcolorobj.Expired = data.backgroundColor
       }
    })
    }
  }

  cardData=(e, label)=>{
    console.log("label",label)
    switch (label) {
      case 'Description':
       return e.description 

      case 'Responsible Company':
      return e?.companyDetails[0].Company.companyName;

      case 'Responsible Person':
      return `Resp: ${e?.memberDetails[0].Member.User.firstName} ${e.memberDetails[0].Member.User.lastName}`;

      case 'Gate':
        return e?.gateDetails[0]?.Gate?.gateName;

      case 'Delivery ID':
        return e?.DeliveryId ;


      case 'Definable Feature Of Work':
        return e?.defineWorkDetails.length >0 ? e.defineWorkDetails[0].DeliverDefineWork?.DFOW : '' ;

      case 'Equipment':
      return e?.equipmentDetails.length > 0 ? e.equipmentDetails[0]?.Equipment?.equipmentName : '';

      case 'Crane Pick ID':
      return e?.CraneRequestId; 

      case 'Picking To':
      return e?.requestType == Strings.events.deliveryRequestWithCrane ? e?.craneDropOffLocation : e?.dropOffLocation;
      
      case 'Picking From':
      return e?.requestType == Strings.events.deliveryRequestWithCrane ? e?.cranePickUpLocation : e?.pickUpLocation;

      case 'Concrete Request ID':
      return e?.ConcreteRequestId;

      case 'Location':
      return e?.locationDetails[0].ConcreteLocation.location;

      case 'Concrete Supplier':
        return e?.concreteSupplierDetails[0].Company.companyName;
      default:
        return''
    }
  }

getCraneUpcoming=async()=>{
  await getCraneUpcomingList(
    `${GET_CRANE_UPCOMING}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`,
    {},
    ()=>null,
    (response)=>{
      console.log("getCraneUpcoming",response)
    if(response.status){
      if(response.status==200){
        if(!response.data.data[0].craneList.length){
          let noData=[{noData:true,text:Strings.dashboard.no_crane},{noData:true}]
          this.setState({
            sampleUpcomingList:noData
          })
        }else{
        let events = [];
        let eventData = JSON.parse(response.data.data[0].cardData.craneCard);
        eventData.map((data)=>{
          if(data.line ==1){
            label1 = data.label
          } else if (data.line ==2){
            label2 = data.label
          }
        })
        response.data.data[0].craneList.forEach((e)=>{
          events.push({
            start: moment(e.requestType==='craneRequest'?e.craneDeliveryStart:e.deliveryStart).format("YYYY-MM-DD HH:mm:ss"),
            end: moment(e.requestType==='craneRequest'?e.craneDeliveryEnd:e.deliveryEnd).format("YYYY-MM-DD HH:mm:ss"),
            title: e.description,
            summary: e.additonalNotes,
            color: eventcolorobj[e.status],
            id: e.id,
            day: moment(e.craneDeliveryStart).format("YYYY-MM-DD"),
            responsiblePerson: e.memberDetails,
            isDelivery:e.requestType=="deliveryRequestWithCrane"?true:false,
            craneRequestId:e.CraneRequestId,
            status: e.status,
            label1Data: e.requestType == 'calendarEvent' ? "":this.cardData(e, label1),
            label2Data: e.requestType == 'calendarEvent' ?"":this.cardData(e, label2),
          })
        })
        let days = [];
        events.forEach((e) => {
          if (days.includes(e.day)) {
            delete e.day;
          } else {
            days.push(e.day);
          }
        });
      this.setState({
        sampleUpcomingList:events,
      })
      }
    }
      else if(response.status==400){
        let noData=[{noData:true,text:Strings.dashboard.no_crane},{noData:true}]
        this.setState({
            sampleUpcomingList:noData
        })
      }else{
        let noData=[{noData:true,text:Strings.dashboard.no_crane},{noData:true}]
        this.setState({
            sampleUpcomingList:noData
        })
      }
    }else{
      let noData=[{noData:true,text:Strings.dashboard.no_crane},{noData:true}]
      this.setState({
          sampleUpcomingList:noData
      })
    }
  })
}

  
  async getConcreteUpcoming() {
    await getCraneUpcomingList(
      `${GET_CRANE_UPCOMING}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`,
      {},
      ()=>null,
      (response)=>{
      if(response.status){
        if(response.status==200){
          if(!response.data.data[0].concreteList.length){
            let noData=[{noData:true,text:Strings.dashboard.no_concrete},{noData:true}]
            let sample=[
              this.state.upcomingList,
              this.state.sampleUpcomingList,
              noData
            ];
            this.setState({
              craneUpcomingList:sample
            })
          }else{
          let events = [];
          let eventData = JSON.parse(response.data.data[0].cardData.concreteCard);
          eventData.map((data)=>{
            if(data.line ==1){
              label1 = data.label
            } else if (data.line ==2){
              label2 = data.label
            }
          })
          const colorData = JSON.parse(response.data.data[0].statusData.statusColorCode)
           this.colorDisplay(colorData,response.data.data[0].statusData.isDefaultColor)
          response.data.data[0].concreteList.forEach((e)=>{
            events.push({
              start: moment(e.concretePlacementStart).format("YYYY-MM-DD HH:mm:ss"),
              end: moment(e.concretePlacementEnd).format("YYYY-MM-DD HH:mm:ss"),
              title: e.description,
              summary: e.additonalNotes,
              color: eventcolorobj[e.status],
              id: e.id,
              day: moment(e.concretePlacementStart).format("YYYY-MM-DD"),
              responsiblePerson: e.memberDetails,
              isDelivery:false,
              craneRequestId:e.ConcreteRequestId,
              status: e.status,
              label1Data: e.requestType == 'calendarEvent' ? "":this.cardData(e, label1),
              label2Data: e.requestType == 'calendarEvent' ?"":this.cardData(e, label2),
            })
          })
          let days = [];
          events.forEach((e) => {
            if (days.includes(e.day)) {
              delete e.day;
            } else {
              days.push(e.day);
            }
          });
          
          let sample=[
            this.state.upcomingList,
            this.state.sampleUpcomingList,
            events
          ];
        this.setState({
          craneUpcomingList:sample,
          colorData: colorData,
          isDefaultColor: response.data.data[0].statusData.isDefaultColor,
        })
        }
      }
        
        else if(response.status==400){
          let noData=[{noData:true,text:Strings.dashboard.no_concrete},{noData:true}]
          let sample=[
            this.state.upcomingList,
            this.state.sampleUpcomingList,
            noData
          ];
          this.setState({
            craneUpcomingList:sample
          })
        }else{
          let noData=[{noData:true,text:Strings.dashboard.no_concrete},{noData:true}]
          let sample=[
            this.state.upcomingList,
            this.state.sampleUpcomingList,
            noData
          ];
          this.setState({
            craneUpcomingList:sample
          })
        }
      }else{
        let noData=[{noData:true,text:Strings.dashboard.no_concrete},{noData:true}]
        let sample=[
          this.state.upcomingList,
          this.state.sampleUpcomingList,
          noData
        ];
        this.setState({
          craneUpcomingList:sample
        })
      }
    })
  }

  async getDrList() {
    var param = {
      ParentCompanyId: this.props.projectDetails.ParentCompany.id,
      queuedNdr: false,
      upcoming:true,
    };
    await getEquipList(
      `${GET_DR_LIST}${this.props.projectDetails.id}/2/${1}/0`,
      param,
      () => {},
      (response) => {
        console.log("getDrList",response)
        
        this.setState({
          showLoader: false,
        });
        if (response.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastMessage: Strings.errors.checkInternet,
              type: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        } else if (response.status) {
          if (
            response.status == 200
          ) {
            this.setState({ showLoader: false });

            if (response.data.data.count == 0) {
              let noData=[{noData:true,text:"No Upcoming Delivery Bookings Found"},{noData:true}]
              this.setState({ showUpcomingNoData: true, upcomingList:noData });
            } else {
              //store DRList's last id
              this.props.storeLastid(response.data.lastId.DeliveryId);
               let eventData = JSON.parse(response.data.cardData.deliveryCard);
              eventData.map((data)=>{
                if(data.line ==1){
                  label1 = data.label
                } else if (data.line ==2){
                  label2 = data.label
                }
              })
              
              let events = [];
              response.data.data.rows.forEach((e) => {
                events.push({
                  start: moment(e.deliveryStart).format("YYYY-MM-DD HH:mm:ss"),
                  end: moment(e.deliveryEnd).format("YYYY-MM-DD HH:mm:ss"),
                  title: e.description,
                  summary: e.notes,
                  color: eventcolorobj[e.status],
                  day: moment(e.deliveryStart).format("YYYY-MM-DD"),
                  id: e.id,
                  responsiblePerson: e.memberDetails,
                  isDelivery:true,
                  status: e.status,
                  label1Data: e.requestType == 'calendarEvent' ? "":this.cardData(e, label1),
                  label2Data: e.requestType == 'calendarEvent' ?"":this.cardData(e, label2),
                });
              });
              events.sort((a, b) => moment(a.start) - moment(b.start));
              let days = [];
              events.forEach((e) => {
                if (days.includes(e.day)) {
                  delete e.day;
                } else {
                  days.push(e.day);
                }
              });
              this.setState({
                upcomingList: events.slice(0,2),
                upcomingViewAll: true,
                showUpcomingNoData: false,
              });
            }
          } else if (response?.data?.message?.message) {
            this.showError("error", response.data.message.message);
          } else {
            this.showError("error", response?.data?.message || "Unknown error");
          }
        } else {
          this.showError("error", response.toString());
        }
        //update DR list
        this.props.updateDrList(true);
      }
    );
  }
  getExtension = (fileName) => {
    // console.log('fillteee', fileName.split(".").pop())
     return fileName.split(".").pop();
  };
  getLogistics = () => {
    getProjectSettingsDetails(
      `${GET_PROJECT_SETTING_DETAILS}?ProjectId=${this.props.projectDetails.id}`,
      {},
      () => {},
      (response) => {
        console.log(" getProjectSettingsDetails",response)
        if (response?.data?.status == 200) {
        console.log("logisticImageLink",response?.data?.data?.projectSettings?.pdfToImageLinks)
          this.setState({
            logisticsLink:
              response?.data?.data?.projectSettings?.projectLogisticPlanUrl,
               logisticImageLink: response?.data?.data?.projectSettings?.pdfToImageLinks,
            isPdfShow: response.data.data.projectSettings.isPdfUploaded,
            logisticFileName:
              response?.data?.data?.projectSettings?.pdfOriginalName,
            logisticFileExtension: response?.data?.data?.projectSettings?.isPdfUploaded
              ? response?.data?.data?.projectSettings?.fileExtension 
              : " ",
          });
        } else if (response?.data?.status == 401) {
        } else if (response?.data?.status == 404) {
        } else {
        }
      }
    );
  };
  getAssignedMeList = () => {
    var param = {
      assignedFilter: true,
      statusFilter: "Approved",
      ParentCompanyId: this.props.projectDetails.ParentCompany.id,
      queuedNdr: false,
    };
    getEquipList(
      `${GET_DR_LIST}${this.props.projectDetails.id}/5/${1}/0`,
      param,
      () => {},
      (response) => {
        this.setState({
          showLoader: false,
        });
        if (response.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastMessage: Strings.errors.checkInternet,
              type: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        } else if (response.status) {
          const colorData = JSON.parse(response.data.statusData.statusColorCode)
          if (
            response.status == 200
          ) {
            this.setState({ showLoader: false });

            if (response.data.data.count == 0) {
              this.setState({ showAssignedmeNoData: true, assignedmeList: [] });
            } else {
              this.setState({
                assignedmeList: response.data.data.rows,
                assignedmeViewAll: true,
                showAssignedmeNoData: false,
                colorData: colorData,
                isDefaultColor: response.data.statusData.isDefaultColor,
              });
            }
          } else if (response?.data?.message?.message) {
            this.showError("error", response.data.message.message);
          } else {
            this.showError("error", response?.data?.message || "Unknown error");
          }
        } else {
          this.showError("error", response.toString());
        }
      }
    );
  };

  showError = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showToaster: false,
          });
        }, 2000);
      }
    );
  };

  getProjectLists = () => {
    if (this.props.projectDetails && this.props.projectDetails.id) {
      this.setState(
        { parentcompanyidd: this.props.projectDetails.ParentCompany.id },
        () => {
          this.getCompanyProject();
        }
      );
    } else {
      this.getcomapnylist();
    }
  };

  getCompanyProject = async () => {
    let response = await _getProjectList(this.state.parentcompanyidd);

    this.setState({ showLoader: false });
    if (response.status) {
      if (response.data.data) {
        let data = [];
        if (this.props.projectDetails.id) {
          this.props.selectedProjectDetails(this.props.projectDetails);
          data = this.props.projectDetails;
        } else {
          this.props.selectedProjectDetails(response.data.data[0]);
          data = response.data.data[0];
        }

        await this.getRole(data);

        this.props.projectList(response.data.data);
        this.setState({ showLoader: true });
        await this.getProjectAdminDetail();
        await this.getLogistics();
       await  this.getDrList();
        await this.getUnReadCounts();
        await this.getAssignedMeList();
       await  this.getCraneUpcoming();
       this.timerHandle = setTimeout(async () => {
        await this.getConcreteUpcoming();
       }, 500);
      } else {
        this.setState(
          {
            showToaster: true,
            toastMessage: Strings.errors.projectListFailed,
            toastType: "error",
          },
          () => {
            setTimeout(() => {
              this.setState({
                showToaster: false,
              });
            }, 2000);
          }
        );
      }
    } else {
      this.setState(
        {
          showToaster: true,
          toastMessage: response.toString(),
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({
              showToaster: false,
            });
          }, 2000);
        }
      );
    }
  };

  getRole = async (data) => {
    // this.setState({ showLoader: false });
    let url = GET_PROJECT_ROLE + data.id + "/" + data.ParentCompany.id;
    this.setState({ showLoader: true });
    let response = await _getData(url);
    this.setState({ showLoader: false });
    if (response.data) {
     await this.props.storeProjectRole(response.data.data.RoleId);
      this.setState({ roleId: response.data.data.RoleId });
      let roleData={
        email:response.data.data.User.email,
        id:response.data.data.id,
        userId:response.data.data.UserId,
        name:`${response.data.data.User.firstName} ${response.data.data.User.lastName}(${response.data.data.User.email})`,
      }
      this.props.drResponsiblePerson(roleData)
    }
  };

  getcomapnylist = async () => {
    let resp = await _getCompanyProjectList();

    if (resp.status == 200) {
      this.props.companyList(resp.data.data)
      this.props.selectedCompany(resp.data.data[0])
      this.setState({ parentcompanyidd: resp.data.data[0].id }, () => {
        this.getCompanyProject();
      });
    } else {
      this.setState(
        {
          showToaster: true,
          toastMessage: resp.toString(),
          toastType: "error",
        },
        () => {
          setTimeout(() => {
            this.setState({
              showToaster: false,
            });
          }, 2000);
        }
      );
    }
  };

  renderHeader() {
    const { userDetails } = this.props;
    let onProfileClick = () => {
      this?.props?.setPage("Profile");
      this?.props?.cameBack(true);
    };

    return (
      <View style={styles.headerContainer}>
        <Text style={styles.title}>{Strings.dashboard.dashboard}</Text>
        <View style={styles.headerRowContainer}>
          <TouchableWithoutFeedback onPress={onProfileClick}>
            {  userDetails.profilePic ? 
            <Image
              source={
              { uri: userDetails.profilePic }
              }
              style={styles.profileAvatarStyle}
            /> :
            <View style={styles.profileContainer}>
               <Text style={styles.profileText}>{this.state.profileName}</Text>
             </View>
  }
          </TouchableWithoutFeedback>
        </View>
      </View>
    );
  }

  dashboardDetails(item) {
    if (item.title == "Deliveries") {
      trackScreen('Deliveries')
      this.props.setPage(Strings.menu.dr);
    } else if (item.title == "Companies") {
      trackScreen('Companies')
      this.props.setPage(Strings.menu.company);
    } else if (item.title == "Members") {
      trackScreen('Members')
      this.props.setPage(Strings.menu.members);
    }
    else if(item.title=="Equipments"){
      trackScreen('Equipments')
      this.props.setPage(Strings.menu.equip);
    }
  }

  renderFlatListItem = ({ item, index }) => {
    return (
      <OverviewCard
        onPress={() => this.dashboardDetails(item)}
        item={item}
        key={index}
      />
    );
  };
  upcomingListHeader = () => {
    return (
      <View style={{ width: wp("90%"), flexDirection: "row" }}>
        <Text
          style={[styles.upcomingText, { marginLeft: wp("3%") }]}
          numberOfLines={1}
        >
          {Strings.dashboard.upcoming_deliveries}
        </Text>

        {this.state.upcomingViewAll && (
          <TouchableOpacity
            style={{ alignItems: "flex-end" }}
            onPress={() => {
              this.props.setPage(Strings.menu.dr);
              this.props.cameBack(false);
            }}
          >
            <Text style={styles.viewallText} numberOfLines={1}>
              {Strings.dashboard.viewall}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  renderUpcomingFlatListItem = ({ item, index }) => {
    if (index <= 2) {
      return (
        <UpcomingListItem
          onPressData={(data) => {
            if (index == 2) {
              this.props.concreteDetailsID(data.craneRequestId);
              this.props.onPressConcreteDetail("ConcreteDetails");
            } else {
              let decidingData = {
                id: data.isDelivery ? data.id : data.craneRequestId,
                isDelivery: data.isDelivery,
              };
              this.props.showDeliverdetailsid(decidingData);
              this.props.onTapDetail("drdetailspage");
            }
          }}
          item={item}
          index={index}
          color={this.state.colorData}
          defaultColor={this.state.isDefaultColor}
          onViewAll={() => {
            trackScreen("Deliveries");
            if (index == 0) {
              this.props.setPage(Strings.menu.dr);
              this.props.cameBack(false);
            } else if (index == 1) {
              this.props.getDrPage(1);
              this.props.setPage(Strings.menu.dr);
              this.props.cameBack(false);
            } else {
              this.props.getDrPage(2);
              this.props.setPage(Strings.menu.dr);
              this.props.cameBack(false);
            }
          }}
        />
      );
    } else {
      return null;
    }
  };

  deliveryListHeader = () => {
    return (
      <View>
        <View style={{ width: wp("90%"), flexDirection: "row" }}>
          <Text
            style={[styles.upcomingText, { marginLeft: wp("3%") }]}
            numberOfLines={1}
          >
            {Strings.dashboard.Deliveries_Assigned_to_me}
          </Text>

          {this.state.assignedmeViewAll && (
            <TouchableOpacity
              style={{ alignItems: "flex-end" }}
              onPress={() => {
                trackScreen("Deliveries");
                this.props.setPage(Strings.menu.dr);
                this.props.cameBack(false);
              }}
            >
              <Text style={styles.viewallText} numberOfLines={1}>
                {Strings.dashboard.viewall}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {this.state.showAssignedmeNoData == false && (
          <View style={styles.headerRow}>
            <Text
              style={[styles.deliveryText, { width: wp("15%") }]}
              numberOfLines={1}
            >
              {Strings.placeholders.id}
            </Text>

            <Text
              style={[styles.deliveryText, { width: wp("55%") }]}
              numberOfLines={1}
            >
              {Strings.placeholders.description}
            </Text>

            <Text
              style={[styles.deliveryText, { width: wp("15%") }]}
              numberOfLines={1}
            >
              {Strings.addDR.status}
            </Text>
          </View>
        )}
      </View>
    );
  };

  renderDeliveryFlatListItem = ({ item, index }) => {
    if (index <= 2) {
      return (
        <AssignedListItem
          item={item}
          color={this.state.colorData}
          defaultColor={this.state.isDefaultColor}
          onPress={() => {
            let decidingData = {
              id: item.id,
              isDelivery: true,
            };
            this.props.showDeliverdetailsid(decidingData);
            this.props.onTapDetail("drdetailspage");
          }}
        />
      );
    }
  };

  renderSeparator = () => (
    <View
      style={{
        backgroundColor: "#A8B2B9",
        height: 0.5,
      }}
    />
  );

  renderEmptyList = (text) => {
    return (
      <View style={styles.emptyview}>
        <Text style={styles.textEmpty}>{text}</Text>
      </View>
    );
  };
  onReset = () => {
    this.networkCheck();
    this.fetchInitialData();
  };

  renderRow = (option, index, isSelected) => {
    return (
      <View style={styles.rowMainView}>
        <Image
          source={option.image}
          style={{
            marginLeft: 10,
            // resizeMode: 'contain',
            height: 20,
            width: 20,
          }}
        />
        <Text style={styles.optionText}>{option.id}</Text>
      </View>
    );
  };

  onSelect = (index) => {
    const localFile = `${RNFS.DocumentDirectoryPath}/${this.state.logisticFileName}`;
    const androidPath = `${RNFS.DownloadDirectoryPath}/${this.state.logisticFileName}`;
    const options = {
      fromUrl: this.state.logisticsLink,
      toFile: localFile,
    };
  
    if (index == 0) {  // Expand option
      console.log("Expand option selected.");
      Orientation.unlockAllOrientations();
      
      if (Platform.OS == "ios") {
        console.log("iOS platform detected.");
        setTimeout(() => {
          RNFS.downloadFile(options)
            .promise.then(() =>
              FileViewer.open(localFile, {
                onDismiss: () => {
                  console.log("File dismissed, locking to portrait.");
                  Orientation.lockToPortrait();
                },
              })
            )
            .then((response) => console.log("File opened successfully, response:", response))
            .catch((error) => {
              console.log("Error while opening file:", error);
              if (error.message.includes("No app associated with this mime type")) {
                Alert.alert(
                  "No PDF Viewer Found",
                  "Please install a PDF viewer app from the Play Store to view this file."
                );
              }
            });
        }, 200); // Adjust the delay as needed
      } else {
        console.log("Android platform detected.");
        RNFS.downloadFile(options)
          .promise.then(() =>
            FileViewer.open(localFile, {
              showOpenWithDialog: true, // Explicitly ask for an app chooser dialog
              displayName: "Sample PDF",
              mimeType: "application/pdf", // Explicitly set the MIME type
              onDismiss: () => {
                console.log("File dismissed, locking to portrait.");
                Orientation.lockToPortrait();
              },
            })
          )
          .then((response) => console.log("File opened successfully, response:", response))
          .catch((error) => {
            console.log("Error while opening file:", error);
            if (error.message.includes("No app associated with this mime type")) {
              Alert.alert(
                "No PDF Viewer Found",
                "Please install a PDF viewer app from the Play Store to view this file."
              );
            }
          });
      }
    }
    else if (index == 1) { 
      Clipboard.setString(this.state.logisticsLink);
    } else {
      if (Platform.OS == "android") {
        try {
          checkAndRequestPermission(
            Platform.Version >= 33
              ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
              : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE
          ).then((res) => {
            if (res) {
              ReactNativeBlobUtil.config({
                path: androidPath,
                fileCache: true,
                addAndroidDownloads: {
                  useDownloadManager: true,
                  path: androidPath,
                },
              })
                .fetch("GET", this.state.logisticsLink)
                .then(async (resp) => {
                    if (resp) {
                      this.setState(
                        {
                          showToaster: true,
                          toastType: "success",
                          toastMessage: `${Strings.popup.downloadStatus} at ${androidPath}`,
                        },
                        () => {
                          setTimeout(() => {
                            this.setState({
                              showToaster: false,
                              showLoader: false,
                            });
                          }, 2000);
                        }
                      );
                    }                
                  this.setState({ showLoader: false });
                })
                .catch((err) => {
                  this.setState({ showLoader: false });
                });
            } else {
              this.setState({
                showPopUp: true,
                showText: Strings.permissions.files_media_permission,
                showLoader: false,
              });
            }
          });
        } catch (err) {}
      } else {
        RNFS.downloadFile(options)
          .promise.then(() => {
            Share.open({
              urls: [localFile],
              saveToFiles: true,
            });
          })
          .then((res) => {
            this.setState({ showLoader: false });

            // success
          })
          .catch((error) => {
            this.setState({ showLoader: false });

            // error
          });
      }
    }
};


  render() {
    console.log("logisticImageLink---->",this.state.logisticImageLink)
    const { isForceUpdate, showLoader } = this.state;
    return (
      <>
        {this.state.isNetworkCheck ? (
          <NoInternet Refresh={() => this.networkCheck()} />
        ) : (
          <SafeAreaView style={styles.parentContainer}>
            <StatusBar backgroundColor="white" barStyle="dark-content" />

            {this.renderHeader()}
            <ScrollView
              refreshControl={
                <RefreshControl
                  refreshing={this.state.refreshing}
                  onRefresh={this.onReset}
                />
              }
            >
              {/* Overview lists => Total Deliveries, Total Members , Total Companies */}
              <View style={styles.parentContainer}>
                {this.state.dashboardOverview.length > 0 && (
                  <View>
                    <View style={styles.mainview}>
                      <FlatList
                        horizontal
                        data={this.state.dashboardOverview}
                        renderItem={this.renderFlatListItem}
                        showsHorizontalScrollIndicator={false}
                        keyExtractor={(item, index) => index.toString()}
                        onEndReachedThreshold={0}
                        onMomentumScrollBegin={() => {
                          this.onEndReachedCalledDuringMomentum = false;
                        }}
                        extraData={this.state}
                      />
                    </View>

                    {/*Logistics Plan */}
                    {this.state.isPdfShow && (
                      <View style={[styles.upcomingdeliveryContainer]}>
                        {/*Logistics Header */}
                        <View
                          style={[styles.nameContainer, { width: wp("90%") }]}
                        >
                          <Text style={styles.nameText} numberOfLines={1}>
                            {Strings.dashboard.logistics_plan}
                          </Text>

                          <View>
                            <ModalDropdown
                              saveScrollPosition={false}
                              style={styles.customDropdownStyle}
                              dropdownStyle={[
                                 styles.customOptionsStyle,
                              ]}
                              dropdownTextStyle={styles.customOptionsTextStyle}
                              options={DROPDOWNOPTIONS}
                              renderRow={(option) => this.renderRow(option)}
                              showsVerticalScrollIndicator={false}
                              onSelect={(options) => {
                                this.onSelect(options);
                              }}
                              defaultValue=""
                              dropdownListProps={{}}
                            >
                                   <View style={styles.imageContainer}>
                                <Image
                                  style={{ width: 30, height: 10 }}
                                  source={Images.dotmenu}
                                />
                              </View>
                            </ModalDropdown>
                            {/* }  */}
                          </View>
                        </View>
                        <View
                          style={{
                            flex: 1,
                            marginBottom: 10,
                          }}
                        >
                          {this.state.logisticFileExtension == "pdf" ? (                      
                           <View>
                            <FlatList
          data={this.state.logisticImageLink}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({item}) => (
            <View style={styles.image_Container}>
              <Image style={styles.image} source={{uri: item}} />
            </View>
          )}
        />
        </View>
                          ) : (
                            <View>
                              <Image
                                style={{
                                  width: 300,
                                  height: 200,
                                  alignSelf: "center",
                                }}
                                source={{ uri: this.state.logisticsLink }}
                              />
                            </View>
                          )}
                        </View>
                      </View>
                    )}

                    {/* Upcoming Deliveries list */}
                    <FlatList
                      horizontal
                      data={this.state.craneUpcomingList}
                      renderItem={this.renderUpcomingFlatListItem}
                      keyExtractor={(item, index) => index.toString()}
                      onEndReachedThreshold={0}
                      showsHorizontalScrollIndicator={false}
                      showsVerticalScrollIndicator={false}
                      style={{ marginBottom: hp("2%"), marginLeft: hp("1%") }}
                    />

                    {/* {this.state.showUpcomingNoData == true &&
                    this.renderEmptyList("No Upcoming Delivery List Found")} */}

                    {/* Delivery Assigned to me list */}

                    <View
                      style={[
                        styles.upcomingdeliveryContainer,
                        {
                          height:
                            Platform.OS == "android" ? hp("27.7%") : hp("27%"),
                          marginTop: -10,
                        },
                      ]}
                    >
                      <FlatList
                        data={this.state.assignedmeList}
                        ListHeaderComponent={this.deliveryListHeader}
                        renderItem={this.renderDeliveryFlatListItem}
                        keyExtractor={(item, index) => index.toString()}
                        onEndReachedThreshold={0}
                        scrollEnabled={false}
                        showsVerticalScrollIndicator={false}
                        ItemSeparatorComponent={this.renderSeparator}
                      />
                      {this.state.showAssignedmeNoData == true &&
                        this.renderEmptyList("No Deliveries Found")}
                    </View>
                  </View>
                )}

                {isForceUpdate && !showLoader && (
                  <ForceUpdateDropdown
                    updateButton={() => this.appUpdate()}
                    isModal={isForceUpdate}
                    skipButton={() => this.setState({ isForceUpdate: false })}
                  />
                )}

               

                {this.state.showLoader && this.state.isScreenFocused && (
                  <Modal
                    isVisible={true}
                    backdropOpacity={0}
                    style={styles.modalstyle}
                  >
                    <AppLoader viewRef={this.state.showLoader} />
                  </Modal>
                )}
              </View>
            </ScrollView>
            {this.state.showToaster && (
                  <Toastpopup
                    backPress={() => this.setState({ showToaster: false })}
                    toastMessage={this.state.toastMessage}
                    type={this.state.toastType}
                    container={{ marginBottom: hp("5%") }}
                  />
                )}
          </SafeAreaView>
        )}
      </>
    );
  }
}

const mapStateToProps = (state) => {
  const {
    projectDetails,
    checkCameBack,
    currentPage,
    userDetails,
    refresh_dashboard,
    refreshPageOnComeBack,
    showMenu,
    projectSwitched,
  } = state.LoginReducer;

  return {
    projectDetails,
    checkCameBack,
    userDetails,
    currentPage,
    refresh_dashboard,
    refreshPageOnComeBack,
    showMenu,
    projectSwitched,
  };
};

export default connect(mapStateToProps, {
  selectedProjectDetails,
  projectList,
  onTapDetail,
  showDeliverdetailsid,
  cameBack,
  setPage,
  showBadgecount,
  getUserDetails,
  refreshDashboard,
  _getUserDetails,
  storeLastid,
  updateDrList,
  drResponsiblePerson,
  selectedCompany,
  companyList,
  storeProjectRole,
  getDrPage,
  concreteDetailsID,
  onPressConcreteDetail,
  setSelectedCalendarDate,
})(tab1);

const styles = StyleSheet.create({
  mainview: {
    height: hp("12%"),
    width: wp("92%"),
    marginLeft: 10,
    marginTop: 2,
  },
  parentContainer: {
    flex: 1,
    backgroundColor: "#FCFBFC",
  },
  headerContainer: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderBottomWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  title: {
    color: Colors.black,
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratBold,
    marginBottom: hp("2%"),
    marginLeft: wp("4%"),
  },
  headerRowContainer: {
    flex: 1,
    height: hp("15%"),
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  image: {
    // width: wp("10%"),
    // height: wp('10%'),
    // borderRadius: wp('5%'),
    marginRight: wp("6%"),
    marginBottom: hp("2%"),
  },

  upcomingdeliveryContainer: {
    width: wp("92%"),
    // height: hp('15%'),
    marginVertical: 10,
    backgroundColor: Colors.white,
    // backgroundColor: 'red',
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("3%"),
    margin: wp("1%"),
  },
  upcomingText: {
    color: "#2E3039",
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratMedium,
    marginTop: hp("2%"),
    width: wp("70%"),
  },

  viewallText: {
    fontSize: wp("3.5%"),
    fontFamily: Fonts.montserratMedium,
    marginTop: hp("2%"),
    width: wp("30%"),
    alignItems: "flex-end",
    color: "#F35E28",
  },
  deliveryText: {
    color: "#2E3039",
    fontSize: wp("3%"),
    fontFamily: Fonts.montserratBold,
    marginTop: hp("1%"),
  },
  emptyview: {
    justifyContent: "center",
    alignSelf: "center",
    height: hp("20%"),
  },
  textEmpty: {
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    textAlign: "center",
  },
  headerRow: {
    flexDirection: "row",
    backgroundColor: "#f5f5f5",
    marginTop: hp("1%"),
    justifyContent: "center",
    height: hp("4%"),
  },
  modalstyle: {
    paddingTop: 45,
    paddingBottom: 30,
    margin: 0,
    backgroundColor: "#0000",
  },
  profileAvatarStyle: {
    width: hp("5%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    marginBottom: hp("1%"),
    marginRight: hp("2%"),
  },
  profileContainer: {
    backgroundColor: "grey",
    alignItems: "center",
    justifyContent: "center",
    width: hp("5%"),
    height: hp("5%"),
    marginBottom: hp("1%"),
    marginRight: hp("2%"),
    borderRadius: hp("3%"),
  },
  profileText: {
    color: "white",
    fontSize: 20,
  },
  imageContainer: {
    marginRight: wp("20%"),
    height: 25,
    width: 40,
    justifyContent: "center",
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
    borderRadius: wp("10%"),
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderWidth: 3,
    borderColor: Colors.borderCardColor,
    marginRight: wp("20%"),
    alignSelf: "center",
    borderRadius: wp("1.5%")
  },
  image_Container: {
    width: Dimensions.get('window').width,
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: 300,
    height: 200,
  },
  nameContainer: {
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  nameText: {
    color: Colors.black,
    fontSize: 15,
    fontFamily: Fonts.montserratBold,
    marginTop: 10,
    marginLeft: 10,
    width: "85%",
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  rowMainView: {
    width: wp("40%"),
    flexDirection: "row",
    alignItems: "center",
    height: hp("4.5%"),
    backgroundColor: "white",
  },
  optionText: {
    fontSize: wp("3%"),
    fontFamily: Fonts.montserratMedium,
    color: Colors.blackGrey,
    marginLeft: 10,
  },
  flatlistContainer: {
    width: wp("92%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    alignSelf: "center",
    borderRadius: wp("2%"),
    shadowColor: "rgba(0,0,0,0.14)",
    shadowOffset: {
      width: 4,
      height: 4,
    },
    shadowOpacity: 1,
    shadowRadius: 4.65,
    elevation: 3,
  },
});