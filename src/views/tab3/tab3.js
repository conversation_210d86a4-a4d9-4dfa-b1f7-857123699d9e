import React, { Component } from 'react';
import { View, StyleSheet, SafeAreaView } from 'react-native';
import {changeTab, showSideMenu, cameBack} from '../../actions/postAction';
import {connect} from 'react-redux';
import Colors from '../../common/color';
import Strings from '../../common/string';
import {setPage} from '../../actions/postAction';
import MemebersList from '../members/membersList';
import CompanyList from '../companies/companyList';
import GateList from '../gates/gateList';
import Equipment from '../equipment/equipList'
import DFOW from '../dfow/Dfow';
import DR from '../dr/drList';
import CalendarSettings from '../CalendarSettings/CalendarSettings';
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";

class Tab3 extends Component {
  constructor(props) {
    super(props);
    this.state = {
      renderData: false,
      updateData: false
    };
  }

  componentDidMount = () => {
    // this.checkRef= React.createRef();
     this.checkRef = React.createRef();
  }

  backAction = () => {
    onBackPress()
  };

  onBackPress = () => {
      this.props.setPage('')
      this.props.navigation.navigate('Tab1')
    return true;
  };

 
  render() {
    return (
    <SafeAreaView style={styles.safeArea}>
  
      <View style={styles.subContainer}>

          {this.props.currentPage == Strings.menu.members && 
                    <MemebersList navigation={this.props.navigation}/>
          }

          {this.props.currentPage == Strings.menu.gates && 
          <GateList 
          />
          }

          {this.props.currentPage == Strings.menu.company && 
          <CompanyList 
          />
          }

          {this.props.currentPage == Strings.menu.equip &&
          <Equipment
           />
          }

          {this.props.currentPage == Strings.menu.df &&
          <DFOW
           />
          }
          {this.props.currentPage == Strings.menu.dr &&
          <DR
            refreshData={this.state.updateData}
           />
          }
          {this.props.currentPage === Strings.menu.calendarSettings &&
          <CalendarSettings
           />
          } 
      </View>
      </SafeAreaView>
    )
}
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: Colors.white
      },
      subContainer: {
          flex: 1,
          backgroundColor: Colors.white
      }
})

const mapStateToProps = (state) => {
  const {changeTab, showMenu, currentPage, checkCameBack} = state.LoginReducer;

  return {
      changeTab,
      showMenu,
      currentPage,
      checkCameBack
  };
};

export default compose(
  connect(mapStateToProps, {
    changeTab,
    showSideMenu,
    cameBack,
    setPage
  }),
  withBackHandler
)(Tab3);