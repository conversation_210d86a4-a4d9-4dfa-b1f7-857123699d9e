import React, { Component } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Modal, Image, Dimensions, TouchableWithoutFeedback, Alert, Pressable, ActivityIndicator, Platform } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  runOnJS,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import {
  PanGestureHandler,
  TapGestureHandler,
} from 'react-native-gesture-handler';
import Colors from "../../common/color";
import Images from "../../common/images";
import Fonts from "../../common/fonts";
import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from 'moment';
import {
  GET_TIME_SLOTS,
  GET_PROJECT_SETTING_DETAILS,
} from "../../api/Constants";
import {
  getSlotsDetail,
  getProjectSettingsDetails,
} from "../../api/Api";

const HOUR_HEIGHT = 100;
const MINUTE_HEIGHT = HOUR_HEIGHT / 4;
const MIN_EVENT_DURATION = 15; // minutes
const SNAP_INTERVAL = 15; // minutes

// Animated Event Component
const AnimatedEvent = ({
  event,
  isSelected,
  onMove,
  onResize,
  onTap,
  onDragStart,
  onDragEnd,
  bookedIntervals,
  extendedStartDecimal,
  workingWindowStart,
  workingWindowEnd,
  checkConflict,
  isEditMode
}) => {
  const translateY = useSharedValue(0);
  const height = useSharedValue((event.end - event.start) * HOUR_HEIGHT);
  const opacity = useSharedValue(1);

  const isDragging = useSharedValue(false);
  const isResizing = useSharedValue(false);
  const initialEventStart = useSharedValue(event.start);
  const initialEventEnd = useSharedValue(event.end);

  // Convert time to Y position
  const timeToY = (time) => {
    'worklet';
    return (time - extendedStartDecimal) * HOUR_HEIGHT;
  };

  // Convert Y position to time
  const yToTime = (y) => {
    'worklet';
    const time = extendedStartDecimal + (y / HOUR_HEIGHT);
    // Snap to 15-minute intervals
    const minutes = Math.round(time * 60);
    const snappedMinutes = Math.round(minutes / SNAP_INTERVAL) * SNAP_INTERVAL;
    return snappedMinutes / 60;
  };

  // Convert time to time string
  const timeToString = (time) => {
    'worklet';
    const hours = Math.floor(time);
    const minutes = Math.round((time - hours) * 60);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  // Worklet conflict checker using precomputed intervals
  const hasOverlapWithBooked = (startTimeDecimal, endTimeDecimal) => {
    'worklet';
    // Safety check: if no booked intervals exist, there can't be a conflict
    if (!bookedIntervals || !Array.isArray(bookedIntervals) || bookedIntervals.length === 0) {
      return false;
    }

    // Small epsilon to handle floating point precision issues
    const EPSILON = 0.001;

    for (let i = 0; i < bookedIntervals.length; i++) {
      const interval = bookedIntervals[i];

      // Safety check: ensure interval has valid start and end values
      if (!interval || typeof interval.start !== 'number' || typeof interval.end !== 'number') {
        continue;
      }

      const bStart = interval.start;
      const bEnd = interval.end;

      // Skip invalid intervals where start >= end
      if (bStart >= bEnd) {
        continue;
      }

      // Two time ranges overlap if:
      // - new start is strictly before booked end AND
      // - new end is strictly after booked start
      // Adjacent slots (where one ends exactly when another starts) should NOT overlap
      const overlaps = (
        (startTimeDecimal < bEnd - EPSILON) &&
        (endTimeDecimal > bStart + EPSILON)
      );

      if (overlaps) {
        return true;
      }
    }
    return false;
  };

  const showConflictAlert = () => {
    Alert.alert(
      'Slot Conflict',
      'This time slot conflicts with an existing booking.',
      [{ text: 'OK' }]
    );
  };

  const panGestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startY = translateY.value;
      context.startHeight = height.value;
      context.initialStart = initialEventStart.value;
      context.initialEnd = initialEventEnd.value;

      isDragging.value = true;
      if (onDragStart) {
        runOnJS(onDragStart)();
      }
      opacity.value = withTiming(0.9);
    },

    onActive: (event, context) => {
      const deltaY = event.translationY;
      const currentY = timeToY(context.initialStart) + deltaY;
      const newStartTime = yToTime(currentY); // snaps to 15-min intervals

      // Constrain to working hours - ensure end time doesn't exceed workingWindowEnd
      const eventDuration = context.initialEnd - context.initialStart;
      const maxAllowedStart = workingWindowEnd - eventDuration;
      const constrainedStart = Math.max(
        workingWindowStart,
        Math.min(maxAllowedStart, newStartTime)
      );
      const newEndTime = constrainedStart + eventDuration;

      // Update position (relative offset from original start)
      translateY.value = timeToY(constrainedStart) - timeToY(context.initialStart);

      // Conflict check
      const hasConflict = hasOverlapWithBooked(constrainedStart, newEndTime);
      opacity.value = withTiming(hasConflict ? 0.6 : 0.9);
    },

    onEnd: (event, context) => {
      const deltaY = event.translationY;
      const currentY = timeToY(context.initialStart) + deltaY;
      const newStartTime = yToTime(currentY); // snaps to 15-min intervals

      // Constrain to working hours - ensure end time doesn't exceed workingWindowEnd
      const eventDuration = context.initialEnd - context.initialStart;
      const maxAllowedStart = workingWindowEnd - eventDuration;
      const constrainedStart = Math.max(
        workingWindowStart,
        Math.min(maxAllowedStart, newStartTime)
      );
      const newEndTime = constrainedStart + eventDuration;

      // Conflict check
      const hasConflict = hasOverlapWithBooked(constrainedStart, newEndTime);

      if (!hasConflict) {
        // Update event in JS
        runOnJS(onMove)(timeToString(constrainedStart), timeToString(newEndTime));

        // Update worklet values
        initialEventStart.value = constrainedStart;
        initialEventEnd.value = newEndTime;

        // ✅ Smoothly animate to the new offset (no bounce back)
        translateY.value = withTiming(
          timeToY(constrainedStart) - timeToY(context.initialStart),
          { duration: 150 }
        );
      } else {
        // ❌ Conflict → revert instantly (no spring bounce)
        translateY.value = withTiming(0, { duration: 150 });
        runOnJS(showConflictAlert)();
      }

      isDragging.value = false;
      opacity.value = withTiming(1);

      if (onDragEnd) {
        runOnJS(onDragEnd)();
      }
    },
  });


  // Helper function to snap time to 15-minute intervals
  const snapToFifteenMinutes = (timeDecimal) => {
    'worklet';
    const minutes = Math.round(timeDecimal * 60);
    const snappedMinutes = Math.round(minutes / SNAP_INTERVAL) * SNAP_INTERVAL;
    return snappedMinutes / 60;
  };

  // Resize gesture handler for the bottom handle
  const resizeGestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startHeight = height.value;
      context.initialEnd = initialEventEnd.value;
      isResizing.value = true;
      if (onDragStart) {
        runOnJS(onDragStart)();
      }
    },
    onActive: (event, context) => {
      const newHeight = Math.max(MINUTE_HEIGHT, context.startHeight + event.translationY);
      const newEndTime = initialEventStart.value + (newHeight / HOUR_HEIGHT);

      // Snap to 15-minute intervals
      const snappedEndTime = snapToFifteenMinutes(newEndTime);

      // Constrain to working hours and minimum duration
      const constrainedEnd = Math.max(
        initialEventStart.value + (MIN_EVENT_DURATION / 60),
        Math.min(workingWindowEnd, snappedEndTime)
      );

      height.value = (constrainedEnd - initialEventStart.value) * HOUR_HEIGHT;

      // Check for conflicts (pure worklet)
      const hasConflict = hasOverlapWithBooked(initialEventStart.value, constrainedEnd);
      opacity.value = withTiming(hasConflict ? 0.6 : 0.9);
    },
    onEnd: (event, context) => {
      const newHeight = Math.max(MINUTE_HEIGHT, context.startHeight + event.translationY);
      const newEndTime = initialEventStart.value + (newHeight / HOUR_HEIGHT);

      // Snap to 15-minute intervals
      const snappedEndTime = snapToFifteenMinutes(newEndTime);

      // Constrain to working hours and minimum duration
      const constrainedEnd = Math.max(
        initialEventStart.value + (MIN_EVENT_DURATION / 60),
        Math.min(workingWindowEnd, snappedEndTime)
      );

      // Check for conflicts (pure worklet)
      const hasConflict = hasOverlapWithBooked(initialEventStart.value, constrainedEnd);

      if (!hasConflict) {
        // Update the event
        runOnJS(onResize)(timeToString(initialEventStart.value), timeToString(constrainedEnd));
        initialEventEnd.value = constrainedEnd;
        height.value = withSpring((constrainedEnd - initialEventStart.value) * HOUR_HEIGHT);
      } else {
        // Revert to original size
        height.value = withSpring(context.startHeight);
        runOnJS(showConflictAlert)();
      }

      isResizing.value = false;
      opacity.value = withTiming(1);
      if (onDragEnd) {
        runOnJS(onDragEnd)();
      }
    },
  });

  // Resize gesture handler for the top handle
  const resizeTopGestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startHeight = height.value;
      context.initialStart = initialEventStart.value;
      context.initialEnd = initialEventEnd.value;

      isResizing.value = true;
      if (onDragStart) {
        runOnJS(onDragStart)();
      }
    },

    onActive: (event, context) => {
      // Dragging the top: positive translationY shrinks height, negative grows
      const newHeightRaw = context.startHeight - event.translationY;
      const newHeightClamped = Math.max(MINUTE_HEIGHT, newHeightRaw);
      const tentativeStart = context.initialEnd - newHeightClamped / HOUR_HEIGHT;

      // Snap to 15-min intervals
      const snappedStart = snapToFifteenMinutes(tentativeStart);

      // Constrain to working hours and minimum duration
      const minStart = workingWindowStart;
      const maxStart = context.initialEnd - MIN_EVENT_DURATION / 60;
      const constrainedStart = Math.max(minStart, Math.min(maxStart, snappedStart));

      // Apply
      height.value = (context.initialEnd - constrainedStart) * HOUR_HEIGHT;
      translateY.value = timeToY(constrainedStart) - timeToY(context.initialStart);

      // Conflict feedback
      const hasConflict = hasOverlapWithBooked(constrainedStart, context.initialEnd);
      opacity.value = withTiming(hasConflict ? 0.6 : 0.9);
    },

    onEnd: (event, context) => {
      const newHeightRaw = context.startHeight - event.translationY;
      const newHeightClamped = Math.max(MINUTE_HEIGHT, newHeightRaw);
      const tentativeStart = context.initialEnd - newHeightClamped / HOUR_HEIGHT;

      // Snap to 15-min intervals
      const snappedStart = snapToFifteenMinutes(tentativeStart);

      const minStart = workingWindowStart;
      const maxStart = context.initialEnd - MIN_EVENT_DURATION / 60;
      const constrainedStart = Math.max(minStart, Math.min(maxStart, snappedStart));

      const hasConflict = hasOverlapWithBooked(constrainedStart, context.initialEnd);

      if (!hasConflict) {
        // ✅ Commit start change
        if (onResize) {
          runOnJS(onResize)(timeToString(constrainedStart), timeToString(context.initialEnd));
        }

        initialEventStart.value = constrainedStart;

        // Smoothly animate to final position (no bounce back)
        height.value = withTiming(
          (context.initialEnd - constrainedStart) * HOUR_HEIGHT,
          { duration: 150 }
        );
        translateY.value = withTiming(
          timeToY(constrainedStart) - timeToY(context.initialStart),
          { duration: 150 }
        );
      } else {
        // ❌ Revert to original
        height.value = withTiming(context.startHeight, { duration: 150 });
        translateY.value = withTiming(0, { duration: 150 });
        runOnJS(showConflictAlert)();
      }

      isResizing.value = false;
      opacity.value = withTiming(1);

      if (onDragEnd) {
        runOnJS(onDragEnd)();
      }
    },
  });


  const tapGestureHandler = useAnimatedGestureHandler({
    onEnd: () => {
      if (!isDragging.value && !isResizing.value) {
        runOnJS(onTap)();
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    const startPosition = (event.start - extendedStartDecimal) * HOUR_HEIGHT;

    return {
      transform: [
        { translateY: translateY.value }
      ],
      opacity: opacity.value,
      top: startPosition,
      height: height.value,
    };
  });

  const resizeHandleStyle = useAnimatedStyle(() => {
    return {
      opacity: isSelected ? withTiming(1) : withTiming(0),
    };
  });

  const isSmallSlot = (event.end - event.start) * HOUR_HEIGHT < 30;
  const isApiEvent = event.isApiEvent;

  // Determine colors
  let backgroundColor = event.color;
  let borderColor = isApiEvent ? '#CCCCCC' : '#2E7D32';

  // if (isSelected && !isApiEvent) {
  //   backgroundColor = '#87CEEB';
  //   borderColor = '#87CEEB';
  // }

  if (!!isEditMode && !isApiEvent) {
    backgroundColor = '#87CEEB'; // Light blue for edit case
    borderColor = '#87CEEB';
  } else if (!isEditMode && isSelected) {
    backgroundColor = '#4CAF50'; // Green for new selection
    borderColor = '#4CAF50';
  }

  return (
    <PanGestureHandler
      onGestureEvent={panGestureHandler}
      enabled={isSelected && !isApiEvent}>
      <Animated.View>
        <TapGestureHandler onGestureEvent={tapGestureHandler} enabled={!isApiEvent}>
          <Animated.View
            style={[
              styles.event,
              animatedStyle,
              {
                backgroundColor,
                borderColor,
                padding: isSmallSlot ? 1 : 4,
                justifyContent: isSmallSlot ? 'flex-start' : 'center',
              },
            ]}
          >
            <View style={[
              styles.textContainer,
              isSmallSlot && styles.smallTextContainer
            ]}>
              {/* Only show labels on API events (booked slots from backend), never on user-created slots */}
              {event.isApiEvent && !isSelected && (
                <Text
                  style={[
                    styles.bookedLabel,
                    isSmallSlot ? styles.smallBookedLabel : null,
                  ]}
                  numberOfLines={1}
                >
                  Booked Slot
                </Text>
              )}
              <Text
                style={[
                  styles.eventTitle,
                  isSelected ? styles.selectedEventTitle : null,
                  isSmallSlot ? styles.smallEventTitle : null,
                  isApiEvent ? styles.apiEventTitle : null,
                ]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {event.title}
              </Text>
            </View>

            {/* Corner Resize Handles */}
            {isSelected && !isApiEvent && (
              <>
                {/* Top-left resize handle */}
                <PanGestureHandler
                  onGestureEvent={resizeTopGestureHandler}
                >
                  <Animated.View style={[
                    styles.resizeHandleTopLeftTouchArea,
                    resizeHandleStyle,
                    { height: (event.end - event.start) * HOUR_HEIGHT }
                  ]}>
                    <Animated.View style={[
                      styles.resizeHandleTopLeft,
                      { backgroundColor: borderColor }
                    ]}>
                      <View style={styles.resizeCornerGrip} />
                    </Animated.View>
                  </Animated.View>
                </PanGestureHandler>
                {/* Bottom-right resize handle */}
                <PanGestureHandler onGestureEvent={resizeGestureHandler}>
                  <Animated.View style={[
                    styles.resizeHandleBottomRightTouchArea,
                    resizeHandleStyle,
                    { height: (event.end - event.start) * HOUR_HEIGHT }
                  ]}>
                    <Animated.View style={[
                      styles.resizeHandleBottomRight,
                      { backgroundColor: borderColor }
                    ]}>
                      <View style={styles.resizeCornerGrip} />
                    </Animated.View>
                  </Animated.View>
                </PanGestureHandler>
              </>
            )}
          </Animated.View>
        </TapGestureHandler>
      </Animated.View>
    </PanGestureHandler>
  );
};

// Timeline Tap Handler Component
const TimelineTapHandler = ({
  children,
  onTap,
  extendedStartDecimal,
  workingWindowStart,
  workingWindowEnd,
}) => {
  const tapGestureHandler = useAnimatedGestureHandler({
    onEnd: (event) => {
      const y = event.y;
      const time = extendedStartDecimal + (y / HOUR_HEIGHT);

      // Only allow taps within working hours
      if (time >= workingWindowStart && time <= workingWindowEnd - (MIN_EVENT_DURATION / 60)) {
        // Snap to 15-minute intervals
        const minutes = Math.round(time * 60);
        const snappedMinutes = Math.round(minutes / SNAP_INTERVAL) * SNAP_INTERVAL;
        const snappedTime = snappedMinutes / 60;

        runOnJS(onTap)(snappedTime);
      }
    },
  });

  return (
    <TapGestureHandler onGestureEvent={tapGestureHandler}>
      <Animated.View style={StyleSheet.absoluteFill}>
        {children}
      </Animated.View>
    </TapGestureHandler>
  );
};

class timeSlotInspection extends Component {
  constructor(props) {
    super(props);
    // Safely get params from either route prop or navigation state
    const params = props.route?.params ||
      props.navigation?.state?.params ||
      {};

    // Parse date from params or use default
    const paramDate = params.date
      ? moment(params.date, "MM/DD/YYYY").toDate()
      : new Date();

    // Use time from params (case-insensitive) or default values
    const paramFromTime = params.startTime
      ? this.formatTimeCase(params.startTime)
      : '00:00 AM';

    const paramToTime = params.endTime
      ? this.formatTimeCase(params.endTime)
      : '00:00 AM';

    this.state = {
      selectedDate: paramDate,
      showDatePicker: false,
      fromTime: paramFromTime,
      toTime: paramToTime,
      showFromTimePicker: false,
      showToTimePicker: false,
      events: [], // Initialize empty events array
      locationID: params.locationId || params.locationID || '',
      equipment: params.equipment || [],
      gateID: params.gateId || params.gateID || '',
      timeZone: params.timeZone,
      projectID: params.projectId,
      bookingId: params.bookingId || null,
      selectedEvent: null,
      selectedEventId: null, // Track which event is selected
      previousValidTimes: {
        fromTime: paramFromTime,
        toTime: paramToTime
      },
      apiResponse: null,
      isLoading: false,
      scrollEnabled: true,
      isDragging: false,
      workingWindowStartHours: 9,
      workingWindowStartMinutes: 30,
      workingWindowEndHours: 17,
      workingWindowEndMinutes: 0,
      isEditMode: paramFromTime !== '00:00 AM' && paramToTime !== '00:00 AM',
      isDayBlocked: false, // Track if entire day is blocked due to all-day events
      blockedDayMessage: '' // Message to show when day is blocked
    };

    this.timeOptions = this.generateTimeOptions();
    this.getProjectSettingsDetails = this.getLogistics.bind(this);
  }

  // Helper method to standardize time case (AM/PM)
  formatTimeCase = (timeStr) => {
    if (!timeStr) return timeStr;
    return timeStr.replace(/\b(am|pm)\b/gi, match => match.toUpperCase());
  };

  // Convert API response to events format
  processApiResponseToEvents = (apiResponse) => {
    if (!apiResponse || !apiResponse.data) return [];

    return apiResponse.data.map((slot, index) => {
      // Parse the ISO timestamps preserving the timezone offset from API
      // Use parseZone to keep the time in its original timezone without conversion
      const startMoment = moment.parseZone(slot.start);
      const endMoment = moment.parseZone(slot.end);

      // Convert to decimal hours (0-24) for timeline positioning
      const startHour = startMoment.hours() + (startMoment.minutes() / 60);
      const endHour = endMoment.hours() + (endMoment.minutes() / 60);

      // Format for display
      const startTime = startMoment.format('h:mm A');
      const endTime = endMoment.format('h:mm A');

      // For calendar events, use the title from the API if available, otherwise use default
      const title = slot.calendarEvent ?
        `Booked Event - ${startTime} - ${endTime}` :
        `${startTime} - ${endTime}`;

      return {
        id: slot.id || `api-event-${index}-${Date.now()}`, // Use original API ID
        apiId: slot.id, // Store original API ID separately for reference
        title: title,
        start: startHour,
        end: endHour,
        color: '#CCCCCC',
        isApiEvent: true,
        isCalendarEvent: slot.calendarEvent || false,
        gateDetails: slot.gateDetails || []
      };
    });
  };

  timeToDecimal = (timeStr) => {
    const time = timeStr.replace(/(AM|PM)/i, ' $1').trim();
    const [timePart, period] = time.split(' ');
    let [hours, minutes] = timePart.split(':').map(Number);

    if (period === 'PM' && hours !== 12) {
      hours += 12;
    } else if (period === 'AM' && hours === 12) {
      hours = 0;
    }

    return hours + (minutes / 60);
  };

  updateSelectedEvent = () => {
    const { fromTime, toTime, isEditMode, isDayBlocked } = this.state;

    // Don't show selected event if day is blocked
    if (isDayBlocked) {
      this.setState({ selectedEvent: null, selectedEventId: null });
      return;
    }

    // Don't show selected event if both times are '00:00 AM'
    if (fromTime === '00:00 AM' && toTime === '00:00 AM' && !isEditMode) {
      this.setState({ selectedEvent: null, selectedEventId: null });
      return;
    }

    const parseTimeToDecimal = (timeStr) => {
      if (typeof timeStr !== 'string') return 0;

      const normalizedTime = timeStr.trim().toUpperCase();
      const [timePart, period] = normalizedTime.split(/(AM|PM)/).filter(Boolean);

      let [hours, minutes] = timePart.split(':').map(Number);

      if (period === 'PM' && hours !== 12) hours += 12;
      if (period === 'AM' && hours === 12) hours = 0;

      return hours + (minutes / 60);
    };

    const start = parseTimeToDecimal(fromTime);
    const end = parseTimeToDecimal(toTime);

    // Ensure minimum slot duration of 15 minutes
    const adjustedEnd = end > start ? end : start + 0.25;

    // Check if this time conflicts with any existing events
    const hasConflict = this.checkForTimeConflict(fromTime, toTime);

    const eventId = `selected-slot-${Date.now()}`;
    this.setState({
      selectedEvent: {
        id: eventId,
        title: isEditMode ?
          `Current Slot - ${fromTime} - ${toTime}` :
          `New Slot - ${fromTime} - ${toTime}`,
        start,
        end: adjustedEnd,
        color: isEditMode ? '#87CEEB' : '#4CAF50',
        hasConflict,
        isApiEvent: false
      },
      selectedEventId: eventId
    });
  };

  // Handle timeline tap to create new event
  handleTimelineTap = (time) => {
    // Convert decimal time to time string
    const hours = Math.floor(time);
    const minutes = Math.round((time - hours) * 60);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    const startTime = `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;

    // Create 30-minute slot by default
    const endTime = this.minutesToTimeString(this.timeToMinutes(startTime) + 15);

    // Check for conflicts
    if (this.checkForTimeConflict(startTime, endTime)) {
      Alert.alert(
        'Slot Not Available',
        'This time slot is already booked. Please choose a different time.',
        [{ text: 'OK' }]
      );
      return;
    }

    this.setState({
      fromTime: startTime,
      toTime: endTime,
      previousValidTimes: { fromTime: startTime, toTime: endTime }
    }, this.updateSelectedEvent);
  };

  // Handle event tap
  handleEventTap = (eventId) => {
    this.setState({
      selectedEventId: this.state.selectedEventId === eventId ? null : eventId
    });
  };

  // Handle event move
  handleEventMove = (startTime, endTime) => {
    this.setState({
      fromTime: startTime,
      toTime: endTime,
      previousValidTimes: { fromTime: startTime, toTime: endTime }
    }, this.updateSelectedEvent);
  };

  // Handle event resize
  handleEventResize = (startTime, endTime) => {
    this.setState({
      fromTime: startTime,
      toTime: endTime,
      previousValidTimes: { fromTime: startTime, toTime: endTime }
    }, this.updateSelectedEvent);
  };

  onDragStart = () => {
    this.setState({ scrollEnabled: false, isDragging: true });
  };

  onDragEnd = () => {
    this.setState({ scrollEnabled: true, isDragging: false });
  };

  isTimeInEvent(time, event) {
    return time >= event.start && time < event.end;
  }

  isTimeOccupied(time) {
    return this.state.events.some(event => this.isTimeInEvent(time, event));
  }

  // Compute the extended window shown in the timeline
  getExtendedWindowDecimals = () => {
    const { workingWindowStartHours, workingWindowStartMinutes, workingWindowEndHours, workingWindowEndMinutes } = this.state;
    const workingStartDecimal = workingWindowStartHours + (workingWindowStartMinutes / 60);
    const workingEndDecimal = workingWindowEndHours + (workingWindowEndMinutes / 60);
    const extendedStartDecimal = Math.floor(workingStartDecimal);
    // Always extend to show the full hour that contains the working end time
    const extendedEndDecimal = Math.ceil(workingEndDecimal);
    return { workingStartDecimal, workingEndDecimal, extendedStartDecimal, extendedEndDecimal };
  }

  renderHours() {
    const { extendedStartDecimal, extendedEndDecimal } = this.getExtendedWindowDecimals();
    const totalDuration = extendedEndDecimal - extendedStartDecimal;
    if (totalDuration <= 0.5) {
      return null;
    }
    const hours = [];
    // Generate hours for the full extended range, including the end hour
    const startHour = Math.floor(extendedStartDecimal);
    const endHour = Math.ceil(extendedEndDecimal);

    for (let hour = startHour; hour <= endHour; hour++) {
      const displayHours = hour % 12 || 12;
      const period = hour >= 12 ? 'PM' : 'AM';
      hours.push(
        <View key={`hour-${hour}`} style={styles.hourContainer}>
          <Text style={styles.hourText}>{`${displayHours}:00 ${period}`}</Text>
          <View style={styles.hourLine} />
        </View>
      );
    }
    return hours;
  }

  renderEvents() {
    const { events, selectedEvent, selectedEventId, isEditMode } = this.state;
    const allEvents = [...events];

    if (selectedEvent) {
      allEvents.push(selectedEvent);
    }

    // Filter events to only show those within working hours
    const workingWindowStartDecimal = this.state.workingWindowStartHours + (this.state.workingWindowStartMinutes / 60);
    const workingWindowEndDecimal = this.state.workingWindowEndHours + (this.state.workingWindowEndMinutes / 60);
    const { extendedStartDecimal } = this.getExtendedWindowDecimals();

    let filteredEvents = allEvents.filter(event => {
      return event.start >= workingWindowStartDecimal && event.start < workingWindowEndDecimal;
    });


    // While editing, hide only the specific booking being edited (not all bookings)
    const { bookingId } = this.state;
    if (isEditMode) {
      const beforeEditFilter = filteredEvents.length;
      filteredEvents = filteredEvents.filter(event => {
        if (!event.isApiEvent) return true;
        // Only hide the specific booking being edited to avoid showing it twice
        if (bookingId && (event.id === bookingId || event.apiId === bookingId || event.bookingId === bookingId)) {
          return false;
        }
        return true;
      });
    }

    // Build static booked intervals for worklet use (decimal hours)
    // In edit mode, exclude the original booking from conflict detection
    let bookedIntervals = events
      .filter(ev => {
        if (!ev || !ev.isApiEvent) return false;
        // Skip the event being edited - check all possible ID fields
        if (bookingId && (ev.id === bookingId || ev.apiId === bookingId || ev.bookingId === bookingId)) {
          return false;
        }
        // Ensure event has valid start and end times
        if (typeof ev.start !== 'number' || typeof ev.end !== 'number') {
          return false;
        }
        // Skip invalid events where start >= end
        if (ev.start >= ev.end) {
          return false;
        }
        return true;
      })
      .map(ev => ({ start: ev.start, end: ev.end }));

    return filteredEvents.map(event => {
      const isSelected = event.id === selectedEventId;

      return (
        <AnimatedEvent
          key={event.id}
          event={event}
          isSelected={isSelected}
          onMove={this.handleEventMove}
          onResize={this.handleEventResize}
          onTap={() => this.handleEventTap(event.id)}
          onDragStart={this.onDragStart}
          onDragEnd={this.onDragEnd}
          bookedIntervals={bookedIntervals}
          extendedStartDecimal={extendedStartDecimal}
          workingWindowStart={workingWindowStartDecimal}
          workingWindowEnd={workingWindowEndDecimal}
          checkConflict={this.checkForTimeConflict}
          isEditMode={isEditMode}
        />
      );
    });
  }

  renderTimeSlots() {
    const slots = [];
    const { workingWindowStartHours, workingWindowStartMinutes, workingWindowEndHours, workingWindowEndMinutes } = this.state;

    // Convert working hours to decimal
    const startDecimal = workingWindowStartHours + (workingWindowStartMinutes / 60);
    const endDecimal = workingWindowEndHours + (workingWindowEndMinutes / 60);

    // Generate 15-min clickable points across the window,
    // but for the last 30 minutes only keep two anchors: end-30 and end-15
    for (let time = startDecimal; time <= endDecimal; time += 0.25) {
      const inLastHalfHourWindow = time >= (endDecimal - 0.5);
      if (inLastHalfHourWindow && !(Math.abs(time - (endDecimal - 0.5)) < 1e-6 || Math.abs(time - (endDecimal - 0.25)) < 1e-6)) {
        continue;
      }

      const isOccupied = this.isTimeOccupied(time);
      if (!isOccupied) {
        const top = (time - startDecimal) * HOUR_HEIGHT;
        const hour = Math.floor(time);
        const minute = Math.round((time - hour) * 60);
        const isHourMark = minute === 0;
        slots.push(
          <View key={`slot-${time}`} style={[styles.timeSlot, { top }]}>
            {isHourMark ? (
              <View style={styles.hourMarker} />
            ) : (
              <View style={styles.minuteMarker} />
            )}
          </View>
        );
      }
    }
    return slots;
  }

  generateTimeOptions = () => {
    const options = [];
    const { workingWindowStartHours, workingWindowStartMinutes, workingWindowEndHours, workingWindowEndMinutes } = this.state;

    // Convert working hours to decimal
    const startDecimal = workingWindowStartHours + (workingWindowStartMinutes / 60);
    const endDecimal = workingWindowEndHours + (workingWindowEndMinutes / 60);

    // Generate options within working hours with last 30 min rule
    for (let time = startDecimal; time <= endDecimal; time += 0.25) { // 15 minutes = 0.25 hours
      const inLastHalfHourWindow = time >= (endDecimal - 0.5);
      if (inLastHalfHourWindow && !(Math.abs(time - (endDecimal - 0.5)) < 1e-6 || Math.abs(time - (endDecimal - 0.25)) < 1e-6)) {
        continue;
      }
      const hour = Math.floor(time);
      const minute = Math.round((time - hour) * 60);
      const displayHour = hour % 12 || 12; // Convert 0 to 12
      const period = hour < 12 ? 'AM' : 'PM';
      options.push(`${displayHour}:${minute.toString().padStart(2, '0')} ${period}`);
    }

    // Ensure exact end time is present as a TO option
    const endHour = Math.floor(endDecimal);
    const endMinute = Math.round((endDecimal - endHour) * 60);
    const endDisplayHour = endHour % 12 || 12;
    const endPeriod = endHour < 12 ? 'AM' : 'PM';
    const endLabel = `${endDisplayHour}:${endMinute.toString().padStart(2, '0')} ${endPeriod}`;
    if (!options.includes(endLabel)) {
      options.push(endLabel);
    }
    return options;
  };

  formatTime(time) {
    const hours = Math.floor(time);
    const minutes = Math.round((time - hours) * 60);
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    return `${displayHours}:${minutes < 10 ? '0' + minutes : minutes} ${ampm}`;
  }

  timeToMinutes = (timeStr) => {
    const [time, period] = timeStr.split(' ');
    const [hours, minutes] = time.split(':').map(Number);

    let totalMinutes = hours * 60 + minutes;
    if (period === 'PM' && hours !== 12) totalMinutes += 12 * 60;
    if (period === 'AM' && hours === 12) totalMinutes -= 12 * 60;

    return totalMinutes;
  };

  minutesToTimeString = (totalMinutes) => {
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  formatDate = (date) => {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const day = date.getDate().toString().padStart(2, '0');
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${day} ${month} ${year}`;
  };

  handleDateChange = (event, date) => {
    const selectedDate = date || this.state.selectedDate;
    this.setState({
      showDatePicker: false,
      selectedDate,
      events: [], // Clear existing events while loading new ones
      isDayBlocked: false,
      blockedDayMessage: ''
    }, () => {
      this.postAvailableTimeSlots(); // Call API after state update
    });
  };


  navigateDate = (days) => {
    const newDate = new Date(this.state.selectedDate);
    newDate.setDate(newDate.getDate() + days);
    this.setState({
      selectedDate: newDate,
      events: [], // Clear existing events while loading new ones
      isDayBlocked: false,
      blockedDayMessage: ''
    }, () => {
      this.postAvailableTimeSlots(); // Call API after state update
    });
  };

  handleFromTimeSelect = (time) => {
    const fromTimeIndex = this.timeOptions.indexOf(time);
    // Get the next 30-minute slot
    let newToTime = this.timeOptions[fromTimeIndex + 1] || time;

    // Check for conflict
    if (this.checkForTimeConflict(time, newToTime)) {
      Alert.alert(
        'Slot Not Available',
        'This time slot is already booked. Please choose a different time.',
        [{ text: 'OK' }]
      );
      // Reset to previous valid times
      this.setState({
        fromTime: this.state.previousValidTimes.fromTime,
        toTime: this.state.previousValidTimes.toTime,
        showFromTimePicker: false
      }, this.updateSelectedEvent);
      return;
    }

    this.setState({
      fromTime: time,
      toTime: newToTime,
      showFromTimePicker: false,
      previousValidTimes: { fromTime: time, toTime: newToTime }
    }, this.updateSelectedEvent);
  };

  handleToTimeSelect = (time) => {
    // Ensure minimum 30-minute duration
    const fromTimeIndex = this.timeOptions.indexOf(this.state.fromTime);
    const toTimeIndex = this.timeOptions.indexOf(time);

    if (toTimeIndex - fromTimeIndex < 1) {
      Alert.alert(
        'Invalid Duration',
        'Please select a time slot of at least 30 minutes.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Check for conflict
    if (this.checkForTimeConflict(this.state.fromTime, time)) {
      Alert.alert(
        'Slot Not Available',
        'This time slot is already booked. Please choose a different time.',
        [{ text: 'OK' }]
      );
      // Reset to previous valid times
      this.setState({
        toTime: this.state.previousValidTimes.toTime,
        showToTimePicker: false
      }, this.updateSelectedEvent);
      return;
    }

    this.setState({
      toTime: time,
      showToTimePicker: false,
      previousValidTimes: {
        fromTime: this.state.fromTime,
        toTime: time
      }
    }, this.updateSelectedEvent);
  };

  onBackPress = async () => {
    this.props.navigation.goBack();
    return true;
  };

  handleSubmit = async () => {
    const { selectedDate, fromTime, toTime, events, inspectionAllowOverlappingBooking, inspectionAllowOverlappingCalenderEvents, bookingId } = this.state;

    // Convert selected times to decimal hours
    const selectedStart = this.timeToDecimal(fromTime);
    const selectedEnd = this.timeToDecimal(toTime);

    // Check for conflicts with API events
    let hasConflict = false;

    // Small epsilon to handle floating point precision issues
    const EPSILON = 0.001;

    for (const event of events) {
      if (!event || !event.isApiEvent) continue;

      // Skip conflict check if this is the event being edited
      if (bookingId && (event.id === bookingId || event.apiId === bookingId || event.bookingId === bookingId)) {
        continue;
      }

      // Skip the "original" placeholder event shown during edit mode
      if (event.isOriginal) {
        continue;
      }

      // Safety check: ensure event has valid start and end values
      if (typeof event.start !== 'number' || typeof event.end !== 'number') {
        continue;
      }

      // Skip invalid events where start >= end
      if (event.start >= event.end) {
        continue;
      }

      // Two time ranges overlap if:
      // - new start is strictly before event end AND
      // - new end is strictly after event start
      // Adjacent slots (where one ends exactly when another starts) should NOT overlap
      const hasOverlap = (
        (selectedStart < event.end - EPSILON) &&
        (selectedEnd > event.start + EPSILON)
      );

      if (!hasOverlap) continue;

      // If there's an overlap, check if it's allowed based on the event type
      if (event.isCalendarEvent) {
        // For calendar events, only prevent if overlapping is not allowed
        if (!inspectionAllowOverlappingCalenderEvents) {
          hasConflict = true;
          break;
        }
      } else {
        // For regular bookings, only prevent if overlapping is not allowed
        if (!inspectionAllowOverlappingBooking) {
          hasConflict = true;
          break;
        }
      }
    }

    if (hasConflict) {
      Alert.alert(
        'Overlapping Not Allowed',
        'This time slot overlaps with an existing booking and overlapping is not allowed.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Create the data object to be stored
    const data = {
      date: `${(selectedDate.getMonth() + 1).toString().padStart(2, '0')}/${selectedDate.getDate().toString().padStart(2, '0')}/${selectedDate.getFullYear()}`,
      fromTime,
      toTime
    };

    try {
      await AsyncStorage.setItem('Isback', "true");
      await AsyncStorage.setItem('DRDateTime', JSON.stringify(data));
      this.props.navigation.navigate('addinspection', { timestamp: Date.now() });
    } catch (error) {
      console.error('Error storing data:', error);
    }
  };

  formatDateForAddDR = (date) => {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${month}/${day}/${year}`;
  };

  componentDidMount() {
    this.postAvailableTimeSlots();
    this.updateSelectedEvent();
    this.getLogistics();
  }

  postAvailableTimeSlots = () => {
    if (!this.state.projectID) {
      return;
    }

    this.setState({ isLoading: true });

    const selectedEquipmentIds = this.state.equipment
      .filter(item => item.selected === true)
      .map(item => item.id);

    // Format date as MM/DD/YYYY for API
    const formattedDate = moment(this.state.selectedDate).format("YYYY-MM-DD");

    const URL = `${GET_TIME_SLOTS}?ProjectId=${this.state.projectID}`;
    const params = {
      date: formattedDate,
      currentBookingId: this.state.bookingId || undefined,
      equipmentId: selectedEquipmentIds,
      timeZone: this.state.timeZone || "",
      GateId: this.state.gateID,
      LocationId: this.state.locationID,
      bookingType: 'inspectionRequest'
    };

    getSlotsDetail(
      URL,
      params,
      () => { },
      (response) => {
        this.setState({ isLoading: false });

        if (response?.data?.message?.statusCode === 400) {
          Alert.alert(
            'Error',
            'Invalid request parameters. Please try again.',
            [{ text: 'OK' }]
          );
          return;
        }

        // First check if response exists and has data property
        if (!response || !response.data) {
          this.setState({
            errorMessage: 'No data received from server',
            events: []
          });
          return;
        }

        // Handle case where data might not be an array
        let apiData = response.data;
        if (!Array.isArray(apiData)) {
          // Check if data is in the format you shared { message, data: [...] }
          if (apiData.data && Array.isArray(apiData.data)) {
            apiData = apiData.data;
          } else {
            this.setState({
              errorMessage: 'Unexpected data format from server',
              events: []
            });
            return;
          }
        }


        try {
          // Check for all-day events or midnight-to-midnight events in the API response
          let hasAllDayEvent = false;
          let allDayEventTitle = '';

          for (const slot of apiData) {
            // Parse timestamps preserving the timezone offset from API
            const startMoment = moment.parseZone(slot.start);
            const endMoment = moment.parseZone(slot.end);
            const startHour = startMoment.hours() + (startMoment.minutes() / 60);
            const endHour = endMoment.hours() + (endMoment.minutes() / 60);
            const isMidnightToMidnight = startHour === 0 && endHour === 0;
            const isAllDay = slot.isAllDay === true;

            if (isAllDay || isMidnightToMidnight) {
              hasAllDayEvent = true;
              allDayEventTitle = slot.title || 'All-day event';
              break;
            }
          }

          // If there's an all-day event, block the entire day
          if (hasAllDayEvent) {
            this.setState({
              events: [],
              apiResponse: apiData,
              isDayBlocked: true,
              blockedDayMessage: `This day has an all-day event: "${allDayEventTitle}". No time slots available for booking.`,
              selectedEvent: null,
              selectedEventId: null
            }, () => {
              this.updateSelectedEvent();
            });
            return;
          }

          // Process the API response into events (only if no all-day events)
          const apiEvents = apiData.map((slot, index) => {
            // Parse the ISO timestamps preserving the timezone offset from API
            // Use parseZone to keep the time in its original timezone without conversion
            const startMoment = moment.parseZone(slot.start);
            const endMoment = moment.parseZone(slot.end);

            // Convert to decimal hours (0-24) for timeline positioning
            const startHour = startMoment.hours() + (startMoment.minutes() / 60);
            const endHour = endMoment.hours() + (endMoment.minutes() / 60);

            // Format for display
            const timeString = `${startMoment.format('h:mm A')} - ${endMoment.format('h:mm A')}`;

            return {
              id: slot.id || `api-event-${index}-${Date.now()}`, // Use original API ID
              apiId: slot.id, // Store original API ID separately for reference
              bookingId: slot.id, // Also store as bookingId for consistent filtering
              title: timeString,
              start: startHour,
              end: endHour,
              color: '#CCCCCC',
              isApiEvent: true,
              isCalendarEvent: slot.calendarEvent || false,
              isAllDay: slot.isAllDay || false,
              gateDetails: slot.gateDetails || [],
              equipmentDetail: slot.equipmentDetail || [],
              type: slot.type || slot.requestType || null
            };
          });
          this.setState({
            events: apiEvents,
            apiResponse: apiData,
            isDayBlocked: false,
            blockedDayMessage: ''
          }, () => {
            this.updateSelectedEvent();
          });
        } catch (error) {
          console.log("Time Slots API Processing Error:", error);
          this.setState({
            errorMessage: 'Failed to process time slots',
            events: [],
            isDayBlocked: false,
            blockedDayMessage: ''
          }, () => {
            this.updateSelectedEvent();
          });
        }
      },
      (error) => {
        console.log("Time Slots API Failure:", error);
        this.setState({ isLoading: false });
        Alert.alert('Error', 'Network error occurred while fetching time slots');
      }
    );
  };

  getLogistics = () => {
    // Validate projectID
    if (!this.state.projectID) {
      return;
    }

    const URL = `${GET_PROJECT_SETTING_DETAILS}?ProjectId=${this.state.projectID}`;

    getProjectSettingsDetails(
      URL,
      {},
      () => { },
      (response) => {
        if (response?.data?.status === 500) {
          Alert.alert(
            'Error',
            'Server error occurred while fetching project settings.',
            [{ text: 'OK' }]
          );
          return;
        }

        if (response?.data?.status == 200) {
          const settings = response?.data?.data?.projectSettings;
          // Set default values if null
          this.setState({
            workingWindowStartHours: settings.workingWindowStartHours ?? 0,
            workingWindowStartMinutes: settings.workingWindowStartMinutes ?? 0,
            workingWindowEndHours: settings.workingWindowEndHours ?? 24,
            workingWindowEndMinutes: settings.workingWindowEndMinutes ?? 0
          }, () => {
            this.timeOptions = this.generateTimeOptions();
          });
        } else if (response?.data?.status == 401) {
          Alert.alert(
            'Error',
            'You are not authorized to access this project.',
            [{ text: 'OK' }]
          );
        } else if (response?.data?.status == 404) {
          Alert.alert(
            'Error',
            'Project settings not found.',
            [{ text: 'OK' }]
          );
        } else {
          Alert.alert(
            'Error',
            'Failed to fetch project settings.',
            [{ text: 'OK' }]
          );
        }
      },
      (error) => {
        console.log("Project Settings API Error:", error);
        // Set default values if API fails
        this.setState({
          workingWindowStartHours: 9,
          workingWindowStartMinutes: 30,
          workingWindowEndHours: 17,
          workingWindowEndMinutes: 0
        }, () => {
          this.timeOptions = this.generateTimeOptions();
        });
        Alert.alert(
          'Error',
          'Failed to fetch project settings. Using default values.',
          [{ text: 'OK' }]
        );
      }
    );
  };

  checkForTimeConflict = (fromTime, toTime) => {
    const { events, bookingId } = this.state;

    // Safety check: if no events exist, there can't be a conflict
    if (!events || !Array.isArray(events) || events.length === 0) {
      return false;
    }

    const selectedStart = this.timeToMinutes(fromTime);
    const selectedEnd = this.timeToMinutes(toTime);

    // Check if selected time is valid
    if (selectedStart >= selectedEnd) {
      return true;
    }

    // Check for conflicts with existing events
    for (const event of events) {
      // Only check API events (booked slots)
      if (!event || !event.isApiEvent) continue;

      // Skip conflict check if this is the event being edited - check all possible ID fields
      if (bookingId && (event.id === bookingId || event.apiId === bookingId || event.bookingId === bookingId)) {
        continue;
      }

      // Skip the "original" placeholder event shown during edit mode
      if (event.isOriginal) {
        continue;
      }

      // Safety check: ensure event has valid start and end values
      if (typeof event.start !== 'number' || typeof event.end !== 'number') {
        continue;
      }

      // Convert event times to minutes
      const eventStart = event.start * 60; // Convert decimal hours to minutes
      const eventEnd = event.end * 60;

      // Skip invalid events where start >= end
      if (eventStart >= eventEnd) {
        continue;
      }

      // Small epsilon to handle floating point precision issues (0.06 minutes = ~3.6 seconds)
      const EPSILON = 0.06;

      // Two time ranges overlap if:
      // - new start is strictly before event end AND
      // - new end is strictly after event start
      // Adjacent slots (where one ends exactly when another starts) should NOT overlap
      const hasOverlap = (
        (selectedStart < eventEnd - EPSILON) &&
        (selectedEnd > eventStart + EPSILON)
      );

      if (hasOverlap) {
        return true;
      }
    }

    return false;
  };

  formatWorkingHoursText = () => {
    const { workingWindowStartHours, workingWindowStartMinutes, workingWindowEndHours, workingWindowEndMinutes } = this.state;

    const formatTime = (hours, minutes) => {
      const period = hours >= 12 ? 'PM' : 'AM';
      const displayHours = hours % 12 || 12;
      return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
    };

    const startTime = formatTime(workingWindowStartHours, workingWindowStartMinutes);
    const endTime = formatTime(workingWindowEndHours, workingWindowEndMinutes);

    return `${startTime} to ${endTime}`;
  }

  renderBlockedOverlays = () => {
    const { workingWindowStartHours, workingWindowStartMinutes, workingWindowEndHours, workingWindowEndMinutes } = this.state;
    const { extendedStartDecimal, extendedEndDecimal } = this.getExtendedWindowDecimals();
    const workingStartDecimal = workingWindowStartHours + (workingWindowStartMinutes / 60);
    const workingEndDecimal = workingWindowEndHours + (workingWindowEndMinutes / 60);

    const overlays = [];
    // Block before working start
    if (extendedStartDecimal < workingStartDecimal) {
      overlays.push(
        <View
          key="blocked-before"
          style={[
            styles.blockedOverlay,
            {
              top: (workingStartDecimal - extendedStartDecimal <= 0 ? 0 : 0) + 0,
              height: (workingStartDecimal - extendedStartDecimal) * HOUR_HEIGHT,
            },
          ]}
          pointerEvents="none"
        />
      );
    }
    // Block after working end
    if (extendedEndDecimal > workingEndDecimal) {
      overlays.push(
        <View
          key="blocked-after"
          style={[
            styles.blockedOverlay,
            {
              top: (workingEndDecimal - extendedStartDecimal) * HOUR_HEIGHT,
              height: (extendedEndDecimal - workingEndDecimal) * HOUR_HEIGHT,
            },
          ]}
          pointerEvents="none"
        />
      );
    }
    return overlays;
  }

  render() {
    const {
      selectedDate,
      showDatePicker,
      fromTime,
      toTime,
      showFromTimePicker,
      showToTimePicker,
      isLoading,
      isDayBlocked,
      blockedDayMessage
    } = this.state;

    const { workingWindowStartHours, workingWindowStartMinutes, workingWindowEndHours, workingWindowEndMinutes } = this.state;
    const { extendedStartDecimal, extendedEndDecimal } = this.getExtendedWindowDecimals();
    const workingStartDecimal = workingWindowStartHours + (workingWindowStartMinutes / 60);
    const workingEndDecimal = workingWindowEndHours + (workingWindowEndMinutes / 60);

    return (
      <View style={styles.container}>
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.themeColor} />
          </View>
        )}
        <View style={styles.headerContainer}>
          <Text style={styles.header}>Select Date & Time</Text>
        </View>

        <View style={styles.dateSelector}>
          <TouchableOpacity onPress={() => this.navigateDate(-1)}>
            <Image
              source={Images.arrow_left}
              style={styles.arrowImage}
              resizeMode="contain"
            />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => this.setState({ showDatePicker: true })}>
            <Text style={styles.dateText}>{this.formatDate(selectedDate)}</Text>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => this.navigateDate(1)}>
            <Image
              source={Images.arrow_right}
              style={styles.arrowImage}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>

        {showDatePicker && (
          <DateTimePicker
            value={selectedDate}
            mode="date"
            display={Platform.OS === 'ios' ? 'inline' : 'default'}
            onChange={this.handleDateChange}
            minimumDate={new Date()}
            style={{
              backgroundColor: "#fff",
              width: '100%',
            }}
            {...(Platform.OS === 'ios' && {
              themeVariant: 'light',
              textColor: "#000",
              accentColor: Colors.themeColor
            })}
          />
        )}

        <View style={styles.workingHoursContainer}>
          <Text style={styles.workingHoursText}>
            <View>
              <Text>
                <Text style={styles.boldText}>Project Working Hours: </Text>
                {this.formatWorkingHoursText()}
              </Text>
            </View>
          </Text>
          <Text style={styles.TimeZoneText}>
            <Text style={styles.boldText}>TimeZone: </Text>
            <Text style={styles.timeZoneValue}>{this.state.timeZone}</Text>
          </Text>
        </View>

        <ScrollView
          contentContainerStyle={{ flexGrow: 0 }}
          scrollEnabled={this.state.scrollEnabled}
          keyboardShouldPersistTaps="handled"
        >
          <View style={[styles.timeline, {
            height: (() => {
              const { extendedStartDecimal, extendedEndDecimal } = this.getExtendedWindowDecimals();
              // Ensure we show the full range by using ceil for the end
              const displayEndDecimal = Math.ceil(extendedEndDecimal);
              const calculatedHeight = (displayEndDecimal - extendedStartDecimal) * HOUR_HEIGHT;
              // Add extra space to ensure all hours are visible
              const heightWithBuffer = calculatedHeight + (HOUR_HEIGHT * 0.5); // Add half an hour of extra space
              return heightWithBuffer;
            })()
          }]}>
            <View style={styles.hoursColumn}>{this.renderHours()}</View>
            <View style={styles.eventsColumn}>
              {(() => {
                const { extendedStartDecimal: startDecimal, extendedEndDecimal: endDecimal } = this.getExtendedWindowDecimals();
                const steps = Math.round((endDecimal - startDecimal) * 4); // 15m steps
                return Array.from({ length: steps + 1 }).map((_, i) => {
                  const current = startDecimal + (i / 4);
                  // Hour start aligned to working window start (e.g., 9:30, 10:30, ...)
                  const isHourStart = Math.abs(((current - startDecimal) % 1)) < 1e-6;
                  const isEndBoundary = Math.abs(current - endDecimal) < 1e-6;
                  return (
                    <View
                      key={`minute-${i}`}
                      style={[
                        styles.minuteMarker,
                        { top: (current - startDecimal) * HOUR_HEIGHT },
                        isEndBoundary ? styles.endHourMarker : (isHourStart ? styles.hourMarker : null),
                      ]}
                      pointerEvents="none"
                    />
                  );
                });
              })()}
              {this.renderBlockedOverlays()}

              {/* Timeline Tap Handler */}
              {!isDayBlocked && (
                <TimelineTapHandler
                  onTap={this.handleTimelineTap}
                  extendedStartDecimal={extendedStartDecimal}
                  workingWindowStart={workingStartDecimal}
                  workingWindowEnd={workingEndDecimal}
                >
                  <View style={StyleSheet.absoluteFill} />
                </TimelineTapHandler>
              )}
              {this.renderEvents()}

              {/* Full Day Blocked Overlay */}
              {isDayBlocked && (
                <View style={styles.fullDayBlockedOverlay}>
                  <View style={styles.blockedOverlayContent}>
                    <Text style={styles.blockedOverlayTitle}>Day Blocked</Text>
                    <Text style={styles.blockedOverlayText}>This day has all-day events.</Text>
                  </View>
                </View>
              )}
            </View>
          </View>
        </ScrollView>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={this.onBackPress}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.submitButton, isDayBlocked && styles.submitButtonDisabled]}
            onPress={isDayBlocked ? null : this.handleSubmit}
            disabled={isDayBlocked}
          >
            <Text style={[styles.submitButtonText, isDayBlocked && styles.submitButtonTextDisabled]}>Submit</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 30,
    backgroundColor: 'white',
  },
  header: {
    fontSize: 24,
    fontWeight: '900',
    fontFamily: Fonts.montserratBold
  },
  dateSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
  dateText: {
    fontSize: 16,
    fontFamily: Fonts.montserratBold,
    margin: 10,
  },
  arrowImage: {
    width: 20,
    height: 20,
    tintColor: '#CCCCCC',
  },
  timeSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  timeColumn: {
    flex: 1,
    marginRight: 10,
  },
  timeLabel: {
    color: Colors.placeholder,
    fontFamily: Fonts.montserratMedium,
    fontSize: 12,
    marginLeft: 10
  },
  timeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderColor: '#ccc',
    padding: 5,
    marginHorizontal: 10,
  },
  timeButtonText: {
    fontSize: 14,
    fontFamily: Fonts.montserratMedium
  },
  timeline: {
    flexDirection: 'row',
  },
  event: {
    position: 'absolute',
    left: 10,
    right: 10,
    borderRadius: 4,
    padding: 4,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: MINUTE_HEIGHT,
    borderWidth: 2,
  },
  resizeHandle: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 0,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  resizeHandleTopLeft: {
    position: 'absolute',
    top: -12,
    left: 0,
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  resizeHandleBottomRight: {
    position: 'absolute',
    bottom: -12,
    right: 0,
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  resizeCornerGrip: {
    width: 4,
    height: 4,
    backgroundColor: '#fff',
    borderRadius: 2,
  },
  resizeHandleTopLeftTouchArea: {
    position: 'absolute',
    top: 0,
    left: -10,
    width: "25%",
    height: 120,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    zIndex: 10,
  },
  resizeHandleBottomRightTouchArea: {
    position: 'absolute',
    bottom: 0,
    right: -10,
    width: "25%",
    height: 120,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    zIndex: 10,
  },
  resizeGrip: {
    width: 20,
    height: 2,
    backgroundColor: 'rgba(255,255,255,0.8)',
    borderRadius: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: Colors.borderGreyColor,
    padding: 15,
    borderRadius: 25,
    marginRight: 10,
    alignItems: 'center',
  },
  submitButton: {
    flex: 1,
    backgroundColor: Colors.pendingEventColor,
    padding: 15,
    borderRadius: 25,
    marginLeft: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: Colors.lightGrey,
    fontFamily: Fonts.montserratMedium,
    fontSize: 16
  },
  submitButtonText: {
    color: Colors.themeColor,
    fontFamily: Fonts.montserratMedium,
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    width: '80%',
    maxHeight: '60%',
    borderRadius: 10,
    padding: 20,
  },
  timeOption: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  closeButton: {
    marginTop: 10,
    padding: 10,
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 5,
  },
  headerContainer: {
    marginTop: 20,
    alignItems: 'center',
    justifyContent: 'center'
  },
  shadowLine: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 1,
    elevation: 5,
    backgroundColor: '#fff',
    borderRadius: 1,
    paddingBottom: 0.5
  },
  workingHoursContainer: {
    margin: 20,
  },
  workingHoursText: {
    fontSize: 14,
    color: Colors.black,
    lineHeight: 18,
    fontFamily: Fonts.montserratLight,
    fontWeight: '900'
  },
  boldText: {
    fontWeight: '900',
    fontFamily: Fonts.montserratBold,
    color: Colors.black
  },
  timelineScrollView: {
    flex: 1,
  },
  hoursColumn: {
    width: 70,
    paddingRight: 10,
    alignItems: 'flex-end',
  },
  hourContainer: {
    height: HOUR_HEIGHT,
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingRight: 10,
  },
  hourText: {
    fontSize: 10,
    color: '#666',
    textAlign: 'right',
  },
  hourLine: {
    height: 1,
    backgroundColor: '#eee',
    width: '100%',
    marginTop: 5,
  },
  eventsColumn: {
    flex: 1,
    borderLeftWidth: 1,
    borderLeftColor: '#eee',
    position: 'relative',
    paddingRight: 10,
  },
  blockedOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.07)',
    zIndex: 2,
  },
  minuteMarker: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: '#f5f5f5',
    zIndex: -1,
  },
  hourMarker: {
    backgroundColor: '#ddd',
  },
  eventTitle: {
    fontWeight: 'bold',
    color: '#333',
    fontSize: 10,
    textAlign: 'center',
    width: '100%',
  },
  selectedEvent: {
    zIndex: 10,
    justifyContent: 'center',
    padding: 2,
  },
  conflictEvent: {
    borderWidth: 2,
    borderColor: '#D32F2F',
    zIndex: 10,
  },
  selectedEventTitle: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  conflictEventTitle: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  loadingContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.7)',
    zIndex: 100
  },
  dragSelection: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
    borderWidth: 1,
    borderColor: '#4CAF50',
    zIndex: 5,
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  smallTextContainer: {
    justifyContent: 'flex-start',
    marginTop: -2,
  },
  smallEventTitle: {
    fontSize: 8,
    padding: 1,
    textAlign: 'left',
    marginTop: 0,
  },
  apiEventTitle: {
    color: '#666666',
  },
  bookedLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: Colors.blackGrey,
    textAlign: 'center',
    marginBottom: 2,
    textTransform: 'capitalize',
  },
  smallBookedLabel: {
    fontSize: 10,
    marginBottom: 1,
  },
  endHourMarker: {
    backgroundColor: Colors.themeColor,
    height: 2,
  },
  TimeZoneText: {
    fontSize: 14,
    color: Colors.black,
    lineHeight: 18,
    fontFamily: Fonts.montserratLight,
    fontWeight: '900',
    marginTop: 10,
    marginBottom: 10,
  },
  timeZoneValue: {
    flexShrink: 1,
    fontWeight: 'normal',
  },
  fullDayBlockedOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(128, 128, 128, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  blockedOverlayContent: {
    backgroundColor: '#fff',
    padding: 30,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  blockedOverlayTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: Fonts.montserratBold,
    color: '#D32F2F',
    marginBottom: 10,
  },
  blockedOverlayText: {
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
    color: '#666',
    textAlign: 'center',
    marginTop: 5,
  },
  submitButtonDisabled: {
    backgroundColor: '#E0E0E0',
    opacity: 0.6,
  },
  submitButtonTextDisabled: {
    color: '#9E9E9E',
  },
});

export default timeSlotInspection;