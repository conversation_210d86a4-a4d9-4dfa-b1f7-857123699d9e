import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Image,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  StatusBar,
  Platform,
} from "react-native";

import {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  clickAdd,
  editData,
  onTapSearch,
  storeRole,
  goToVoid,
  refreshDeliveryList,
  refreshDashboard,
} from "../../actions/postAction";
import { connect } from "react-redux";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import Colors from "../../common/color";
import Images from "../../common/images";
import Strings from "../../common/string";
import Fonts from "../../common/fonts";
import {
  GET_DR_LIST,
  VOID_DR,
  CREATE_VOID_INS,
  GET_NEW_COMPANIES,
  GET_EQUIPMENT_TYPE_LIST,
  GET_GATE_LIST,
  LIST_ALL_MEMBER,
  RESTORE_VOID,
  CRANE_DELIVERY_LIST,
  GET_EQUIP_LIST,
  GET_VOID_DETAILS,
  CONCRETE_DROPDOWN_DETAILS
} from "../../api/Constants";
import {
  getEquipList,
  addVoid,
  getNewCompanyList,
  getEquipTypeList,
  getGateList,
  getAllMemberList,
  restoreVoid,
  getCraneDeliveryList,
  getConcreteDropDownDetails
} from "../../api/Api";
import Loader from "../../components/loader/Loader";
import Alert from "../../components/toastpopup/alert";
import Toastpopup from "../../components/toastpopup/Toastpopup";
import DeletePop from "../../components/toastpopup/logoutPop";
import Modal from "react-native-modal";
import { TextField } from "../../components/textinput/Textinput";
import moment from "moment";
import _ from "lodash";
import search from "../search/search";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import AppLoader from "../../components/apploader/AppLoader";
let DROPDOWNOPTIONS = [
  { id: "Edit", image: Images.editBlue, name: "Edit" },
  { id: "Void", image: Images.void, name: "Void" },
];
import DateTimePicker from "@react-native-community/datetimepicker";
import Dropdown from "../../components/dropdown/dropdown";

import NetInfo from '@react-native-community/netinfo';
import NoInternet from "../../components/NoInternet/noInternet";
import withBackHandler from "../../components/backhandler";
import { compose } from "redux";

let dateSele = new Date();

class VoidList extends Component {
  constructor(props) {
    super(props);
    this.searchList = _.debounce(this.searchList, 1000);

    this.state = {
      showLoader: false,
      showToaster: false,
      showAlert: false,
      toastMessage: "",
      toastType: "error",
      refreshing: false,
      lastId: 0,
      showFilter: false,
      rolelist: [],
      searchText: "",
      selectedRole: null,
      selectedCompany: null,
      drList: [],
      searchbarShow: false,
      selectedResponsibleNameId: 0,
      selectedGateNameId: 0,
      selectedEquipNameId: 0,
      selectedEquipName:null,
      selectedStatusId: 0,
      selectedCompanyId: 0,
      selectedRoleId: 0,
      selectedCompanyName: null,
      descriptionFilter: "",
      selectedResponsibleName: null,
      selectedGateName: null,
      selectedEquipmentName: null,
      selectedStatusName: null,
      companyFilterList: [],
      responiblePersonList: [],
      gateList: [],
      equipmentList: [],
      pickFrom: "",
      pickTo: "",
      statusList: [
        {
          label: "Approved",
          value: "Approved",
          id: "1",
          name:"Approved",
        },
        {
          label: "Completed",
          value: "Completed",
          id: "2",
          name:"Completed",
        },
        {
          label: "Declined",
          value: "Declined",
          id: "3",
          name:"Declined",
        },
        {
          label: "Delivered",
          value: "Delivered",
          id: "4",
          name:"Delivered",
        },
        {
          label: "Pending",
          value: "Pending",
          id: "5",
          name:"Pending",
        },
        {
          label: "Tentative",
          value: "Tentative",
          id: "6",
          name: "Tentative",
        },
      ],
      deliveryDate:"",
      selectedLocationName: null,
      selectedLocationId: 0,
      selectedMixDesignName:null,
      selectedMixDesignId:0,
      mixDesignList:[],
      locationsList: [],
      delDate:"",
      filterCountData:0,
      showDateTime:false,
      // selectedStatusName: null,
      locationModalVisible:false,
      mixDesignDropdown:false,
      companyModalVisible:false,
      responisblePersonModal:false,
      gateModalVisible:false,
      equipModalVisible:false,
      statusModalVisible:false,
      isNetworkCheck: false,
    };

    this.onEndReachedCalledDuringMomentum = true;
    this.page_number = 1;
  }

  componentDidMount() {
    if(Platform.OS === 'ios') {
    this.networkCheck();
    } else {
      this.renderInitial();
    }
  }

  networkCheck=()=>{
    NetInfo.addEventListener(state=>{
    if(!state.isConnected && !state.isInternetReachable){
      this.setState({isNetworkCheck: true})
     } else{
      this.setState({isNetworkCheck: false})
      this.renderInitial();
     }
  })
}

  onBackPress = () => {
    this.props.navigation.goBack();
    return true;
  };

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showMenu === true) {
      this.props.navigation.navigate("Home");
      this.props.showSideMenu(false);
    }

    if (nextProps.checkCameBack == true) {
      this.renderInitial();
      this.props.cameBack(false);
    }

    if (this.props.projectSwitched != nextProps.projectSwitched) {
      this.renderInitial();
    }
  }

  renderInitial = async() => {
    this.setState({ showLoader: true });
    this.page_number = 1;
    await this.getDropdownDetails();
    await this.getdrlist();
   await  this.getComapnyList();
    await this.getResponsibelList();
    await this.getGateList();
    await this.getEquipList();
  
  };

  getDropdownDetails = () => {
    let url = `${CONCRETE_DROPDOWN_DETAILS}?ProjectId=${this.props.projectDetails.id}&ParentCompanyId=${this.props.projectDetails.ParentCompany.id}`;
    try {
      getConcreteDropDownDetails(
        url,
        {},
        () => null,
        (response) => {
          this.setState({
            showLoader: false,
          });
          if (response.toString() == Strings.errors.timeout) {
            this.setState(
              {
                showToaster: true,
                toastMessage: Strings.errors.checkInternet,
                type: "error",
              },
              () => {
                setTimeout(() => {
                  this.setState({
                    showToaster: false,
                    disableSubmit: false,
                  });
                }, 2000);
              }
            );
          } else if (response.status) {
            if (response.status == 200 || response.status == 201) {
              this.storeDropDownList(response.data.data);
            } else if (response.data.message.message) {
              this.showToaster("error", response.data.message.message);
            } else {
              this.showToaster("error", response.data.message);
            }
          } else {
            this.showToaster("error", response.toString());
          }
        }
      );
    } catch (e) {
    }
  };

  storeDropDownList = (data) => {
    let locList = [];
    if(data.locationDropdown!=null&&data.locationDropdown!=undefined&&data.locationDropdown.length>0){
      for (let item of data.locationDropdown) {
        locList.push({
          label: item.location,
          value: item.location,
          id: item.id,
          name:item.location,
        });
      }
      this.setState({ locationsList: locList });
    }

    let mixDesignList = [];
    if(data.mixDesignDropdown!=null&&data.mixDesignDropdown!=undefined&&data.mixDesignDropdown.length>0){
      for (let item of data.mixDesignDropdown) {
        mixDesignList.push({
          label: item.mixDesign,
          value: item.mixDesign,
          id: item.id,
          name:item.mixDesign,
        });
      }
      this.setState({ mixDesignList: mixDesignList });
    }
  };

  getEquipList = () => {
    let url = `${GET_EQUIP_LIST}${this.props.projectDetails.id}/20/${this.page_number}/${this.props.projectDetails.ParentCompany.id}`;
    let param={isFilter:true,showActivatedAlone: true}
    getEquipList(
      url,
      param,
      () => {},
      (equipResp) => {
        if (equipResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (equipResp.status) {
          if (equipResp.data.data.rows.length !== 0) {
            let equipTypelist = [];
            for (let item of equipResp.data.data.rows) {
              if (item.id) {
                equipTypelist.push({
                  id: item.id,
                  value: item.equipmentName,
                  label: item.equipmentName,
                  name:item.equipmentName,
                });
              }
            }
            this.setState({
              equipmentList: equipTypelist,
            });
          }
        }
      }
    );
  };

  getGateList = () => {
    let param={isFilter:true,showActivatedAlone: true}
    let url =
      GET_GATE_LIST +
      this.props.projectDetails.id +
      "/0/0/" +
      this.props.projectDetails.ParentCompany.id;
    getGateList(
      url,
      param,
      () => {},
      (gateResp) => {
        if (gateResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (gateResp.status) {
          if (gateResp.data.data.length !== 0) {
            let gateList = [];
            for (let i = 0; i < gateResp.data.data.length; i++) {
              gateList.push({
                id: gateResp.data.data[i].id,
                value: gateResp.data.data[i].gateName,
                label: gateResp.data.data[i].gateName,
                name:gateResp.data.data[i].gateName,
              });
            }
            this.setState({
              gateList: gateList,
            });
          }
        }
      }
    );
  };

  getResponsibelList = () => {
    getAllMemberList(
      `${LIST_ALL_MEMBER}${this.props.projectDetails.id}/${this.props.projectDetails.ParentCompany.id}`,
      {},
      () => {},
      (memResp) => {
        if (memResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (memResp.status) {
          if (memResp.data.message == "Member listed Successfully.") {
            this.storeMemberList(memResp.data.data);
          }
        }
      }
    );
  };

  getComapnyList = () => {
    getNewCompanyList(
      `${GET_NEW_COMPANIES}${this.props.projectDetails.id}/${this.props.projectDetails.ParentCompany.id}`,
      {},
      () => {},
      (compResp) => {
        if (compResp.toString() == Strings.errors.timeout) {
          this.showError("error", Strings.errors.checkInternet);
        } else if (compResp.status) {
          if (compResp.data.message == "Company list.") {
            this.storeCompanyList(compResp.data.data);
          }
        }
      }
    );
  };

  storeMemberList = (data) => {
    let memberList = [];
    for (let item of data) {
      if(item.User!=null){
      if(item.User.firstName!=null){
      memberList.push({
        label: item.User.firstName+" "+item.User.lastName+" ("+item.User.email+")",
        value: item.User.email,
        id: item.id,
        name:item.User.email,
      });
    }else{
      memberList.push({
        label: item.User.email,
        value: item.User.email,
        id: item.id,
        name:item.User.email,
      });
    }
  }
    }
    this.setState({ responiblePersonList: memberList });
  };

  storeCompanyList = (data) => {
    let companyList = [];
    for (let i = 0; i < data.length; i++) {
      companyList.push({
        label: data[i].companyName,
        value: data[i].companyName,
        id: data[i].id,
        name:data[i].companyName,
      });
    }
    this.setState({ companyFilterList: companyList });
  };

  clearSearch = () => {
    this.setState({
      clearSearch: false,
      searchText: "",
      showIndicator: true,
    });
    this.searchList();
  };

  getdrlist = () => {
    let param = {};

    if (this.state.filter == true) {
      param = {
        companyFilter: this.state.selectedCompanyName,
        descriptionFilter: this.state.descriptionFilter,
        memberFilter: this.state.selectedResponsibleNameId,
        gateFilter: this.state.selectedGateNameId,
        equipmentFilter: this.state.selectedEquipName,
        statusFilter: this.state.selectedStatusName,
        search: this.state.searchText,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
        locationFilter:this.state.selectedLocationName,
        mixDesignFilter: this.state.selectedMixDesignName,
        dateFilter:this.state.delDate,
        queuedNdr:false,
        sort:"DESC",
        sortByField:"id",
        filterCount:this.state.filterCountData
      };
    } else {
      param = {
        search: this.state.searchText,
        ParentCompanyId: this.props.projectDetails.ParentCompany.id,
      };
    }

    let url = `${GET_VOID_DETAILS}/${this.props.projectDetails.id}/20/${this.page_number}/1`;
    getCraneDeliveryList(
      url,
      param,
      () => {},
      (response) => {
        this.setState({
          showIndicator: false,
          clearSearch: this.state.searchText ? true : false,
          showLoader: false,
          showNoData: false,
        });
 
        if (response.toString() == Strings.errors.timeout) {
          this.setState(
            {
              showToaster: true,
              toastMessage: Strings.errors.checkInternet,
              type: "error",
            },
            () => {
              setTimeout(() => {
                this.setState({
                  showToaster: false,
                });
              }, 2000);
            }
          );
        } else if (response.status) {
          if (response.status == 200|| response.status==201) {
            this.setState({ showLoader: false });
            let data = this.state.drList;
            //this.props.storeLastid(response.data.lastId.DeliveryId);

            if (this.page_number == 1) {
              if (response.data.data.count == 0) {
                this.setState({ showNoData: true, drList: [] });
              } else {
                this.setState({
                  drList: response.data.data.rows,
                  totalCount: response.data.data.count,
                  //lastId: response.data.lastId.DeliveryId,
                });
              }
            } else {
              let data1 = response.data.data.rows;
              this.setState({
                drList: data.concat(data1),
                totalCount: response.data.data.count,
                //lastId: response.data.lastId.DeliveryId,
              });
            }
          } else if (response.data.message.message) {
            this.showError("error", response.data.message.message);
          } else {
            this.showError("error", response.data.message);
          }
        } else {
          this.showError("error", response.toString());
        }
      }
    );
  };


  showError = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
        showLoader: false,
      },
      () => {
        setTimeout(() => {
          this.setState({ showToaster: false });
        }, 2000);
      }
    );
  };

  _onReset = () => {
    this.page_number = 1;
    this.setState(
      {
        drList: [],
        totalCount: 0,
        showLoader: true,
        filterCountData:0,
        //load: true,
      },
      () => {
        this.getdrlist();
      }
    );
  };

  onEndReached = () => {
    if (this.state.drList.length < this.state.totalCount) {
      this.page_number = this.page_number + 1;
      this.getdrlist();
      this.onEndReachedCalledDuringMomentum = true;
    }
  };

  renderSearchBar = () => {
    if (this.state.searchbarShow == true) {
      return this.searchBar();
    } else {
      return this.renderHeader();

    }
  };

  searchBar = () => {
    return (
      <View style={searchStyles.searchHeader}>
        <View style={searchStyles.mainContainer}>
          <TouchableOpacity
            style={searchStyles.closeBtn}
            onPress={() => {
              this.setState(
                {
                  searchbarShow: false,
                  searchText: "",
                },
                ()=>this.renderInitial()
              );
            }}
          >
            <Image
              resizeMode={"contain"}
              source={Images.closeBlack}
              style={searchStyles.closeImg}
            />
          </TouchableOpacity>

          <View
            style={{ flex: 1, justifyContent: "center", alignItems: "center" }}
          >
            <Text style={searchStyles.titleText}>{Strings.search.title}</Text>
          </View>

          <TouchableOpacity
            style={searchStyles.closeBtn}
            onPress={() => {
              if (this.state.showright) {
                this.setState({ showAllDelete: true });
              }
            }}
          >
            {this.state.showright == true && (
              <Image
                resizeMode={"contain"}
                source={Images.delete1}
                style={searchStyles.closeImg}
              />
            )}
          </TouchableOpacity>
        </View>
        <View style={{ flexDirection: "row", justifyContent: "center" }}>
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.SearchHere}
            title={Strings.placeholders.SearchHere}
            value={this.state.searchText}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: wp("4%"),
              fontFamily: Fonts.montserratMedium,
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />
          <View
            style={{
              position: "absolute",
              right: wp("5%"),
              width: wp("10%"),
              height: hp("5%"),
              marginTop: hp("3%"),
              justifyContent: "flex-end",
              alignItems: "center",
            }}
          >
            {this.state.showIndicator == true && (
              <ActivityIndicator style={{ marginBottom: 5 }} />
            )}
            {this.state.clearSearch == true && (
              <TouchableOpacity onPress={() => this.clearSearch()}>
                <Image source={Images.closeBlack} style={{ marginBottom: 5 }} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    );
  };

  updateMasterState = (key, value) => {
    this.setState(
      {
        searchText: value,
      },
      () => {
        this.searchList();
      }
    );
  };

  searchList = () => {
    this.setState({ showIndicator: true, clearSearch: false }, () => {
      this.renderInitial();
    });
  };

  onBackPressed() {
    this.props.refreshDeliveryList(true, "VolidList")
    this.props.navigation.goBack();
  }

  renderHeader() {
    // const { cameBack } = this.props;
    let count = 0;

    if (this.state.descriptionFilter !== "") {
      count = count + 1;
      this.state.filterCountData=count;
    }

    if (this.state.delDate !== "") {
      count = count + 1;
      this.state.filterCountData=count;
    }

    if (this.state.selectedCompanyName !== '' && this.state.selectedCompanyName!==null) {
      count = count + 1;
      this.state.filterCountData=count;
    }

    if (this.state.selectedResponsibleNameId !== 0) {
      count = count + 1;
      this.state.filterCountData=count;
    }

    if (this.state.selectedLocationId !== 0) {
      count = count + 1;
      this.state.filterCountData=count;
    }

    if (this.state.selectedMixDesignId !== 0) {
      count = count + 1;
      this.state.filterCountData=count;
    }

    if (this.state.selectedGateNameId !== 0) {
      count = count + 1;
      this.state.filterCountData=count;
    }

    if (this.state.selectedEquipName!==null) {
      count = count + 1;
      this.state.filterCountData=count;
    }

    if (this.state.selectedStatusName !== null) {
      count = count + 1;
      this.state.filterCountData=count;
    }

    return (
      <View style={styles.headerContainer}>
        <TouchableOpacity
          style={[styles.image]}
          onPress={() => {
            this.onBackPressed();
          }}
        >
          <Image
            source={Images.backArrow}
            style={{ marginBottom: hp("1%"), marginLeft: 10 }}
          />
        </TouchableOpacity>

        <View
          style={{ flex: 3, justifyContent: "center", alignItems: "center" }}
        >
          <Text style={styles.title}>{Strings.void.voidList}</Text>
        </View>

        <View style={styles.headerRowContainer}>
          <TouchableOpacity
            style={[styles.image, { marginTop: wp("1%") }]}
            onPress={() => this.setState({ showFilter: true })}
          >
            <Image source={Images.filter} />

            {this.state.filter == true && count !== 0 && (
              <View
                style={{
                  position: "absolute",
                  marginTop: -10,
                  right: -10,
                  backgroundColor: Colors.themeColor,
                  width: 16,
                  justifyContent: "center",
                  alignItems: "center",
                  height: 16,
                  borderRadius: 8,
                }}
              >
                <Text style={{ color: "white" }}>{count}</Text>
              </View>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.image}
            onPress={() => {
              this.setState({
                searchbarShow: true,
                drList: [],
              });
            }}
          >
       <Image source={Images.Search1} style={{height:21,width:21,}}/>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  renderRow = (option, index, isSelected) => {
    return (
      <TouchableOpacity
        style={{
          width: wp("40%"),
          flexDirection: "row",
          alignItems: "center",
          height: hp("7%"),
          backgroundColor: "white",
        }}
      >
        <Image
          resizeMode={"center"}
          source={option.image}
          style={{
            width: option.id == "Edit" ? wp("5%") : wp("6%"),
            height: option.id == "Edit" ? hp("4%") : hp("5%"),
            marginLeft: 10,
          }}
        />
        <Text
          style={{
            fontSize: wp("4%"),
            fontFamily: Fonts.montserratMedium,
            color: Colors.planDesc,
            marginLeft: 10,
          }}
        >
          {option.id}
        </Text>
      </TouchableOpacity>
    );
  };

  renderSeparator = () => {
    return <View style={{ flex: 1, backgroundColor: Colors.white }} />;
  };

  voidDR = (item, index) => {
    this.setState({
      showLoader: true,
    });

    let param = {
      DeliveryRequestId: item.id,
      ProjectId: this.props.projectDetails.id,
    };
console.log("params",param)
    addVoid(
      `${VOID_DR}`,
      param,
      () => {},
      (voidResp) => {
        console.log('void list---> delivery',voidResp)
        if (voidResp.toString() == Strings.errors.timeout) {
          this.showToaster("error", Strings.errors.timeout);
        } else if (voidResp.status) {
          if (
            voidResp.data.message ==
            "Delivery Request marked as void successfully."
          ) {
            this.showToaster("error", voidResp.data.message);
            this.renderInitial();
          } else {
            this.showToaster("error", voidResp.data.message);
          }
        } else {
          this.showToaster("error", voidResp.toString());
        }
      }
    );
  };


  voidINS = (item, index) => {
    this.setState({
      showLoader: true,
    });

    let param = {
      InspectionRequest: item.id,
      ProjectId: this.props.projectDetails.id,
    };
console.log("params",param)
    addVoid(
      `${CREATE_VOID_INS}`,
      param,
      () => {},
      (voidResp) => {
        if (voidResp.toString() == Strings.errors.timeout) {
          this.showToaster("error", Strings.errors.timeout);
        } else if (voidResp.status) {
          if (
            voidResp.data.message ==
            "Inspection Request marked as void successfully."
          ) {
            this.showToaster("error", voidResp.data.message);
            this.renderInitial();
          } else {
            this.showToaster("error", voidResp.data.message);
          }
        } else {
          this.showToaster("error", voidResp.toString());
        }
      }
    );
  };


  showToaster = (type, message) => {
    this.setState(
      {
        showToaster: true,
        toastType: type,
        toastMessage: message,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showToaster: false,
          });
        }, 2000);
      }
    );
  };
  updateMasterStateFilter = (key, value) => {
    if (key == Strings.filter.pickFrom) {
      this.setState({
        pickFrom: value,
      });
    } else if (key == Strings.filter.pickTO) {
      this.setState({
        pickTo: value,
      });
    }
  };
  editDR = (item, index) => {
    // this.props.editData({
    //   item: item,
    //   index: index
    // })
    // this.props.clickAdd(true)
  };

  applyFilter = () => {
    if (
      this.state.selectedCompanyName !== null ||
      this.state.descriptionFilter !== "" ||
      this.state.selectedResponsibleName !== null ||
      this.state.selectedGateName !== null ||
      this.state.selectedEquipNameId !==0 ||
      this.state.selectedEquipName!==null||
      this.state.selectedStatusName !== null ||
      this.state.delDate!==""||
      this.state.selectedLocationName!==""||
      this.state.selectedMixDesignName!==""
    ) {
      this.setState(
        {
          filter: true,
          showFilter: false,
        },
        () => {
          this.setState({ showLoader: true });
          this.page_number = 1;
          this.getdrlist();
          //     this.renderInitial()
        }
      );
    } else {
      this.setState({
        showFilter: false,
      });
    }
  };

  onPressCompanyType=(item)=>{
    this.setState({
      selectedCompanyName: item.value,
      selectedCompanyId: item.id,
      companyModalVisible:false
    })
  }

  onPressResPersonType=(item)=>{
    this.setState({
      selectedResponsibleName: item.value,
      selectedResponsibleNameId: item.id,
      responisblePersonModal:false
    })
  }

  onPressGateModalType=(item)=>{
    this.setState({
      selectedGateName: item.value,
      selectedGateNameId: item.id,
      gateModalVisible:false
    })
  }

  onPressLocationType=(item)=>{
    this.setState({
      selectedLocationName: item.value,
      selectedLocationId: item.id,
      locationModalVisible:false
    })
  }


  onPressMixDesignType=(item)=>{
    this.setState({
      selectedMixDesignName: item.value,
      selectedMixDesignId: item.id,
      mixDesignDropdown:false
    })
  }

  onPressEquipModalType=(item)=>{
    this.setState({
      selectedEquipName: item.value,
      selectedEquipNameId: item.id,
      equipModalVisible:false,
    })
  }

  onPressStatusModalType=(item)=>{
    this.setState({
      selectedStatusName: item.value,
      selectedStatusId: item.id,
      statusModalVisible:false
    })
  }

  renderFilter = () => {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <View style={modalStyles.container}>
        <View style={modalStyles.topContainer}>
          <TouchableOpacity
            onPress={() => this.setState({ showFilter: false })}
            style={{ width: 40 }}
          >
            <Image source={Images.closeBlack} />
          </TouchableOpacity>
          <View style={modalStyles.titleContainer}>
            <Text style={modalStyles.title}>{Strings.filter.title}</Text>
          </View>
          <View style={{ width: 40, height: 40 }} />
        </View>

        <KeyboardAwareScrollView
          contentContainerStyle={{ minHeight: hp("80%") }}
        >
          <TextField
            showLeft={true}
            attrName={Strings.placeholders.description}
            title={Strings.placeholders.description}
            value={this.state.descriptionFilter}
            updateMasterState={(key, value) => {
              this.setState({
                descriptionFilter: value,
              });
            }}
            hideShow={false}
            hideImage={""}
            textInputStyles={{
              color: Colors.black,
              fontSize: 14,
              width: "75%",
              marginLeft: wp("10%"),
              fontFamily: Fonts.montserratMedium,
              paddingTop: 10,
            }}
            textTitleStyles={{
              marginLeft: wp("10%"),
              fontSize: 14,
              fontFamily: Fonts.montserratMedium,
            }}
            leftImage={Images.searchGray}
            leftButton={{ bottom: 0 }}
          />

          <TextField
            attrName={Strings.placeholders.deliveryDate}
            title={Strings.placeholders.deliveryDate}
            value={this.state.delDate}
            updateMasterState={(key, value) => {
              this.updateMasterState(key, value);
            }}
            mandatory={true}
            textInputStyles={{
              // here you can add additional TextInput drStyles
              color: Colors.black,
              fontSize: 14,
              marginLeft: wp("4%"),
            }}
            textTitleStyles={{
              fontSize:14,
              fontFamily: Fonts.montserratMedium,
              marginLeft: wp("4%"),
            }}
            showButton={true}
            onPress={() => {
              this.setState({
                showDateTime: true,
              });
            }}
            container={{
              marginTop: 5,
            }}
            imageSource={Images.calGray}
          />

            <TextField
              attrName={Strings.placeholders.company}
              title={Strings.placeholders.company}
              value={this.state.selectedCompanyName}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              showButton={true}
              onPress={() => {
                this.setState({ companyModalVisible: true });
              }}
              imageSource={Images.downArr}
            />


          {/* <DropDownPicker
            items={this.state.companyFilterList}
            defaultValue={this.state.selectedCompanyName}
            multiple={false}
            placeholder={Strings.placeholders.company}
            placeholderStyle={modalStyles.filterPlaceholder}
            containerStyle={{ height: hp("6%"), marginTop: 10 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            selectedLabelStyle={{ color: Colors.black }}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) => {
              this.setState({
                selectedCompanyName: item.value,
                selectedCompanyId: item.id,
              });
            }}
            zIndex={8700}
          />  */}

              <TextField
                attrName={Strings.placeholders.responisblePerson}
                title={Strings.placeholders.responisblePerson}
                value={this.state.selectedResponsibleName}
                updateMasterState={(key, value) => {
                  this.updateMasterState(key, value);
                }}
                mandatory={true}
                textInputStyles={{
                  // here you can add additional TextInput styles
                  color: Colors.black,
                  fontSize: 14,
                }}
                showButton={true}
                onPress={() => {
                  this.setState({ responisblePersonModal: true });
                }}
                imageSource={Images.downArr}
              />

          {/* <DropDownPicker
            items={this.state.responiblePersonList}
            defaultValue={this.state.selectedResponsibleName}
            placeholder={Strings.placeholders.responisblePerson}
            placeholderStyle={[modalStyles.filterPlaceholder,modalStyles.marginPlaceholder]}
            containerStyle={modalStyles.responisblePersonFilter}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            selectedLabelStyle={{ color: Colors.black }}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedResponsibleName: item.value,
                selectedResponsibleNameId: item.id,
              })
            }
            zIndex={6000}
          /> */}

            <TextField
              attrName={Strings.placeholders.location}
              title={Strings.placeholders.location}
              value={this.state.selectedLocationName}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              showButton={true}
              onPress={() => {
                this.setState({ locationModalVisible: true });
              }}
              imageSource={Images.downArr}
            />          

          {/* <DropDownPicker
            items={this.state.locationsList}
            defaultValue={this.state.selectedLocationName}
            placeholder={Strings.placeholders.location}
            placeholderStyle={modalStyles.filterPlaceholder}
            containerStyle={{ height: hp("6%"), marginTop: 30 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            selectedLabelStyle={{ color: Colors.black }}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedLocationName: item.value,
                selectedLocationId: item.id,
              })
            }
            zIndex={5000}
          /> */}

          <TextField
              attrName={Strings.placeholders.mixDesign}
              title={Strings.placeholders.mixDesign}
              value={this.state.selectedMixDesignName}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              showButton={true}
              onPress={() => {
                this.setState({ mixDesignDropdown:true });
              }}
              imageSource={Images.downArr}
            />

          {/* <DropDownPicker
            items={this.state.mixDesignList}
            defaultValue={this.state.selectedMixDesignName}
            placeholder={Strings.placeholders.mixDesign}
            placeholderStyle={modalStyles.filterPlaceholder}
            containerStyle={{ height: hp("6%"), marginTop: 30 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            selectedLabelStyle={{ color: Colors.black }}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedMixDesignName: item.value,
                selectedMixDesignId: item.id,
              })
            }
            zIndex={4000}
          /> */}

          <TextField
              attrName={Strings.placeholders.gate}
              title={Strings.placeholders.gate}
              value={this.state.selectedGateName}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              showButton={true}
              onPress={() => {
                this.setState({ gateModalVisible:true });
              }}
              imageSource={Images.downArr}
            />
          
          {/* <DropDownPicker
            items={this.state.gateList}
            defaultValue={this.state.selectedGateName}
            placeholder={Strings.placeholders.gate}
            placeholderStyle={modalStyles.filterPlaceholder}
            containerStyle={{ height: hp("6%"), marginTop: 30 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedGateName: item.value,
                selectedGateNameId: item.id,
              })
            }
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={3000}
          /> */}

          <TextField
              attrName={Strings.placeholders.equip}
              title={Strings.placeholders.equip}
              value={this.state.selectedEquipName}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              showButton={true}
              onPress={() => {
                this.setState({ equipModalVisible:true });
              }}
              imageSource={Images.downArr}
            />

          {/* <DropDownPicker
            items={this.state.equipmentList}
            defaultValue={this.state.selectedEquipName}
            placeholder={Strings.placeholders.equip}
            placeholderStyle={modalStyles.filterPlaceholder}
            containerStyle={{ height: hp("6%"), marginTop: 30 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedEquipName: item.value,
                selectedEquipNameId: item.id,
              })
            }
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={2000}
          /> */}

          <TextField
              attrName={Strings.placeholders.status}
              title={Strings.placeholders.status}
              value={this.state.selectedStatusName}
              updateMasterState={(key, value) => {
                this.updateMasterState(key, value);
              }}
              mandatory={true}
              textInputStyles={{
                // here you can add additional TextInput styles
                color: Colors.black,
                fontSize: 14,
              }}
              showButton={true}
              onPress={() => {
                this.setState({ statusModalVisible:true });
              }}
              imageSource={Images.downArr}
            />

          {/* <DropDownPicker
            items={this.state.statusList}
            defaultValue={this.state.selectedStatusName}
            placeholder={"Status"}
            placeholderStyle={modalStyles.filterPlaceholder}
            containerStyle={{ height: hp("6%"), marginTop: 30 }}
            style={{
              backgroundColor: Colors.white,
              width: wp("90%"),
              borderColor: "#0000",
              borderBottomColor: Colors.placeholder,
              alignSelf: "center",
              height: hp("5%"),
            }}
            itemStyle={{
              justifyContent: "flex-start",
              marginVertical: 5,
            }}
            customArrowUp={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            customArrowDown={(size) => (
              <Image
                source={Images.downArr}
                style={{ width: size, height: size, alignSelf: "flex-end" }}
              />
            )}
            dropDownStyle={{
              backgroundColor: Colors.white,
              width: "90%",
              alignSelf: "center",
            }}
            onChangeItem={(item) =>
              this.setState({
                selectedStatusName: item.value,
                selectedStatusId: item.id,
              })
            }
            selectedLabelStyle={{ color: Colors.black }}
            zIndex={1000}
          /> */}
           {Platform.OS == "ios" && (
       <Modal
          isVisible={this.state.showDateTime}
          onBackdropPress={() => {
            this.setState({ showDateTime: false, });
          }}
          animationInTiming={500}
          style={{
            paddingTop: 45,
            margin: 0,
            justifyContent: "flex-end",
          }}
        >
          <DateTimePicker
            value={dateSele}
            style={{
              backgroundColor: Colors.white,
              width:'100%',
            }}
           display={"inline"}
           themeVariant='light'
           accentColor={Colors.themeColor}
            onChange={(time, date) => {
              this.onchangeDate(time, date);
            }}

          />
           <View>
              <TouchableOpacity
                activeOpacity={0.5}
                style={styles.datePickerOkContainer}
                onPress={() => {
                  this.setState({ showDateTime: false, });
                }}
              >
                <Text style={styles.datePickerOkLabel}>Done</Text>
              </TouchableOpacity>
            </View>
        </Modal>)}
        {Platform.OS == "android" && this.state.showDateTime && (
           <DateTimePicker
           // testID="date"
           // timeZoneOffsetInMinutes={0}
           // minuteInterval={interval}
           //  minimumDate={new Date()}
           value={dateSele}
           style={{
             backgroundColor: "#fff",
             width:'100%',
           }}
           display="default"
           // mode={mode}
           onChange={(time, date) => {
             this.onchangeDate(time, date);
           }}

         />
        )}
          <View
            style={[
              modalStyles.buttonContainer,
              { marginBottom: 30, marginTop: 80 },
            ]}
          >
            <TouchableOpacity
              style={
                this.state.filter == true
                  ? [
                      modalStyles.cancelButton,
                      { backgroundColor: Colors.themeOpacity },
                    ]
                  : modalStyles.cancelButton
              }
              onPress={() =>
                this.setState(
                  {
                    showFilter: false,
                    selectedControlledBy: null,
                    selectedControlledById: 0,
                    selectedEquipName: null,
                    selectedEquipId: 0,
                    searchId: "",
                    searchText: "",
                    selectedGateName: null,
                    selectedResponsibleName:null,
                    selectedResponsibleNameId: 0,
                    selectedGateNameId: 0,
                    selectedEquipNameId: 0,
                    selectedStatusId: 0,
                    selectedCompanyId: 0,
                    selectedRoleId: 0,
                    selectedCompanyName: null,
                    descriptionFilter: "",
                    selectedEquipmentName: null,
                    selectedStatusName: null,
                    pickFrom: "",
                    pickTo: "",
                    delDate: "",
                    selectedMixDesignName: null,
                    selectedMixDesignId: 0,
                    selectedLocationName: null,
                    selectedLocationId: 0,
                    filterCountData:0                  },
                  () => {
                    if (this.state.filter == true) {
                      dateSele=new Date()
                      this.setState(
                        {
                          filter: false,
                          selectedResponsibleNameId: 0,
                          selectedGateNameId: 0,
                          selectedEquipNameId: 0,
                          selectedStatusId: 0,
                          selectedCompanyId: 0,
                          selectedRoleId: 0,
                          selectedEquipName: null,
                          selectedCompanyName: null,
                          descriptionFilter: "",
                          selectedResponsibleName: null,
                          selectedGateName: null,
                          selectedEquipmentName: null,
                          selectedStatusName: null,
                          pickFrom: "",
                          pickTo: "",
                          delDate: "",
                          selectedMixDesignName: null,
                          selectedMixDesignId: 0,
                          selectedLocationName: null,
                          selectedLocationId: 0,
                          filterCountData:0,
                          
                        },
                        () => {
                          this.renderInitial();
                        }
                      );
                    }
                  }
                )
              }
            >
              <Text
                style={[
                  modalStyles.cancelText,
                  {
                    color:
                      this.state.filter == true
                        ? Colors.themeColor
                        : Colors.buttonBackground,
                  },
                ]}
              >
                {this.state.filter == true
                  ? Strings.addMember.reset
                  : Strings.addMember.cancel}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={modalStyles.applyButton}
              onPress={() => {
                this.applyFilter();
              }}
            >
              <Text style={modalStyles.applyText}>{Strings.filter.apply}</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAwareScrollView>

        <Dropdown
            data={this.state.companyFilterList}
            title={Strings.placeholders.company}
            value={this.state.selectedCompanyName}
            closeBtn={() => this.setState({ companyModalVisible:false })}
            onPress={(item) => this.onPressCompanyType(item)}
            visible={this.state.companyModalVisible}
            onbackPress={() => this.setState({ companyModalVisible:false  })}
            container={styles.equipmentContainer}
            customMainContainer={styles.renderEquipStyle}
            equipTextContainer={styles.equipTextStyle}
          />

        <Dropdown
            data={this.state.responiblePersonList}
            title={Strings.placeholders.responisblePerson}
            value={this.state.selectedResponsibleName}
            closeBtn={() => this.setState({ responisblePersonModal:false })}
            onPress={(item) => this.onPressResPersonType(item)}
            visible={this.state.responisblePersonModal}
            onbackPress={() => this.setState({ responisblePersonModal:false  })}
            container={styles.equipmentContainer}
            customMainContainer={styles.renderEquipStyle}
            equipTextContainer={styles.equipTextStyle}
          />

          <Dropdown
            data={this.state.gateList}
            title={Strings.placeholders.gate}
            value={this.state.selectedGateName}
            closeBtn={() => this.setState({ gateModalVisible:false })}
            onPress={(item) => this.onPressGateModalType(item)}
            visible={this.state.gateModalVisible}
            onbackPress={() => this.setState({ gateModalVisible:false  })}
            container={styles.equipmentContainer}
            customMainContainer={styles.renderEquipStyle}
            equipTextContainer={styles.equipTextStyle}
          />

          <Dropdown
            data={this.state.locationsList}
            title={Strings.placeholders.location}
            value={this.state.selectedLocationName}
            closeBtn={() => this.setState({ locationModalVisible: false })}
            onPress={(item) => this.onPressLocationType(item)}
            visible={this.state.locationModalVisible}
            onbackPress={() => this.setState({ locationModalVisible: false })}
            container={styles.equipmentContainer}
            customMainContainer={styles.renderEquipStyle}
            equipTextContainer={styles.equipTextStyle}
          />

          <Dropdown
            data={this.state.equipmentList}
            title={Strings.placeholders.equip}
            value={this.state.selectedEquipName}
            closeBtn={() => this.setState({ equipModalVisible:false })}
            onPress={(item) => this.onPressEquipModalType(item)}
            visible={this.state.equipModalVisible}
            onbackPress={() => this.setState({ equipModalVisible:false   })}
            container={styles.equipmentContainer}
            customMainContainer={styles.renderEquipStyle}
            equipTextContainer={styles.equipTextStyle}
          />

          <Dropdown
            data={this.state.mixDesignList}
            title={Strings.placeholders.mixDesign}
            value={this.state.selectedMixDesignName}
            closeBtn={() => this.setState({ mixDesignDropdown:false})}
            onPress={(item) => this.onPressMixDesignType(item)}
            visible={this.state.mixDesignDropdown}
            onbackPress={() => this.setState({ mixDesignDropdown: false })}
            container={styles.equipmentContainer}
            customMainContainer={styles.renderEquipStyle}
            equipTextContainer={styles.equipTextStyle}
          />

          <Dropdown
            data={this.state.statusList}
            title={Strings.placeholders.status}
            value={this.state.selectedStatusName}
            closeBtn={() => this.setState({ statusModalVisible:false })}
            onPress={(item) => this.onPressStatusModalType(item)}
            visible={this.state.statusModalVisible}
            onbackPress={() => this.setState({ statusModalVisible:false   })}
            container={styles.equipmentContainer}
            customMainContainer={styles.renderEquipStyle}
            equipTextContainer={styles.equipTextStyle}
          />      
      </View>
       }
       </>
    );
  };

  onchangeDate = (tevent, date1) => {
    //let date = moment(date1).format("MMM Do YYYY, h:mm:ss a");
    if (tevent.type == "set" || tevent.type == "dismissed") {
      this.setState({
        showDateTime: false,
      });
    }
    if (Platform.OS == "ios" || tevent.type == "set") {
    // selectedDate = date,
    const fullYear = date1.getFullYear();
    const fullMonth = date1.getMonth();
    const date = date1.getDate();
    this.setState({
     delDate: moment(date1).format("MM/DD/YYYY")
    });
    dateSele = date1;

}
  };

  restoreList = (item, index) => {
    let param = {
      id: [item.voidList[0].id],
      ProjectId: this.props.projectDetails.id,
      isSelectAll: false,
    };

    this.setState({
      showLoader: true,
    });

    restoreVoid(
      `${RESTORE_VOID}`,
      param,
      () => {},
      (response) => {
        if (response.status) {
          if (response.status == 201) {
            this.renderInitial();
            this.showToaster(
              "success",
              "Request Restored Successfully"
            );
          }else if(response.status==400){
            this.showToaster(
              "error",
              response.data.message
            );
          }else if(response.status==500){
            this.showToaster(
              "error",
              response.data.message
            );
          }
          else{
            this.showToaster(
              "error",
              "Something Went Wrong"
            );
          }
        }else{
          this.showToaster("error", response.toString());
        }
      }
    );
  };

  renderFlatListItem = ({ item, index }) => {
    let date='';
    if(item.requestType=="craneRequest"){
      date = moment(item.craneDeliveryStart).format("lll");
    }else if(item.requestType=="concreteRequest"){
      date = moment(item.concretePlacementStart).format("lll");
    }else if(item.requestType=="inspectionRequest"){
      date = moment(item.inspectionStart).format("lll");
    }
    else{
      date = moment(item.deliveryStart).format("lll");
    }
    

    let userDetails = item.approverDetails;
    return (
      <View style={styles.flatlistContainer}>
        <View>
          <View style={[styles.nameContainer, { width: wp("95%") }]}>
            <Text style={styles.nameText} numberOfLines={4}>
              {item.description}
            </Text>
            <View></View>
          </View>

          <View style={{ width: wp("96%") }}>
            <View style={[styles.subContainer,{ marginTop: hp('2') }]}>
              <Text style={styles.subtext}>{Strings.addDR.dateTime}</Text>
              <Text style={styles.subtext}>{Strings.addDR.approvedBy}</Text>
            </View>

            <View style={[styles.subContainer, { marginTop: 5 }]}>
              <Text
                style={[styles.subtext, { color: "#1E1E1E", fontSize: 14 }]}
              >
                {date.toString()}
              </Text>
              <Text
                style={[styles.subtext, { color: "#1E1E1E", fontSize: 14 }]}
              >
                {userDetails ? item.approverDetails.User.firstName+' '+item.approverDetails.User.lastName : "----"}
              </Text>
            </View>

            <View style={[styles.subContainer, { marginTop: hp('2') }]}>
              <Text style={styles.subtext}>{Strings.equip.equipment}</Text>
              <View style={{ width: wp("45%") }}>
                <TouchableOpacity
                  onPress={() => {
                    this.setState({
                      showDelete: true,
                      selectedItem: item,
                      selectedIndex: index,
                    });
                  }}
                  style={{
                    width: wp("25%"),
                    height: hp("4%"),
                    borderRadius: hp("2%"),
                    justifyContent: "center",
                    alignItems: "center",
                    backgroundColor: Colors.themeOpacity,
                  }}
                >
                  <Text
                    style={{
                      fontSize: 12,
                      marginRight: 10,
                      fontFamily: Fonts.montserratRegular,
                      marginLeft: 10,
                      color: Colors.themeColor,
                    }}
                  >
                    {Strings.addDR.restore}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View
              style={[styles.subContainer, { marginBottom: 15 ,marginTop:hp('-1.5%'),}]}
            >
            {/* DO For Later */}
              {/* <Text
                style={[
                  styles.subtext,
                  { color: "#1E1E1E", fontSize: 14 },
                ]}
              >
                {item.requestType=="concreteRequest"?"---":item.equipmentDetails[0].Equipment.equipmentName}
              </Text> */}
            </View>
          </View>
        </View>
      </View>
    );
  };

  render() {
    return (
      <>
      {this.state.isNetworkCheck ?
        <NoInternet  
        Refresh={()=>this.networkCheck()} /> :
      <SafeAreaView style={styles.safeArea}>
        <StatusBar backgroundColor="white" barStyle="dark-content" />
        <View
          style={[
            styles.parentContainer,
            {
              backgroundColor:
                this.state.searchbarShow == true ? Colors.white : "FCFBFC",
            },
          ]}
        >
          {this.renderSearchBar()}

          <FlatList
            data={this.state.drList}
            renderItem={this.renderFlatListItem}
             ItemSeparatorComponent={this.itemSeparator}
            keyExtractor={(item, index) => index.toString()}
            onEndReached={() => this.onEndReached()}
            onEndReachedThreshold={3}
           onMomentumScrollBegin={() => {
             this.onEndReachedCalledDuringMomentum = false;
            }}
            onRefresh={() => this._onReset()}
            refreshing={this.state.refreshing}
          />
          {this.state.showNoData == true && (
            <Text
              style={{
                alignSelf: "center",
                position: "absolute",
                fontSize: wp("6%"),
                fontFamily: Fonts.montserratRegular,
                marginTop: hp("45%"),
              }}
            >
              No Void Deliveries Found
            </Text>
          )}
        </View>

        {this.state.showLoader && <AppLoader viewRef={this.state.showLoader} />}

        {this.state.showToaster && (
          <Toastpopup
            backPress={() => this.setState({ showToaster: false })}
            toastMessage={this.state.toastMessage}
            type={this.state.toastType}
          />
        )}

        {this.state.showAlert && (
          <Alert
            title={Strings.popup.success}
            desc={Strings.popup.forgotSuccess}
            okTap={() => {
              this.okTap();
            }}
          />
        )}

        {this.state.showDelete && (
          <DeletePop
            container={{
              bottom: 0,
            }}
            title={Strings.popup.success}
            desc={Strings.popup.restore}
            acceptTap={() => {
              this.setState({ showDelete: false });
              this.restoreList(
                this.state.selectedItem,
                this.state.selectedIndex
              );
            }}
            declineTap={() => {
              this.setState({
                showDelete: false,
                selectedItem: [],
                selectedIndex: null,
              });
            }}
          />
        )}
  {this.state.showFilter &&
        <Modal
          isVisible={this.state.showFilter}
          style={{
            paddingTop: 45,
            paddingBottom: 30,
            margin: 0,
            backgroundColor: Colors.white,
          }}
        >
          {this.renderFilter()}
        </Modal>
        }
      </SafeAreaView>
        }
        </>
    );
  }
}

const mapStateToProps = (state) => {
  const {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    userDetails,
    projectSwitched,
  } = state.LoginReducer;

  return {
    changeTab,
    showMenu,
    projectDetails,
    checkCameBack,
    userDetails,
    projectSwitched,
  };
};

export default compose(
  connect(mapStateToProps, {
  changeTab,
  showSideMenu,
  storeLastid,
  cameBack,
  clickAdd,
  editData,
  onTapSearch,
  storeRole,
  goToVoid,
  refreshDeliveryList,
  refreshDashboard,
}),
withBackHandler
)(VoidList);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.white,
    // height: hp("50%"),
  },
  parentContainer: {
    flex: 1,
    backgroundColor: "#FCFBFC",
  },
  headerContainer: {
    width: wp("100%"),
    height: hp("8%"),
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "#FFF",
    borderColor: Colors.shadowColor,
    borderBottomWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
  },
  void: {
    alignSelf: "flex-end",
    color: "#FF3939",
    margin: 15,
    fontSize: wp("4%"),
    textDecorationLine: "underline",
  },
  title: {
    color: Colors.black,
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratBold,
    marginBottom: hp("2%"),
    marginLeft: wp("4%"),
  },
  headerRowContainer: {
    flex: 1,
    height: hp("15%"),
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  image: {
    marginRight: wp("3%"),
    marginBottom: hp("2%"),
  },
  flatlistContainer: {
    width: wp("95%"),
    marginVertical: 10,
    backgroundColor: Colors.white,
    borderColor: Colors.shadowColor,
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("2%"),
  },
  nameContainer: {
    width: wp("95%"),
    alignSelf: "center",
    // alignItems: 'center',
    flexDirection: "row",
  },
  detailContainer: {
    width: wp("85%"),
    marginLeft: 20,
    justifyContent: "center",
  },
  imagePlaceholder: {
    width: wp("14%"),
    height: wp("14%"),
    borderRadius: wp("7%"),
    marginLeft: wp("6%"),
  },
  nameText: {
    color: Colors.black,
    fontSize: 14,
    fontFamily: Fonts.montserratSemiBold,
    marginTop: 10,
    marginLeft: 10,
    width: "85%",
  },
  companyText: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    marginTop: hp("1%"),
  },
  dotMenu: {
    marginTop: hp("2%"),
  },
  emailContainer: {
    flex: 1,
    marginLeft: 10,
    marginRight: 10,
  },
  emailTitle: {
    color: "#5B5B5B",
    fontSize: wp("4%"),
    fontFamily: Fonts.montserratRegular,
    margin: 5,
  },
  emailText: {
    color: "#1E1E1E",
    fontSize: 14,
    fontFamily: Fonts.montserratRegular,
    margin: 5,
    marginTop: 0,
  },
  customDropdownStyle: {
    justifyContent: "center",
    alignItems: "center",
    width: wp("30%"),
    height: 40,
    marginRight: 10,
  },
  customOptionsStyle: {
    justifyContent: "flex-end",
    height: hp("14%"),
    width: wp("35%"),
    marginLeft: 5,
    borderColor: Colors.white,
    marginRight: wp("20%"),
    borderWidth: 0.3,
    shadowOffset: { width: 4, height: 4 },
    shadowOpacity: 1,
    elevation: 3,
    shadowColor: "rgba(0,0,0,0.14)",
    alignSelf: "center",
    borderRadius: wp("5%"),
    //outlineProvider: 'bounds'
  },
  customOptionsTextStyle: {
    fontSize: 11,
    fontFamily: Fonts.montserratMedium,
    textAlign: "center",
    color: Colors.planDesc,
    height: hp("5%"),
  },
  imageContainer: {
    marginRight: wp("20%"),
  },
  subContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 5,
  },
  subtext: {
    width: "45%",
    color: "#5b5b5b",
    fontSize: 11,
    marginLeft: 10,
    fontFamily: Fonts.montserratRegular,
  },
    datePickerOkLabel: {
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 6,
    paddingBottom: 6,
    fontFamily: Fonts.montserratBold,
  },
  datePickerOkContainer: {
    width: "100%",
    alignItems: "center",
    backgroundColor: Colors.white,
    paddingBottom: hp("2%"),
  },
  equipmentContainer:{
    height: hp("4%"),
    paddingBottom:5
  },
  renderEquipStyle:{
   marginBottom:10,
  },
  equipTextStyle:{
    width:'100%',
    fontSize:16,
    paddingTop:2
  },
});

const searchStyles = StyleSheet.create({
  searchHeader: {
    marginTop: hp("2%"),
    height: hp("18%"),
    width: wp("95%"),
    alignSelf: "center",
  },
  mainContainer: {
    width: "100%",
    height: hp("6%"),
    flexDirection: "row",
    alignItems: "center",
  },
  closeBtn: {
    width: wp("15%"),
    height: hp("8%"),
    marginLeft: wp("2%"),
    justifyContent: "center",
    alignItems: "center",
  },
  closeImg: {
    width: wp("5%"),
    height: hp("5%"),
  },
  titleText: {
    color: Colors.black,
    fontSize: wp("6%"),
    fontFamily: Fonts.montserratSemiBold,
  },
});

const modalStyles = StyleSheet.create({
  container: {
    flex: 1,
    width: wp("100%"),
    height: hp("95%"),
  },
  topContainer: {
    flexDirection: "row",
    margin: 20,
    height: 40,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 20,
  },
  title: {
    color: Colors.black,
    fontSize: wp("5.5%"),
    fontFamily: Fonts.montserratMedium,
  },
  buttonContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    marginBottom: 50,
    justifyContent: "space-around",
  },
  cancelButton: {
    backgroundColor: `rgba(117,117,117,0.2)`,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  cancelText: {
    color: Colors.buttonBackground,
    fontSize: wp("4.5%"),
    fontFamily: Fonts.montserratBold,
  },
  applyButton: {
    backgroundColor: Colors.themeOpacity,
    width: wp("40%"),
    height: hp("5%"),
    borderRadius: hp("2.5%"),
    justifyContent: "center",
    alignItems: "center",
  },
  applyText: {
    color: Colors.themeColor,
    fontSize: wp("4.5%"),
    fontFamily: Fonts.montserratBold,
  },
  filterPlaceholder:{
    color: Colors.placeholder,
    fontSize: 14,
    fontFamily: Fonts.montserratMedium,
  },
  marginPlaceholder:{
    marginTop:10,
  },
  responisblePersonFilter:{
    marginTop: 10,
    height:65 
  },
});