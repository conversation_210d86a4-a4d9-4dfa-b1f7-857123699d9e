#!/bin/bash

# Verify that patches are correctly applied

echo "🔍 Verifying React Native iOS patches..."

# Check boost.podspec
echo "Checking boost.podspec..."
if grep -q "boost_1_83_0.tar.bz2" node_modules/react-native/third-party-podspecs/boost.podspec; then
    echo "✅ boost.podspec: Updated to version 1.83.0"
else
    echo "❌ boost.podspec: Still using old version"
fi

# Check yoga boolean operands fix
echo "Checking yoga boolean operands fix..."
if grep -q "hadOverflow() |" node_modules/react-native/ReactCommon/yoga/yoga/Yoga.cpp; then
    echo "✅ yoga boolean operands: Fixed (using bitwise OR)"
else
    echo "❌ yoga boolean operands: Still using logical OR"
fi

# Check if react-native-calendars patch exists
echo "Checking react-native-calendars patch..."
if [ -f "patches/react-native-calendars+1.1313.0.patch" ]; then
    echo "✅ react-native-calendars patch: Available"
else
    echo "⚠️  react-native-calendars patch: Not found"
fi

# Check patch-package in package.json
echo "Checking postinstall script..."
if grep -q "patch-package" package.json; then
    echo "✅ postinstall script: patch-package configured"
else
    echo "❌ postinstall script: patch-package not configured"
fi

echo ""
echo "🎉 Patch verification complete!"
echo ""
echo "Next steps:"
echo "1. Run 'npm install' to ensure all patches are applied"
echo "2. Run 'cd ios && pod install' to update iOS dependencies"
echo "3. Build and test your app" 